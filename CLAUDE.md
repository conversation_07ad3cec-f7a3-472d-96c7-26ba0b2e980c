# Fleet Management Frontend Guidelines

## Build/Dev Commands
```
npm run dev     # Start development server
npm run build   # Build for production
npm run start   # Run production build
npm run lint    # Run ESLint
npm run storybook # Run Storybook on port 6006
```

## Code Style Guidelines
- **TypeScript**: Use strict typing. Avoid `any` when possible
- **Naming**: Use camelCase for variables/functions, PascalCase for components/types
- **Imports**: Group imports by external libraries, then internal modules
- **Components**: Follow React functional component pattern with proper type definitions
- **State Management**: Use React context for global state
- **CSS**: Use Tailwind CSS utility classes for styling
- **Error Handling**: Use try/catch for async operations
- **Forms**: Leverage react-hook-form with zod schemas for validation

## Project Structure
- Components live in `src/components/`
- Page components in `src/app/`
- Context providers in `src/context/`
- Reusable hooks in `src/hooks/`
- Types in `src/lib/types/`