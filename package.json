{"name": "fleet-management", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:https": "next dev --experimental-https", "build": "next build", "start": "next start", "lint": "next lint", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^4.1.0", "@mantine/dates": "^7.17.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.79.0", "@tanstack/react-query-next-experimental": "^5.79.0", "@tanstack/react-table": "^8.21.2", "@types/node": "^22.14.0", "@types/pako": "^2.0.3", "axios": "^1.8.4", "barchart": "^0.6.1", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-react": "^8.5.2", "file-saver": "^2.0.5", "framer-motion": "^12.4.3", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.0", "jspdf-autotable": "^5.0.2", "jszip": "^3.10.1", "leaflet": "^1.9.4", "leaflet-routing-machine": "^3.2.12", "lucide-react": "^0.475.0", "next": "15.1.6", "next-auth": "^4.24.11", "next-themes": "^0.4.4", "pako": "^2.1.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.6.6", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-leaflet": "^5.0.0-rc.2", "react-to-print": "^3.0.5", "recharts": "^2.15.2", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.1", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@storybook/react": "8.5.8", "@storybook/test": "8.5.8", "@types/file-saver": "^2.0.7", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.9", "@types/leaflet": "^1.9.17", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.1.6", "eslint-plugin-storybook": "^0.11.3", "postcss": "^8.5.1", "storybook": "8.5.8", "typescript": "^5"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}