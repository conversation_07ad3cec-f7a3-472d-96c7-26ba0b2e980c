"use client";

import { createContext, useContext, useEffect, useState } from "react";

interface PrazoDePagamentoContextProps {
  prazosDePagamento: prazo_de_pagamento[];
  setPrazosDePagamento: React.Dispatch<
    React.SetStateAction<prazo_de_pagamento[]>
  >;
  loading: boolean;
  error: string | null;
}

export const PrazoDePagamentoContext = createContext<
  PrazoDePagamentoContextProps | undefined
>(undefined);

export const PrazoDePagamentoProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const [prazosDePagamento, setPrazosDePagamento] = useState<
    prazo_de_pagamento[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPrazosDePagamento = async () => {
      try {
        const response = await fetch("/api/prazo-de-pagamento");
        if (!response.ok)
          throw new Error("Erro ao buscar prazos de pagamento.");

        const data = await response.json();
        setPrazosDePagamento(data.data.prazos);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchPrazosDePagamento();
  }, []);

  return (
    <PrazoDePagamentoContext.Provider
      value={{ prazosDePagamento, setPrazosDePagamento, loading, error }}
    >
      {children}
    </PrazoDePagamentoContext.Provider>
  );
};

export const usePrazoDePagamento = () => {
  const context = useContext(PrazoDePagamentoContext);
  if (!context) {
    throw new Error(
      "usePrazoDePagamento deve ser usado dentro de um PrazoDePagamentoProvider"
    );
  }
  return context;
};
