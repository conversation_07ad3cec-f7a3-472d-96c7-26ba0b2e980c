import { createContext, useContext, useEffect, useState } from "react";

interface LembreteContextProps {
  lembretes: lembrete[];
  setLembretes: React.Dispatch<React.SetStateAction<lembrete[]>>;
  loading: boolean;
  error: string | null;
}

export const LembreteContext = createContext<LembreteContextProps | undefined>(
  undefined
);

export const LembreteProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [lembretes, setLembretes] = useState<lembrete[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchLembretes = async () => {
      try {
        const response = await fetch("/api/lembrete");
        if (!response.ok) throw new Error("Erro ao buscar lembretes.");

        const data = await response.json();
        setLembretes(data.data);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchLembretes();
  }, []);

  return (
    <LembreteContext.Provider
      value={{ lembretes, setLembretes, loading, error }}
    >
      {children}
    </LembreteContext.Provider>
  );
};

export const useLembrete = () => {
  const context = useContext(LembreteContext);
  if (!context) {
    throw new Error("useLembrete deve ser usado dentro de um LembreteProvider");
  }
  return context;
};
