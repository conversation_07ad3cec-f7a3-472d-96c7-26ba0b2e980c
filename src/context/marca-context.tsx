import { createContext, useContext, useEffect, useState } from "react";

interface MarcaContextProps {
  marcas: marca[];
  setMarcas: React.Dispatch<React.SetStateAction<marca[]>>;
  loading: boolean;
  error: string | null;
}

export const MarcaContext = createContext<MarcaContextProps | undefined>(
  undefined
);

export const MarcaProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [marcas, setMarcas] = useState<marca[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMarcas = async () => {
      try {
        const response = await fetch("/api/marca");
        if (!response.ok) throw new Error("Erro ao buscar marcas.");

        const data = await response.json();
        setMarcas(data.data.data);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchMarcas();
  }, []);

  return (
    <MarcaContext.Provider value={{ marcas, setMarcas, loading, error }}>
      {children}
    </MarcaContext.Provider>
  );
};

export const useMarca = () => {
  const context = useContext(MarcaContext);
  if (!context) {
    throw new Error("useMarca deve ser usado dentro de um MarcaProvider");
  }
  return context;
};
