"use client";

import { createContext, useContext, useEffect, useState } from "react";

interface ServicoCredenciadoContextProps {
  servicosCredenciados: servico_credenciado[];
  setServicosCredenciados: React.Dispatch<
    React.SetStateAction<servico_credenciado[]>
  >;
  loading: boolean;
  error: string | null;
}

export const ServicoCredenciadoContext = createContext<
  ServicoCredenciadoContextProps | undefined
>(undefined);

export const ServicoCredenciadoProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const [servicosCredenciados, setServicosCredenciados] = useState<
    servico_credenciado[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchServicosCredenciados = async () => {
      try {
        const response = await fetch("/api/servico-credenciado");
        if (!response.ok)
          throw new Error("Erro ao buscar serviços credenciados.");

        const data = await response.json();
        setServicosCredenciados(data.data);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchServicosCredenciados();
  }, []);

  return (
    <ServicoCredenciadoContext.Provider
      value={{ servicosCredenciados, setServicosCredenciados, loading, error }}
    >
      {children}
    </ServicoCredenciadoContext.Provider>
  );
};

export const useServicoCredenciado = () => {
  const context = useContext(ServicoCredenciadoContext);
  if (!context) {
    throw new Error(
      "useServicoCredenciado deve ser usado dentro de um ServicoCredenciadoProvider"
    );
  }
  return context;
};
