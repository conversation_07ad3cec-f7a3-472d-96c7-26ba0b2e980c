"use client";

import { createContext, useContext, useEffect, useState } from "react";
interface TiposDeOsContextProps {
  tiposDeOs: tipo_de_os[];
  setTiposDeOs: React.Dispatch<React.SetStateAction<tipo_de_os[]>>;
  loading: boolean;
  error: string | null;
}
export const TiposDeOsContext = createContext<
  TiposDeOsContextProps | undefined
>(undefined);

export const TiposDeOsProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [tiposDeOs, setTiposDeOs] = useState<tipo_de_os[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchApi = async () => {
      try {
        const response = await fetch("/api/tipos-de-os");
        if (!response.ok) throw new Error("Erro ao buscar veículos.");

        const data = await response.json();
        setTiposDeOs(data.data);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchApi();
  }, []);

  return (
    <TiposDeOsContext.Provider
      value={{ setTiposDeOs, tiposDeOs, loading, error }}
    >
      {children}
    </TiposDeOsContext.Provider>
  );
};

export const useTiposDeOs = () => {
  const context = useContext(TiposDeOsContext);
  if (!context) {
    throw new Error(
      "useTiposDeOs deve ser usado dentro de um TiposDeOsProvider"
    );
  }
  return context;
};
