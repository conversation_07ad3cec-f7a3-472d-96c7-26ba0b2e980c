import { createContext, useContext, useEffect, useState } from "react";

interface PercursoContextProps {
  percursos: percurso[];
  setPercursos: React.Dispatch<React.SetStateAction<percurso[]>>;
  loading: boolean;
  error: string | null;
}

export const PercursoContext = createContext<PercursoContextProps | undefined>(
  undefined
);

export const PercursoProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [percursos, setPercursos] = useState<percurso[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPercursos = async () => {
      try {
        const response = await fetch("/api/percurso");
        if (!response.ok) throw new Error("Erro ao buscar percursos.");

        const data = await response.json();
        setPercursos(data.data.percursos);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchPercursos();
  }, []);

  return (
    <PercursoContext.Provider
      value={{ percursos, setPercursos, loading, error }}
    >
      {children}
    </PercursoContext.Provider>
  );
};

// Criando o hook personalizado para usar o contexto
export const usePercurso = () => {
  const context = useContext(PercursoContext);
  if (!context) {
    throw new Error("usePercurso deve ser usado dentro de um PercursoProvider");
  }
  return context;
};
