"use client";

import { createContext, useContext, useEffect, useState } from "react";

// Interface para o tipo de histórico de manutenção
export interface HistoricoManutencao {
  id: string;
  centro_de_custo: string;
  placa: string;
  marca: string;
  modelo: string;
  versao: string;
  data_servico: Date;
  tipo_servico: string;
  valor: number;
  credenciado: string;
}

interface HistoricoManutencaoContextProps {
  historicoManutencoes: HistoricoManutencao[];
  setHistoricoManutencoes: React.Dispatch<React.SetStateAction<HistoricoManutencao[]>>;
  loading: boolean;
  error: string | null;
}

export const HistoricoManutencaoContext = createContext<
  HistoricoManutencaoContextProps | undefined
>(undefined);

export const HistoricoManutencaoProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const [historicoManutencoes, setHistoricoManutencoes] = useState<HistoricoManutencao[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchHistoricoManutencoes = async () => {
      try {
        const response = await fetch("/api/historico-manutencao");
        
        if (!response.ok) {
          throw new Error("Erro ao buscar histórico de manutenções");
        }
        
        const data = await response.json();
        
        if (data.success && data.data.historicoManutencoes) {
          setHistoricoManutencoes(data.data.historicoManutencoes);
        } else {
          throw new Error(data.message || "Erro desconhecido");
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido");
      } finally {
        setLoading(false);
      }
    };

    fetchHistoricoManutencoes();
  }, []);

  return (
    <HistoricoManutencaoContext.Provider
      value={{ historicoManutencoes, setHistoricoManutencoes, loading, error }}
    >
      {children}
    </HistoricoManutencaoContext.Provider>
  );
};

export const useHistoricoManutencao = () => {
  const context = useContext(HistoricoManutencaoContext);
  if (!context) {
    throw new Error(
      "useHistoricoManutencao deve ser usado dentro de um HistoricoManutencaoProvider"
    );
  }
  return context;
}; 