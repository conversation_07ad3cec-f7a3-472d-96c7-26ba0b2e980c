import { createContext, useContext, useEffect, useState } from "react";

interface ModeloContextProps {
  modelos: modelo[];
  setModelos: React.Dispatch<React.SetStateAction<modelo[]>>;
  loading: boolean;
  error: string | null;
}

export const ModeloContext = createContext<ModeloContextProps | undefined>(
  undefined
);

export const ModeloProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [modelos, setModelos] = useState<modelo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchModelos = async () => {
      try {
        const response = await fetch("/api/modelo");
        if (!response.ok) throw new Error("Erro ao buscar modelos.");

        const data = await response.json();
        setModelos(data.data);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchModelos();
  }, []);

  return (
    <ModeloContext.Provider value={{ modelos, setModelos, loading, error }}>
      {children}
    </ModeloContext.Provider>
  );
};

export const useModelo = () => {
  const context = useContext(ModeloContext);
  if (!context) {
    throw new Error("useModelo deve ser usado dentro de um ModeloProvider");
  }
  return context;
};
