import { createContext, useContext, useEffect, useState } from "react";

interface CredenciadoContextProps {
  credenciados: credenciado[];
  setCredenciados: React.Dispatch<React.SetStateAction<credenciado[]>>;
  loading: boolean;
  setPagination: React.Dispatch<
    React.SetStateAction<{
      total: number;
      pages: number;
      currentPage: number;
      perPage: number;
    }>
  >;
  error: string | null;
  pagination: {
    total: number;
    pages: number;
    currentPage: number;
    perPage: number;
  };
  fetchCredenciados: (
    page?: number,
    limit?: number,
    search?: string,
    filters?: any
  ) => Promise<void>;
}

export const CredenciadoContext = createContext<
  CredenciadoContextProps | undefined
>(undefined);

export const CredenciadoProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [credenciados, setCredenciados] = useState<credenciado[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    total: 0,
    pages: 0,
    currentPage: 1,
    perPage: 10,
  });

  const fetchCredenciados = async (
    page = 1,
    limit = 10,
    search = "",
    filters = {}
  ) => {
    setLoading(true);
    try {
      // Construir URL com parâmetros de consulta
      let url = `/api/credenciado?page=${page}&limit=${limit}`;
      if (search) url += `&search=${encodeURIComponent(search)}`;

      // Adicionar filtros adicionais à URL
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== "") {
            url += `&${key}=${encodeURIComponent(String(value))}`;
          }
        });
      }

      const response = await fetch(url);
      if (!response.ok) throw new Error("Erro ao buscar credenciados.");

      const result = await response.json();
      if (result.success && result.data) {
        setCredenciados(result.data.credenciados);
        setPagination((prev) => ({
          ...prev,
          total: result.data.pagination.total,
          pages: result.data.pagination.pages,
        }));
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Erro desconhecido.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCredenciados();
  }, []);

  useEffect(() => {
    fetchCredenciados(pagination.currentPage, pagination.perPage);
  }, [pagination.currentPage, pagination.perPage]);

  return (
    <CredenciadoContext.Provider
      value={{
        credenciados,
        setCredenciados,
        loading,
        error,
        pagination,
        fetchCredenciados,
        setPagination,
      }}
    >
      {children}
    </CredenciadoContext.Provider>
  );
};

export const useCredenciado = () => {
  const context = useContext(CredenciadoContext);
  if (!context) {
    throw new Error(
      "useCredenciado deve ser usado dentro de um CredenciadoProvider"
    );
  }
  return context;
};
