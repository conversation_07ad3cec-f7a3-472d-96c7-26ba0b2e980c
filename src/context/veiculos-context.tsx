"use client";

import { createContext, useContext, useEffect, useState } from "react";

interface VeiculosContextProps {
  veiculos: veiculo[];
  setVeiculos: React.Dispatch<React.SetStateAction<veiculo[]>>;
  loading: boolean;
  error: string | null;
}

export const VeiculosContext = createContext<VeiculosContextProps>({
  veiculos: [],
  setVeiculos: () => {},
  loading: true,
  error: null,
});

export const VeiculosProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState<Omit<VeiculosContextProps, "setVeiculos">>({
    veiculos: [],
    loading: true,
    error: null,
  });

  useEffect(() => {
    const fetchVeiculos = async () => {
      try {
        const response = await fetch("/api/veiculos");
        if (!response.ok) throw new Error("Erro ao buscar veículos.");

        const data = await response.json();
        setState((prev) => ({
          ...prev,
          veiculos: data.data?.veiculos || [],
          loading: false,
        }));
      } catch (err) {
        setState((prev) => ({
          ...prev,
          error: err instanceof Error ? err.message : "Erro desconhecido.",
          loading: false,
        }));
      }
    };

    fetchVeiculos();
  }, []);

  const setVeiculos = (newVeiculos: React.SetStateAction<veiculo[]>) => {
    if (typeof newVeiculos === "function") {
      setState((prev) => ({
        ...prev,
        veiculos: newVeiculos(prev.veiculos),
      }));
    } else {
      setState((prev) => ({
        ...prev,
        veiculos: newVeiculos,
      }));
    }
  };

  return (
    <VeiculosContext.Provider value={{ ...state, setVeiculos }}>
      {children}
    </VeiculosContext.Provider>
  );
};

export const useVeiculos = () => {
  const context = useContext(VeiculosContext);
  return context;
};
