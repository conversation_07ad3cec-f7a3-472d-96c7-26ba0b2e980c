import { createContext, useContext, useEffect, useState } from "react";

interface VersaoContextProps {
  versoes: versao[];
  setVersoes: React.Dispatch<React.SetStateAction<versao[]>>;
  loading: boolean;
  error: string | null;
}

// Criando o contexto
export const VersaoContext = createContext<VersaoContextProps | undefined>(
  undefined
);

// Criando o provider
export const VersaoProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [versoes, setVersoes] = useState<versao[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchVersoes = async () => {
      try {
        const response = await fetch("/api/versao");
        if (!response.ok) throw new Error("Erro ao buscar versões.");

        const data = await response.json();
        setVersoes(data.data.data);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchVersoes();
  }, []);

  return (
    <VersaoContext.Provider value={{ versoes, setVersoes, loading, error }}>
      {children}
    </VersaoContext.Provider>
  );
};

export const useVersao = () => {
  const context = useContext(VersaoContext);
  if (!context) {
    throw new Error("useVersao deve ser usado dentro de um VersaoProvider");
  }
  return context;
};
