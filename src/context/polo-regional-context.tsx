"use client";

import { createContext, useContext, useEffect, useState } from "react";

interface PoloRegionalContextProps {
  polosRegionais: polo_regional[];
  setPolosRegionais: React.Dispatch<React.SetStateAction<polo_regional[]>>;
  loading: boolean;
  error: string | null;
}

export const PoloRegionalContext = createContext<
  PoloRegionalContextProps | undefined
>(undefined);

export const PoloRegionalProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [polosRegionais, setPolosRegionais] = useState<polo_regional[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPolosRegionais = async () => {
      try {
        const response = await fetch("/api/polo-regional");
        if (!response.ok) throw new Error("Erro ao buscar polos regionais.");

        const data = await response.json();
        setPolosRegionais(data.data.polos);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchPolosRegionais();
  }, []);

  return (
    <PoloRegionalContext.Provider
      value={{ polosRegionais, setPolosRegionais, loading, error }}
    >
      {children}
    </PoloRegionalContext.Provider>
  );
};

export const usePoloRegional = () => {
  const context = useContext(PoloRegionalContext);
  if (!context) {
    throw new Error(
      "usePoloRegional deve ser usado dentro de um PoloRegionalProvider"
    );
  }
  return context;
};
