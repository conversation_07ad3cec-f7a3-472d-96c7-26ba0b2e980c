"use client";
import { createContext, useContext, useEffect, useState } from "react";

interface AbastecimentoContextProps {
  abastecimentos: abastecimento[];
  setAbastecimentos: React.Dispatch<React.SetStateAction<abastecimento[]>>;
  loading: boolean;
  error: string | null;
}

export const AbastecimentoContext = createContext<
  AbastecimentoContextProps | undefined
>(undefined);

export const AbastecimentoProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [abastecimentos, setAbastecimentos] = useState<abastecimento[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAbastecimentos = async () => {
      try {
        const response = await fetch("/api/abastecimento");
        if (!response.ok) throw new Error("Erro ao buscar abastecimentos.");

        const data = await response.json();
        setAbastecimentos(data.data.abastecimentos);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchAbastecimentos();
  }, []);

  return (
    <AbastecimentoContext.Provider
      value={{ abastecimentos, setAbastecimentos, loading, error }}
    >
      {children}
    </AbastecimentoContext.Provider>
  );
};

export const useAbastecimento = () => {
  const context = useContext(AbastecimentoContext);
  if (!context) {
    throw new Error(
      "useAbastecimento deve ser usado dentro de um AbastecimentoProvider"
    );
  }
  return context;
};
