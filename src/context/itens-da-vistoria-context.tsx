"use client";

import { createContext, useContext, useEffect, useState } from "react";
interface ItensDaVistoriaContextProps {
  itensDaVistoria: item_da_vistoria[];
  setItensDaVistoria: React.Dispatch<React.SetStateAction<item_da_vistoria[]>>;
  loading: boolean;
  error: string | null;
}
export const ItensDaVistoriaContext = createContext<
  ItensDaVistoriaContextProps | undefined
>(undefined);

export const ItensDaVistoriaProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const [itensDaVistoria, setItensDaVistoria] = useState<item_da_vistoria[]>(
    []
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchApi = async () => {
      try {
        const response = await fetch("/api/itens-da-vistoria");
        if (!response.ok) throw new Error("Erro ao buscar veículos.");

        const data = await response.json();
        setItensDaVistoria(data.itens_da_vistoria);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchApi();
  }, []);

  return (
    <ItensDaVistoriaContext.Provider
      value={{ itensDaVistoria, setItensDaVistoria, loading, error }}
    >
      {children}
    </ItensDaVistoriaContext.Provider>
  );
};

export const useItensDaVistoria = () => {
  const context = useContext(ItensDaVistoriaContext);
  if (!context) {
    throw new Error(
      "useItensDaVistoria deve ser usado dentro de um ItensDaVistoriaProvider"
    );
  }
  return context;
};
