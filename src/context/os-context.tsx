import { createContext, useContext, useEffect, useState } from "react";

interface OSContextProps {
  ordensDeServico: OS[];
  setOrdensDeServico: React.Dispatch<React.SetStateAction<OS[]>>;
  loading: boolean;
  error: string | null;
}

export const OSContext = createContext<OSContextProps | undefined>(undefined);

export const OSProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [ordensDeServico, setOrdensDeServico] = useState<OS[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrdensDeServico = async () => {
      try {
        const response = await fetch("/api/os");
        if (!response.ok) throw new Error("Erro ao buscar ordens de serviço.");

        const data = await response.json();
        setOrdensDeServico(data.data);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchOrdensDeServico();
  }, []);

  return (
    <OSContext.Provider
      value={{ ordensDeServico, setOrdensDeServico, loading, error }}
    >
      {children}
    </OSContext.Provider>
  );
};

export const useOS = () => {
  const context = useContext(OSContext);
  if (!context) {
    throw new Error("useOS deve ser usado dentro de um OSProvider");
  }
  return context;
};
