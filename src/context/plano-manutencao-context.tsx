"use client";

import { planoManutencaoSchema } from "@/components/forms/schemas/plano-manutencao.schemas";
import { createContext, useContext, useState } from "react";
import { z } from "zod";

type PlanoManutencao = z.infer<typeof planoManutencaoSchema>;

interface PlanoManutencaoContextType {
  planos: PlanoManutencao[];
  setPlanos: React.Dispatch<React.SetStateAction<PlanoManutencao[]>>;
  loading: boolean;
  error: string | null;
  adicionarPlano: (plano: PlanoManutencao) => Promise<void>;
  editarPlano: (id: string, plano: PlanoManutencao) => Promise<void>;
  removerPlano: (id: string) => Promise<void>;
  gerarPreventivas: (planoId: string) => Promise<void>;
}

const PlanoManutencaoContext = createContext<PlanoManutencaoContextType | undefined>(undefined);

export function PlanoManutencaoProvider({ children }: { children: React.ReactNode }) {
  const [planos, setPlanos] = useState<PlanoManutencao[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const adicionarPlano = async (plano: PlanoManutencao) => {
    try {
      setLoading(true);
      // Implementar chamada à API para criar plano
      const response = await fetch('/api/planos-manutencao', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(plano)
      });

      if (!response.ok) throw new Error('Erro ao criar plano de manutenção');

      const novoPlan = await response.json();
      setPlanos(plans => [...plans, novoPlan]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const editarPlano = async (id: string, plano: PlanoManutencao) => {
    try {
      setLoading(true);
      // Implementar chamada à API para editar plano
      const response = await fetch(`/api/planos-manutencao/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(plano)
      });

      if (!response.ok) throw new Error('Erro ao editar plano de manutenção');

      const planoAtualizado = await response.json();
      setPlanos(plans => plans.map(p => p.id === id ? planoAtualizado : p));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const removerPlano = async (id: string) => {
    try {
      setLoading(true);
      // Implementar chamada à API para remover plano
      const response = await fetch(`/api/planos-manutencao/${id}`, {
        method: 'DELETE'
      });

      if (!response.ok) throw new Error('Erro ao remover plano de manutenção');

      setPlanos(plans => plans.filter(p => p.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const gerarPreventivas = async (planoId: string) => {
    try {
      setLoading(true);
      // Implementar chamada à API para gerar ordens de serviço preventivas
      const response = await fetch(`/api/planos-manutencao/${planoId}/gerar-preventivas`, {
        method: 'POST'
      });

      if (!response.ok) throw new Error('Erro ao gerar ordens de serviço preventivas');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return (
    <PlanoManutencaoContext.Provider 
      value={{ 
        planos, 
        setPlanos, 
        loading, 
        error, 
        adicionarPlano, 
        editarPlano, 
        removerPlano,
        gerarPreventivas
      }}
    >
      {children}
    </PlanoManutencaoContext.Provider>
  );
}

export function usePlanoManutencao() {
  const context = useContext(PlanoManutencaoContext);
  if (context === undefined) {
    throw new Error('usePlanoManutencao must be used within a PlanoManutencaoProvider');
  }
  return context;
} 