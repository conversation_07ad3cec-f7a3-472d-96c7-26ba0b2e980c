"use client";

import { createContext, useContext, useEffect, useState } from "react";
interface VistoriasContextProps {
  vistorias: vistoria[];
  setVistorias: React.Dispatch<React.SetStateAction<vistoria[]>>;
  loading: boolean;
  error: string | null;
}
export const VistoriasContext = createContext<
  VistoriasContextProps | undefined
>(undefined);

export const VistoriasProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const [vistorias, setVistorias] = useState<vistoria[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  return (
    <VistoriasContext.Provider
      value={{ vistorias, setVistorias, loading, error }}
    >
      {children}
    </VistoriasContext.Provider>
  );
};

export const useVistorias = () => {
  const context = useContext(VistoriasContext);
  if (!context) {
    throw new Error(
      "useVistorias deve ser usado dentro de um VistoriasProvider"
    );
  }
  return context;
};
