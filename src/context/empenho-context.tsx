import { createContext, useContext, useEffect, useState } from "react";

interface EmpenhoContextProps {
  empenhos: empenho[];
  setEmpenhos: React.Dispatch<React.SetStateAction<empenho[]>>;
  loading: boolean;
  error: string | null;
}

// Criando o contexto
export const EmpenhoContext = createContext<EmpenhoContextProps | undefined>(
  undefined
);

// Criando o provider
export const EmpenhoProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [empenhos, setEmpenhos] = useState<empenho[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEmpenhos = async () => {
      try {
        const response = await fetch("/api/empenho");
        if (!response.ok) throw new Error("Erro ao buscar empenhos.");

        const data = await response.json();
        setEmpenhos(data.data.empenhos);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchEmpenhos();
  }, []);

  return (
    <EmpenhoContext.Provider value={{ empenhos, setEmpenhos, loading, error }}>
      {children}
    </EmpenhoContext.Provider>
  );
};

export const useEmpenho = () => {
  const context = useContext(EmpenhoContext);
  if (!context) {
    throw new Error("useEmpenho deve ser usado dentro de um EmpenhoProvider");
  }
  return context;
};
