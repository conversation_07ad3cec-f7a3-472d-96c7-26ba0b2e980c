import { createContext, useContext, useEffect, useState } from "react";

interface ContratoContextProps {
  contratos: contrato[];
  setContratos: React.Dispatch<React.SetStateAction<contrato[]>>;
  loading: boolean;
  error: string | null;
}

export const ContratoContext = createContext<ContratoContextProps | undefined>(
  undefined
);

export const ContratoProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [contratos, setContratos] = useState<contrato[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchContratos = async () => {
      try {
        const response = await fetch("/api/contratos");
        if (!response.ok) throw new Error("Erro ao buscar contratos.");

        const data = await response.json();
        setContratos(data.data.contratos);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchContratos();
  }, []);

  return (
    <ContratoContext.Provider
      value={{ contratos, setContratos, loading, error }}
    >
      {children}
    </ContratoContext.Provider>
  );
};

export const useContrato = () => {
  const context = useContext(ContratoContext);
  if (!context) {
    throw new Error("useContrato deve ser usado dentro de um ContratoProvider");
  }
  return context;
};
