"use client";
import { createContext, useContext, useEffect, useState } from "react";

interface TipoDeFrotaContextProps {
  tiposDeFrota: tipo_de_frota[];
  setTiposDeFrota: React.Dispatch<React.SetStateAction<tipo_de_frota[]>>;
  loading: boolean;
  error: string | null;
}

export const TipoDeFrotaContext = createContext<
  TipoDeFrotaContextProps | undefined
>(undefined);

export const TipoDeFrotaProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [tiposDeFrota, setTiposDeFrota] = useState<tipo_de_frota[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTiposDeFrota = async () => {
      try {
        const response = await fetch("/api/tipo-de-frota");
        if (!response.ok) throw new Error("Erro ao buscar tipos de frota.");

        const data = await response.json();
        setTiposDeFrota(data.data);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchTiposDeFrota();
  }, []);

  return (
    <TipoDeFrotaContext.Provider
      value={{ tiposDeFrota, setTiposDeFrota, loading, error }}
    >
      {children}
    </TipoDeFrotaContext.Provider>
  );
};

export const useTipoDeFrota = () => {
  const context = useContext(TipoDeFrotaContext);
  if (!context) {
    throw new Error(
      "useTipoDeFrota deve ser usado dentro de um TipoDeFrotaProvider"
    );
  }
  return context;
};
