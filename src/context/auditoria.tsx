"use client";

import api from "@/service/api";
import React, { createContext, useCallback, useContext, useState } from "react";

interface Log {
  id: string;
  usuario_nome: string;
  acao_do_usuario: string;
  funcao_do_usuario: string;
  createdAd: Date;
}

interface AuditoriaContextData {
  logs: Log[];
  isLoading: boolean;
  fetchLogs: (page?: number, limit?: number) => Promise<void>;
  totalPages: number;
}

const AuditoriaContext = createContext<AuditoriaContextData>({} as AuditoriaContextData);

export function AuditoriaProvider({ children }: { children: React.ReactNode }) {
  const [logs, setLogs] = useState<Log[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [totalPages, setTotalPages] = useState(1);

  const fetchLogs = useCallback(async (page = 1, limit = 10) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/auditoria`);
      const data = await response.json();
      if (data) {
        setLogs(data.data);
        setTotalPages(Math.ceil(data.length / limit));
      }
    } catch (error) {
      console.error("Erro ao buscar logs:", error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  return (
    <AuditoriaContext.Provider 
      value={{ 
        logs, 
        isLoading, 
        fetchLogs, 
        totalPages 
      }}
    >
      {children}
    </AuditoriaContext.Provider>
  );
}

export function useAuditoria() {
  const context = useContext(AuditoriaContext);

  if (!context) {
    throw new Error("useAuditoria deve ser usado dentro de um AuditoriaProvider");
  }

  return context;
}