"use client";

import { createContext, useContext, useEffect, useState } from "react";
interface CondutoresContextProps {
  condutores: condutor[];
  setCondutores: React.Dispatch<React.SetStateAction<condutor[]>>;
  loading: boolean;
  error: string | null;
}
export const CondutoresContext = createContext<
  CondutoresContextProps | undefined
>(undefined);
export const CondutorProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [condutores, setCondutores] = useState<condutor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCondutores = async () => {
      try {
        const response = await fetch("/api/condutores");
        if (!response.ok) throw new Error("Erro ao buscar condutores.");

        const data = await response.json();
        setCondutores(data.data.condutores);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchCondutores();
  }, []);

  return (
    <CondutoresContext.Provider
      value={{ condutores, setCondutores, loading, error }}
    >
      {children}
    </CondutoresContext.Provider>
  );
};

export const useCondutor = () => {
  const context = useContext(CondutoresContext);
  if (!context) {
    throw new Error(
      "usecondutores deve ser usado dentro de um condutoresProvider"
    );
  }
  return context;
};
