import { createContext, useContext, useEffect, useState } from "react";

interface TipoDeVeiculoContextProps {
  tiposDeVeiculo: tipo_de_veiculo[];
  setTiposDeVeiculo: React.Dispatch<React.SetStateAction<tipo_de_veiculo[]>>;
  loading: boolean;
  error: string | null;
}

export const TipoDeVeiculoContext = createContext<
  TipoDeVeiculoContextProps | undefined
>(undefined);

export const TipoDeVeiculoProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [tiposDeVeiculo, setTiposDeVeiculo] = useState<tipo_de_veiculo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTiposDeVeiculo = async () => {
      try {
        const response = await fetch("/api/tipo-de-veiculo");
        if (!response.ok) throw new Error("Erro ao buscar tipos de veículo.");

        const data = await response.json();
        setTiposDeVeiculo(data.data);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchTiposDeVeiculo();
  }, []);

  return (
    <TipoDeVeiculoContext.Provider
      value={{ tiposDeVeiculo, setTiposDeVeiculo, loading, error }}
    >
      {children}
    </TipoDeVeiculoContext.Provider>
  );
};

export const useTipoDeVeiculo = () => {
  const context = useContext(TipoDeVeiculoContext);
  if (!context) {
    throw new Error(
      "useTipoDeVeiculo deve ser usado dentro de um TipoDeVeiculoProvider"
    );
  }
  return context;
};
