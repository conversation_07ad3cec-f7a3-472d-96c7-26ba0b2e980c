"use client";
import { createContext, useContext, useEffect, useState } from "react";

interface DespesaContextProps {
  despesas: despesa[];
  setDespesas: React.Dispatch<React.SetStateAction<despesa[]>>;
  loading: boolean;
  error: string | null;
}

export const DespesaContext = createContext<DespesaContextProps | undefined>(
  undefined
);

export const DespesaProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [despesas, setDespesas] = useState<despesa[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDespesas = async () => {
      try {
        const response = await fetch("/api/despesa");
        if (!response.ok) throw new Error("Erro ao buscar despesas.");

        const data = await response.json();
        setDespesas(data.data.despesas);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchDespesas();
  }, []);

  return (
    <DespesaContext.Provider value={{ despesas, setDespesas, loading, error }}>
      {children}
    </DespesaContext.Provider>
  );
};

// Criando o hook personalizado para usar o contexto
export const useDespesa = () => {
  const context = useContext(DespesaContext);
  if (!context) {
    throw new Error("useDespesa deve ser usado dentro de um DespesaProvider");
  }
  return context;
};
