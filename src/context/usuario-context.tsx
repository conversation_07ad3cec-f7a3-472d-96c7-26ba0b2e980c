"use client";
import { createContext, useContext, useEffect, useState } from "react";

interface UsuarioContextProps {
  usuarios: usuario[];
  setUsuarios: React.Dispatch<React.SetStateAction<usuario[]>>;
  loading: boolean;
  error: string | null;
}

export const UsuarioContext = createContext<UsuarioContextProps | undefined>(undefined);

export const UsuarioProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [usuarios, setUsuarios] = useState<usuario[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUsuarios = async () => {
      try {
        const response = await fetch("/api/usuario");
        if (!response.ok) throw new Error("Erro ao buscar usuários.");
        const data = await response.json();
        setUsuarios(data.data.users);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchUsuarios();
  }, []);

  return (
    <UsuarioContext.Provider value={{ usuarios, setUsuarios, loading, error }}>
      {children}
    </UsuarioContext.Provider>
  );
};

export const useUsuario = () => {
  const context = useContext(UsuarioContext);
  if (!context) {
    throw new Error("useUsuario deve ser usado dentro de um UsuarioProvider");
  }
  return context;
};
