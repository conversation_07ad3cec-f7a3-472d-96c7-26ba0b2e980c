"use client";

import { createContext, useContext, useEffect, useState } from "react";

interface ChecklistsContextProps {
  checklists: checklist[];
  setChecklists: React.Dispatch<React.SetStateAction<checklist[]>>;
  loading: boolean;
  error: string | null;
}

export const ChecklistsContext = createContext<
  ChecklistsContextProps | undefined
>(undefined);

export const ChecklistProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [checklists, setChecklists] = useState<checklist[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchChecklists = async () => {
      try {
        const response = await fetch("/api/checklist");
        if (!response.ok) throw new Error("Erro ao buscar checklists.");

        const data = await response.json();
        setChecklists(data.data.checklists);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchChecklists();
  }, []);

  return (
    <ChecklistsContext.Provider
      value={{ checklists, setChecklists, loading, error }}
    >
      {children}
    </ChecklistsContext.Provider>
  );
};

export const useChecklist = () => {
  const context = useContext(ChecklistsContext);
  if (!context) {
    throw new Error(
      "useChecklist deve ser usado dentro de um ChecklistProvider"
    );
  }
  return context;
};
