"use client";
import { createContext, useContext, useEffect, useState } from "react";

interface TipoDeDespesaContextProps {
  tiposDeDespesa: tipo_de_despesa[];
  setTiposDeDespesa: React.Dispatch<React.SetStateAction<tipo_de_despesa[]>>;
  loading: boolean;
  error: string | null;
}

export const TipoDeDespesaContext = createContext<
  TipoDeDespesaContextProps | undefined
>(undefined);

export const TipoDeDespesaProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [tiposDeDespesa, setTiposDeDespesa] = useState<tipo_de_despesa[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTiposDeDespesa = async () => {
      try {
        const response = await fetch("/api/tipos-de-despesa");
        if (!response.ok) throw new Error("Erro ao buscar tipos de despesa.");

        const data = await response.json();
        setTiposDeDespesa(data.data);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchTiposDeDespesa();
  }, []);

  return (
    <TipoDeDespesaContext.Provider
      value={{ tiposDeDespesa, setTiposDeDespesa, loading, error }}
    >
      {children}
    </TipoDeDespesaContext.Provider>
  );
};

// Criando o hook personalizado para usar o contexto
export const useTipoDeDespesa = () => {
  const context = useContext(TipoDeDespesaContext);
  if (!context) {
    throw new Error(
      "useTipoDeDespesa deve ser usado dentro de um TipoDeDespesaProvider"
    );
  }
  return context;
};
