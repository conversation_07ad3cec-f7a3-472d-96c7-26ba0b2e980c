import { createContext, useContext, useEffect, useState } from "react";

interface CentroDeCustoContextProps {
  centrosDeCusto: centro_de_custo[];
  setCentrosDeCusto: React.Dispatch<React.SetStateAction<centro_de_custo[]>>;
  loading: boolean;
  error: string | null;
}

export const CentroDeCustoContext = createContext<CentroDeCustoContextProps | undefined>(undefined);

export const CentroDeCustoProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [centrosDeCusto, setCentrosDeCusto] = useState<centro_de_custo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCentrosDeCusto = async () => {
      try {
        const response = await fetch("/api/centro-de-custo");
        if (!response.ok) throw new Error("Erro ao buscar centros de custo.");

        const data = await response.json();
        console.log("Dados do contexto:", data); // Debug

        if (!data.success) {
          throw new Error(data.message || "Erro ao buscar centros de custo.");
        }

        // A nova API retorna data.centrosCusto diretamente
        if (data.data && Array.isArray(data.data.centrosCusto)) {
          setCentrosDeCusto(data.data.centrosCusto);
        } else if (data.data && Array.isArray(data.data)) {
          setCentrosDeCusto(data.data);
        } else {
          setCentrosDeCusto([]);
        }
      } catch (err) {
        console.error("Erro no contexto:", err);
        setError(err instanceof Error ? err.message : "Erro desconhecido.");
      } finally {
        setLoading(false);
      }
    };

    fetchCentrosDeCusto();
  }, []);

  return (
    <CentroDeCustoContext.Provider value={{ centrosDeCusto, setCentrosDeCusto, loading, error }}>
      {children}
    </CentroDeCustoContext.Provider>
  );
};

export const useCentroDeCusto = () => {
  const context = useContext(CentroDeCustoContext);
  if (!context) {
    throw new Error("useCentroDeCusto deve ser usado dentro de um CentroDeCustoProvider");
  }
  return context;
};
