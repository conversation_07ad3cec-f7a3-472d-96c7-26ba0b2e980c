"use server";

import { 
  CentroCustoCreateRequest, 
  CentroCustoUpdateRequest,
  CentroCustoCompleto,
  CentroCustoResponse,
  validateCentroCustoCreate,
  mapCentroCustoToBackend,
  mapCentroCustoUpdateToBackend
} from "@/types/centro-custo.types";

// Base URL para as APIs
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api';

interface ActionResult<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

/**
 * Busca todos os centros de custo
 */
export async function getCentrosCusto(): Promise<ActionResult<CentroCustoResponse>> {
  try {
    const response = await fetch(`${API_BASE_URL}/centro-de-custo`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store', // Sempre buscar dados atualizados
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return {
        success: false,
        error: errorData.message || 'Erro ao buscar centros de custo',
      };
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data,
      message: 'Centros de custo carregados com sucesso',
    };
  } catch (error) {
    console.error('Erro na action getCentrosCusto:', error);
    return {
      success: false,
      error: 'Erro interno do servidor',
    };
  }
}

/**
 * Busca um centro de custo por ID
 */
export async function getCentroCustoById(id: string): Promise<ActionResult<CentroCustoCompleto>> {
  try {
    const response = await fetch(`${API_BASE_URL}/centro-de-custo/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store',
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return {
        success: false,
        error: errorData.message || 'Erro ao buscar centro de custo',
      };
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data,
      message: 'Centro de custo carregado com sucesso',
    };
  } catch (error) {
    console.error('Erro na action getCentroCustoById:', error);
    return {
      success: false,
      error: 'Erro interno do servidor',
    };
  }
}

/**
 * Cria um novo centro de custo
 */
export async function createCentroCusto(data: any): Promise<ActionResult<CentroCustoCompleto>> {
  try {
    // Validação dos dados
    const validation = validateCentroCustoCreate(data);
    if (!validation.isValid) {
      return {
        success: false,
        error: `Campos obrigatórios faltando: ${validation.missingFields.join(', ')}`,
      };
    }

    // Mapeia os dados para o formato do backend
    const mappedData = mapCentroCustoToBackend(data);

    const response = await fetch(`${API_BASE_URL}/centro-de-custo`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(mappedData),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return {
        success: false,
        error: errorData.message || 'Erro ao criar centro de custo',
      };
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data,
      message: result.message || 'Centro de custo criado com sucesso',
    };
  } catch (error) {
    console.error('Erro na action createCentroCusto:', error);
    return {
      success: false,
      error: 'Erro interno do servidor',
    };
  }
}

/**
 * Atualiza um centro de custo existente
 */
export async function updateCentroCusto(id: string, data: any): Promise<ActionResult<CentroCustoCompleto>> {
  try {
    // Mapeia os dados para o formato do backend
    const mappedData = mapCentroCustoUpdateToBackend(data);

    const response = await fetch(`${API_BASE_URL}/centro-de-custo/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(mappedData),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return {
        success: false,
        error: errorData.message || 'Erro ao atualizar centro de custo',
      };
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data,
      message: result.message || 'Centro de custo atualizado com sucesso',
    };
  } catch (error) {
    console.error('Erro na action updateCentroCusto:', error);
    return {
      success: false,
      error: 'Erro interno do servidor',
    };
  }
}

/**
 * Exclui um centro de custo
 */
export async function deleteCentroCusto(id: string): Promise<ActionResult<null>> {
  try {
    const response = await fetch(`${API_BASE_URL}/centro-de-custo/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return {
        success: false,
        error: errorData.message || 'Erro ao excluir centro de custo',
      };
    }

    const result = await response.json();
    return {
      success: true,
      data: null,
      message: result.message || 'Centro de custo excluído com sucesso',
    };
  } catch (error) {
    console.error('Erro na action deleteCentroCusto:', error);
    return {
      success: false,
      error: 'Erro interno do servidor',
    };
  }
}

/**
 * Busca centros de custo por contrato ID
 */
export async function getCentrosCustoPorContrato(contratoId: string): Promise<ActionResult<CentroCustoCompleto[]>> {
  try {
    const response = await fetch(`${API_BASE_URL}/centro-de-custo/contrato/${contratoId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store',
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return {
        success: false,
        error: errorData.message || 'Erro ao buscar centros de custo por contrato',
      };
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data,
      message: 'Centros de custo carregados com sucesso',
    };
  } catch (error) {
    console.error('Erro na action getCentrosCustoPorContrato:', error);
    return {
      success: false,
      error: 'Erro interno do servidor',
    };
  }
}

/**
 * Valida se um centro de custo pode ser excluído
 */
export async function validateCentroCustoDelete(id: string): Promise<ActionResult<boolean>> {
  try {
    const centroCusto = await getCentroCustoById(id);
    
    if (!centroCusto.success || !centroCusto.data) {
      return {
        success: false,
        error: 'Centro de custo não encontrado',
      };
    }

    const data = centroCusto.data;
    
    // Verifica se tem centros filhos
    if (data.centro_custos_filhos && data.centro_custos_filhos.length > 0) {
      return {
        success: false,
        error: 'Este centro de custo possui centros filhos e não pode ser excluído',
      };
    }

    // Verifica se tem veículos associados
    if (data.lotacao_veiculos && data.lotacao_veiculos.length > 0) {
      return {
        success: false,
        error: 'Este centro de custo possui veículos associados e não pode ser excluído',
      };
    }

    // Verifica se tem empenhos associados
    if (data.empenhos && data.empenhos.length > 0) {
      return {
        success: false,
        error: 'Este centro de custo possui empenhos associados e não pode ser excluído',
      };
    }

    return {
      success: true,
      data: true,
      message: 'Centro de custo pode ser excluído',
    };
  } catch (error) {
    console.error('Erro na action validateCentroCustoDelete:', error);
    return {
      success: false,
      error: 'Erro interno do servidor',
    };
  }
}
