const BASE_URL = "https://api.cnpja.com/office";
const API_TOKEN = process.env.CNPJ_API_KEY;

interface SuivApiError {
  message: string;
  status?: number;
}

export async function fetchCnpjAPI<T>(
  endpoint: string,
  params: Record<string, string | number>
): Promise<T> {
  const queryString = new URLSearchParams(
    Object.entries(params).map(([k, v]) => [k, String(v)])
  ).toString();
  
  const url = `${BASE_URL}${endpoint}?${queryString}`;
  
  const response = await fetch(url, {
    headers: {
      Authorization: API_TOKEN || ""
    }
  });
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw {
      message: errorData.message || "Erro na API SUIV",
      status: response.status,
    } as SuivApiError;
  }
  
  return response.json();
}