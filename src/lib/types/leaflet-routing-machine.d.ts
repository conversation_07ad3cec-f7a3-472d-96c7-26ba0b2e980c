import * as L from "leaflet";

declare module "leaflet" {
  namespace Routing {
    interface Waypoint {
      latLng: L.LatLng;
      name?: string;
    }

    interface RoutingControlOptions extends L.ControlOptions {
      waypoints: L.LatLng[];
      router?: any;
      createMarker?: (i: number, waypoint: Waypoint, n: number, options?: any) => L<PERSON>Marker | null;
    }

    interface Control extends L.Control {
      // Adicione métodos/propriedades conforme necessário
    }

    function control(options?: RoutingControlOptions): Control;
  }
}