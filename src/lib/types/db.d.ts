interface OS {
  id: string;
  osNumber: number;
  checklistEntradaId?: string | null;
  checklistSaidaId?: string | null;
  veiculoId: string; //referencia veiculo
  veiculo?: veiculo;
  condutorId?: string; // referencia condutor
  condutor?: condutor;
  tipo_de_osId: string; // referencia tipo_de_os
  tipos_de_os?: tipo_de_os;
  TiposDeOs?: tipo_de_os;
  tipo_manutencao: tipo_de_manutencao;
  estado_de_localizacao: string;
  cidade_de_localizacao: string;
  cidade_loc?: string;
  estado_loc?: string;
  gestorCriador?: string;
  gestorAprovador?: string;
  odometro_atual?: string;
  odometro: number;
  mobilizado: boolean;
  imobilizado: boolean;
  OSId?: string; //referencia e houver
  OS?: OS;
  complemento_os: OS;
  orcamentos?: Orcamento[];
  arquivos: string[];
  arquivosNotaFiscal: string[];
  descricao: string;
  credenciadoId?: string; //referencia ao credenciado se houver
  credenciado?: credenciado;
  orcamento_individual: boolean;
  minimun_orcament: string;
  valor_autorizado: number;
  send_nf_to?: string;
  observacao?: string;
  ordens_de_servicos: OS[];
  createdAt: Date;
  upDateTimedAt: Date;
  status:
    | "lançada"
    | "orcamentaçao"
    | "analise"
    | "autorizada"
    | "execucao"
    | "finalizada"
    | "faturada"
    | "cancelada"
    | "pendente"
    | "Aguardando Aprovação";
  quoteExpiration: number;
  quoteExpirationDate?: Date;
}

interface Orcamento {
  id: string;
  osId: string;
  os: OS;
  credenciadoId: string;
  credenciado: credenciado;
  servicoId?: string;
  servico?: OrcamentoServico[];
  pecasId?: string;
  pecas?: OrcamentoPecas[];
  processedPecas?: OrcamentoPecas[];
  processedServicos?: OrcamentoServico[];
  numeroOrcamento: string;
  dataLancamento: Date;
  validade: Date;
  data_emissao: Date;
  data_entrada: Date;
  prazoEntrega: number;
  valorTotal: number;
  observacoes?: string;
  arquivos: string[];
  arquivosNotaFiscal?: string[];
  createdAt: Date;
  updatedAt: Date;
  status?: string;
}

interface OrcamentoServico {
  id: string;
  descricao: string;
  tipoServico: string;
  quantidadeHoras: number;
  valor: number;
  valorDesconto: number;
  garantia: Date;
  orcamentos: Orcamento[];
  createdAt: Date;
  updatedAt: Date;
  valorUnitario: number;
}

interface OrcamentoPecas {
  id: string;
  descricao: string;
  codigo: string;
  marca: string;
  garantia: Date;
  tipoPecas: string;
  quantidade: number;
  valorUnitario: number;
  valorDesconto: number;
  valorNegociado: number;
  orcamentos: Orcamento[];
  createdAt: Date;
  updatedAt: Date;
}

type tipo_de_manutencao = "Corretiva" | "Preventiva" | "Preditiva" | "Sinistro";

interface tipo_de_os {
  id: string;
  descricao: string;
  obs?: string;
  prazo_para_execucao: string;
  os: OS[];
}

interface veiculo {
  id: string;
  marcaId: string; //referencia com marca
  marca?: marca;
  modeloId: string; //referencia com o modelo
  modelo?: modelo;
  tipo_de_veiculoId: string; //referencia com o tipo_de_veiculo
  tipo_de_veiculo?: tipo_de_veiculo;
  tiposVeiculos?: tipo_de_veiculo;
  tipo_de_frotaId: string; //referencia com o tipo_de_frota
  tipo_de_frota?: tipo_de_frota;
  versaoId?: string; //referencia com a versao
  versao?: versao;
  renovam?: string;
  vin?: string;
  numero_do_motor?: string;
  ano_fab?: string;
  ano_modelo?: string;
  ano_do_modelo?: string;
  cor?: string;
  odometro_atual?: string;
  matricula?: string;
  tag_rfid?: string;
  placa: string;
  data_compra?: string; // Nova data de compra
  data_cedencia?: string; // Nova data de cedência
  combustivel: combustiveis_do_veiculo;
  definicoes: definicoes_do_veiculo;
  lotacao_veiculos: lotacao_do_veiculo;
  faturamentoVeiculo: faturamento_do_veiculo;
  codigo_fipe: fipe_do_veiculo;
  valor_venal: string;
  status: "Ativo" | "Inativo" | "Em Manuntenção" | "Sem condições de uso";
  os: OS[];
  vistorias: vistoria[];
  checklists: checklist[];
  abastecimentos: abastecimento[];
  despesas: despesa[];
  percuros: percurso[];
  lembretes: lembrete[];
  manutencoes: manutencao[];
}

type manutencao = {
  createdAt: string;
  id: string;
  itens: string;
  km: number;
  periodo: number;
  valor: string;
  veiculoId: string;
};

type fipe_do_veiculo = {
  codigo_fipe?: string;
  valor_venal?: string;
};
type faturamento_do_veiculo = {
  centro_de_custoId: string; // referencia a o centro de custo
  centro_de_custo?: centro_de_custo;
  centro_custo?: centro_de_custo;
  empenhoId: string; //referncia ao empenho
  empenho?: empenho;
};

type lotacao_do_veiculo = {
  centro_de_custoId: string; // referencia com o centro_de_custo
  centro_custoID?: string;
  centro_custo_ascdID?: string;
  centro_custo?: centro_de_custo;
  estado: string;
  cidade: string;
};

type combustiveis_do_veiculo = {
  capacidade_do_tanque?: string;
  tipos_de_combustiveis: tipos_de_combustiveis[];
};

type tipos_de_combustiveis =
  | "Gasolina"
  | "Etanol / Álcool"
  | "Diesel"
  | "Querosene";

type definicoes_do_veiculo = {
  numero_de_cambio?: string;
  cilindradas?: number;
  potencia?: string;
  segmento?: string;
  carroceria?: string;
  transmissao?: string;
  quantidade_de_portas?: number;
  quantidade_de_assentos?: number;
  quantidade_de_eixos?: number;
  numero_de_valvulas?: string;
  litragem?: number;
  combustivel?: string;
  origem_do_veiculo: "nacional" | "importado";
};

interface versao {
  id: string;
  descricao: string;
  modelos: modelo[];
  modeloId: string;
  veiculos: veiculo[];
}

interface marca {
  id: string;
  descricao: string;
  codigo_fipe?: string;
  codigo_suiv?: string;
  veiculos: veiculo[];
  modelos: modelo[];
}

interface modelo {
  id: string;
  marcaId?: string;
  marca?: marca;
  descricao: string;
  codigo_fipe?: string;
  codigo_suiv?: string;
  veiculos: veiculo[];
}

interface tipo_de_veiculo {
  id: string;
  descricao: string;
  veiculos: veiculo[];
  itens_da_vistoria: item_da_vistoria[];
}
interface tipo_de_frota {
  id: string;
  descricao: string;
  veiculos: veiculo[];
}

interface centro_de_custo {
  id: string;
  descricao: string;
  centro_custo_pai?: centro_de_custo;
  centro_de_custo_ascendenteId?: string; // referencia com outro centro de custo
  centro_de_custo?: centro_de_custo;
  dotacao_orcamentista?: string;
  valor_dotacao: number;
  nome_responsavel?: string;
  contato?: string;
  email?: string;
  cnpj: string;
  razao_social: string;
  endereco: endereco;
  centro_de_custo_tomadorId?: string; // referencia com outro centro de custo
  active: boolean;
  centro_custo_ascdID?: string;
  faturamento_dos_veiculos: faturamento_do_veiculo[];
  lotacao_dos_veiculos: lotacao_do_veiculo[];
  centros_de_custo: centro_de_custo[];
  centro_custos_filhos: centro_de_custo[];
  empenhos: empenho[];
  veiculos_com_rfid?: boolean | null;
}

interface extrato {
  data: string;
  tipo_lancamento: string;
  observacao: string;
  valor: number;
}

interface empenho {
  id: string;
  centroCustoId: string;
  centro_de_custoId: string;
  centro_de_custo?: centro_de_custo;
  nota_empenho: string;
  ano_de_competencia: string;
  data_inicial: Date;
  data_final: Date;
  dotacao_orcamentaria: string;
  dotacao_orcamentada: string;
  valor_pecas: number;
  valor_destinado_as_pecas: number;
  valor_servicos: number;
  empenho_bloqueado: boolean;
  valor_destinado_aos_servicos: number;
  empenho_ativo: boolean;
  bloqueado: boolean;
  ativo: boolean;
  faturamento_dos_veiculos: faturamento_do_veiculo[];
  bloqueado_pecas?: number;
  bloqueado_servicos?: number;
  faturado_pecas?: number;
  faturado_servicos?: number;
  contigenciado_pecas?: number;
  contigenciado_servicos?: number;
  saldo_pecas: number;
  saldo_servicos: number;
  saldo_total: number;
}
interface condutor {
  id: string;
  nome: string;
  matricula: string;
  password?: string;
  cpf?: string; // Campo adicionado
  contato: string;
  email: string;
  endereco: endereco;
  cnh: cnh;
  mopp?: mopp;
  ativo: boolean;
  status: boolean;
  lotacao_condutorId?: string; // Campo adicionado
  lotacao_condutor?: {
    // Relação adicionada
    id: string;
    centro_custoID: string;
    centro_custo?: centro_de_custo;
  };
  createdAt: Date; // Campo adicionado
  OS: OS[];
  checklists: checklist[];
  abastecimentos: abastecimento[];
  despesas: despesa[];
  percursos: percurso[];
}

type endereco = {
  cep?: string;
  logradouro?: string;
  bairro?: string;
  estado?: string;
  cidade?: string;
};

type endereco = {
  cep?: string;
  logradouro?: string;
  bairro?: string;
  estado?: string;
  cidade?: string;
};
type cnh = {
  numero: string;
  categoria: string;
  estado: string;
  data_emissao: Date;
  data_vencimento: Date;
};
type mopp = {
  numero: string;
  emissor: string;
  data_emissao: Date;
  data_vencimento: Date;
};
interface credenciado {
  id: string;
  polo_regionalId: string; //referencia ao polo_regional
  polo_regional?: polo_regional;
  informacoes: informacoes_do_credenciado[];
  contato: contatos_do_credenciado;
  endereco: endereco_do_credenciado;
  estrutura: estrutura_do_credenciado;
  estrutura_credenciado: estrutura_do_credenciado;
  servicos: servico_credenciado[];
  documentacao: string[];
  orcamentista: boolean;
  atende_placa_verde: boolean;
  concessionaria: boolean;
  orcamentacao: boolean;
  optante: boolean;
  responsavel: string;
  prazo_contratualId: string; // referencia prazo_contratual
  prazo_contratual?: prazo_de_pagamento;
  Prazo?: prazo_de_pagamento;
  vistorias: vistoria[];
  OS: OS[];
  ativo?: boolean;
  checklists: checklist[];
  abastecimentos: abastecimento[];
  contratos: {
    contrato?: contrato;
    credenciadoId: string;
    contratoId: string;
  }[];
}

type estrutura_do_credenciado = {
  capacidade_total_de_atendimento: number;
  box_para_veiculos_leves: number;
  box_para_veiculos_pesados: number;
  elevadores_para_veiculos: number;
  estufas_para_pintura: number;
  capacidade_atendimento?: number;
  box_veiculos_leves?: number;
  box_veiculos_pesados?: number;
  elevadores_veiculos?: number;
  estufas_pintura?: number;
  imagens?: string[];
  images?: string[];
};
type endereco_do_credenciado = {
  cep?: string;
  logradouro: string;
  bairro: string;
  estado: string;
  cidade: string;
  latitude?: string;
  longitude?: string;
};
type contatos_do_credenciado = {
  telefone: string;
  celular: string;
  email: string;
  nome_do_gerente?: string;
  telefone_do_gerente?: string;
  nome_do_proprietario: string;
  telefone_do_proprietario: string;
  nome_gerente?: string;
  telefone_gerente?: string;
  nome_proprietario: string;
  telefone_proprietario: string;
};
type informacoes_do_credenciado = {
  cnpj: string;
  razao_social: string;
  inscricao_estadual?: string;
  inscricao_municipal?: string;
  inscri_estadual?: string;
  inscri_municipal?: string;
  nome_fantasia?: string;
  atividade_principal?: string;
  tipos_de_veiculos: tipo_de_veiculo[]; // relacao ao tipo_de_veiculo
  horario_funcionamento?: string;
  data_abertura: Date;
  porte_empresarial?:
    | "Microempreendedor Individual"
    | "Microempresa"
    | "Empresário Individual"
    | "Empresa de pequeno porte"
    | "Empresa Individual de Responsabilidade Limitada"
    | "Sociedade Anônima"
    | "Empresa Limitada";
  capital_social?: string;
  patrimonio_liquido?: string;
  observacoes_gerais?: string;
  logotipo_empresa?: string;
};

interface polo_regional {
  id: string;
  descricao: string;
  credenciados: credenciado[];
  cidades: cidade[];
}

interface cidade {
  estado: string;
  cidade: string;
}

interface prazo_de_pagamento {
  id: string;
  descricao: string;
  credenciados: credenciado[];
}
interface servico_credenciado {
  id: string;
  descricao: string;
  credenciados: credenciado[];
}
interface vistoria {
  dataLimite: string | number | Date;
  id?: string;
  veiculoId: string; //referencia veiculo
  veiculo?: veiculo;
  tipo_de_avaliacao: "Inicial" | "Periódica";
  data: Data;
  credenciadoId: string; //referencia credenciado
  credenciado?: credenciado;
  odometro_atual: number;
  previsao_troca_de_oleo: { km: number; data: Date };
  imagens: string[];
  observacao: string;
  itens_da_vistoria: item_da_vistoria[];
}
type avaliacao = "Bom" | "Reparo" | "Ausente" | "Não Avaliado";

interface item_da_vistoria {
  id: string;
  descricao: string;
  tipo_de_veiculoId?: string; // referencia tipo_de_veiculo
  tipo_veiculo?: tipo_de_veiculo;
}

interface checklist {
  id: string;
  veiculoId: string; //referncia veiculo
  veiculo?: veiculo;
  condutorId: string; //referencia condutor
  condutor?: condutor;
  credenciadoId: string; //referncia com o credenciado
  credenciado?: credenciado;
  situacao_do_veiculo: situacao_veiculo;
  aparencia_do_veiculo: aparencia_veiculo;
  situacao_veiculo: situacao_veiculo;
  aparencia_veiculo: aparencia_veiculo;
  tipo: "Entrada" | "Saída";
  tipo_checklist: string;
  data?: Date;
  previsao_entrega: Date;
  odometro_atual: string;
  nivel_do_combustivel: nivel_do_combustivel;
  imagens?: string[];
  createdAt: Date;
  osId?: string;
  checklist_entrada?: checklist;
  checklist_saida?: checklist;
  observacao?: string;
  entrega?: string;
  data_checklist?: string;
}
type nivel_do_combustivel = "Vazio" | "1/4" | "1/2" | "3/4" | "Cheio";
type Conformidade = "Conforme" | "Não Conforme";

type situacao_veiculo = {
  pneus: Conformidade;
  rodas: Conformidade;
  estepe: Conformidade;
  freio_de_mão: Conformidade;
  freio_de_pé: Conformidade;
  embreagem: Conformidade;
  luzes_do_painel: Conformidade;
  buzina: Conformidade;
  lanternas_dianteiras: Conformidade;
  lanternas_traseiras: Conformidade;
  farol_baixo: Conformidade;
  farol_alto: Conformidade;
  setas_dianteiras: Conformidade;
  setas_traseiras: Conformidade;
  luzes_de_emergência: Conformidade; // Pisca alerta
  luzes_de_freio: Conformidade;
  luzes_de_ré: Conformidade;
  espelhos_retrovisores: Conformidade;
  estado_geral_da_carroceria: Conformidade;
  nível_de_água: Conformidade;
  limpador_para_brisa: Conformidade;
  nível_de_fluído: Conformidade;
  óleo_do_motor: Conformidade;
};
type aparencia_veiculo = {
  situação_geral_do_veículo: Conformidade;
  pintura: Conformidade;
  limpeza: Conformidade;
  para_brisa: Conformidade;
  sem_arranhões: Conformidade;
  sem_amassados: Conformidade;
  lateral_motorista: Conformidade;
  lateral_passageiro: Conformidade;
  traseira: Conformidade;
  dianteira: Conformidade;
};

interface abastecimento {
  id: string;
  veiculoId: string; //referencia veiculo
  veiculo?: veiculo;
  data_e_hora: Date;
  odometro: string;
  combustiveis: tipos_de_combustiveis;
  combustivel: [{ label: string; value: string }];
  preco_l: string;
  valor_total: string;
  litros: string;
  tanque_completo: boolean;
  condutorId: string; //referencia condutor
  condutor?: condutor;
  credenciadoId: string; // referencia credenciado
  credenciado?: credenciado;
  observacoes: string;
  arquivos: string[];
}

interface despesa {
  id: string;
  veiculoId: string; // referencia veiculo
  veiculo?: veiculo;
  veiculos?: veiculo[];
  data_e_hora?: Date;
  odometro: string;
  tipo_de_despesaId: string; //referencia tipo_de_despesa
  tipo_de_despesa?: tipo_de_despesa;
  valor: string;
  local: string;
  condutorId: string; //referncia condutor
  condutor?: condutor;
  observacoes: string;
  arquivos: string[];
  lembretes: lembrete[];
}
interface tipo_de_despesa {
  id: string;
  descricao: string;
  despesas: despesa[];
}
interface percurso {
  id: string;
  veiculoId: string; //referencia veiculo
  veiculo?: veiculo;
  condutorId: string; //referencia condutor
  condutor?: condutor;
  motivo: string;
  origensPercurso: time_percurso[];
  destinosPercurso: time_percurso[];
  arquivos: any;
  observacoes: string;
}

type time_percurso = {
  local: string;
  data?: string;
  odometro: string;
};

interface lembrete {
  id: string;
  veiculoId: string; // referencia veiculo
  veiculo?: veiculo;
  tipo: "DESPESA" | "SERVICO";
  referencialId: string; //referencia  despesa ou serviço
  despesa?: despesa;
  odometro: string;
  repeticao: RepeticaoConfig;
  data: Date;
  dataDesejada?: Date;
  quantidade?: number;
  periodo?: Periodo;
  observacoes?: string;
}

type RepeticaoConfig = {
  tipo: "UNICO" | "REPETIR_A_CADA";
  intervalo?: number; // Quantidade de tempo para a repetição (em dias, semanas, etc.)
};

type Periodo = "dia" | "mes" | "ano";

interface contrato {
  id: string;
  nome: string;
  nome_contrato?: string;
  numero: string;
  data_inicial: Date;
  data_final: Date;
  valor_do_contrato: number;
  valor_contrato?: number;
  taxa_administrativa: number;
  taxa_admin?: number;
  cnpj: string;
  razao_social: string;
  cidade?: string;
  logotipo: string;
  contato: contato_contrato;
  endereco: endereco_contrato;
  validacao_nf: nf_contato;
  configuracao: config_contrato;
  limite_gastos_percent?: number;
  veiculo_rfid?: boolean;
  checklist_simplificado_pecas?: boolean;
  abertura_os_credenciado?: boolean | null;
  ajuste_gestor?: boolean;
  aprovacao_gestor?: boolean;
  restringir_veiculos?: boolean;
  nf_parameter?: {
    placa_veiculo: boolean;
    modelo_veiculo: boolean;
    numero_os: boolean;
    numero_contrato: boolean;
  };
  desconto: ItemContrato[];
  send_nf_to?: string;
  cnpj_financial?: string;
  bairro_financial?: string;
  estado_financial?: string;
  logradouro_financial?: string;
  cep_financial?: string;
}
interface ItemContrato {
  id: string;
  contratoId: string;
  descricao: string;
  tipoItem: string;
  tipoVeiculo: string;
  desconto: number;
  precoMaximo?: number | null;
  createdAt: string;
  updatedAt: string;
}
type contato_contrato = {
  responsavel: string;
  telefone: string;
  email: string;
};
type endereco_contrato = {
  cep: string;
  logradouro: string;
  bairro: string;
  estado: string;
  cidade: string;
};

type nf_contrato = {
  placa_do_veiculo: boolean;
  numero_da_os: boolean;
  modelo_do_veiculo: boolean;
  numero_do_contrato: boolean;
};
type config_contrato = {
  ativo: boolean;
  abertura_os_credenciado: boolean;
  envio_de_emails: boolean;
  veiculos_com_rfid: boolean;
  parametrizacao_obrigatoria: boolean;
};

interface UnidadeFilha {
  id: string;
  descricao?: string;
}

interface usuario {
  id: string;
  nome: string;
  matricula?: string;
  email: string;
  telefone?: string;
  celular?: string;
  role?: string;
  roles: string[];
  ativo: boolean;
  acesso?: acesso[];
  senha: string;
  contrato?: contrato;
  contratos?: contrato[];
  contratoId?: string;
  createdAt?: string;
  create_os?: boolean;
  verified?: boolean;
  centro_de_custoId?: string;
  unidade_filha_id?: string;
  unidades_filha_ids?: string[];
  valor_nivel_aprovacao?: string;
  nivel_aprovacao?: "LVL_1" | "LVL_2";
  unidades_filhas?: UnidadeFilha[];
}

interface roles {
  id: string;
  nome: string;
}

interface acesso {
  id: string;
  permissao: string[];
  contratoId: string;
  contrato?: contrato;
  centro_de_custoId: string;
  centro_de_custo?: centro_de_custo;
  usuarioId: string;
}
