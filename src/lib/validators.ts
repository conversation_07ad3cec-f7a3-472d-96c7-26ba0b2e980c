import { z } from "zod";

/**
 * Validador Zod para garantir que um campo contenha apenas números
 * @param message Mensagem de erro personalizada
 * @returns Um validador Zod para campos numéricos
 */
export const numbersOnlyValidator = (message: string = "Este campo deve conter apenas números") => {
  return z.string().regex(/^\d*$/, { message });
};

/**
 * Validador Zod para CNJ que verifica se contém apenas números e tem o tamanho correto
 * @param minLength Tamanho mínimo do número CNJ
 * @param maxLength Tamanho máximo do número CNJ
 * @returns Um validador Zod para campos CNJ
 */
export const cnjValidator = (minLength: number = 20, maxLength: number = 20) => {
  return z.string()
    .regex(/^\d*$/, { message: "O número CNJ deve conter apenas dígitos" })
    .min(minLength, { message: `O número CNJ deve ter pelo menos ${minLength} dígitos` })
    .max(maxLength, { message: `O número CNJ não pode ter mais que ${maxLength} dígitos` });
};

/**
 * Validador Zod para CNPJ que verifica se contém apenas números e tem 14 dígitos
 * @returns Um validador Zod para campos CNPJ
 */
export const cnpjValidator = () => {
  return z.string()
    .regex(/^\d*$/, { message: "O CNPJ deve conter apenas dígitos" })
    .length(14, { message: "O CNPJ deve ter exatamente 14 dígitos" });
};

/**
 * Validador Zod para CPF que verifica se contém apenas números e tem 11 dígitos
 * @returns Um validador Zod para campos CPF
 */
export const cpfValidator = () => {
  return z.string()
    .regex(/^\d*$/, { message: "O CPF deve conter apenas dígitos" })
    .length(11, { message: "O CPF deve ter exatamente 11 dígitos" });
};

/**
 * Validador Zod para CEP que verifica se contém apenas números e tem 8 dígitos
 * @returns Um validador Zod para campos CEP
 */
export const cepValidator = () => {
  return z.string()
    .regex(/^\d*$/, { message: "O CEP deve conter apenas dígitos" })
    .length(8, { message: "O CEP deve ter exatamente 8 dígitos" });
};

/**
 * Validador Zod para telefone fixo
 * Verifica se contém apenas números e tem 10 dígitos (DDD + número)
 * @returns Um validador Zod para campos de telefone fixo
 */
export const telefoneValidator = () => {
  return z.string()
    .regex(/^\d*$/, { message: "O telefone deve conter apenas dígitos" })
    .length(10, { message: "O telefone deve ter exatamente 10 dígitos (DDD + número)" });
};

/**
 * Validador Zod para celular
 * Verifica se contém apenas números e tem 11 dígitos (DDD + 9 + número)
 * @returns Um validador Zod para campos de celular
 */
export const celularValidator = () => {
  return z.string()
    .regex(/^\d*$/, { message: "O celular deve conter apenas dígitos" })
    .length(11, { message: "O celular deve ter exatamente 11 dígitos (DDD + 9 + número)" });
};

/**
 * Validador genérico para telefone ou celular
 * Aceita tanto o formato de telefone (10 dígitos) quanto o de celular (11 dígitos)
 */
export const telefoneEcelularValidator = () => {
  return z.string()
    .regex(/^\d*$/, { message: "O telefone ou celular deve conter apenas dígitos" })
    .refine(
      (value) => value.length === 10 || value.length === 11,
      { message: "O número deve ter 10 dígitos (telefone fixo) ou 11 dígitos (celular)" }
    );
};

/**
 * Função para limpar caracteres não numéricos de uma string
 * @param value String a ser limpa
 * @returns String contendo apenas números
 */
export const cleanNonNumeric = (value: string): string => {
  return value.replace(/\D/g, '');
};

/**
 * Validador para eventos de teclado que permite apenas números
 * Bloqueia teclas não numéricas, mas permite teclas de navegação e atalhos
 * @param e O evento KeyboardEvent
 * @returns boolean - se a tecla é permitida ou não
 */
export const apenasNumerosValidator = (e: React.KeyboardEvent<HTMLInputElement>): boolean => {
  // Permite: backspace, delete, tab, escape, enter, teclas de setas
  if ([
    'Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'Home', 'End',
    'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'
  ].includes(e.key)) {
    return true;
  }

  // Permite atalhos de teclado (Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X)
  if ((e.ctrlKey || e.metaKey) && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase())) {
    return true;
  }

  // Bloqueia qualquer tecla que não seja número
  if (!/^\d$/.test(e.key)) {
    e.preventDefault();
    return false;
  }

  return true;
}; 