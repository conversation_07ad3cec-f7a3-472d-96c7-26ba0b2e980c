import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Normaliza um valor para nunca ser null ou undefined
 * @param value Valor a ser normalizado
 * @returns String vazia se o valor for null ou undefined, caso contrário o próprio valor
 */
export function normalizeInputValue(value: any): string {
  if (value === null || value === undefined) {
    return "";
  }
  return String(value);
}

export type ROUTE = {
  href: string;
  label: string;
  items?: { href: string; label: string }[];
};
export const ROUTES: ROUTE[] = [
  {
    href: "/abastecimentos",
    label: "Abastecimentos",
    items: [
      { href: "/consultar-abastecimentos", label: "Consultar Abastecimentos" },
      { href: "/novo-abastecimento", label: "Novo Abastecimento" },
    ],
  },
  {
    href: "/centro-de-custo",
    label: "Centro de Custo",
    items: [
      { href: "/cadastro-centro-custo", label: "Cadastro Centro de Custo" },
      { href: "/centros-custo", label: "Centros de Custo" },
    ],
  },
  {
    href: "/condutores",
    label: "Condutores",
    items: [
      { href: "/consultar-condutor", label: "Consultar Condutor" },
      { href: "/novo-condutor", label: "Novo Condutor" },
    ],
  },
  {
    href: "/contratos",
    label: "Contratos",
    items: [
      { href: "/consultar-contrato", label: "Consultar Contrato" },
      { href: "/novo-contrato", label: "Novo Contrato" },
    ],
  },
  {
    href: "/despesas",
    label: "Despesas",
    items: [
      { href: "/consultar-despesas", label: "Consultar Despesas" },
      { href: "/nova-despesa", label: "Nova Despesa" },
      { href: "/tipo-despesa", label: "Tipo de Despesa" },
    ],
  },
  {
    href: "/empenhos",
    label: "Empenhos",
    items: [
      { href: "/novo-empenho", label: "Novo Empenho" },
      { href: "/cadastro-empenho", label: "Cadastro de Empenho" },
      { href: "/extrato-empenho", label: "Extrato de Empenho" },
    ],
  },
  {
    href: "/lembretes",
    label: "Lembretes",
    items: [
      { href: "/consultar-lembretes", label: "Consultar Lembretes" },
      { href: "/novo-lembrete", label: "Novo Lembrete" },
    ],
  },
  {
    href: "/login",
    label: "Login",
  },
  {
    href: "/operacionais",
    label: "Operacionais",
    items: [
      { label: "Ordens de serviço", href: "/ordens-de-servico" },
      { label: "Histórico de manuntenção", href: "/historico-de-manuntencao" },
      { label: "Histórico de orçamentos", href: "/historico-de-orcamentos" },
      { label: "Garantias", href: "/pecas-e-servico-em-garantia" },
    ],
  },
  {
    href: "/checklists",
    label: "Checklists",
    items: [
      { label: "Consultar checklists", href: "/consultar-checklist" },
      { label: "Novo checklist", href: "/novo-checklist" },
    ],
  },
  {
    href: "/ordens-de-servico",
    label: "Ordens de Serviço",
    items: [
      { href: "/nova-ordem", label: "Nova Ordem" },
      { href: "/ordens", label: "Ordens de Serviço" },
      { href: "/tipos-servico", label: "Tipos de Serviço" },
      {
        href: "/chats",
        label: "chat",
      },
    ],
  },
  {
    href: "/percursos",
    label: "Percursos",
    items: [
      { href: "/consultar-percursos", label: "Consultar Percursos" },
      { href: "/novo-percurso", label: "Novo Percurso" },
    ],
  },
  {
    href: "/profile",
    label: "Perfil",
  },

  {
    href: "/rede-credenciada",
    label: "Rede Credenciada",
    items: [
      { href: "/consultar-credenciados", label: "Consultar Credenciados" },
      { href: "/novo-credenciado", label: "Novo Credenciado" },
      { href: "/polos-regionais", label: "Polos Regionais" },
      { href: "/prazos-pagamentos", label: "Prazos de pagamento" },
      { href: "/servicos-oferecidos", label: "Serviços oferecidos" },
      { href: "/vincular-contratos", label: "Vincular contratos" },
    ],
  },
  {
    href: "/polos-regionais",
    label: "Polos Regionais",
  },
  {
    href: "/prazo-pagamentos",
    label: "Prazo de Pagamentos",
  },
  {
    href: "/servicos-oferecidos",
    label: "Serviços Oferecidos",
  },
  {
    href: "/vincular-contratos",
    label: "Vincular Contratos",
  },
  {
    href: "/usuarios",
    label: "Usuários",
    items: [
      { href: "/usuarios-contrato", label: "Usuários por Contrato" },
      { href: "/usuarios-sistema", label: "Usuários Sistema" },
      { href: "/consultar-log", label: "Consultar Logs" },
    ],
  },
  {
    href: "/financeiros",
    label: "Financeiros",
    items: [
      { href: "/resumo-financeiro", label: "Resumo financeiro" },
      { href: "/nota-fiscal", label: "Nota Fiscal" },
      { href: "/faturamento-credenciado", label: "Faturamento Credenciado" },
    ],
  },
  // {
  //   href: "/auditoria",
  //   label: "Auditoria",
  //   items: [{ href: "/consultar-log", label: "Consultar Log" }],
  // },
  {
    href: "/veiculos",
    label: "Veículos",
    items: [
      { href: "/marcas", label: "Marcas" },
      { href: "/modelos", label: "Modelos" },
      { href: "/novo-veiculo", label: "Novo Veículo" },
      { href: "/tipos-frota", label: "Tipos de Frota" },
      { href: "/tipos-veiculo", label: "Tipos de Veículo" },
      { href: "/veiculos", label: "Veículos" },
    ],
  },
  {
    href: "/vistorias",
    label: "Vistorias",
    items: [
      { href: "/itens-vistorias", label: "Itens de Vistorias" },
      { href: "/nova-vistoria", label: "Nova Vistoria" },
      { href: "/vistorias", label: "Vistorias" },
    ],
  },
  {
    href: "/orcamento",
    label: "Orçamento",
    items: [
      { href: "/consultar-orcamento", label: "Consultar Orçamento" },
      { href: "/leitura-tag", label: "Ler Tag NFC" },
    ],
  },
];

export function getRoute(label: string) {
  return ROUTES.find((route) => route.label === label);
}

export function formatCurrency(value: number): string {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(value);
}

export function formatCNPJ(cnpj: string) {
  return cnpj.replace(
    /^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/,
    "$1.$2.$3/$4-$5"
  );
}

export function formatPhoneNumber(phone: string) {
  if (!phone) return phone;
  // Remove caracteres não numéricos
  const cleaned = ("" + phone).replace(/\D/g, "");

  // Checa se é celular (11 dígitos) ou fixo (10 dígitos)
  if (cleaned.length === 11) {
    return `(${cleaned.substring(0, 2)}) ${cleaned.substring(
      2,
      7
    )}-${cleaned.substring(7, 11)}`;
  } else if (cleaned.length === 10) {
    return `(${cleaned.substring(0, 2)}) ${cleaned.substring(
      2,
      6
    )}-${cleaned.substring(6, 10)}`;
  }

  return phone;
}

export function formatDate(date: Date) {
  return new Intl.DateTimeFormat("pt-BR").format(date);
}
