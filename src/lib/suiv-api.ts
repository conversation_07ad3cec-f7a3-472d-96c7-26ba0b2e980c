const BASE_URL = "https://api.suiv.com.br/api/v4";
const API_KEY = process.env.SUIV_API_KEY;

interface SuivApiError {
  message: string;
  status?: number;
}

export async function fetchSuivAPI<T>(
  endpoint: string,
  params: Record<string, string | number>
): Promise<T> {
  const queryString = new URLSearchParams(
    Object.entries(params).map(([k, v]) => [k, String(v)])
  ).toString();
  
  const url = `${BASE_URL}${endpoint}?${queryString}&key=${API_KEY}`;
  const response = await fetch(url);
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw {
      message: errorData.message || "Erro na API SUIV",
      status: response.status,
    } as SuivApiError;
  }
 

  return response.json();
}