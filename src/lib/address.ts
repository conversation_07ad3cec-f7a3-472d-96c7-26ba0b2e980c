export const getAddressFromCoordinates = async (
  lat: string | undefined,
  lng: string | undefined
) => {
  try {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/reverse?format=jsonv2&lat=${lat}&lon=${lng}`
    );
    const data = await response.json();
    return data.address;
  } catch (error) {
    console.error("Erro ao buscar endereço:", error);
    return null;
  }
};
