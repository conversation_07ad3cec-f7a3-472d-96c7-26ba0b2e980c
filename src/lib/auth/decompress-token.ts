import { ungzip, inflate } from "pako";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { cookies } from "next/headers";

// Função para verificar se o token parece ser um JWT válido
function looksLikeJwt(token: string): boolean {
  // JWT geralmente começa com "eyJ"
  return token.startsWith('eyJ') && token.split('.').length === 3;
}

// Função para descomprimir
export function decompressToken(compressedToken: string): string {
  // Safety check
  if (!compressedToken) {
    return "";
  }

  
  // Verifica se já parece ser um JWT
  if (looksLikeJwt(compressedToken)) {
    console.log("Token aparenta ser um JWT válido, retornando sem descompressão");
    return compressedToken;
  }

  // Try several different approaches
  
  // 1. Try as direct JSON (no encoding)
  try {
    JSON.parse(compressedToken);
    return compressedToken;
  } catch (e) {
    // Não é JSON direto
  }
  
  // 2. Try as base64-encoded JSON
  try {
    const decoded = Buffer.from(compressedToken, "base64").toString("utf-8");
    JSON.parse(decoded);
    return decoded;
  } catch (e) {
    // Não é JSON codificado em base64
  }
  
  // 3. Try with URL decoding first
  try {
    const urlDecoded = decodeURIComponent(compressedToken);
    const decoded = Buffer.from(urlDecoded, "base64").toString("utf-8");
    JSON.parse(decoded);
    return decoded;
  } catch (e) {
    // Não é JSON codificado em base64 com URL encoding
  }

  // 4. Try as base64-encoded gzipped JSON
  try {
    const compressed = Buffer.from(compressedToken, "base64");
    const decompressed = ungzip(compressed);
    const result = new TextDecoder().decode(decompressed);
    return result;
  } catch (error: any) {
    console.log("Falha na descompressão com gzip:", error.message);
  }
  
  // 5. Try as base64-encoded deflated JSON
  try {
    const compressed = Buffer.from(compressedToken, "base64");
    const decompressed = inflate(compressed);
    const result = new TextDecoder().decode(decompressed);
    return result;
  } catch (error: any) {
    console.log("Falha na descompressão com deflate:", error.message);
  }
  
  // 6. Try with URL decoding first, then gzip
  try {
    const urlDecoded = decodeURIComponent(compressedToken);
    const compressed = Buffer.from(urlDecoded, "base64");
    const decompressed = ungzip(compressed);
    const result = new TextDecoder().decode(decompressed);
    return result;
  } catch (error) {
    console.log("Falha na descompressão com URL decode + gzip");
  }

  // Se todas as tentativas falharem, retorne o token original
  console.warn("Warning: Não foi possível descomprimir o token. Retornando original.");
  return compressedToken;
}

// Função para obter o token descomprimido
export async function getDecompressedToken(): Promise<string | null> {
  const compressedToken = (await cookies()).get(SESSION_COOKIE_NAME)?.value;
  if (compressedToken) {
    return decompressToken(compressedToken);
  }
  return null;
}