export const SESSION_COOKIE_NAME = "session";
export const SESSION_CHECK_INTERVAL = 7 * 24 * 60 * 60 * 1000; // 7 dias
export const SESSION_COOKIE_MAX_AGE = 10 * 365 * 24 * 60 * 60; // 10 anos



export function hasPermissionByRole (role:string |null,necessaryRole :string){
    if(!role) return false
    if(role === necessaryRole ) return true
    return false
} 

const permissoesValidas = [
    "admin operacional",
    "supervisor de frota",
    "gestor de frota",
    "gestor financeiro",
    "gestor orçamento",
    "abertura de os",
    "credenciado",
  ] as const;
  
  type PermissoesValidas = typeof permissoesValidas[number];
  
  export function hasPermissionByAcess(usuario: usuario | null, permissoesNecessarias: PermissoesValidas[]): boolean {
    if (!usuario) return false;
  
    // Verificar se o usuário tem algum papel de 'role' que corresponde às permissões necessárias
    const permissoesUsuario = usuario?.acesso?.flatMap(a => a.permissao);
  
    return permissoesNecessarias.some(permissao => permissoesUsuario?.includes(permissao));
  }