"use server";
import "server-only";

import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import jwt from "jsonwebtoken";
import { cookies } from "next/headers";
import { Session } from "./types";

export async function getServerSession(): Promise<Session | undefined> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return undefined;
  }
  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return;
  }

  // Descomprime o token antes de usar
  const decompressedToken = decompressToken(compressedToken);
  if (!decompressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return;
  }
  const session = await decode(decompressedToken);
  if (session) {
    return session;
  }
}

export async function decode(token?: string): Promise<Session | undefined> {
  if (!process.env.SESSION_JWT_SECRET) {
    throw new Error("process.env.SESSION_JWT_SECRET is not set");
  }

  if (!token) {
    return undefined;
  }
  try {
    const user = jwt.verify(token, process.env.SESSION_JWT_SECRET, {
      audience: "my-app",
      issuer: "my-app",
    });

    return user as Session;
  } catch (err) {}

  return undefined;
}

export async function encode(user: Session) {
  if (!process.env.SESSION_JWT_SECRET) {
    throw new Error("process.env.SESSION_JWT_SECRET is not set");
  }

  const token = jwt.sign(user, process.env.SESSION_JWT_SECRET, {
    // Alterar de 7 dias para 10 anos para combinar com o cookie
    expiresIn: "3650d", // 10 anos em dias
    audience: "my-app",
    issuer: "my-app",
  });

  return token;
}