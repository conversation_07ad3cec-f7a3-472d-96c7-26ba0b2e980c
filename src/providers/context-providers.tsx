"use client";

import { <PERSON><PERSON><PERSON>imentoProvider } from "@/context/abatecimento-context";
import { CentroDeCustoProvider } from "@/context/centro-de-custo-context";
import { ChecklistProvider } from "@/context/checklist-context";
import { CondutorProvider } from "@/context/condutor-context";
import { ContratoProvider } from "@/context/contrato-context";
import { CredenciadoProvider } from "@/context/credenciado-context";
import { DespesaProvider } from "@/context/despesa-context";
import { EmpenhoProvider } from "@/context/empenho-context";
import { ItensDaVistoriaProvider } from "@/context/itens-da-vistoria-context";
import { LembreteProvider } from "@/context/lembrete-context";
import { MarcaProvider } from "@/context/marca-context";
import { ModeloProvider } from "@/context/modelo-context";
import { OSProvider } from "@/context/os-context";
import { PercursoProvider } from "@/context/percurso-context";
import { PoloRegionalProvider } from "@/context/polo-regional-context";
import { PrazoDePagamentoProvider } from "@/context/prazo-de-pagamento-context";
import { ServicoCredenciadoProvider } from "@/context/servico-credenciado-context";
import { TipoDeFrotaProvider } from "@/context/tipo-de-frota-context";
import { TipoDeVeiculoProvider } from "@/context/tipo-de-veiculo-context";
import { TipoDeDespesaProvider } from "@/context/tipos-de-despesa-context";
import { TiposDeOsProvider } from "@/context/tipos-de-os-context";
import { UsuarioProvider } from "@/context/usuario-context";
import { VeiculosProvider } from "@/context/veiculos-context";
import { VersaoProvider } from "@/context/versao-context";
import { VistoriasProvider } from "@/context/vistoria-context";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useState } from "react";

type ContextProvidersType = {
  children: React.ReactNode;
};
export function Providers({ children }: ContextProvidersType) {
  const [queryClient] = useState(() => new QueryClient());

  return (
    <QueryClientProvider client={queryClient}>
      <VeiculosProvider>
        <CondutorProvider>
          <TiposDeOsProvider>
            <ItensDaVistoriaProvider>
              <VistoriasProvider>
                <ChecklistProvider>
                  <AbastecimentoProvider>
                    <DespesaProvider>
                      <TipoDeDespesaProvider>
                        <CentroDeCustoProvider>
                          <EmpenhoProvider>
                            <PercursoProvider>
                              <CentroDeCustoProvider>
                                <VersaoProvider>
                                  <MarcaProvider>
                                    <ModeloProvider>
                                      <LembreteProvider>
                                        <ContratoProvider>
                                          <CredenciadoProvider>
                                            <PoloRegionalProvider>
                                              <ServicoCredenciadoProvider>
                                                <PrazoDePagamentoProvider>
                                                  <TipoDeVeiculoProvider>
                                                    <TipoDeFrotaProvider>
                                                      <UsuarioProvider>
                                                        <OSProvider>
                                                          {children}
                                                        </OSProvider>
                                                      </UsuarioProvider>
                                                    </TipoDeFrotaProvider>
                                                  </TipoDeVeiculoProvider>
                                                </PrazoDePagamentoProvider>
                                              </ServicoCredenciadoProvider>
                                            </PoloRegionalProvider>
                                          </CredenciadoProvider>
                                        </ContratoProvider>
                                      </LembreteProvider>
                                    </ModeloProvider>
                                  </MarcaProvider>
                                </VersaoProvider>
                              </CentroDeCustoProvider>
                            </PercursoProvider>
                          </EmpenhoProvider>
                        </CentroDeCustoProvider>
                      </TipoDeDespesaProvider>
                    </DespesaProvider>
                  </AbastecimentoProvider>
                </ChecklistProvider>
              </VistoriasProvider>
            </ItensDaVistoriaProvider>
          </TiposDeOsProvider>
        </CondutorProvider>
      </VeiculosProvider>
    </QueryClientProvider>
  );
}
