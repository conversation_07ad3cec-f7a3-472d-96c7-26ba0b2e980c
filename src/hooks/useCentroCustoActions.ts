"use client";

import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import {
  getCentrosCusto,
  getCentroCustoById,
  createCentroCusto,
  updateCentroCusto,
  deleteCentroCusto,
  validateCentroCustoDelete,
  getCentrosCustoPorContrato
} from '@/lib/actions/centro-custo.actions';
import { CentroCustoCompleto, CentroCustoResponse } from '@/types/centro-custo.types';

interface UseCentroCustoActionsReturn {
  // Estados
  loading: boolean;
  error: string | null;
  
  // Ações
  fetchCentrosCusto: () => Promise<CentroCustoResponse | null>;
  fetchCentroCustoById: (id: string) => Promise<CentroCustoCompleto | null>;
  createNewCentroCusto: (data: any) => Promise<CentroCustoCompleto | null>;
  updateExistingCentroCusto: (id: string, data: any) => Promise<CentroCustoCompleto | null>;
  deleteExistingCentroCusto: (id: string) => Promise<boolean>;
  fetchCentrosCustoPorContrato: (contratoId: string) => Promise<CentroCustoCompleto[] | null>;
  
  // Utilitários
  clearError: () => void;
}

export function useCentroCustoActions(): UseCentroCustoActionsReturn {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const handleAction = useCallback(async <T>(
    action: () => Promise<{ success: boolean; data?: T; message?: string; error?: string }>,
    successMessage?: string,
    showSuccessToast = true
  ): Promise<T | null> => {
    setLoading(true);
    setError(null);

    try {
      const result = await action();

      if (result.success) {
        if (showSuccessToast && (successMessage || result.message)) {
          toast.success(successMessage || result.message);
        }
        return result.data || null;
      } else {
        const errorMessage = result.error || 'Erro desconhecido';
        setError(errorMessage);
        toast.error(errorMessage);
        return null;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro interno do servidor';
      setError(errorMessage);
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchCentrosCusto = useCallback(async (): Promise<CentroCustoResponse | null> => {
    return handleAction(
      () => getCentrosCusto(),
      undefined,
      false // Não mostrar toast de sucesso para busca
    );
  }, [handleAction]);

  const fetchCentroCustoById = useCallback(async (id: string): Promise<CentroCustoCompleto | null> => {
    return handleAction(
      () => getCentroCustoById(id),
      undefined,
      false // Não mostrar toast de sucesso para busca
    );
  }, [handleAction]);

  const createNewCentroCusto = useCallback(async (data: any): Promise<CentroCustoCompleto | null> => {
    return handleAction(
      () => createCentroCusto(data),
      'Centro de custo criado com sucesso!'
    );
  }, [handleAction]);

  const updateExistingCentroCusto = useCallback(async (id: string, data: any): Promise<CentroCustoCompleto | null> => {
    return handleAction(
      () => updateCentroCusto(id, data),
      'Centro de custo atualizado com sucesso!'
    );
  }, [handleAction]);

  const deleteExistingCentroCusto = useCallback(async (id: string): Promise<boolean> => {
    // Primeiro valida se pode excluir
    const validation = await handleAction(
      () => validateCentroCustoDelete(id),
      undefined,
      false
    );

    if (!validation) {
      return false;
    }

    // Se pode excluir, procede com a exclusão
    const result = await handleAction(
      () => deleteCentroCusto(id),
      'Centro de custo excluído com sucesso!'
    );

    return result !== null;
  }, [handleAction]);

  const fetchCentrosCustoPorContrato = useCallback(async (contratoId: string): Promise<CentroCustoCompleto[] | null> => {
    return handleAction(
      () => getCentrosCustoPorContrato(contratoId),
      undefined,
      false // Não mostrar toast de sucesso para busca
    );
  }, [handleAction]);

  return {
    // Estados
    loading,
    error,
    
    // Ações
    fetchCentrosCusto,
    fetchCentroCustoById,
    createNewCentroCusto,
    updateExistingCentroCusto,
    deleteExistingCentroCusto,
    fetchCentrosCustoPorContrato,
    
    // Utilitários
    clearError,
  };
}

// Hook específico para operações de CRUD com estado local
export function useCentroCustoCRUD() {
  const actions = useCentroCustoActions();
  const [centrosCusto, setCentrosCusto] = useState<CentroCustoCompleto[]>([]);
  const [selectedCentroCusto, setSelectedCentroCusto] = useState<CentroCustoCompleto | null>(null);

  const loadCentrosCusto = useCallback(async () => {
    const result = await actions.fetchCentrosCusto();
    if (result) {
      setCentrosCusto(result.centrosCusto);
    }
  }, [actions]);

  const loadCentroCustoById = useCallback(async (id: string) => {
    const result = await actions.fetchCentroCustoById(id);
    if (result) {
      setSelectedCentroCusto(result);
    }
    return result;
  }, [actions]);

  const createCentroCusto = useCallback(async (data: any) => {
    const result = await actions.createNewCentroCusto(data);
    if (result) {
      setCentrosCusto(prev => [...prev, result]);
    }
    return result;
  }, [actions]);

  const updateCentroCusto = useCallback(async (id: string, data: any) => {
    const result = await actions.updateExistingCentroCusto(id, data);
    if (result) {
      setCentrosCusto(prev => 
        prev.map(centro => centro.id === id ? result : centro)
      );
      if (selectedCentroCusto?.id === id) {
        setSelectedCentroCusto(result);
      }
    }
    return result;
  }, [actions, selectedCentroCusto]);

  const deleteCentroCusto = useCallback(async (id: string) => {
    const success = await actions.deleteExistingCentroCusto(id);
    if (success) {
      setCentrosCusto(prev => prev.filter(centro => centro.id !== id));
      if (selectedCentroCusto?.id === id) {
        setSelectedCentroCusto(null);
      }
    }
    return success;
  }, [actions, selectedCentroCusto]);

  return {
    // Estados
    centrosCusto,
    selectedCentroCusto,
    loading: actions.loading,
    error: actions.error,
    
    // Ações
    loadCentrosCusto,
    loadCentroCustoById,
    createCentroCusto,
    updateCentroCusto,
    deleteCentroCusto,
    
    // Setters
    setSelectedCentroCusto,
    
    // Utilitários
    clearError: actions.clearError,
  };
}
