"use client";

import { useState, useEffect } from 'react';
import { OSFormField } from '@/components/forms/OSFieldCustomizer';

// Definição dos campos disponíveis no formulário de OS
export const DEFAULT_OS_FIELDS: OSFormField[] = [
  // Informações Básicas
  { id: 'veiculoId', label: 'Veículo', section: 'basic', required: true, description: 'Seleção do veículo para a OS' },
  { id: 'condutorId', label: 'Condutor', section: 'basic', required: true, description: 'Condutor responsável pelo veículo' },
  { id: 'odometro_atual', label: 'Odômetro Atual', section: 'basic', description: 'Quilometragem atual do veículo' },
  { id: 'tipo_de_frota_display', label: 'Tipo de Frota', section: 'basic', description: 'Classificação da frota (leve, médio, pesado, máquina, moto)' },
  
  // Tipo de Serviço
  { id: 'tipo_de_osId', label: 'Tipo de Serviço', section: 'service', required: true, description: 'Categoria do serviço a ser realizado' },
  { id: 'tipo_manutencao', label: 'Tipo de Manutenção', section: 'service', required: true, description: 'Preventiva, corretiva ou preditiva' },
  { id: 'descricao', label: 'Descrição do Serviço', section: 'service', required: true, description: 'Detalhamento do problema ou serviço' },
  
  // Localização da Criação
  { id: 'estado_de_localizacao', label: 'Estado de Localização', section: 'location', required: true, description: 'Estado onde a OS foi criada' },
  { id: 'cidade_de_localizacao', label: 'Cidade de Localização', section: 'location', required: true, description: 'Cidade onde a OS foi criada' },
  { id: 'credenciadoId', label: 'Credenciado/Oficina', section: 'location', description: 'Estabelecimento que realizará o serviço' },
  
  // Detalhes e Configurações
  { id: 'mobilizado', label: 'Mobilizado', section: 'details', description: 'Indica se o veículo está mobilizado' },
  { id: 'orcamento_individual', label: 'Orçamento Individual', section: 'details', description: 'Solicitar orçamento individual' },
  { id: 'minimun_orcament', label: 'Mínimo de Orçamentos', section: 'details', description: 'Quantidade mínima de orçamentos' },
  { id: 'quoteExpiration', label: 'Prazo do Orçamento (horas)', section: 'details', description: 'Tempo limite para apresentação do orçamento' },
  { id: 'valor_autorizado', label: 'Valor Autorizado', section: 'details', description: 'Valor máximo autorizado para o serviço' },
  
  // Arquivos
  { id: 'arquivos', label: 'Anexos', section: 'files', description: 'Documentos e imagens relacionadas à OS' },
];

// Campos obrigatórios que sempre devem estar selecionados
const REQUIRED_FIELDS = DEFAULT_OS_FIELDS.filter(field => field.required).map(field => field.id);

// Ordem padrão dos campos
const DEFAULT_FIELD_ORDER = [
  'veiculoId',
  'condutorId', 
  'tipo_de_osId',
  'tipo_manutencao',
  'tipo_de_frota_display',
  'estado_de_localizacao',
  'cidade_de_localizacao',
  'credenciadoId',
  'odometro_atual',
  'descricao',
  'mobilizado',
  'orcamento_individual',
  'minimun_orcament',
  'quoteExpiration',
  'valor_autorizado',
  'arquivos'
];

// Campos selecionados por padrão (incluindo obrigatórios + alguns opcionais importantes)
const DEFAULT_SELECTED_FIELDS = [
  ...REQUIRED_FIELDS,
  'credenciadoId',
  'odometro_atual',
  'mobilizado',
  'valor_autorizado'
];

export function useOSFieldCustomization() {
  const [selectedFields, setSelectedFields] = useState<string[]>(DEFAULT_SELECTED_FIELDS);
  const [fieldOrder, setFieldOrder] = useState<string[]>(DEFAULT_FIELD_ORDER);

  // Carregar configuração salva do localStorage
  useEffect(() => {
    try {
      const savedConfig = localStorage.getItem('os-field-customization');
      if (savedConfig) {
        const config = JSON.parse(savedConfig);
        if (config.selectedFields && Array.isArray(config.selectedFields)) {
          // Garantir que campos obrigatórios estejam sempre incluídos
          const fieldsWithRequired = [...new Set([...REQUIRED_FIELDS, ...config.selectedFields])];
          setSelectedFields(fieldsWithRequired);
        }
        if (config.fieldOrder && Array.isArray(config.fieldOrder)) {
          setFieldOrder(config.fieldOrder);
        }
      }
    } catch (error) {
      console.error('Erro ao carregar configuração de campos:', error);
    }
  }, []);

  // Salvar configuração no localStorage
  const saveConfiguration = () => {
    try {
      const config = {
        selectedFields,
        fieldOrder,
        lastUpdated: new Date().toISOString()
      };
      localStorage.setItem('os-field-customization', JSON.stringify(config));
    } catch (error) {
      console.error('Erro ao salvar configuração de campos:', error);
    }
  };

  // Toggle de campo (não permite desmarcar campos obrigatórios)
  const toggleField = (fieldId: string) => {
    if (REQUIRED_FIELDS.includes(fieldId)) {
      return; // Não permite desmarcar campos obrigatórios
    }

    setSelectedFields(prev => {
      const newFields = prev.includes(fieldId)
        ? prev.filter(id => id !== fieldId)
        : [...prev, fieldId];
      
      return newFields;
    });
  };

  // Reordenar campos
  const reorderFields = (newOrder: string[]) => {
    setFieldOrder(newOrder);
  };

  // Selecionar todos os campos
  const selectAllFields = () => {
    setSelectedFields(DEFAULT_OS_FIELDS.map(field => field.id));
  };

  // Desmarcar todos (exceto obrigatórios)
  const deselectAllFields = () => {
    setSelectedFields(REQUIRED_FIELDS);
  };

  // Resetar para configuração padrão
  const resetToDefault = () => {
    setSelectedFields(DEFAULT_SELECTED_FIELDS);
    setFieldOrder(DEFAULT_FIELD_ORDER);
  };

  // Obter campos na ordem personalizada
  const getOrderedFields = () => {
    return fieldOrder
      .filter(fieldId => selectedFields.includes(fieldId))
      .map(fieldId => DEFAULT_OS_FIELDS.find(field => field.id === fieldId))
      .filter(Boolean) as OSFormField[];
  };

  // Verificar se um campo está visível
  const isFieldVisible = (fieldId: string) => {
    return selectedFields.includes(fieldId);
  };

  // Obter posição de um campo na ordem
  const getFieldPosition = (fieldId: string) => {
    const orderedFields = getOrderedFields();
    return orderedFields.findIndex(field => field.id === fieldId);
  };

  return {
    // Estado
    selectedFields,
    fieldOrder,
    availableFields: DEFAULT_OS_FIELDS,
    
    // Ações
    toggleField,
    reorderFields,
    selectAllFields,
    deselectAllFields,
    resetToDefault,
    saveConfiguration,
    
    // Utilitários
    getOrderedFields,
    isFieldVisible,
    getFieldPosition,
    
    // Constantes
    requiredFields: REQUIRED_FIELDS
  };
}
