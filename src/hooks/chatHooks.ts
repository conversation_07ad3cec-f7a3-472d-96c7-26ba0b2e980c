"use client";

import { useEffect, useState } from "react";
import { Socket } from "socket.io-client";
import { getSocket } from "@/lib/socket";
import { getMessagesByOrderServiceId } from "@/serverActions/chatMessagesActions";
import { getUsuarioFromSession } from "@/serverActions/getUsuarioFromSession";

type Mensagem = {
  id: string;
  chatId: string;
  remetenteId: string;
  tipo: "TEXTO" | "ARQUIVO";
  conteudo: string;
  urlArquivo: string | null;
  enviadaEm: string;
  usuarioId: string;
};

type Usuario = {
  userId: string;
  name: string;
  roles: string[];
};

export function useChat(
  usuario: Usuario,
  numeroDaOS: string,
  extras?: {
    gestorId?: string;
    credenciadoId?: string;
    numeroDoContrato?: string;
  }
) {
  const [mensagens, setMensagens] = useState<Mensagem[]>([]);
  const [mensagemAtual, setMensagemAtual] = useState("");
  const [socket, setSocket] = useState<Socket | null>(null);

  // Busca as mensagens associadas à OS
  useEffect(() => {
    const fetchMessages = async () => {
      try {
        if (numeroDaOS) {
          const messages = await getMessagesByOrderServiceId(numeroDaOS);

          if (messages?.data) {
            setMensagens(messages.data);
          }
        }
      } catch (error) {
        console.error("fetchMessages error: ", error);
      }
    };
    fetchMessages();
  }, [numeroDaOS]);

  useEffect(() => {
    const setupSocket = async () => {
      const usuarioToken = await getUsuarioFromSession();

      const queryParams = {
        userId: usuario.userId,
        numeroDaOS,
        jwt: usuarioToken,
        ...(extras?.gestorId && { gestorId: extras.gestorId }),
        ...(extras?.credenciadoId && { credenciadoId: extras.credenciadoId }),
        ...(extras?.numeroDoContrato && {
          numeroDoContrato: extras.numeroDoContrato,
        }),
      };

      const s = getSocket(queryParams);
      setSocket(s);

      s.on("connect", () => {
        console.log("🟢 Conectado ao WebSocket:", s.id);
      });

      s.on("mensagem_recebida", (mensagem: Mensagem) => {
        setMensagens((prev) => [...prev, mensagem]);
      });

      s.on("connect_error", (err) => {
        console.error("Erro de conexão:", err);
      });
    };

    setupSocket();

    return () => {
      if (socket) {
        socket.disconnect();
        socket.off("mensagem_recebida");
      }
    };
  }, [usuario.userId, numeroDaOS, extras]);

  // Função para enviar mensagem (o chatId será definido pelo backend)
  const enviarMensagem = (conteudo: string) => {
    return new Promise<void>((resolve, reject) => {
      if (!socket) return reject("Socket não conectado");

      const novaMensagem = {
        chatId: "", // será definido no backend
        conteudo,
        tipo: "TEXTO" as const,
        remetenteId: usuario.userId,
        usuarioId: usuario.userId,
      };

      socket.emit("nova_mensagem", novaMensagem);
      resolve();
    });
  };

  return {
    mensagens,
    mensagemAtual,
    setMensagemAtual,
    enviarMensagem,
  };
}
