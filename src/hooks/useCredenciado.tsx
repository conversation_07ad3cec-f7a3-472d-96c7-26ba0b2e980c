import { getCredenciadoById } from "@/serverActions/credenciadoAction";
import { useQuery } from "@tanstack/react-query";

export function useCredenciado(id: string) {
  return useQuery({
    queryKey: ["credenciado", id],
    queryFn: () => getCredenciadoById(id),
    enabled: !!id,
  });
}
export function useCredenciadoById(id?: string) {
  return useQuery({
    queryKey: ["credenciado", id],
    queryFn: () => getCredenciadoById(id!),
    enabled: !!id, // só executa se houver id
    staleTime: 1000 * 60 * 5, // cache de 5 minutos
  });
}
