import { useState } from "react";

export interface CepResponse {
  cep: string;
  logradouro: string;
  complemento: string;
  bairro: string;
  localidade: string; // cidade
  uf: string; // estado
  ibge: string;
  gia: string;
  ddd: string;
  siafi: string;
  erro?: boolean;
}

export const useCep = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<CepResponse | null>(null);

  const fetchAddressByCep = async (cep: string): Promise<CepResponse | null> => {
    // Remover caracteres não numéricos
    const cepNumbers = cep.replace(/\D/g, "");
    
    // Verificar se o CEP tem 8 dígitos
    if (cepNumbers.length !== 8) {
      console.error("CEP inválido - deve ter 8 dígitos");
      setError("CEP deve ter 8 dígitos");
      setData(null);
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`https://viacep.com.br/ws/${cepNumbers}/json/`);
      
      if (!response.ok) {
        throw new Error(`Erro ao buscar CEP: ${response.statusText}`);
      }
      
      const responseData = await response.json();      
      // A API do ViaCEP retorna um objeto com a propriedade 'erro' quando o CEP não é encontrado
      if (responseData.erro) {
        console.error("CEP não encontrado na base de dados");
        setError("CEP não encontrado");
        setData(null);
        return null;
      }
      
      setData(responseData);
      return responseData;
    } catch (error) {
      console.error("Erro na requisição do CEP:", error);
      setError(error instanceof Error ? error.message : "Erro ao buscar CEP");
      setData(null);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { loading, error, data, fetchAddressByCep };
}; 