interface Country {
    id: number;
    name: string;
}

interface Nature {
    id: number;
    text: string;
}

interface Size {
    id: number;
    acronym: string;
    text: string;
}

interface HistoryItem {
    from: string;
    until: string;
    text: string;
}

interface Simples {
    optant: boolean;
    since: string;
    history: HistoryItem[];
}

interface Person {
    id: string;
    type: string;
    name: string;
    taxId: string;
    age: string;
    country: Country;
}

interface Role {
    id: number;
    text: string;
}

interface Agent {
    person: Person;
    role: Role;
}

interface Member {
    since: string;
    person: Person;
    role: Role;
    agent: Agent;
}

interface Company {
    id: number;
    name: string;
    jurisdiction: string;
    equity: number;
    nature: Nature;
    size: Size;
    simples: Simples;
    simei: Simples;
    members: Member[];
}

interface Status {
    id: number;
    text: string;
}

interface Reason {
    id: number;
    text: string;
}

interface Special {
    id: number;
    text: string;
}

interface Address {
    municipality: number;
    street: string;
    number: string;
    district: string;
    city: string;
    state: string;
    details: string;
    zip: string;
    latitude: number;
    longitude: number;
    country: Country;
}

interface Phone {
    type: string;
    area: string;
    number: string;
}

interface Email {
    ownership: string;
    address: string;
    domain: string;
}

interface MainActivity {
    id: number;
    text: string;
}

interface RegistrationStatus {
    id: number;
    text: string;
}

interface RegistrationType {
    id: number;
    text: string;
}

interface Registration {
    number: string;
    state: string;
    enabled: boolean;
    statusDate: string;
    status: RegistrationStatus;
    type: RegistrationType;
}

interface Incentive {
    tribute: string;
    benefit: string;
    purpose: string;
    basis: string;
}

interface SuframaStatus {
    id: number;
    text: string;
}

interface SuframaItem {
    number: string;
    since: string;
    approved: boolean;
    approvalDate: string;
    status: SuframaStatus;
    incentives: Incentive[];
}

interface Link {
    type: string;
    url: string;
}

export interface CnpjData {
    taxId: string;
    updated: string;
    company: Company;
    alias: string;
    founded: string;
    head: boolean;
    statusDate: string;
    status: Status;
    reason: Reason;
    specialDate: string;
    special: Special;
    address: Address;
    phones: Phone[];
    emails: Email[];
    mainActivity: MainActivity;
    sideActivities: MainActivity[];
    registrations: Registration[];
    suframa: SuframaItem[];
    links: Link[];
}