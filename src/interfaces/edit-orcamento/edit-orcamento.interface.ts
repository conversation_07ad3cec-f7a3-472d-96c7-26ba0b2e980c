export interface ResumoData {
  total_pecas: number;
  total_servicos: number;
  subtotal: number;
  descontos: number;
  total: number;
}

export interface DiagnosticoData {
  observacoes: string;
  previsaoEntrega: number;
  validade: string;
}

export interface FileItem {
  file: File;
  preview?: string;
  id: string;
}

export interface FichaTecnicaItem {
  id: string;
  descricao: string;
  valor: string;
}

export interface FichaTecnicaData {
  veiculo: {
    placa: string;
    modelo: string;
    marca: string;
    ano: string;
  };
  itens: FichaTecnicaItem[];
}
