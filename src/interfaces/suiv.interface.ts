interface PriceHistoryItem {
  monthUpdate: number;
  yearUpdate: number;
  value: number;
  isPrediction: boolean;
}

interface FipeData {
  year: number;
  fipeId: number;
  makerDescription: string;
  modelDescription: string;
  versionDescription: string;
  fuel: string;
  currentValue: number;
  priceHistory: PriceHistoryItem[];
}

interface SuivData {
  fipeId: number;
  versionId: number;
  hasCatalog: boolean;
  versionDescription: string;
  modelId: number;
  modelDescription: string;
  makerId: number;
  makerDescription: string;
}

interface Part {
  id: number;
  description: string;
  labor: string;
  partNumber: string;
  price: number;
  amount: number;
  availableServies: {
    hasUsed: boolean;
    hasOriginal: boolean;
  };
}

interface VehicleInfoResponse {
  fipeDataCollection: FipeData[];
  suivDataCollection: SuivData[];
  maker: string;
  model: string;
  version: string;
  plate: string;
  yearModel: number;
  yearFab: number;
  fuel: string;
  vin: string | null;
  type: string;
  species: string;
  bodywork: string;
  power: number | null;
  engineLiters: number | null;
  engineValves: number | null;
  transmission: string | null;
  line: string | null;
  isNational: boolean;
  axisNumber: number;
  totalGrossWeight: number;
  maximumTractionCapacity: number;
  cubicCentimeters: number;
  seatCount: number;
  loadCapacity: number;
  gearBoxNumber: number | null;
  backAxisNumber: number | null;
  auxAxisNumber: number | null;
  engineNumber: string | null;
  color: string;
}

export type { VehicleInfoResponse, FipeData, SuivData, PriceHistoryItem, Part };
