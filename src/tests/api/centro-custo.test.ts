/**
 * Testes para as APIs do BFF de Centro de Custo
 *
 * Este arquivo testa todas as rotas implementadas:
 * - GET /api/centro-de-custo
 * - POST /api/centro-de-custo
 * - GET /api/centro-de-custo/[id]
 * - PUT /api/centro-de-custo/[id]
 * - DELETE /api/centro-de-custo/[id]
 * - GET /api/centro-de-custo/contrato/[id]
 */

// Jest globals declarations
declare global {
  function describe(name: string, fn: () => void): void;
  function it(name: string, fn: () => void | Promise<void>): void;
  function beforeAll(fn: () => void | Promise<void>): void;
  function afterAll(fn: () => void | Promise<void>): void;
  namespace expect {
    interface Matchers<R> {
      toBe(expected: any): R;
      toHaveProperty(property: string, value?: any): R;
      toBeGreaterThanOrEqual(expected: number): R;
      toBeLessThan(expected: number): R;
    }
  }
  function expect(actual: any): expect.Matchers<void>;
}

// Configuração base para os testes
const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3001";
const BFF_BASE_URL = "http://localhost:3001"; // URL do frontend

// Dados de teste
const testCentroCusto = {
  descricao: "Centro de Custo Teste",
  dotacao_orcamentista: "DOT-TEST-001",
  valor_dotacao: 50000.0,
  nome_responsavel: "João Silva Teste",
  contato: "(11) 99999-9999",
  email: "<EMAIL>",
  cnpj: "12.345.678/0001-90",
  razao_social: "Empresa Teste LTDA",
  cep: "01234-567",
  logradouro: "Rua Teste, 123",
  numero: "123",
  complemento: "Sala 456",
  bairro: "Bairro Teste",
  cidade: "São Paulo",
  estado: "SP",
  pais: "Brasil",
  coordenadas_latitude: -23.5505,
  coordenadas_longitude: -46.6333,
  ativo: true,
};

let createdCentroCustoId: string;

describe("Centro de Custo BFF API Tests", () => {
  beforeAll(async () => {
    console.log("🧪 Iniciando testes do BFF Centro de Custo");
    console.log(`📍 API Base URL: ${API_BASE_URL}`);
    console.log(`📍 BFF Base URL: ${BFF_BASE_URL}`);
  });

  afterAll(async () => {
    // Limpar dados de teste se necessário
    if (createdCentroCustoId) {
      try {
        await fetch(
          `${BFF_BASE_URL}/api/centro-de-custo/${createdCentroCustoId}`,
          {
            method: "DELETE",
          }
        );
        console.log("🧹 Dados de teste limpos");
      } catch (error) {
        console.log("⚠️ Erro ao limpar dados de teste:", error);
      }
    }
  });

  describe("GET /api/centro-de-custo", () => {
    it("deve retornar lista de centros de custo", async () => {
      console.log("🔍 Testando GET /api/centro-de-custo");

      const response = await fetch(`${BFF_BASE_URL}/api/centro-de-custo`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      console.log(`📊 Status: ${response.status}`);

      const data = await response.json();
      console.log("📦 Resposta:", JSON.stringify(data, null, 2));

      // Verificações básicas
      expect(response.status).toBe(200);
      expect(data).toHaveProperty("success");
      expect(data).toHaveProperty("data");

      if (data.success) {
        expect(data.data).toHaveProperty("centrosCusto");
        expect(Array.isArray(data.data.centrosCusto)).toBe(true);
        console.log(
          `✅ Encontrados ${data.data.centrosCusto.length} centros de custo`
        );
      } else {
        console.log("❌ Erro na resposta:", data.message);
      }
    });
  });

  describe("POST /api/centro-de-custo", () => {
    it("deve criar um novo centro de custo", async () => {
      console.log("🔍 Testando POST /api/centro-de-custo");
      console.log("📤 Payload:", JSON.stringify(testCentroCusto, null, 2));

      const response = await fetch(`${BFF_BASE_URL}/api/centro-de-custo`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(testCentroCusto),
      });

      console.log(`📊 Status: ${response.status}`);

      const data = await response.json();
      console.log("📦 Resposta:", JSON.stringify(data, null, 2));

      if (response.status === 201 && data.success) {
        expect(data).toHaveProperty("success", true);
        expect(data).toHaveProperty("data");
        expect(data.data).toHaveProperty("id");

        createdCentroCustoId = data.data.id;
        console.log(
          `✅ Centro de custo criado com ID: ${createdCentroCustoId}`
        );

        // Verificar se os dados foram salvos corretamente
        expect(data.data.descricao).toBe(testCentroCusto.descricao);
        expect(data.data.cnpj).toBe(testCentroCusto.cnpj);
        expect(data.data.razao_social).toBe(testCentroCusto.razao_social);
      } else {
        console.log("❌ Erro na criação:", data.message);
        console.log("📋 Detalhes do erro:", data);
      }
    });

    it("deve falhar ao criar centro de custo sem campos obrigatórios", async () => {
      console.log("🔍 Testando POST com dados incompletos");

      const incompleteData = {
        descricao: "Teste Incompleto",
        // Faltando campos obrigatórios
      };

      const response = await fetch(`${BFF_BASE_URL}/api/centro-de-custo`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(incompleteData),
      });

      console.log(`📊 Status: ${response.status}`);

      const data = await response.json();
      console.log("📦 Resposta:", JSON.stringify(data, null, 2));

      expect(response.status).toBe(400);
      expect(data).toHaveProperty("success", false);
      expect(data).toHaveProperty("message");
      console.log("✅ Validação funcionando corretamente");
    });
  });

  describe("GET /api/centro-de-custo/[id]", () => {
    it("deve retornar um centro de custo específico", async () => {
      if (!createdCentroCustoId) {
        console.log("⏭️ Pulando teste - nenhum centro de custo criado");
        return;
      }

      console.log(
        `🔍 Testando GET /api/centro-de-custo/${createdCentroCustoId}`
      );

      const response = await fetch(
        `${BFF_BASE_URL}/api/centro-de-custo/${createdCentroCustoId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      console.log(`📊 Status: ${response.status}`);

      const data = await response.json();
      console.log("📦 Resposta:", JSON.stringify(data, null, 2));

      if (response.status === 200 && data.success) {
        expect(data).toHaveProperty("success", true);
        expect(data).toHaveProperty("data");
        expect(data.data).toHaveProperty("id", createdCentroCustoId);
        expect(data.data).toHaveProperty("descricao");
        console.log("✅ Centro de custo encontrado");
      } else {
        console.log("❌ Erro ao buscar centro de custo:", data.message);
      }
    });

    it("deve retornar erro para ID inexistente", async () => {
      console.log("🔍 Testando GET com ID inexistente");

      const fakeId = "id-inexistente-123";
      const response = await fetch(
        `${BFF_BASE_URL}/api/centro-de-custo/${fakeId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      console.log(`📊 Status: ${response.status}`);

      const data = await response.json();
      console.log("📦 Resposta:", JSON.stringify(data, null, 2));

      expect(response.status).toBeGreaterThanOrEqual(400);
      expect(data).toHaveProperty("success", false);
      console.log("✅ Tratamento de erro funcionando");
    });
  });

  describe("PUT /api/centro-de-custo/[id]", () => {
    it("deve atualizar um centro de custo existente", async () => {
      if (!createdCentroCustoId) {
        console.log("⏭️ Pulando teste - nenhum centro de custo criado");
        return;
      }

      console.log(
        `🔍 Testando PUT /api/centro-de-custo/${createdCentroCustoId}`
      );

      const updateData = {
        descricao: "Centro de Custo Teste ATUALIZADO",
        nome_responsavel: "Maria Silva Teste",
        cidade: "Rio de Janeiro",
        estado: "RJ",
      };

      console.log(
        "📤 Dados de atualização:",
        JSON.stringify(updateData, null, 2)
      );

      const response = await fetch(
        `${BFF_BASE_URL}/api/centro-de-custo/${createdCentroCustoId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(updateData),
        }
      );

      console.log(`📊 Status: ${response.status}`);

      const data = await response.json();
      console.log("📦 Resposta:", JSON.stringify(data, null, 2));

      if (response.status === 200 && data.success) {
        expect(data).toHaveProperty("success", true);
        expect(data).toHaveProperty("data");
        expect(data.data.descricao).toBe(updateData.descricao);
        expect(data.data.nome_responsavel).toBe(updateData.nome_responsavel);
        console.log("✅ Centro de custo atualizado com sucesso");
      } else {
        console.log("❌ Erro na atualização:", data.message);
      }
    });
  });

  describe("Testes de Estrutura de Dados", () => {
    it("deve verificar a estrutura dos dados retornados", async () => {
      console.log("🔍 Verificando estrutura de dados");

      const response = await fetch(`${BFF_BASE_URL}/api/centro-de-custo`, {
        method: "GET",
      });

      const data = await response.json();

      if (data.success && data.data.centrosCusto.length > 0) {
        const centro = data.data.centrosCusto[0];
        console.log("📋 Estrutura do primeiro centro:", Object.keys(centro));

        // Verificar campos essenciais
        const expectedFields = [
          "id",
          "descricao",
          "cnpj",
          "razao_social",
          "ativo",
        ];
        expectedFields.forEach((field) => {
          expect(centro).toHaveProperty(field);
        });

        console.log("✅ Estrutura de dados válida");
      }
    });
  });

  describe("DELETE /api/centro-de-custo/[id]", () => {
    it("deve excluir um centro de custo", async () => {
      if (!createdCentroCustoId) {
        console.log("⏭️ Pulando teste - nenhum centro de custo criado");
        return;
      }

      console.log(
        `🔍 Testando DELETE /api/centro-de-custo/${createdCentroCustoId}`
      );

      const response = await fetch(
        `${BFF_BASE_URL}/api/centro-de-custo/${createdCentroCustoId}`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      console.log(`📊 Status: ${response.status}`);

      const data = await response.json();
      console.log("📦 Resposta:", JSON.stringify(data, null, 2));

      if (response.status === 200 && data.success) {
        expect(data).toHaveProperty("success", true);
        console.log("✅ Centro de custo excluído com sucesso");

        // Verificar se realmente foi excluído
        const verifyResponse = await fetch(
          `${BFF_BASE_URL}/api/centro-de-custo/${createdCentroCustoId}`
        );
        expect(verifyResponse.status).toBeGreaterThanOrEqual(400);
        console.log("✅ Exclusão confirmada");

        createdCentroCustoId = ""; // Limpar para não tentar excluir novamente
      } else {
        console.log("❌ Erro na exclusão:", data.message);
      }
    });
  });

  describe("Testes de Performance", () => {
    it("deve responder em tempo hábil", async () => {
      console.log("🔍 Testando performance da API");

      const startTime = Date.now();

      const response = await fetch(`${BFF_BASE_URL}/api/centro-de-custo`, {
        method: "GET",
      });

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      console.log(`⏱️ Tempo de resposta: ${responseTime}ms`);

      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(5000); // Menos de 5 segundos

      if (responseTime < 1000) {
        console.log("✅ Performance excelente (< 1s)");
      } else if (responseTime < 3000) {
        console.log("✅ Performance boa (< 3s)");
      } else {
        console.log("⚠️ Performance aceitável (< 5s)");
      }
    });
  });
});

// Função auxiliar para executar os testes manualmente
export async function runCentroCustoTests() {
  console.log("🚀 Executando testes manuais do BFF Centro de Custo");

  try {
    // Teste básico de conectividade
    console.log("\n1️⃣ Testando conectividade...");
    const healthResponse = await fetch(`${BFF_BASE_URL}/api/centro-de-custo`);
    console.log(`Status: ${healthResponse.status}`);

    if (healthResponse.status === 200) {
      const data = await healthResponse.json();
      console.log(`✅ API respondendo. Sucesso: ${data.success}`);

      if (data.success && data.data) {
        console.log(
          `📊 Centros encontrados: ${data.data.centrosCusto?.length || 0}`
        );
      }
    } else {
      console.log("❌ API não está respondendo corretamente");
    }

    // Teste de criação
    console.log("\n2️⃣ Testando criação...");
    const createResponse = await fetch(`${BFF_BASE_URL}/api/centro-de-custo`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(testCentroCusto),
    });

    console.log(`Status criação: ${createResponse.status}`);
    const createData = await createResponse.json();
    console.log(`Sucesso: ${createData.success}`);

    if (createData.success && createData.data) {
      console.log(`✅ Centro criado com ID: ${createData.data.id}`);
      return createData.data.id;
    } else {
      console.log(`❌ Erro na criação: ${createData.message}`);
    }
  } catch (error) {
    console.error("❌ Erro nos testes:", error);
  }
}
