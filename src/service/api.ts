import axios from "axios";

const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json'
  }
});

api.interceptors.request.use(async (config) => {
  try {
    const response = await fetch("/api/auth/session", { 
      method: "POST",
      credentials: "include",
      headers: {
        "Content-Type": "application/json"
      }
    });
    
    if (!response.ok) {
      throw new Error("Sessão inválida");
    }
    
    const session = await response.json();
    
    if (!session?.token) {
      throw new Error("Token não encontrado na sessão");
    }
    
    config.headers.Authorization = `Bearer ${session.token}`;
    return config;
  } catch (error) {
    console.error("Erro ao obter sessão:", error);
    return Promise.reject(error);
  }
});

api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Redirecionar para login se não autorizado
      window.location.href = "/login";
    }
    return Promise.reject(error);
  }
);

export default api;
