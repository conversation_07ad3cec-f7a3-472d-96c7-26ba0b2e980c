import { getVehicleByPlate, getVehicleToken } from "@/serverActions/suivAction";
import { getWithTTL, setWithTTL } from "@/utils/localStorageWithTTL";

const TTL = 2 * 60 * 60 * 1000; // 2 horas em milissegundos

export async function getVehicleTokenWithCache(versionId: number, year: number) {
  const cacheKey = `vehicleToken-${versionId}-${year}`;
  const cached = getWithTTL<{ token: string }>(cacheKey);

  if (cached) return cached;

  const result = await getVehicleToken(versionId, year);
  setWithTTL(cacheKey, result, TTL);
  return result;
}

export async function getVehicleByPlateWithCache(plate: string) {
  const cacheKey = `vehicleByPlate-${plate}`;
  const cached = getWithTTL<any>(cacheKey);

  if (cached) return cached;

  const result = await getVehicleByPlate(plate);
  setWithTTL(cacheKey, result, TTL);
  return result;
}
