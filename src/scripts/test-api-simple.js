/**
 * Script simples para testar as APIs do BFF Centro de Custo
 * 
 * Pode ser executado:
 * 1. No navegador (console do DevTools)
 * 2. No Node.js: node src/scripts/test-api-simple.js
 * 3. Copiar e colar no console do navegador
 */

// Configuração
const BFF_BASE_URL = 'http://localhost:3001';

// Dados de teste
const testCentroCusto = {
  descricao: "Centro de Custo Teste Simples",
  dotacao_orcamentista: "DOT-SIMPLE-001",
  valor_dotacao: 50000.00,
  nome_responsavel: "João Teste Simples",
  contato: "(11) 99999-8888",
  email: "<EMAIL>",
  cnpj: "11.222.333/0001-44",
  razao_social: "Empresa Teste Simples LTDA",
  cep: "01234-567",
  logradouro: "Rua Teste Simples, 789",
  numero: "789",
  bairro: "Bairro Teste",
  cidade: "São Paulo",
  estado: "SP",
  ativo: true
};

// Função para fazer requisições
async function makeRequest(url, options = {}) {
  const startTime = Date.now();
  
  try {
    console.log(`🔍 Fazendo requisição para: ${url}`);
    console.log(`📤 Método: ${options.method || 'GET'}`);
    
    if (options.body) {
      console.log(`📦 Payload:`, JSON.parse(options.body));
    }
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });
    
    const responseTime = Date.now() - startTime;
    const data = await response.json();
    
    console.log(`📊 Status: ${response.status}`);
    console.log(`⏱️ Tempo: ${responseTime}ms`);
    console.log(`📥 Resposta:`, data);
    
    return { response, data, responseTime };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    console.error(`❌ Erro:`, error);
    return { error, responseTime };
  }
}

// Teste 1: GET - Listar todos os centros
async function test1_getAllCentros() {
  console.log('\n🧪 TESTE 1: GET /api/centro-de-custo');
  console.log('=' * 40);
  
  const result = await makeRequest(`${BFF_BASE_URL}/api/centro-de-custo`);
  
  if (result.response && result.response.status === 200 && result.data.success) {
    const count = result.data.data?.centrosCusto?.length || 0;
    console.log(`✅ Sucesso! Encontrados ${count} centros de custo`);
    return true;
  } else {
    console.log(`❌ Falha: ${result.data?.message || result.error?.message || 'Erro desconhecido'}`);
    return false;
  }
}

// Teste 2: POST - Criar novo centro
async function test2_createCentro() {
  console.log('\n🧪 TESTE 2: POST /api/centro-de-custo');
  console.log('=' * 40);
  
  const result = await makeRequest(`${BFF_BASE_URL}/api/centro-de-custo`, {
    method: 'POST',
    body: JSON.stringify(testCentroCusto),
  });
  
  if (result.response && result.response.status === 201 && result.data.success && result.data.data?.id) {
    console.log(`✅ Sucesso! Centro criado com ID: ${result.data.data.id}`);
    return result.data.data.id;
  } else {
    console.log(`❌ Falha: ${result.data?.message || result.error?.message || 'Erro na criação'}`);
    return null;
  }
}

// Teste 3: GET por ID - Buscar centro específico
async function test3_getCentroById(id) {
  console.log(`\n🧪 TESTE 3: GET /api/centro-de-custo/${id}`);
  console.log('=' * 40);
  
  if (!id) {
    console.log('⏭️ Pulando teste - nenhum ID disponível');
    return false;
  }
  
  const result = await makeRequest(`${BFF_BASE_URL}/api/centro-de-custo/${id}`);
  
  if (result.response && result.response.status === 200 && result.data.success) {
    console.log(`✅ Sucesso! Centro encontrado: ${result.data.data?.descricao}`);
    return true;
  } else {
    console.log(`❌ Falha: ${result.data?.message || result.error?.message || 'Centro não encontrado'}`);
    return false;
  }
}

// Teste 4: PUT - Atualizar centro
async function test4_updateCentro(id) {
  console.log(`\n🧪 TESTE 4: PUT /api/centro-de-custo/${id}`);
  console.log('=' * 40);
  
  if (!id) {
    console.log('⏭️ Pulando teste - nenhum ID disponível');
    return false;
  }
  
  const updateData = {
    descricao: "Centro de Custo Teste Simples ATUALIZADO",
    nome_responsavel: "Maria Teste Simples",
    cidade: "Rio de Janeiro",
    estado: "RJ"
  };
  
  const result = await makeRequest(`${BFF_BASE_URL}/api/centro-de-custo/${id}`, {
    method: 'PUT',
    body: JSON.stringify(updateData),
  });
  
  if (result.response && result.response.status === 200 && result.data.success) {
    console.log(`✅ Sucesso! Centro atualizado`);
    return true;
  } else {
    console.log(`❌ Falha: ${result.data?.message || result.error?.message || 'Erro na atualização'}`);
    return false;
  }
}

// Teste 5: Validação - Testar campos obrigatórios
async function test5_validation() {
  console.log('\n🧪 TESTE 5: Validação de campos obrigatórios');
  console.log('=' * 40);
  
  const invalidData = {
    descricao: "Teste Validação"
    // Faltando campos obrigatórios
  };
  
  const result = await makeRequest(`${BFF_BASE_URL}/api/centro-de-custo`, {
    method: 'POST',
    body: JSON.stringify(invalidData),
  });
  
  if (result.response && result.response.status === 400 && !result.data.success) {
    console.log(`✅ Sucesso! Validação funcionando: ${result.data.message}`);
    return true;
  } else {
    console.log(`❌ Falha: Validação não funcionou - deveria retornar erro 400`);
    return false;
  }
}

// Teste 6: DELETE - Excluir centro
async function test6_deleteCentro(id) {
  console.log(`\n🧪 TESTE 6: DELETE /api/centro-de-custo/${id}`);
  console.log('=' * 40);
  
  if (!id) {
    console.log('⏭️ Pulando teste - nenhum ID disponível');
    return false;
  }
  
  const result = await makeRequest(`${BFF_BASE_URL}/api/centro-de-custo/${id}`, {
    method: 'DELETE',
  });
  
  if (result.response && result.response.status === 200 && result.data.success) {
    console.log(`✅ Sucesso! Centro excluído`);
    return true;
  } else {
    console.log(`❌ Falha: ${result.data?.message || result.error?.message || 'Erro na exclusão'}`);
    return false;
  }
}

// Função principal para executar todos os testes
async function runAllTests() {
  console.log('🚀 INICIANDO TESTES DO BFF CENTRO DE CUSTO');
  console.log(`📍 URL Base: ${BFF_BASE_URL}`);
  console.log('🕐 Iniciado em:', new Date().toLocaleString());
  
  const results = [];
  let createdId = null;
  
  try {
    // Teste 1: GET All
    const test1Result = await test1_getAllCentros();
    results.push({ test: 'GET All', passed: test1Result });
    
    // Teste 2: POST Create
    createdId = await test2_createCentro();
    results.push({ test: 'POST Create', passed: !!createdId });
    
    // Teste 3: GET By ID
    const test3Result = await test3_getCentroById(createdId);
    results.push({ test: 'GET By ID', passed: test3Result });
    
    // Teste 4: PUT Update
    const test4Result = await test4_updateCentro(createdId);
    results.push({ test: 'PUT Update', passed: test4Result });
    
    // Teste 5: Validation
    const test5Result = await test5_validation();
    results.push({ test: 'Validation', passed: test5Result });
    
    // Teste 6: DELETE
    const test6Result = await test6_deleteCentro(createdId);
    results.push({ test: 'DELETE', passed: test6Result });
    
  } catch (error) {
    console.error('❌ Erro geral nos testes:', error);
  }
  
  // Relatório final
  console.log('\n📊 RELATÓRIO FINAL');
  console.log('=' * 50);
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(result => {
    const icon = result.passed ? '✅' : '❌';
    console.log(`${icon} ${result.test}: ${result.passed ? 'PASSOU' : 'FALHOU'}`);
  });
  
  console.log(`\n📈 Resultado: ${passed}/${total} testes passaram`);
  
  if (passed === total) {
    console.log('🎉 TODOS OS TESTES PASSARAM!');
  } else {
    console.log(`⚠️ ${total - passed} teste(s) falharam`);
  }
  
  console.log('🕐 Finalizado em:', new Date().toLocaleString());
  
  return { passed, total, results };
}

// Função para testar apenas conectividade
async function quickTest() {
  console.log('🔍 TESTE RÁPIDO DE CONECTIVIDADE');
  console.log('=' * 30);
  
  try {
    const result = await makeRequest(`${BFF_BASE_URL}/api/centro-de-custo`);
    
    if (result.response && result.response.status === 200) {
      console.log('✅ API está respondendo!');
      if (result.data.success) {
        const count = result.data.data?.centrosCusto?.length || 0;
        console.log(`📊 Encontrados ${count} centros de custo`);
      }
      return true;
    } else {
      console.log('❌ API não está respondendo corretamente');
      return false;
    }
  } catch (error) {
    console.log('❌ Erro de conectividade:', error.message);
    return false;
  }
}

// Exportar funções para uso
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runAllTests,
    quickTest,
    makeRequest,
    testCentroCusto
  };
}

// Se executado diretamente no Node.js
if (typeof require !== 'undefined' && require.main === module) {
  runAllTests().catch(console.error);
}

// Para uso no navegador, expor globalmente
if (typeof window !== 'undefined') {
  window.testCentroCustoAPI = {
    runAllTests,
    quickTest,
    makeRequest
  };
  
  console.log('🌐 Funções disponíveis no navegador:');
  console.log('   window.testCentroCustoAPI.runAllTests() - Executar todos os testes');
  console.log('   window.testCentroCustoAPI.quickTest() - Teste rápido de conectividade');
}
