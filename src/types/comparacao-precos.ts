export interface ComparacaoItem {
  data: string;
  preco: number;
  fornecedor: string;
  osNumero: string;
  economia?: number;
}

export interface ComparacaoData {
  ultimosSessentaDias?: ComparacaoItem;
  estado?: ComparacaoItem;
  localidade?: ComparacaoItem;
  fornecedor?: ComparacaoItem;
}

export interface RelatorioComparacao {
  peca: {
    codigo: string;
    descricao: string;
  };
  periodo: string;
  dataGeracao: string;
  comparacoes: ComparacaoData;
  resumo: {
    menorPreco: number;
    maiorEconomia: number;
    recomendacao: string;
  };
}

export interface ComparacaoPrecosProps {
  codigo?: string;
  descricao?: string;
  precoAtual?: number;
  onPrecoSugerido?: (preco: number) => void;
}
