// Tipos específicos para Centro de Custo baseados no controller fornecido

export interface CentroCustoCreateRequest {
  descricao: string;
  centro_custo_tomadorID?: string;
  centro_de_custo_ascendenteId?: string;
  dotacao_orcamentista: string;
  valor_dotacao: number;
  nome_responsavel: string;
  contato: string;
  email: string;
  cnpj: string;
  razao_social: string;
  cep?: string;
  logradouro?: string;
  numero?: string;
  complemento?: string;
  bairro?: string;
  estado?: string;
  cidade?: string;
  pais?: string;
  coordenadas_latitude?: number;
  coordenadas_longitude?: number;
  localidade_completa?: string; // Campo derivado para relatórios
  ativo?: boolean;
}

export interface CentroCustoUpdateRequest {
  descricao?: string;
  centro_custo_tomadorID?: string;
  dotacao_orcamentista?: string;
  valor_dotacao?: number;
  nome_responsavel?: string;
  contato?: string;
  email?: string;
  cnpj?: string;
  razao_social?: string;
  cep?: string;
  logradouro?: string;
  numero?: string;
  complemento?: string;
  bairro?: string;
  estado?: string;
  cidade?: string;
  pais?: string;
  coordenadas_latitude?: number;
  coordenadas_longitude?: number;
  localidade_completa?: string;
  ativo?: boolean;
}

export interface CentroCustoEndereco {
  id: string;
  cep: string;
  logradouro: string;
  bairro: string;
  estado: string;
  cidade: string;
  centroCustoId: string;
}

export interface CentroCustoCompleto {
  id: string;
  descricao: string;
  centro_custo_tomadorID?: string;
  centro_custo_ascdID?: string;
  dotacao_orcamentista: string;
  valor_dotacao: number;
  nome_responsavel: string;
  contato: string;
  email: string;
  cnpj: string;
  razao_social: string;
  ativo: boolean;
  createdAt: Date;
  updatedAt: Date;

  // Relacionamentos
  lotacao_veiculos?: Array<{
    id: string;
    Veiculo: any;
  }>;
  empenhos?: Array<any>;
  endereco?: CentroCustoEndereco[];
  centro_custos_filhos?: CentroCustoCompleto[];
  centro_custo_pai?: CentroCustoCompleto;
  faturamento_dos_veiculos?: Array<any>;
  unidades_filhas?: Array<any>;
}

export interface CentroCustoResponse {
  centrosCusto: CentroCustoCompleto[];
  contrato?: any;
}

export interface CentroCustoApiResponse {
  message: string;
  centroCusto: CentroCustoCompleto;
}

export interface CentroCustoDeleteResponse {
  message: string;
}

// Tipos para validação
export interface CentroCustoValidationError {
  message: string;
  required: string[];
  missing?: string[];
}

// Tipos para relatórios
export interface CentroCustoReportData {
  id: string;
  descricao: string;
  cnpj: string;
  razao_social: string;
  nome_responsavel?: string;
  contato: string;
  email: string;
  dotacao_orcamentista: string;
  valor_dotacao: number;
  ativo: boolean;
  endereco?: {
    logradouro: string;
    cidade: string;
    estado: string;
  };
  centro_custo_pai?: {
    descricao: string;
  };
  centro_custos_filhos?: Array<{ id: string }>;
  lotacao_veiculos?: Array<{ id: string }>;
  faturamento_dos_veiculos?: Array<{ id: string }>;
  empenhos?: Array<{ id: string }>;
}

// Tipos para filtros
export interface CentroCustoFilters {
  status?: "all" | "active" | "inactive";
  hasChildren?: "all" | "yes" | "no";
  searchTerm?: string;
  centro_ascendente?: string;
}

// Tipos para mapeamento de dados
export interface CentroCustoMappingConfig {
  frontend: keyof CentroCustoCreateRequest;
  backend: keyof CentroCustoCreateRequest;
  transform?: (value: any) => any;
}

export const CENTRO_CUSTO_REQUIRED_FIELDS = [
  "descricao",
  "dotacao_orcamentista",
  "valor_dotacao",
  "nome_responsavel",
  "contato",
  "email",
  "cnpj",
  "razao_social",
] as const;

export type CentroCustoRequiredField =
  (typeof CENTRO_CUSTO_REQUIRED_FIELDS)[number];

// Utilitários de validação
export function validateCentroCustoCreate(data: any): {
  isValid: boolean;
  missingFields: string[];
} {
  const missingFields = CENTRO_CUSTO_REQUIRED_FIELDS.filter(
    (field) => !data[field]
  );
  return {
    isValid: missingFields.length === 0,
    missingFields,
  };
}

export function mapCentroCustoToBackend(
  frontendData: any
): CentroCustoCreateRequest {
  // Gerar localidade completa para relatórios
  const localidadeCompleta = [
    frontendData.logradouro,
    frontendData.numero,
    frontendData.bairro,
    frontendData.cidade,
    frontendData.estado,
    frontendData.pais,
  ]
    .filter(Boolean)
    .join(", ");

  return {
    descricao: frontendData.descricao,
    centro_custo_tomadorID: frontendData.centro_custo_tomadorID || undefined,
    centro_de_custo_ascendenteId:
      frontendData.centro_de_custo_ascendenteId || undefined,
    dotacao_orcamentista: frontendData.dotacao_orcamentista,
    valor_dotacao: Number(frontendData.valor_dotacao),
    nome_responsavel: frontendData.nome_responsavel,
    contato: frontendData.contato,
    email: frontendData.email,
    cnpj: frontendData.cnpj,
    razao_social: frontendData.razao_social,
    cep: frontendData.cep || undefined,
    logradouro: frontendData.logradouro || undefined,
    numero: frontendData.numero || undefined,
    complemento: frontendData.complemento || undefined,
    bairro: frontendData.bairro || undefined,
    estado: frontendData.estado || undefined,
    cidade: frontendData.cidade || undefined,
    pais: frontendData.pais || "Brasil",
    coordenadas_latitude: frontendData.coordenadas_latitude
      ? Number(frontendData.coordenadas_latitude)
      : undefined,
    coordenadas_longitude: frontendData.coordenadas_longitude
      ? Number(frontendData.coordenadas_longitude)
      : undefined,
    localidade_completa: localidadeCompleta || undefined,
    ativo: frontendData.ativo !== undefined ? frontendData.ativo : true,
  };
}

export function mapCentroCustoUpdateToBackend(
  frontendData: any
): CentroCustoUpdateRequest {
  const mapped: CentroCustoUpdateRequest = {};

  if (frontendData.descricao !== undefined)
    mapped.descricao = frontendData.descricao;
  if (frontendData.centro_custo_tomadorID !== undefined)
    mapped.centro_custo_tomadorID = frontendData.centro_custo_tomadorID;
  if (frontendData.dotacao_orcamentista !== undefined)
    mapped.dotacao_orcamentista = frontendData.dotacao_orcamentista;
  if (frontendData.valor_dotacao !== undefined)
    mapped.valor_dotacao = Number(frontendData.valor_dotacao);
  if (frontendData.nome_responsavel !== undefined)
    mapped.nome_responsavel = frontendData.nome_responsavel;
  if (frontendData.contato !== undefined) mapped.contato = frontendData.contato;
  if (frontendData.email !== undefined) mapped.email = frontendData.email;
  if (frontendData.cnpj !== undefined) mapped.cnpj = frontendData.cnpj;
  if (frontendData.razao_social !== undefined)
    mapped.razao_social = frontendData.razao_social;
  if (frontendData.cep !== undefined) mapped.cep = frontendData.cep;
  if (frontendData.logradouro !== undefined)
    mapped.logradouro = frontendData.logradouro;
  if (frontendData.numero !== undefined) mapped.numero = frontendData.numero;
  if (frontendData.complemento !== undefined)
    mapped.complemento = frontendData.complemento;
  if (frontendData.bairro !== undefined) mapped.bairro = frontendData.bairro;
  if (frontendData.estado !== undefined) mapped.estado = frontendData.estado;
  if (frontendData.cidade !== undefined) mapped.cidade = frontendData.cidade;
  if (frontendData.pais !== undefined) mapped.pais = frontendData.pais;
  if (frontendData.coordenadas_latitude !== undefined)
    mapped.coordenadas_latitude = Number(frontendData.coordenadas_latitude);
  if (frontendData.coordenadas_longitude !== undefined)
    mapped.coordenadas_longitude = Number(frontendData.coordenadas_longitude);
  if (frontendData.ativo !== undefined) mapped.ativo = frontendData.ativo;

  // Gerar localidade completa se algum campo de endereço foi alterado
  const enderecoFields = [
    "logradouro",
    "numero",
    "bairro",
    "cidade",
    "estado",
    "pais",
  ];
  const hasEnderecoChange = enderecoFields.some(
    (field) => frontendData[field] !== undefined
  );

  if (hasEnderecoChange) {
    const localidadeCompleta = [
      frontendData.logradouro,
      frontendData.numero,
      frontendData.bairro,
      frontendData.cidade,
      frontendData.estado,
      frontendData.pais,
    ]
      .filter(Boolean)
      .join(", ");

    if (localidadeCompleta) {
      mapped.localidade_completa = localidadeCompleta;
    }
  }

  return mapped;
}
