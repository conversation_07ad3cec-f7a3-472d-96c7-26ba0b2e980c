import { OS } from "@/components/sections/os/OS";
import type { Meta, StoryObj } from "@storybook/react";

const meta: Meta<typeof OS> = {
  title: "Components/Sections/ServiceOrder",
  component: OS,
  tags: ["autodocs"],
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
    },
  },
};

export default meta;

type Story = StoryObj<typeof OS>;

export const Default: Story = {
  render: () => <OS />,
};
