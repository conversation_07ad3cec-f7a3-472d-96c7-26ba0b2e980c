import { Login } from "@/components/sections/login";
import SessionProvider from "@/components/session/session-provider";
import type { Meta, StoryObj } from "@storybook/react";
import { Toaster } from "sonner";

const meta: Meta<typeof Login> = {
  title: "Components/Sections/Login",
  component: Login,
  tags: ["autodocs"],
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
    },
  },
};

export default meta;

type Story = StoryObj<typeof Login>;

export const Default: Story = {
  render: () => (
    <>
      <SessionProvider initialSession={undefined}>
        <Login />
        <Toaster />
      </SessionProvider>
    </>
  ),
};
