import { Service } from "@/components/cards/service-card";
import { ServiceBoard } from "@/components/sections/service-board";
import type { Meta, StoryObj } from "@storybook/react";
const InitialServices: Service[] = [
  {
    id: "service-1",
    columnId: "availible",
    service: "Serviço 1",
  },
  {
    id: "service-2",
    columnId: "availible",
    service: "Serviço 2",
  },
  {
    id: "service-3",
    columnId: "choosed",
    service: "Serviço 3",
  },
  {
    id: "service-4",
    columnId: "choosed",
    service: "Serviço 4",
  },
];
const meta: Meta<typeof ServiceBoard> = {
  title: "Components/Sections/DndServices",
  component: ServiceBoard,
  tags: ["autodocs"],
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
    },
  },
};

export default meta;

type Story = StoryObj<typeof ServiceBoard>;

export const Default: Story = {
  render: () => (
    <ServiceBoard initialServices={InitialServices} handleServices={() => {}} />
  ),
};
