import Breadcrumbs from "@/components/navigation/Breadcrumbs";
import { SideBar } from "@/components/navigation/side-nav";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import type { <PERSON>a, StoryObj } from "@storybook/react";

const meta: Meta<typeof SideBar> = {
  title: "Components/Navigation/SideNav",
  component: SideBar,
  tags: ["autodocs"],
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
    },
  },
};

export default meta;

type Story = StoryObj<typeof SideBar>;

export const Default: Story = {
  render: () => (
    <SidebarProvider>
      <SideBar />
      <SidebarInset>
        <div className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />

            <Breadcrumbs />
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  ),
};
