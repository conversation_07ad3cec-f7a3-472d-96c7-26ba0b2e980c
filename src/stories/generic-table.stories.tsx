import type { <PERSON><PERSON>, <PERSON><PERSON>b<PERSON> } from "@storybook/react";
import { Toaster } from "@/components/ui/sonner";
import { ColumnDef } from "@tanstack/react-table";

import { z } from "zod";

import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { MoreHorizontal } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";

type User = {
  id: string;
  name: string;
  email: string;
  role: string;
};

const userSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  email: z.string().email("E-mail inválido"),
  role: z.string().min(1, "Cargo é obrigatório"),
});

const users: User[] = [
  { id: "1", name: "<PERSON>", email: "<EMAIL>", role: "Ad<PERSON>" },
  { id: "2", name: "Maria Souza", email: "<EMAIL>", role: "User" },
  { id: "3", name: "Pedro Costa", email: "<EMAIL>", role: "User" },
];

const meta: Meta<typeof DataTable> = {
  title: "Components/Tables/DataTable",
  component: DataTable,
  tags: ["autodocs"],
  parameters: {
    nextjs: {
      appDirectory: true,
    },
  },
};

export default meta;

type Story = StoryObj<typeof DataTable>;

export const Default: Story = {
  render: () => {
    const userColumns: ColumnDef<User>[] = [
      {
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
        ),
        enableSorting: false,
        enableHiding: false,
      },
      {
        accessorKey: "name",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Nome" />
        ),
      },
      {
        accessorKey: "email",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Email" />
        ),
      },
      {
        accessorKey: "role",
        header: "Cargo",
      },
      {
        id: "actions",
        cell: ({ row }) => {
          const payment = row.original;

          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem
                  onClick={() => navigator.clipboard.writeText(payment.id)}
                >
                  Copy payment ID
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>View customer</DropdownMenuItem>
                <DropdownMenuItem>View payment details</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
      },
    ];
    const fieldConfig = {
      name: {
        label: "Nome",
        placeholder: "e.g. Luis Paulo",
        description: "O nome completo do usuário",
        inputType: "text",
      },
      email: {
        label: "E-mail",
        placeholder: "e.g. <EMAIL>",
        description: "O e-mail deve ser válido",
        inputType: "email",
      },
      role: {
        label: "Cargo",
        placeholder: " o cargoDigite",
        description: "O cargo do usuário",
        inputType: "text",
      },
    };
    return (
      <>
        <DataTable
          onNewItem={(item) => console.log(item)}
          newItem={{
            schema: userSchema,
            defaultValues: { name: "", email: "", role: "" },
            fieldConfig,
          }}
          exportTo={true}
          data={users}
          columns={userColumns}
        />
        <Toaster />
      </>
    );
  },
};
