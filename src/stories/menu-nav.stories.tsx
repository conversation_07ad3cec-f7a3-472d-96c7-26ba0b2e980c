import MenuNav from "@/components/navigation/menu-nav";
import type { Meta, StoryObj } from "@storybook/react";

const meta: Meta<typeof MenuNav> = {
  title: "Components/Navigation/MenuNav",
  component: MenuNav,
  tags: ["autodocs"],
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
    },
  },
};

export default meta;

type Story = StoryObj<typeof MenuNav>;

export const Default: Story = {
  render: () => <MenuNav />,
};
