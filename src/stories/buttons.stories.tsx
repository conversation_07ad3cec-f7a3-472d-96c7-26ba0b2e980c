import { ExportTo } from "@/components/buttons/export-to";
import { Fullscreen } from "@/components/buttons/fullscreen";
import { ToggleTheme } from "@/components/buttons/toggle-theme";

import type { Meta, StoryObj } from "@storybook/react";

const meta: Meta<typeof ExportTo> = {
  title: "Components/Buttons/Galery",
  component: ExportTo,
  tags: ["autodocs"],
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
    },
  },
};

export default meta;

type Story = StoryObj<typeof ExportTo>;

export const Default: Story = {
  render: () => (
    <div className="flex flex-col items-center gap-2">
      <Fullscreen />
      <ToggleTheme />
      <ExportTo exportTo={() => {}} />
    </div>
  ),
};
