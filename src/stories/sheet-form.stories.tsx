// SheetForm.stories.tsx
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { z } from "zod";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { SheetForm } from "@/components/forms/sheet-form";

const schema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  email: z.string().email("E-mail inválido"),
});

const meta: Meta<typeof SheetForm> = {
  title: "Components/Forms/SheetForm",
  component: SheetForm,
  tags: ["autodocs"],
  parameters: {
    nextjs: {
      appDirectory: true,
    },
  },
};

export default meta;

type Story = StoryObj<typeof SheetForm>;

export const Default: Story = {
  args: {
    title: "Formulário de Exemplo",
    schema: schema,
    onSubmit: (values: any) => {
      console.log("Valores enviados:", values);
    },
    triggerLabel: "<PERSON><PERSON><PERSON>",
    defaultValues: {
      name: "<PERSON>",
      email: "",
    },
    children: (
      <>
        <FormField
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nome</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>E-mail</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </>
    ),
  },
};
