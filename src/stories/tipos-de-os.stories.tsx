import { OSTypes } from "@/components/sections/os/os-types";
import type { Meta, StoryObj } from "@storybook/react";

const meta: Meta<typeof OSTypes> = {
  title: "Components/Sections/OsTypes",
  component: OSTypes,
  tags: ["autodocs"],
  parameters: {
    layout: "fullscreen",
    nextjs: {
      appDirectory: true,
    },
  },
};

export default meta;

type Story = StoryObj<typeof OSTypes>;

export const Default: Story = {
  render: () => <OSTypes />,
};
