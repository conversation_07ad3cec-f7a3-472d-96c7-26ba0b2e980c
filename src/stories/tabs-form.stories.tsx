// import { AccreditorForm } from "@/components/forms/accreditor-form";
// import type { <PERSON>a, StoryObj } from "@storybook/react";

// const meta: Meta<typeof AccreditorForm> = {
//   title: "Components/Forms/AccreditorForm",
//   component: AccreditorForm,
//   tags: ["autodocs"],
//   parameters: {
//     layout: "fullscreen",
//     nextjs: {
//       appDirectory: true,
//     },
//   },
// };

// export default meta;

// type Story = StoryObj<typeof AccreditorForm>;

// export const Default: Story = {
//   render: () => <AccreditorForm />,
// };
