"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { useCentroCustoCRUD } from '@/hooks/useCentroCustoActions';

/**
 * Exemplo de uso do BFF implementado para Centro de Custo
 * 
 * Este componente demonstra como usar:
 * 1. Hooks customizados
 * 2. Server Actions
 * 3. Validação automática
 * 4. Mapeamento de dados
 * 5. Tratamento de erros
 */
export function CentroCustoUsageExample() {
  const {
    centrosCusto,
    selectedCentroCusto,
    loading,
    error,
    loadCentrosCusto,
    createCentroCusto,
    updateCentroCusto,
    deleteCentroCusto,
    setSelectedCentroCusto,
    clearError
  } = useCentroCustoCRUD();

  const [formData, setFormData] = useState({
    descricao: '',
    cnpj: '',
    razao_social: '',
    nome_responsavel: '',
    contato: '',
    email: '',
    dotacao_orcamentista: '',
    valor_dotacao: '',
    cep: '',
    logradouro: '',
    bairro: '',
    estado: '',
    cidade: '',
    ativo: true
  });

  const [isEditing, setIsEditing] = useState(false);

  // Carrega os centros de custo ao montar o componente
  useEffect(() => {
    loadCentrosCusto();
  }, [loadCentrosCusto]);

  // Limpa erros quando o usuário interage
  useEffect(() => {
    if (error) {
      const timer = setTimeout(clearError, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (isEditing && selectedCentroCusto) {
        // Atualizar centro existente
        const result = await updateCentroCusto(selectedCentroCusto.id, formData);
        if (result) {
          resetForm();
          toast.success('Centro de custo atualizado com sucesso!');
        }
      } else {
        // Criar novo centro
        const result = await createCentroCusto(formData);
        if (result) {
          resetForm();
          toast.success('Centro de custo criado com sucesso!');
        }
      }
    } catch (error) {
      console.error('Erro no formulário:', error);
    }
  };

  const handleEdit = (centro: any) => {
    setSelectedCentroCusto(centro);
    setFormData({
      descricao: centro.descricao || '',
      cnpj: centro.cnpj || '',
      razao_social: centro.razao_social || '',
      nome_responsavel: centro.nome_responsavel || '',
      contato: centro.contato || '',
      email: centro.email || '',
      dotacao_orcamentista: centro.dotacao_orcamentista || '',
      valor_dotacao: centro.valor_dotacao?.toString() || '',
      cep: centro.endereco?.[0]?.cep || '',
      logradouro: centro.endereco?.[0]?.logradouro || '',
      bairro: centro.endereco?.[0]?.bairro || '',
      estado: centro.endereco?.[0]?.estado || '',
      cidade: centro.endereco?.[0]?.cidade || '',
      ativo: centro.ativo ?? true
    });
    setIsEditing(true);
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Tem certeza que deseja excluir este centro de custo?')) {
      const success = await deleteCentroCusto(id);
      if (success) {
        toast.success('Centro de custo excluído com sucesso!');
      }
    }
  };

  const resetForm = () => {
    setFormData({
      descricao: '',
      cnpj: '',
      razao_social: '',
      nome_responsavel: '',
      contato: '',
      email: '',
      dotacao_orcamentista: '',
      valor_dotacao: '',
      cep: '',
      logradouro: '',
      bairro: '',
      estado: '',
      cidade: '',
      ativo: true
    });
    setIsEditing(false);
    setSelectedCentroCusto(null);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold">Exemplo de Uso - Centro de Custo BFF</h1>
      
      {/* Formulário */}
      <Card>
        <CardHeader>
          <CardTitle>
            {isEditing ? 'Editar Centro de Custo' : 'Criar Novo Centro de Custo'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Campos obrigatórios */}
              <div>
                <Label htmlFor="descricao">Descrição *</Label>
                <Input
                  id="descricao"
                  value={formData.descricao}
                  onChange={(e) => handleInputChange('descricao', e.target.value)}
                  required
                />
              </div>

              <div>
                <Label htmlFor="cnpj">CNPJ *</Label>
                <Input
                  id="cnpj"
                  value={formData.cnpj}
                  onChange={(e) => handleInputChange('cnpj', e.target.value)}
                  required
                />
              </div>

              <div>
                <Label htmlFor="razao_social">Razão Social *</Label>
                <Input
                  id="razao_social"
                  value={formData.razao_social}
                  onChange={(e) => handleInputChange('razao_social', e.target.value)}
                  required
                />
              </div>

              <div>
                <Label htmlFor="nome_responsavel">Nome do Responsável *</Label>
                <Input
                  id="nome_responsavel"
                  value={formData.nome_responsavel}
                  onChange={(e) => handleInputChange('nome_responsavel', e.target.value)}
                  required
                />
              </div>

              <div>
                <Label htmlFor="contato">Contato *</Label>
                <Input
                  id="contato"
                  value={formData.contato}
                  onChange={(e) => handleInputChange('contato', e.target.value)}
                  required
                />
              </div>

              <div>
                <Label htmlFor="email">E-mail *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  required
                />
              </div>

              <div>
                <Label htmlFor="dotacao_orcamentista">Dotação Orçamentária *</Label>
                <Input
                  id="dotacao_orcamentista"
                  value={formData.dotacao_orcamentista}
                  onChange={(e) => handleInputChange('dotacao_orcamentista', e.target.value)}
                  required
                />
              </div>

              <div>
                <Label htmlFor="valor_dotacao">Valor da Dotação *</Label>
                <Input
                  id="valor_dotacao"
                  type="number"
                  step="0.01"
                  value={formData.valor_dotacao}
                  onChange={(e) => handleInputChange('valor_dotacao', e.target.value)}
                  required
                />
              </div>

              {/* Campos opcionais de endereço */}
              <div>
                <Label htmlFor="cep">CEP</Label>
                <Input
                  id="cep"
                  value={formData.cep}
                  onChange={(e) => handleInputChange('cep', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="logradouro">Logradouro</Label>
                <Input
                  id="logradouro"
                  value={formData.logradouro}
                  onChange={(e) => handleInputChange('logradouro', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="bairro">Bairro</Label>
                <Input
                  id="bairro"
                  value={formData.bairro}
                  onChange={(e) => handleInputChange('bairro', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="estado">Estado</Label>
                <Input
                  id="estado"
                  value={formData.estado}
                  onChange={(e) => handleInputChange('estado', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="cidade">Cidade</Label>
                <Input
                  id="cidade"
                  value={formData.cidade}
                  onChange={(e) => handleInputChange('cidade', e.target.value)}
                />
              </div>
            </div>

            <div className="flex gap-2">
              <Button type="submit" disabled={loading}>
                {loading ? 'Salvando...' : (isEditing ? 'Atualizar' : 'Criar')}
              </Button>
              {isEditing && (
                <Button type="button" variant="outline" onClick={resetForm}>
                  Cancelar
                </Button>
              )}
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Lista de Centros de Custo */}
      <Card>
        <CardHeader>
          <CardTitle>Centros de Custo Cadastrados</CardTitle>
        </CardHeader>
        <CardContent>
          {loading && <p>Carregando...</p>}
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}
          
          <div className="space-y-2">
            {centrosCusto.map((centro) => (
              <div key={centro.id} className="flex items-center justify-between p-4 border rounded">
                <div>
                  <h3 className="font-medium">{centro.descricao}</h3>
                  <p className="text-sm text-gray-600">
                    {centro.razao_social} - {centro.cnpj}
                  </p>
                  <p className="text-sm text-gray-500">
                    Responsável: {centro.nome_responsavel}
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleEdit(centro)}
                  >
                    Editar
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleDelete(centro.id)}
                  >
                    Excluir
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
