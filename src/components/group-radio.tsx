"use client";

import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useId } from "react";

export interface GroupRadioProps {
  legend: string;
  value: string;
  onValueChange: (value: string) => void;
}

export function GroupRadio({ legend, value, onValueChange }: GroupRadioProps) {
  const id = useId();

  const items = [
    { value: "Bom", label: "Bom" },
    { value: "Reparo", label: "Reparo" },
    { value: "Ausente", label: "Ausente" },
    { value: "Não Avaliado", label: "Não Avaliado" },
  ];

  return (
    <fieldset className="space-y-4 max-w-[400px]">
      <legend className="text-sm font-medium leading-none text-foreground">
        {legend}
      </legend>
      <RadioGroup
        value={value}
        onValueChange={onValueChange}
        className="flex gap-4 items-center"
      >
        {items.map((item) => (
          <div
            key={`${id}-${item.value}`}
            className="relative flex items-start gap-2 rounded-lg border border-input p-3 shadow-sm shadow-black/5"
          >
            <div className="flex items-center gap-1">
              <RadioGroupItem
                id={`${id}-${item.value}`}
                value={item.value}
                className="w-4 h-4"
              />
              <Label htmlFor={`${id}-${item.value}`}>{item.label}</Label>
            </div>
          </div>
        ))}
      </RadioGroup>
    </fieldset>
  );
}
