"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { GetPercursoConfig } from "@/components/forms/inputConfig";

import { percursoSchema } from "@/components/forms/schemas";

import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { Checkbox } from "@/components/ui/checkbox";
import { useCondutor } from "@/context/condutor-context";
import { usePercurso } from "@/context/percurso-context";
import { useVeiculos } from "@/context/veiculos-context";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { toast } from "sonner";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { ChevronDown, FileText, Filter, Plus, RefreshCw, Search, Loader2, Settings2 } from "lucide-react";
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export const percursoColumns: ColumnDef<percurso>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "id",
    header: ({ column }) => <DataTableColumnHeader column={column} title="ID" />,
  },
  {
    accessorKey: "centro_de_custo",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Centro de Custo" />,
    accessorFn: (row) => row.veiculo?.lotacao_veiculos?.centro_custo?.descricao || "N/A",
  },
  {
    accessorKey: "veiculo",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Veículo" />,
    accessorFn: (row) => {
      const veiculo = row.veiculo;
      if (!veiculo) return "Não informado";
      
      const placa = veiculo.matricula || '';
      const modelo = veiculo.modelo?.descricao || '';
      
      return placa ? `${placa} | ${modelo}` : modelo;
    }
  },
  {
    accessorKey: "origem",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Origem" />,
    accessorFn: (row) => {
      if (!row.origensPercurso || row.origensPercurso.length === 0) return "Não especificada";
      return row.origensPercurso[0].local || "Não especificada";
    }
  },
  {
    accessorKey: "data_inicial",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Data Inicial" />,
    cell: ({ row }) => {
      if (!row.original.origensPercurso || row.original.origensPercurso.length === 0) {
        return <span className="text-muted-foreground">-</span>;
      }
      const data = row.original.origensPercurso[0].data;
      return data ? (
        <span>{format(new Date(data), "dd/MM/yyyy HH:mm")}</span>
      ) : (
        <span className="text-muted-foreground">-</span>
      );
    },
  },
  {
    accessorKey: "odometro_inicial",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Odômetro Inicial" />,
    accessorFn: (row) => {
      if (!row.origensPercurso || row.origensPercurso.length === 0) return "N/A";
      const odometro = row.origensPercurso[0].odometro;
      return odometro ? `${odometro} km` : "N/A";
    }
  },
  {
    accessorKey: "destino",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Destino" />,
    accessorFn: (row) => {
      if (!row.destinosPercurso || row.destinosPercurso.length === 0) return "Não especificada";
      return row.destinosPercurso[0].local || "Não especificada";
    }
  },
  {
    accessorKey: "data_final",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Data Final" />,
    cell: ({ row }) => {
      if (!row.original.destinosPercurso || row.original.destinosPercurso.length === 0) {
        return <span className="text-muted-foreground">-</span>;
      }
      const data = row.original.destinosPercurso[0].data;
      return data ? (
        <span>{format(new Date(data), "dd/MM/yyyy HH:mm")}</span>
      ) : (
        <span className="text-muted-foreground">-</span>
      );
    },
  },
  {
    accessorKey: "odometro_final",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Odômetro Final" />,
    accessorFn: (row) => {
      if (!row.destinosPercurso || row.destinosPercurso.length === 0) return "N/A";
      const odometro = row.destinosPercurso[0].odometro;
      return odometro ? `${odometro} km` : "N/A";
    }
  },
  {
    accessorKey: "condutor",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Condutor" />,
    accessorFn: (row) => row.condutor?.nome || "Não informado",
  },
  {
    accessorKey: "motivo",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Motivo" />,
  },
];

export function Percurso() {
  const router = useRouter();
  const { percursos, setPercursos, loading, error } = usePercurso();
  const { veiculos } = useVeiculos();
  const { condutores } = useCondutor();
  
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [selectedVeiculo, setSelectedVeiculo] = useState("all");
  const [selectedCondutor, setSelectedCondutor] = useState("all");
  const [dataInicio, setDataInicio] = useState("");
  const [dataFim, setDataFim] = useState("");
  const [origemFilter, setOrigemFilter] = useState("");
  const [destinoFilter, setDestinoFilter] = useState("");
  
  const [filteredPercursos, setFilteredPercursos] = useState<percurso[]>([]);
  const [isExporting, setIsExporting] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [refreshing, setRefreshing] = useState(false);
  const [columnVisibility, setColumnVisibility] = useState<Record<string, boolean>>({
    'id': true,
    'centro_de_custo': true,
    'veiculo': true,
    'origem': true,
    'data_inicial': true,
    'odometro_inicial': true,
    'destino': true,
    'data_final': true,
    'odometro_final': true,
    'condutor': true,
    'motivo': true
  });
  
  // Filtrar percursos conforme busca e filtros
  useEffect(() => {
    if (!Array.isArray(percursos)) {
      setFilteredPercursos([]);
      return;
    }
    
    let filtered = [...percursos];
    
    // Aplicar filtro de busca rápida
    if (searchTerm) {
      const lowerCaseSearch = searchTerm.toLowerCase();
      filtered = filtered.filter(percurso => {
        // Busca em vários campos
        return JSON.stringify(percurso).toLowerCase().includes(lowerCaseSearch);
      });
    }
    
    // Aplicar filtros avançados
    if (selectedVeiculo && selectedVeiculo !== "all") {
      filtered = filtered.filter(percurso => 
        percurso.veiculo?.id === selectedVeiculo);
    }
    
    if (selectedCondutor && selectedCondutor !== "all") {
      filtered = filtered.filter(percurso => 
        percurso.condutor?.id === selectedCondutor);
    }
    
    if (origemFilter) {
      filtered = filtered.filter(percurso => 
        percurso.origensPercurso?.[0]?.local?.toLowerCase().includes(origemFilter.toLowerCase()));
    }
    
    if (destinoFilter) {
      filtered = filtered.filter(percurso => 
        percurso.destinosPercurso?.[0]?.local?.toLowerCase().includes(destinoFilter.toLowerCase()));
    }
    
    if (dataInicio) {
      const startDate = new Date(dataInicio);
      filtered = filtered.filter(percurso => {
        if (!percurso.origensPercurso?.[0]?.data) return false;
        return new Date(percurso.origensPercurso[0].data) >= startDate;
      });
    }
    
    if (dataFim) {
      const endDate = new Date(dataFim);
      endDate.setHours(23, 59, 59, 999);
      filtered = filtered.filter(percurso => {
        if (!percurso.destinosPercurso?.[0]?.data) return false;
        return new Date(percurso.destinosPercurso[0].data) <= endDate;
      });
    }
    
    setFilteredPercursos(filtered);
    setCurrentPage(1);
  }, [searchTerm, selectedVeiculo, selectedCondutor, origemFilter, destinoFilter, dataInicio, dataFim, percursos]);
  
  // Calcular paginação
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredPercursos.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredPercursos.length / itemsPerPage);
  
  // Criar novo percurso
  const handleNovoPercurso = () => {
    router.push("/dashboard/percursos/novo-percurso");
  };
  
  // Navegação de página
  const goToPage = (page: number) => {
    if (page > 0 && page <= totalPages) {
      setCurrentPage(page);
    }
  };
  
  // Limpar os filtros de busca
  const handleLimparFiltros = () => {
    setSearchTerm("");
    setSelectedVeiculo("all");
    setSelectedCondutor("all");
    setDataInicio("");
    setDataFim("");
    setOrigemFilter("");
    setDestinoFilter("");
    toast.success("Filtros limpos com sucesso");
  };
  
  // Função para atualizar a lista de percursos
  // const handleRefresh = async () => {
  //   setRefreshing(true);
  //   try {
  //     await refreshPercursos();
  //     toast.success("Lista de percursos atualizada com sucesso!");
  //   } catch (err) {
  //     toast.error("Erro ao atualizar lista", {
  //       description: err instanceof Error ? err.message : "Ocorreu um erro desconhecido"
  //     });
  //   } finally {
  //     setRefreshing(false);
  //   }
  // };
  
  // Exportar para Excel
  const exportToExcel = async () => {
    try {
      setIsExporting(true);
      
      // Preparar dados para exportação
      const dataToExport = filteredPercursos.map(percurso => {
        return {
          ID: percurso.id,
          'Centro de Custo': percurso.veiculo?.lotacao_veiculos?.centro_custo?.descricao || "N/A",
          'Veículo': percurso.veiculo ? `${percurso.veiculo.matricula || ''} | ${percurso.veiculo.modelo?.descricao || ''}` : "Não informado",
          'Origem': percurso.origensPercurso?.[0]?.local || "Não especificada",
          'Data Inicial': percurso.origensPercurso?.[0]?.data ? format(new Date(percurso.origensPercurso[0].data), "dd/MM/yyyy HH:mm") : "-",
          'Odômetro Inicial': percurso.origensPercurso?.[0]?.odometro ? `${percurso.origensPercurso[0].odometro} km` : "N/A",
          'Destino': percurso.destinosPercurso?.[0]?.local || "Não especificada",
          'Data Final': percurso.destinosPercurso?.[0]?.data ? format(new Date(percurso.destinosPercurso[0].data), "dd/MM/yyyy HH:mm") : "-",
          'Odômetro Final': percurso.destinosPercurso?.[0]?.odometro ? `${percurso.destinosPercurso[0].odometro} km` : "N/A",
          'Condutor': percurso.condutor?.nome || "Não informado",
          'Motivo': percurso.motivo || "-"
        };
      });
      
      // Criar worksheet
      const ws = XLSX.utils.json_to_sheet(dataToExport);
      
      // Ajustar largura das colunas
      const wscols = [
        { wch: 15 },  // ID
        { wch: 20 },  // Centro de custo
        { wch: 30 },  // Veículo
        { wch: 25 },  // Origem
        { wch: 20 },  // Data Inicial
        { wch: 15 },  // Odômetro Inicial
        { wch: 25 },  // Destino
        { wch: 20 },  // Data Final
        { wch: 15 },  // Odômetro Final
        { wch: 20 },  // Condutor
        { wch: 25 },  // Motivo
      ];
      ws['!cols'] = wscols;
      
      // Criar workbook e adicionar worksheet
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Percursos");
      
      // Gerar arquivo Excel
      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      
      // Baixar arquivo
      saveAs(data, `percursos_${format(new Date(), 'dd-MM-yyyy')}.xlsx`);
      
      toast.success("Exportação para Excel concluída!");
    } catch (error) {
      toast.error("Erro ao exportar para Excel", {
        description: "Ocorreu um erro durante a exportação. Tente novamente."
      });
      console.error("Erro ao exportar para Excel:", error);
    } finally {
      setIsExporting(false);
    }
  };
  
  // Exportar para PDF
  const exportToPDF = async () => {
    try {
      setIsExporting(true);
      
      // Criar documento PDF
      const doc = new jsPDF('landscape');
      
      // Adicionar título
      doc.setFontSize(18);
      doc.text("Relatório de Percursos", 14, 22);
      
      // Adicionar data
      doc.setFontSize(11);
      doc.text(`Gerado em: ${format(new Date(), 'dd/MM/yyyy HH:mm')}`, 14, 30);
      
      // Preparar dados para a tabela
      const tableColumn = ["ID", "Centro de Custo", "Veículo", "Origem", "Data Inicial", "Odômetro Inicial", "Destino", "Data Final", "Odômetro Final", "Condutor", "Motivo"];
      const tableRows = filteredPercursos.map(percurso => [
        percurso.id,
        percurso.veiculo?.lotacao_veiculos?.centro_custo?.descricao || "N/A",
        percurso.veiculo ? `${percurso.veiculo.matricula || ''} | ${percurso.veiculo.modelo?.descricao || ''}` : "Não informado",
        percurso.origensPercurso?.[0]?.local || "Não especificada",
        percurso.origensPercurso?.[0]?.data ? format(new Date(percurso.origensPercurso[0].data), "dd/MM/yyyy HH:mm") : "-",
        percurso.origensPercurso?.[0]?.odometro ? `${percurso.origensPercurso[0].odometro} km` : "N/A",
        percurso.destinosPercurso?.[0]?.local || "Não especificada",
        percurso.destinosPercurso?.[0]?.data ? format(new Date(percurso.destinosPercurso[0].data), "dd/MM/yyyy HH:mm") : "-",
        percurso.destinosPercurso?.[0]?.odometro ? `${percurso.destinosPercurso[0].odometro} km` : "N/A",
        percurso.condutor?.nome || "Não informado",
        percurso.motivo || "-"
      ]);
      
      // Adicionar tabela ao PDF
      (doc as any).autoTable({
        head: [tableColumn],
        body: tableRows,
        startY: 40,
        styles: {
          fontSize: 8,
          cellPadding: 3,
          lineColor: [44, 62, 80],
          lineWidth: 0.1,
        },
        headStyles: {
          fillColor: [41, 128, 185],
          textColor: 255,
          fontStyle: 'bold',
        },
        alternateRowStyles: {
          fillColor: [245, 245, 245]
        },
      });
      
      // Salvar o PDF
      doc.save(`percursos_${format(new Date(), 'dd-MM-yyyy')}.pdf`);
      
      toast.success("Exportação para PDF concluída!");
    } catch (error) {
      toast.error("Erro ao exportar para PDF", {
        description: "Ocorreu um erro durante a exportação. Tente novamente."
      });
      console.error("Erro ao exportar para PDF:", error);
    } finally {
      setIsExporting(false);
    }
  };
  
  // Função para criar novo percurso
  async function onNewPercurso(values: z.infer<typeof percursoSchema>) {
    const response = await fetch("/api/percurso", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });

    if (!response.ok) {
      toast("Ops, algo deu errado", {
        description: "Houve um erro ao criar o percurso",
      });
      return;
    }

    const data = await response.json();
    setPercursos((prev) => [...prev, data.data]);
    toast.success("Percurso criado com sucesso");
  }
  
  // Renderização condicional para loading
  if (loading && !refreshing) {
    return (
      <div className="flex justify-center items-center h-64 w-full">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        <span className="ml-2 text-lg text-gray-500">Carregando percursos...</span>
      </div>
    );
  }
  
  // Renderização condicional para erro
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64 w-full">
        <p className="text-red-500 text-lg">Erro ao carregar percursos</p>
        <p className="text-sm text-gray-500 mt-2">{error}</p>
        <Button 
          className="mt-4" 
          // onClick={handleRefresh}
        >
          Tentar novamente
        </Button>
      </div>
    );
  }

  return (
    <div className="w-full space-y-4">
      <div className="flex flex-wrap items-center gap-2">
        <div className="relative flex-1 min-w-[200px]">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Pesquisar percursos..."
            className="pl-8 w-full md:w-[300px] lg:w-[400px]"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <Button variant="outline" size="sm" onClick={() => setShowFilters(!showFilters)}>
          <Filter className="h-4 w-4 mr-2" />
          Filtros avançados
        </Button>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <Settings2 className="h-4 w-4 mr-2" />
              Colunas
              <ChevronDown className="ml-1 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[180px]">
            <DropdownMenuLabel>Colunas visíveis</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {percursoColumns.slice(1).map((column, index) => {
              const columnId = (column as any).accessorKey || column.id || `column-${index}`;
              const columnTitle = (column.header as any)?.title || columnId;
              
              return (
                <DropdownMenuCheckboxItem
                  key={index}
                  className="capitalize"
                  checked={columnVisibility[columnId] !== false}
                  onCheckedChange={(value) => {
                    setColumnVisibility(prev => ({
                      ...prev,
                      [columnId]: value
                    }));
                  }}
                >
                  {columnTitle}
                </DropdownMenuCheckboxItem>
              );
            })}
          </DropdownMenuContent>
        </DropdownMenu>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" disabled={isExporting}>
              {isExporting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Exportando...
                </>
              ) : (
                <>
                  <FileText className="h-4 w-4 mr-2" />
                  Exportar
                </>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[160px]">
            <DropdownMenuItem onClick={exportToExcel}>
              <FileText className="h-4 w-4 mr-2" />
              Exportar para Excel
            </DropdownMenuItem>
            <DropdownMenuItem onClick={exportToPDF}>
              <FileText className="h-4 w-4 mr-2" />
              Exportar para PDF
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        
        <Button onClick={handleNovoPercurso}>
          <Plus className="h-4 w-4 mr-2" />
          Novo Percurso
        </Button>
      </div>
      
      {showFilters && (
        <div className="bg-slate-50 border rounded-md p-4 animate-in fade-in-20 slide-in-from-top-1 duration-100">
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Veículo</label>
              <Select value={selectedVeiculo} onValueChange={setSelectedVeiculo}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um veículo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os veículos</SelectItem>
                  {veiculos.map((veiculo) => (
                    <SelectItem key={veiculo.id} value={veiculo.id}>
                      {veiculo.matricula} - {veiculo.modelo?.descricao}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Condutor</label>
              <Select value={selectedCondutor} onValueChange={setSelectedCondutor}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um condutor" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os condutores</SelectItem>
                  {condutores.map((condutor) => (
                    <SelectItem key={condutor.id} value={condutor.id}>
                      {condutor.nome}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Origem</label>
              <Input 
                placeholder="Filtrar por origem"
                value={origemFilter}
                onChange={(e) => setOrigemFilter(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Destino</label>
              <Input 
                placeholder="Filtrar por destino"
                value={destinoFilter}
                onChange={(e) => setDestinoFilter(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Data inicial</label>
              <Input 
                type="date"
                value={dataInicio}
                onChange={(e) => setDataInicio(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Data final</label>
              <Input 
                type="date"
                value={dataFim}
                onChange={(e) => setDataFim(e.target.value)}
              />
            </div>
          </div>
          
          <div className="flex justify-end gap-2 mt-4">
            <Button variant="outline" size="sm" onClick={handleLimparFiltros}>
              Limpar filtros
            </Button>
            {/* <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}>
              {refreshing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Atualizando...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Atualizar
                </>
              )}
            </Button> */}
          </div>
        </div>
      )}
      
      <div className="border rounded-md shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-slate-50 border-b">
                {columnVisibility['id'] !== false && (
                  <th className="h-10 px-4 text-left align-middle font-medium text-xs text-slate-500">
                    ID
                  </th>
                )}
                {columnVisibility['centro_de_custo'] !== false && (
                  <th className="h-10 px-4 text-left align-middle font-medium text-xs text-slate-500">
                    Centro de Custo
                  </th>
                )}
                {columnVisibility['veiculo'] !== false && (
                  <th className="h-10 px-4 text-left align-middle font-medium text-xs text-slate-500">
                    Veículo
                  </th>
                )}
                {columnVisibility['origem'] !== false && (
                  <th className="h-10 px-4 text-left align-middle font-medium text-xs text-slate-500">
                    Origem
                  </th>
                )}
                {columnVisibility['data_inicial'] !== false && (
                  <th className="h-10 px-4 text-left align-middle font-medium text-xs text-slate-500">
                    Data Inicial
                  </th>
                )}
                {columnVisibility['odometro_inicial'] !== false && (
                  <th className="h-10 px-4 text-left align-middle font-medium text-xs text-slate-500">
                    Odômetro Inicial
                  </th>
                )}
                {columnVisibility['destino'] !== false && (
                  <th className="h-10 px-4 text-left align-middle font-medium text-xs text-slate-500">
                    Destino
                  </th>
                )}
                {columnVisibility['data_final'] !== false && (
                  <th className="h-10 px-4 text-left align-middle font-medium text-xs text-slate-500">
                    Data Final
                  </th>
                )}
                {columnVisibility['odometro_final'] !== false && (
                  <th className="h-10 px-4 text-left align-middle font-medium text-xs text-slate-500">
                    Odômetro Final
                  </th>
                )}
                {columnVisibility['condutor'] !== false && (
                  <th className="h-10 px-4 text-left align-middle font-medium text-xs text-slate-500">
                    Condutor
                  </th>
                )}
                {columnVisibility['motivo'] !== false && (
                  <th className="h-10 px-4 text-left align-middle font-medium text-xs text-slate-500">
                    Motivo
                  </th>
                )}
              </tr>
            </thead>
            <tbody>
              {currentItems.length > 0 ? (
                currentItems.map((percurso, index) => (
                  <tr 
                    key={percurso.id || index} 
                    className="border-b hover:bg-slate-50 cursor-pointer" 
                    onClick={() => router.push(`/dashboard/percursos/detalhes/${percurso.id}`)}
                  >
                    {columnVisibility['id'] !== false && (
                      <td className="p-2 px-4 text-sm">{percurso.id}</td>
                    )}
                    {columnVisibility['centro_de_custo'] !== false && (
                      <td className="p-2 px-4 text-sm">{percurso.veiculo?.lotacao_veiculos?.centro_custo?.descricao || "N/A"}</td>
                    )}
                    {columnVisibility['veiculo'] !== false && (
                      <td className="p-2 px-4 text-sm">
                        {percurso.veiculo ? `${percurso.veiculo.matricula || ''} | ${percurso.veiculo.modelo?.descricao || ''}` : "Não informado"}
                      </td>
                    )}
                    {columnVisibility['origem'] !== false && (
                      <td className="p-2 px-4 text-sm">{percurso.origensPercurso?.[0]?.local || "Não especificada"}</td>
                    )}
                    {columnVisibility['data_inicial'] !== false && (
                      <td className="p-2 px-4 text-sm">
                        {percurso.origensPercurso?.[0]?.data 
                          ? format(new Date(percurso.origensPercurso[0].data), "dd/MM/yyyy HH:mm") 
                          : "-"}
                      </td>
                    )}
                    {columnVisibility['odometro_inicial'] !== false && (
                      <td className="p-2 px-4 text-sm">
                        {percurso.origensPercurso?.[0]?.odometro 
                          ? `${percurso.origensPercurso[0].odometro} km` 
                          : "N/A"}
                      </td>
                    )}
                    {columnVisibility['destino'] !== false && (
                      <td className="p-2 px-4 text-sm">{percurso.destinosPercurso?.[0]?.local || "Não especificada"}</td>
                    )}
                    {columnVisibility['data_final'] !== false && (
                      <td className="p-2 px-4 text-sm">
                        {percurso.destinosPercurso?.[0]?.data 
                          ? format(new Date(percurso.destinosPercurso[0].data), "dd/MM/yyyy HH:mm") 
                          : "-"}
                      </td>
                    )}
                    {columnVisibility['odometro_final'] !== false && (
                      <td className="p-2 px-4 text-sm">
                        {percurso.destinosPercurso?.[0]?.odometro 
                          ? `${percurso.destinosPercurso[0].odometro} km` 
                          : "N/A"}
                      </td>
                    )}
                    {columnVisibility['condutor'] !== false && (
                      <td className="p-2 px-4 text-sm">{percurso.condutor?.nome || "Não informado"}</td>
                    )}
                    {columnVisibility['motivo'] !== false && (
                      <td className="p-2 px-4 text-sm">{percurso.motivo || "-"}</td>
                    )}
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={Object.values(columnVisibility).filter(Boolean).length} className="p-4 text-center text-sm text-slate-500">
                    Nenhum percurso encontrado.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
      
      <div className="flex flex-col-reverse md:flex-row justify-between items-center gap-4">
        <div className="text-sm text-slate-500">
          Exibindo {filteredPercursos.length > 0 ? indexOfFirstItem + 1 : 0} a {Math.min(indexOfLastItem, filteredPercursos.length)} 
          de {filteredPercursos.length} {filteredPercursos.length === 1 ? 'percurso' : 'percursos'}
        </div>
        
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="h-8 w-8 p-0"
            onClick={() => goToPage(1)} 
            disabled={currentPage === 1}
          >
            <span className="sr-only">Primeira página</span>
            <ChevronDown className="h-4 w-4 rotate-90" />
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            className="h-8 w-8 p-0"
            onClick={() => goToPage(currentPage - 1)} 
            disabled={currentPage === 1}
          >
            <span className="sr-only">Página anterior</span>
            <ChevronDown className="h-4 w-4 -rotate-90" />
          </Button>
          
          <span className="text-sm font-medium">
            Página {currentPage} de {totalPages || 1}
          </span>
          
          <Button 
            variant="outline" 
            size="sm" 
            className="h-8 w-8 p-0"
            onClick={() => goToPage(currentPage + 1)} 
            disabled={currentPage === totalPages || totalPages === 0}
          >
            <span className="sr-only">Próxima página</span>
            <ChevronDown className="h-4 w-4 rotate-90" />
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            className="h-8 w-8 p-0"
            onClick={() => goToPage(totalPages)} 
            disabled={currentPage === totalPages || totalPages === 0}
          >
            <span className="sr-only">Última página</span>
            <ChevronDown className="h-4 w-4 -rotate-90" />
          </Button>
          
          <Select
            value={itemsPerPage.toString()}
            onValueChange={(value) => {
              setItemsPerPage(Number(value));
              setCurrentPage(1);
            }}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder="10" />
            </SelectTrigger>
            <SelectContent side="top">
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}
