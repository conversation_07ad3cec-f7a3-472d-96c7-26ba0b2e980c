"use client";

import { percursoSchema } from "@/components/forms/schemas";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useCondutor } from "@/context/condutor-context";
import { usePercurso } from "@/context/percurso-context";
import { useVeiculos } from "@/context/veiculos-context";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter, useSearchParams } from "next/navigation";
import { useState, useRef, useCallback } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { FileIcon, Search, Trash2, Upload, X } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import { Progress } from "@/components/ui/progress";

// Interface para os arquivos anexados
interface AttachedFile {
  file: File;
  id: string;
  progress: number;
  uploaded: boolean;
  preview?: string;
}

export function PercursoForm() {
  const router = useRouter();
  const { veiculos } = useVeiculos();
  const { condutores } = useCondutor();
  const { setPercursos } = usePercurso();
  const [isUploading, setIsUploading] = useState(false);
  const [openVeiculoCombobox, setOpenVeiculoCombobox] = useState(false);
  const [openCondutorCombobox, setOpenCondutorCombobox] = useState(false);
  const [attachedFiles, setAttachedFiles] = useState<AttachedFile[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const searchParams = useSearchParams();

  const placeOrigin = searchParams.get("placeOrigin");
  const destinationLocation = searchParams.get("destinationLocation");

  const form = useForm<z.infer<typeof percursoSchema>>({
    resolver: zodResolver(percursoSchema),
    defaultValues: {
      veiculoId: "",
      condutorId: "",
      motivo: "",
      origem: {
        local: placeOrigin ?? "",
        odometro: 0,
      },
      destino: {
        local: destinationLocation ?? "",
        odometro: 0,
      },
      observacoes: "",
      arquivos: [],
    },
  });

  const undoPercurso = async (id: string) => {
    const res = await fetch(`/api/percurso/${id}`, {
      method: "DELETE",
    });

    if (!res.ok) {
      toast("Erro ao reverter", {
        description: "Falha ao cancelar o percurso",
      });
      return;
    }

    setPercursos((prev) => prev.filter((percurso) => percurso.id !== id));
    toast("Percurso removido", {
      description: "O registro foi cancelado com sucesso",
    });
  };

  const onSubmit = async (values: z.infer<typeof percursoSchema>) => {
    try {
      // Simular upload dos arquivos antes de enviar o formulário
      if (attachedFiles.length > 0) {
        setIsUploading(true);

        // Em uma aplicação real, aqui você faria upload dos arquivos para um servidor
        // e depois incluiria os links/IDs dos arquivos no formulário

        // Simulação de upload com progresso
        for (let i = 0; i < attachedFiles.length; i++) {
          if (!attachedFiles[i].uploaded) {
            for (let progress = 0; progress <= 100; progress += 10) {
              setAttachedFiles((prev) =>
                prev.map((file, index) =>
                  index === i ? { ...file, progress } : file
                )
              );
              await new Promise((resolve) => setTimeout(resolve, 100));
            }

            setAttachedFiles((prev) =>
              prev.map((file, index) =>
                index === i ? { ...file, uploaded: true } : file
              )
            );
          }
        }

        // Aqui você estaria obtendo URLs ou IDs dos arquivos do servidor
        // e os incluiria no payload do formulário
        const fileIds = attachedFiles.map((file) => file.id);
        values.arquivos = fileIds;

        setIsUploading(false);
      }

      const res = await fetch("/api/percurso", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      if (!res.ok) {
        toast("Erro no registro", {
          description: "Falha ao registrar o percurso",
        });
        return;
      }

      const data = await res.json();
      setPercursos((prev) => [...prev, data.data]);

      toast("Percurso registrado!", {
        description: "Registro realizado com sucesso",
        action: (
          <Button
            variant="outline"
            onClick={async () => await undoPercurso(data.data.id)}
          >
            Desfazer
          </Button>
        ),
      });

      // Redirecionar de volta para a lista de percursos
      router.push("/dashboard/percursos");
    } catch (error) {
      console.error("Erro ao enviar formulário:", error);
      setIsUploading(false);
      toast("Erro no registro", {
        description: "Ocorreu um erro ao processar sua solicitação",
      });
    }
  };

  // Função para processar os arquivos selecionados
  const processFiles = useCallback((files: FileList | null) => {
    if (!files || files.length === 0) return;

    console.log("Processando arquivos:", files);

    const fileArray = Array.from(files);
    const newAttachedFiles: AttachedFile[] = fileArray.map((file) => {
      // Gerar ID único para cada arquivo
      const fileId = Math.random().toString(36).substring(2, 15);

      // Criar preview para imagens
      let preview = undefined;
      if (file.type.startsWith("image/")) {
        preview = URL.createObjectURL(file);
      }

      return {
        file,
        id: fileId,
        progress: 0,
        uploaded: false,
        preview,
      };
    });

    setAttachedFiles((prev) => [...prev, ...newAttachedFiles]);

    toast("Arquivos adicionados", {
      description: `${fileArray.length} arquivo(s) adicionado(s) à lista.`,
    });
  }, []);

  // Manipulador para o input de arquivo
  const handleAttachFiles = (event: React.ChangeEvent<HTMLInputElement>) => {
    console.log("Evento de mudança de arquivo acionado");
    processFiles(event.target.files);

    // Limpar input de arquivo para permitir selecionar os mesmos arquivos novamente
    if (event.target.value) {
      event.target.value = "";
    }
  };

  // Manipulador para o botão de anexar
  const handleAttachButton = () => {
    console.log("Botão de anexar clicado");
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Manipuladores para drag and drop
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    e.dataTransfer.dropEffect = "copy";
    setIsDragging(true);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    console.log("Arquivos recebidos por drag and drop");
    processFiles(e.dataTransfer.files);
  };

  const removeFile = (id: string) => {
    setAttachedFiles((prev) => {
      const fileToRemove = prev.find((file) => file.id === id);

      // Limpar URL de preview, se existir
      if (fileToRemove?.preview) {
        URL.revokeObjectURL(fileToRemove.preview);
      }

      return prev.filter((file) => file.id !== id);
    });

    toast("Arquivo removido", {
      description: "O arquivo foi removido da lista.",
    });
  };

  // Função para obter ícone correspondente ao tipo de arquivo
  const getFileIcon = (file: File) => {
    if (file.type.startsWith("image/")) return "Imagem";
    if (file.type === "application/pdf") return "PDF";
    if (file.type.includes("spreadsheet") || file.type.includes("excel"))
      return "Excel";
    if (file.type.includes("document") || file.type.includes("word"))
      return "Word";
    return "Arquivo";
  };

  // Função para formatar o tamanho do arquivo
  const formatFileSize = (size: number) => {
    if (size < 1024) return `${size} bytes`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
    return `${(size / (1024 * 1024)).toFixed(1)} MB`;
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="space-y-6">
          {/* Veículo */}
          <div className="space-y-2">
            <Label htmlFor="veiculoId">Veículo</Label>
            <Popover
              open={openVeiculoCombobox}
              onOpenChange={setOpenVeiculoCombobox}
            >
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={openVeiculoCombobox}
                  className="w-full justify-between text-muted-foreground text-left font-normal"
                >
                  {form.watch("veiculoId") &&
                  veiculos.find(
                    (veiculo) => veiculo.id === form.watch("veiculoId")
                  )
                    ? `${
                        veiculos.find(
                          (veiculo) => veiculo.id === form.watch("veiculoId")
                        )?.matricula || ""
                      } | ${
                        veiculos.find(
                          (veiculo) => veiculo.id === form.watch("veiculoId")
                        )?.modelo?.descricao || ""
                      }`
                    : "Pesquise pela placa, versão, marca ou modelo"}
                  <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-full p-0">
                <div className="rounded-lg border shadow-md">
                  <div className="flex items-center border-b px-3">
                    <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                    <input
                      className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                      placeholder="Pesquise pelo veículo..."
                    />
                  </div>
                  <div className="max-h-60 overflow-y-auto">
                    {veiculos.length === 0 ? (
                      <div className="py-6 text-center text-sm">Nenhum veículo encontrado.</div>
                    ) : (
                      <div className="overflow-hidden p-1 text-foreground">
                        {veiculos.map((veiculo) => (
                          <div
                            key={veiculo.id}
                            className="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground"
                            onClick={() => {
                              form.setValue("veiculoId", veiculo.id);
                              setOpenVeiculoCombobox(false);
                            }}
                          >
                            {veiculo.matricula} | {veiculo.modelo?.descricao}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </PopoverContent>
            </Popover>
            {form.formState.errors.veiculoId && (
              <p className="text-sm text-red-500">
                {form.formState.errors.veiculoId.message}
              </p>
            )}
          </div>

          {/* Condutor */}
          <div className="space-y-2">
            <Label htmlFor="condutorId">Condutor</Label>
            <Popover
              open={openCondutorCombobox}
              onOpenChange={setOpenCondutorCombobox}
            >
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={openCondutorCombobox}
                  className="w-full justify-between text-muted-foreground text-left font-normal"
                >
                  {form.watch("condutorId") &&
                  condutores.find(
                    (condutor) => condutor.id === form.watch("condutorId")
                  )
                    ? condutores.find(
                        (condutor) => condutor.id === form.watch("condutorId")
                      )?.nome
                    : "Pesquise pelo nome, matrícula ou CNH"}
                  <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-full p-0">
                <div className="rounded-lg border shadow-md">
                  <div className="flex items-center border-b px-3">
                    <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                    <input
                      className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                      placeholder="Pesquise pelo condutor..."
                    />
                  </div>
                  <div className="max-h-60 overflow-y-auto">
                    {condutores.length === 0 ? (
                      <div className="py-6 text-center text-sm">Nenhum condutor encontrado.</div>
                    ) : (
                      <div className="overflow-hidden p-1 text-foreground">
                        {condutores.map((condutor) => (
                          <div
                            key={condutor.id}
                            className="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground"
                            onClick={() => {
                              form.setValue("condutorId", condutor.id);
                              setOpenCondutorCombobox(false);
                            }}
                          >
                            {condutor.nome}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </PopoverContent>
            </Popover>
            {form.formState.errors.condutorId && (
              <p className="text-sm text-red-500">
                {form.formState.errors.condutorId.message}
              </p>
            )}
          </div>

          {/* Motivo */}
          <div className="space-y-2">
            <Label htmlFor="motivo">Motivo</Label>
            <Textarea
              id="motivo"
              placeholder="Viagem a trabalho"
              {...form.register("motivo")}
              className="min-h-24"
            />
            {form.formState.errors.motivo && (
              <p className="text-sm text-red-500">
                {form.formState.errors.motivo.message}
              </p>
            )}
          </div>

          {/* Origem */}
          <div className="space-y-4 border rounded-md p-4">
            <h3 className="font-medium text-sm">Origem</h3>

            {/* Local de origem */}
            <div className="space-y-2">
              <Label htmlFor="origem.local">Local de origem</Label>
              <Input
                id="origem.local"
                placeholder="Rua Sem Saída - 130, Centro, Curitiba/PR"
                {...form.register("origem.local")}
              />
              {form.formState.errors.origem?.local && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.origem.local.message}
                </p>
              )}
            </div>

            {/* Data de partida */}
            <div className="space-y-2">
              <Label htmlFor="origem.data">Data de partida</Label>
              <Input
                id="origem.data"
                type="datetime-local"
                className="w-full"
                {...form.register("origem.data", {
                  setValueAs: (v) => (v ? new Date(v) : undefined),
                })}
              />
              {form.formState.errors.origem?.data && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.origem.data.message}
                </p>
              )}
            </div>

            {/* Odômetro na partida */}
            <div className="space-y-2">
              <Label htmlFor="origem.odometro">Odômetro na partida</Label>
              <Input
                id="origem.odometro"
                type="number"
                placeholder="123456789 km"
                {...form.register("origem.odometro", {
                  valueAsNumber: true,
                })}
              />
              {form.formState.errors.origem?.odometro && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.origem.odometro.message}
                </p>
              )}
            </div>
          </div>
        </div>

        <div className="space-y-6">
          {/* Destino */}
          <div className="space-y-4 border rounded-md p-4">
            <h3 className="font-medium text-sm">Destino</h3>

            {/* Local de destino */}
            <div className="space-y-2">
              <Label htmlFor="destino.local">Local de destino</Label>
              <Input
                id="destino.local"
                placeholder="Praça Tiradentes, Centro, Curitiba/PR"
                {...form.register("destino.local")}
              />
              {form.formState.errors.destino?.local && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.destino.local.message}
                </p>
              )}
            </div>

            {/* Data de chegada */}
            <div className="space-y-2">
              <Label htmlFor="destino.data">Data de chegada</Label>
              <Input
                id="destino.data"
                type="datetime-local"
                className="w-full"
                {...form.register("destino.data", {
                  setValueAs: (v) => (v ? new Date(v) : undefined),
                })}
              />
              {form.formState.errors.destino?.data && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.destino.data.message}
                </p>
              )}
            </div>

            {/* Odômetro na chegada */}
            <div className="space-y-2">
              <Label htmlFor="destino.odometro">Odômetro na chegada</Label>
              <Input
                id="destino.odometro"
                type="number"
                placeholder="123456789 km"
                {...form.register("destino.odometro", {
                  valueAsNumber: true,
                })}
              />
              {form.formState.errors.destino?.odometro && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.destino.odometro.message}
                </p>
              )}
            </div>
          </div>

          {/* Arquivos */}
          <div className="space-y-4 border rounded-md p-4">
            <h3 className="font-medium text-sm">Arquivos</h3>

            {/* Input de arquivo escondido */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*,.pdf,.doc,.docx,.xls,.xlsx"
              multiple
              className="hidden"
              onChange={handleAttachFiles}
            />

            {/* Área para arrastar e soltar arquivos */}
            <div
              className={`flex items-center justify-center bg-gray-50 p-4 rounded-md border-2 border-dashed ${
                isDragging ? "border-blue-400 bg-blue-50" : "border-gray-200"
              }`}
              onDragEnter={handleDragEnter}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={handleAttachButton}
            >
              <div className="cursor-pointer text-center">
                <div className="flex flex-col items-center gap-2">
                  <Upload
                    className={`h-6 w-6 ${
                      isDragging ? "text-blue-500" : "text-gray-400"
                    }`}
                  />
                  <span className="text-sm text-gray-500">
                    {isDragging
                      ? "Solte os arquivos aqui"
                      : "Arraste arquivos ou clique para selecionar"}
                  </span>
                  <Button
                    type="button"
                    variant="secondary"
                    size="sm"
                    className="mt-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAttachButton();
                    }}
                  >
                    <FileIcon className="h-4 w-4 mr-2" />
                    Anexar arquivos
                  </Button>
                </div>
              </div>
            </div>

            {/* Lista de arquivos anexados */}
            {attachedFiles.length > 0 && (
              <div className="mt-4 space-y-3">
                <h4 className="text-sm font-medium">
                  Arquivos anexados ({attachedFiles.length})
                </h4>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {attachedFiles.map((attachedFile) => (
                    <div
                      key={attachedFile.id}
                      className="flex items-start gap-3 p-2 bg-gray-50 rounded-md"
                    >
                      {/* Preview para imagens */}
                      {attachedFile.preview ? (
                        <div className="h-10 w-10 rounded-md overflow-hidden flex-shrink-0">
                          <img
                            src={attachedFile.preview}
                            alt={attachedFile.file.name}
                            className="h-full w-full object-cover"
                          />
                        </div>
                      ) : (
                        <FileIcon className="h-10 w-10 text-blue-500 flex-shrink-0" />
                      )}

                      {/* Informações do arquivo */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium truncate">
                            {attachedFile.file.name}
                          </p>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 text-gray-500 hover:text-red-500"
                            onClick={(e) => {
                              e.stopPropagation();
                              removeFile(attachedFile.id);
                            }}
                            disabled={isUploading}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                        <p className="text-xs text-gray-500">
                          {getFileIcon(attachedFile.file)} •{" "}
                          {formatFileSize(attachedFile.file.size)}
                        </p>

                        {/* Barra de progresso */}
                        {(isUploading || attachedFile.progress > 0) && (
                          <div className="mt-1">
                            <Progress
                              value={attachedFile.progress}
                              className="h-1"
                            />
                            <p className="text-xs text-gray-500 mt-1">
                              {attachedFile.uploaded
                                ? "Enviado com sucesso"
                                : `Enviando... ${attachedFile.progress}%`}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Observações */}
          <div className="space-y-2">
            <Label htmlFor="observacoes">Observações</Label>
            <Textarea
              id="observacoes"
              placeholder="Digite alguma observação..."
              {...form.register("observacoes")}
              className="min-h-24"
            />
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <Button
          type="submit"
          className="bg-emerald-500 hover:bg-emerald-600 text-white"
          disabled={isUploading}
        >
          {isUploading ? (
            <>
              <Upload className="mr-2 h-4 w-4 animate-pulse" />
              Enviando arquivos...
            </>
          ) : (
            "Cadastrar percurso"
          )}
        </Button>
      </div>
    </form>
  );
}
