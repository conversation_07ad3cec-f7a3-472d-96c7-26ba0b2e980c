"use client";
import { useMemo, useRef, useState } from "react";
import { createPortal } from "react-dom";

import {
  DndContext,
  type DragEndEvent,
  type DragOverEvent,
  DragOverlay,
  type DragStartEvent,
  useSensor,
  useSensors,
  KeyboardSensor,
  Announcements,
  UniqueIdentifier,
  TouchSensor,
  MouseSensor,
} from "@dnd-kit/core";
import { SortableContext, arrayMove } from "@dnd-kit/sortable";
import { type Service, ServiceCard } from "../cards/service-card";

import { hasDraggableData } from "../utils";
import { coordinateGetter } from "../ui/multipleContainersKeyboardPreset";
import { BoardColumn, BoardContainer, Column } from "../ui/board-colum";

const defaultCols = [
  {
    id: "availible" as const,
    title: "Serviços disponíveis",
  },
  {
    id: "choosed" as const,
    title: "Serviços selecionados",
  },
] satisfies Column[];

export type ColumnId = (typeof defaultCols)[number]["id"] | null;

type ServiceBoardType = {
  initialServices: Service[];
  handleServices: (services: Service[]) => void;
};
export function ServiceBoard({
  initialServices,
  handleServices,
}: ServiceBoardType) {
  const [columns, setColumns] = useState<Column[]>(defaultCols);
  const pickedUpServiceColumn = useRef<ColumnId | null>(null);
  const columnsId = useMemo(() => columns.map((col) => col.id), [columns]);

  const [services, setServices] = useState<Service[]>(initialServices);

  const [activeColumn, setActiveColumn] = useState<Column | null>(null);

  const [activeService, setActiveService] = useState<Service | null>(null);

  const sensors = useSensors(
    useSensor(MouseSensor),
    useSensor(TouchSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: coordinateGetter,
    })
  );

  function getDraggingServiceData(
    serviceId: UniqueIdentifier,
    columnId: ColumnId
  ) {
    const servicesInColumn = services.filter(
      (service) => service.columnId === columnId
    );
    const servicePosition = servicesInColumn.findIndex(
      (service) => service.id === serviceId
    );
    const column = columns.find((col) => col.id === columnId);
    return {
      servicesInColumn,
      servicePosition,
      column,
    };
  }

  const announcements: Announcements = {
    onDragStart({ active }) {
      if (!hasDraggableData(active)) return;
      if (active.data.current?.type === "Column") {
        const startColumnIdx = columnsId.findIndex((id) => id === active.id);
        const startColumn = columns[startColumnIdx];
        return `Picked up Column ${startColumn?.title} at position: ${
          startColumnIdx + 1
        } of ${columnsId.length}`;
      } else if (active.data.current?.type === "Service") {
        pickedUpServiceColumn.current = active.data.current.service.columnId;
        const { servicesInColumn, servicePosition, column } =
          getDraggingServiceData(active.id, pickedUpServiceColumn.current);
        return `Picked up Service ${
          active.data.current.service.service
        } at position: ${servicePosition + 1} of ${
          servicesInColumn.length
        } in column ${column?.title}`;
      }
    },
    onDragOver({ active, over }) {
      if (!hasDraggableData(active) || !hasDraggableData(over)) return;

      if (
        active.data.current?.type === "Column" &&
        over.data.current?.type === "Column"
      ) {
        const overColumnIdx = columnsId.findIndex((id) => id === over.id);
        return `Column ${active.data.current.column.title} was moved over ${
          over.data.current.column.title
        } at position ${overColumnIdx + 1} of ${columnsId.length}`;
      } else if (
        active.data.current?.type === "Service" &&
        over.data.current?.type === "Service"
      ) {
        const { servicesInColumn, servicePosition, column } =
          getDraggingServiceData(over.id, over.data.current.service.columnId);
        if (
          over.data.current.service.columnId !== pickedUpServiceColumn.current
        ) {
          return `Service ${
            active.data.current.service.service
          } was moved over column ${column?.title} in position ${
            servicePosition + 1
          } of ${servicesInColumn.length}`;
        }
        return `Service was moved over position ${servicePosition + 1} of ${
          servicesInColumn.length
        } in column ${column?.title}`;
      }
    },
    onDragEnd({ active, over }) {
      if (!hasDraggableData(active) || !hasDraggableData(over)) {
        pickedUpServiceColumn.current = null;
        return;
      }
      handleServices(services);
      if (
        active.data.current?.type === "Column" &&
        over.data.current?.type === "Column"
      ) {
        const overColumnPosition = columnsId.findIndex((id) => id === over.id);

        return `Column ${
          active.data.current.column.title
        } was dropped into position ${overColumnPosition + 1} of ${
          columnsId.length
        }`;
      } else if (
        active.data.current?.type === "Service" &&
        over.data.current?.type === "Service"
      ) {
        const { servicePosition, servicesInColumn, column } =
          getDraggingServiceData(over.id, over.data.current.service.columnId);
        if (
          over.data.current.service.columnId !== pickedUpServiceColumn.current
        ) {
          return `Service was dropped into column ${
            column?.title
          } in position ${servicePosition + 1} of ${servicesInColumn.length}`;
        }
        return `Service was dropped into position ${servicePosition + 1} of ${
          servicesInColumn.length
        } in column ${column?.title}`;
      }
      pickedUpServiceColumn.current = null;
    },
    onDragCancel({ active }) {
      pickedUpServiceColumn.current = null;
      if (!hasDraggableData(active)) return;
      return `Dragging ${active.data.current?.type} cancelled.`;
    },
  };

  return (
    <DndContext
      accessibility={{
        announcements,
      }}
      sensors={sensors}
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}
      onDragOver={onDragOver}
    >
      <BoardContainer>
        <SortableContext items={columnsId}>
          {columns.map((col) => (
            <BoardColumn
              key={col.id}
              column={col}
              services={services.filter(
                (service) => service.columnId === col.id
              )}
            />
          ))}
        </SortableContext>
      </BoardContainer>

      {"document" in window &&
        createPortal(
          <DragOverlay>
            {activeColumn && (
              <BoardColumn
                isOverlay
                column={activeColumn}
                services={services.filter(
                  (service) => service.columnId === activeColumn.id
                )}
              />
            )}
            {activeService && <ServiceCard service={activeService} isOverlay />}
          </DragOverlay>,
          document.body
        )}
    </DndContext>
  );

  function onDragStart(event: DragStartEvent) {
    if (!hasDraggableData(event.active)) return;
    const data = event.active.data.current;
    if (data?.type === "Column") {
      setActiveColumn(data.column);
      return;
    }

    if (data?.type === "Service") {
      setActiveService(data.service);
      return;
    }
  }

  function onDragEnd(event: DragEndEvent) {
    setActiveColumn(null);
    setActiveService(null);

    const { active, over } = event;
    if (!over) return;

    const activeId = active.id;
    const overId = over.id;

    if (!hasDraggableData(active)) return;

    const activeData = active.data.current;

    if (activeId === overId) return;

    const isActiveAColumn = activeData?.type === "Column";
    if (!isActiveAColumn) return;

    setColumns((columns) => {
      const activeColumnIndex = columns.findIndex((col) => col.id === activeId);

      const overColumnIndex = columns.findIndex((col) => col.id === overId);

      return arrayMove(columns, activeColumnIndex, overColumnIndex);
    });
  }

  function onDragOver(event: DragOverEvent) {
    const { active, over } = event;
    if (!over) return;

    const activeId = active.id;
    const overId = over.id;

    if (activeId === overId) return;

    if (!hasDraggableData(active) || !hasDraggableData(over)) return;

    const activeData = active.data.current;
    const overData = over.data.current;

    const isActiveAService = activeData?.type === "Service";
    const isOverAService = overData?.type === "Service";

    if (!isActiveAService) return;

    if (isActiveAService && isOverAService) {
      setServices((services) => {
        const activeIndex = services.findIndex((s) => s.id === activeId);
        const overIndex = services.findIndex((s) => s.id === overId);
        const activeService = services[activeIndex];
        const overService = services[overIndex];
        if (
          activeService &&
          overService &&
          activeService.columnId !== overService.columnId
        ) {
          activeService.columnId = overService.columnId;
          return arrayMove(services, activeIndex, overIndex - 1);
        }

        return arrayMove(services, activeIndex, overIndex);
      });
    }

    const isOverAColumn = overData?.type === "Column";

    if (isActiveAService && isOverAColumn) {
      setServices((services) => {
        const activeIndex = services.findIndex((t) => t.id === activeId);
        const activeService = services[activeIndex];
        if (activeService) {
          activeService.columnId = overId as ColumnId;
          return arrayMove(services, activeIndex, activeIndex);
        }
        return services;
      });
    }
  }
}
export const transformServices = (servicosBackend: servico_credenciado[]): Service[] => {
  return servicosBackend.map((serv)=>({
    id:serv.id,
    columnId:'availible',
    service:serv.descricao
  }))
};