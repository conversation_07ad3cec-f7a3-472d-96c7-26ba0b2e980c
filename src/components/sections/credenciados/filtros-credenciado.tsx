import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { X } from "lucide-react";

interface FilterCredenciadoProps {
  showFilterDialog: boolean;
  setShowFilterDialog: (show: boolean) => void;
  filters: {
    atividade_principal: string;
    cnpj: string;
    razao_social: string;
    nome_fantasia: string;
    email: string;
    telefone: string;
    celular: string;
    cidade: string;
    estado: string;
    ativo: boolean;
    orcamentista: boolean;
    optante: boolean;
    placa_verde: boolean;
    concessionaria: boolean;
    taxa_adm: number;
  };
  handleFilterChange: (field: string, value: any) => void;
  clearFilters: () => void;
  applyFilters: () => void;
}

const FilterCredenciado = ({
  showFilterDialog,
  setShowFilterDialog,
  filters,
  handleFilterChange,
  clearFilters,
  applyFilters,
}: FilterCredenciadoProps) => {
  return (
    <Dialog open={showFilterDialog} onOpenChange={setShowFilterDialog}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex justify-between items-center">
            <span>Pesquisar registros</span>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowFilterDialog(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 gap-4 pt-2">
          <div className="grid gap-2">
            <Label htmlFor="atividade_principal">Atividade principal</Label>
            <Input
              id="atividade_principal"
              value={filters.atividade_principal}
              onChange={(e) =>
                handleFilterChange("atividade_principal", e.target.value)
              }
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="cnpj">CNPJ</Label>
            <Input
              id="cnpj"
              value={filters.cnpj}
              onChange={(e) => handleFilterChange("cnpj", e.target.value)}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="razao_social">Razão social</Label>
            <Input
              id="razao_social"
              value={filters.razao_social}
              onChange={(e) =>
                handleFilterChange("razao_social", e.target.value)
              }
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="razao_social">Taxa Administrativa (%)</Label>
            <Input
              id="taxa_adm"
              type="number"
              value={filters.taxa_adm}
              onChange={(e) => handleFilterChange("taxa_adm", e.target.value)}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="nome_fantasia">Nome fantasia</Label>
            <Input
              id="nome_fantasia"
              value={filters.nome_fantasia}
              onChange={(e) =>
                handleFilterChange("nome_fantasia", e.target.value)
              }
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              value={filters.email}
              onChange={(e) => handleFilterChange("email", e.target.value)}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="telefone">Telefone</Label>
            <Input
              id="telefone"
              value={filters.telefone}
              onChange={(e) => handleFilterChange("telefone", e.target.value)}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="celular">Celular</Label>
            <Input
              id="celular"
              value={filters.celular}
              onChange={(e) => handleFilterChange("celular", e.target.value)}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="cidade">Cidade</Label>
            <Input
              id="cidade"
              value={filters.cidade}
              onChange={(e) => handleFilterChange("cidade", e.target.value)}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="estado">UF</Label>
            <Select
              value={filters.estado}
              onValueChange={(value) => handleFilterChange("estado", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione um estado" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="AC">AC</SelectItem>
                <SelectItem value="AL">AL</SelectItem>
                <SelectItem value="AP">AP</SelectItem>
                <SelectItem value="AM">AM</SelectItem>
                <SelectItem value="BA">BA</SelectItem>
                <SelectItem value="CE">CE</SelectItem>
                <SelectItem value="DF">DF</SelectItem>
                <SelectItem value="ES">ES</SelectItem>
                <SelectItem value="GO">GO</SelectItem>
                <SelectItem value="MA">MA</SelectItem>
                <SelectItem value="MT">MT</SelectItem>
                <SelectItem value="MS">MS</SelectItem>
                <SelectItem value="MG">MG</SelectItem>
                <SelectItem value="PA">PA</SelectItem>
                <SelectItem value="PB">PB</SelectItem>
                <SelectItem value="PR">PR</SelectItem>
                <SelectItem value="PE">PE</SelectItem>
                <SelectItem value="PI">PI</SelectItem>
                <SelectItem value="RJ">RJ</SelectItem>
                <SelectItem value="RN">RN</SelectItem>
                <SelectItem value="RS">RS</SelectItem>
                <SelectItem value="RO">RO</SelectItem>
                <SelectItem value="RR">RR</SelectItem>
                <SelectItem value="SC">SC</SelectItem>
                <SelectItem value="SP">SP</SelectItem>
                <SelectItem value="SE">SE</SelectItem>
                <SelectItem value="TO">TO</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex gap-4 my-4">
            <div className="flex gap-2">
              <Label>Ativo</Label>
              <Checkbox
                id="ativo"
                checked={filters.ativo}
                onCheckedChange={(checked) =>
                  handleFilterChange("ativo", checked)
                }
              />
            </div>
            <div className="flex gap-2">
              <Label>Orçamentista</Label>
              <Checkbox
                id="orcamentista"
                checked={filters.orcamentista}
                onCheckedChange={(checked) =>
                  handleFilterChange("orcamentista", checked)
                }
              />
            </div>
            <div className="flex gap-2">
              <Label>Optante Simples</Label>
              <Checkbox
                id="optante"
                checked={filters.optante}
                onCheckedChange={(checked) =>
                  handleFilterChange("optante", checked)
                }
              />
            </div>
          </div>

          <div className="flex gap-4 my-4">
            <div className="flex gap-2">
              <Label>Placa verde</Label>
              <Checkbox
                id="placa_verde"
                checked={filters.placa_verde}
                onCheckedChange={(checked) =>
                  handleFilterChange("placa_verde", checked)
                }
              />
            </div>
            <div className="flex gap-2">
              <Label>Concessionária</Label>
              <Checkbox
                id="concessionaria"
                checked={filters.concessionaria}
                onCheckedChange={(checked) =>
                  handleFilterChange("concessionaria", checked)
                }
              />
            </div>
          </div>
        </div>

        <DialogFooter className="flex justify-between sm:justify-between">
          <Button type="button" variant="outline" onClick={clearFilters}>
            Limpar
          </Button>
          <Button type="button" onClick={applyFilters}>
            Aplicar filtros
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default FilterCredenciado;
