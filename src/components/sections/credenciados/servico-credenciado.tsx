"use client";

import { ServicoCredenciadoConfig } from "@/components/forms/inputConfig";
import { servicoCredenciadoSchema } from "@/components/forms/schemas";

import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { Checkbox } from "@/components/ui/checkbox";
import { useServicoCredenciado } from "@/context/servico-credenciado-context";
import { ColumnDef } from "@tanstack/react-table";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { z } from "zod";

export const servicoCredenciadoColumn: ColumnDef<servico_credenciado>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "descricao",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Descrição" />
    ),
  },
];

export function ServicoCredenciadoTable() {
  const router = useRouter()
  const { servicosCredenciados, setServicosCredenciados } =
    useServicoCredenciado();

  async function onNewServicoCredenciado(
    values: z.infer<typeof servicoCredenciadoSchema>
  ) {
    const response = await fetch("/api/servico-credenciado", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });

    if (!response.ok) {
      toast("Ops, algo deu errado", {
        description: "Houve um erro ao criar o serviço credenciado",
      });
      return;
    }

    const data = await response.json();
    setServicosCredenciados((prev) => [...prev, data.data]);
   window.location.href = "/dashboard/rede-credenciada/servicos-oferecidos";
  }

  return (
    <DataTable
      data={servicosCredenciados}
      newItem={{
        defaultValues: {
          description: "",
        },
        fieldConfig: ServicoCredenciadoConfig,
        schema: servicoCredenciadoSchema,
      }}
      onNewItem={onNewServicoCredenciado}
      exportTo={true}
      columns={servicoCredenciadoColumn}
    />
  );
}
