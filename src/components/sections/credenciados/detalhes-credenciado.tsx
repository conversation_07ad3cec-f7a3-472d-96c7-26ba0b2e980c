"use client";
import React, { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Avatar } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import {
  MapPin,
  Phone,
  Mail,
  Building,
  FileText,
  Calendar,
  Clock,
  User,
  AlertTriangle,
} from "lucide-react";
import { useCredenciado } from "@/context/credenciado-context";
import { formatCNPJ, formatPhoneNumber, formatDate } from "@/lib/utils";
import { toast } from "sonner";
import {
  desvincularCredenciadoContrato,
  getCredenciadoById,
} from "@/serverActions/credenciadoAction";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";

interface DetalheCredenciadoProps {
  id?: string;
}

const DetalheCredenciado = ({ id }: DetalheCredenciadoProps) => {
  const [credenciado, setCredenciado] = useState<credenciado>();
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [contratoParaDesvincular, setContratoParaDesvincular] =
    useState<any>(null);
  const [isDesvinculating, setIsDesvinculating] = useState(false);
  useEffect(() => {
    const fetchCredenciado = async () => {
      console.log("Fetching credenciado with ID:", id);
      const res = await getCredenciadoById(id as string);
      if (res.success) {
        setCredenciado(res.data.credenciado);
      }
    };
    fetchCredenciado();
  }, [id]);

  const info = credenciado?.informacoes[0] as informacoes_do_credenciado;
  const estrutura =
    credenciado?.estrutura_credenciado as estrutura_do_credenciado;
  const endereco = credenciado?.endereco as endereco_do_credenciado;
  const contato = credenciado?.contato as contatos_do_credenciado;
  // Função para renderizar imagens
  const renderImages = (images: unknown) => {
    // Garantir que images é um array
    const imageArray = Array.isArray(images) ? images : [];

    if (imageArray.length === 0) {
      return (
        <p className="text-sm text-muted-foreground italic">
          Nenhuma imagem disponível
        </p>
      );
    }

    return (
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {imageArray.map((image, index) => (
          <div
            key={index}
            className="relative aspect-square rounded-md overflow-hidden border"
          >
            <img
              src={image}
              alt={`Imagem ${index + 1}`}
              className="w-full h-full object-cover"
            />
          </div>
        ))}
      </div>
    );
  };

  const getImages = () => {
    try {
      if (!estrutura?.images) return [];

      if (Array.isArray(estrutura?.images)) {
        return estrutura?.images;
      }

      if (typeof estrutura?.images === "string") {
        // Remover aspas extras e caracteres de escape
        let imagesStr = (estrutura?.images as string).replace(/\\/g, "");

        // Se a string começa com aspas duplas e termina com aspas duplas, remova-as
        if (imagesStr.startsWith('"') && imagesStr.endsWith('"')) {
          imagesStr = imagesStr.slice(1, -1);
        }

        // Verificar se a string parece um array JSON válido
        if (!imagesStr.startsWith("[") || !imagesStr.endsWith("]")) {
          // Se não for um formato válido de array, retorne vazio
          return [];
        }

        try {
          const parsedImages = JSON.parse(imagesStr);
          return Array.isArray(parsedImages) ? parsedImages : [];
        } catch {
          return [];
        }
      }

      return [];
    } catch (error) {
      console.error("Erro ao processar imagens:", error);
      return [];
    }
  };
  const handleOpenConfirmDialog = (contrato: any) => {
    setContratoParaDesvincular(contrato);
    setIsConfirmDialogOpen(true);
  };
  const handleDesvincularContrato = async () => {
    if (
      !contratoParaDesvincular ||
      !credenciado ||
      !contratoParaDesvincular.contrato?.id
    ) {
      toast.error("Dados incompletos para desvincular");
      return;
    }

    setIsDesvinculating(true);
    try {
      const response = await desvincularCredenciadoContrato(
        credenciado.id,
        contratoParaDesvincular.contrato.id
      );

      if (response.success) {
        toast.success("Contrato desvinculado com sucesso!");

        // Versão corrigida para lidar corretamente com o tipo
        setCredenciado((prevCredenciado) => {
          if (!prevCredenciado) return prevCredenciado;
          return {
            ...prevCredenciado,
            contratos: prevCredenciado.contratos.filter(
              (c) => c.contrato?.id !== contratoParaDesvincular.contrato?.id
            ),
          };
        });
      } else {
        toast.error(`Erro ao desvincular: ${response.error}`);
      }
    } catch (error) {
      console.error("Erro ao desvincular credenciado do contrato:", error);
      toast.error("Não foi possível desvincular o contrato");
    } finally {
      setIsDesvinculating(false);
      setIsConfirmDialogOpen(false);
    }
  };
  return !credenciado ? (
    <div className="flex justify-center items-center h-screen">
      <svg className="animate-spin h-8 w-8 text-gray-600" viewBox="0 0 24 24">
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8v8H4z"
        />
      </svg>
    </div>
  ) : (
    <div className="space-y-4">
      {/* Cabeçalho */}
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex flex-col">
              <CardTitle className="text-2xl font-bold">
                {info?.nome_fantasia || info?.razao_social}
              </CardTitle>
              <CardDescription>{info?.razao_social}</CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={credenciado?.ativo ? "default" : "destructive"}>
                {credenciado?.ativo ? "Ativo" : "Inativo"}
              </Badge>
              <Button
                variant="outline"
                onClick={() =>
                  (window.location.href = `/dashboard/rede-credenciada/editar-credenciado/${id}`)
                }
              >
                Editar
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 flex-wrap">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <MapPin className="h-4 w-4" />
              <span>
                {endereco?.cidade}, {endereco?.estado}
              </span>
            </div>

            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Building className="h-4 w-4" />
              <span>
                {info?.atividade_principal || "Sem atividade definida"}
              </span>
            </div>

            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span>
                Prazo: {credenciado?.Prazo?.descricao || "Não definido"}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Abas */}
      <Tabs defaultValue="informacoes" className="w-full">
        <TabsList className="grid grid-cols-6 mb-4">
          <TabsTrigger value="informacoes">Informações</TabsTrigger>
          <TabsTrigger value="contato">Contato</TabsTrigger>
          <TabsTrigger value="endereco">Endereço</TabsTrigger>
          <TabsTrigger value="estrutura">Estrutura</TabsTrigger>
          <TabsTrigger value="contratos">Contratos</TabsTrigger>
          <TabsTrigger value="servicos">Serviços</TabsTrigger>
        </TabsList>

        {/* Aba de Informações */}
        <TabsContent value="informacoes">
          <Card>
            <CardHeader>
              <CardTitle>Informações Básicas</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      CNPJ
                    </p>
                    <p>
                      {info?.cnpj ? formatCNPJ(info?.cnpj) : "Não informado"}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Razão Social
                    </p>
                    <p>{info?.razao_social || "Não informado"}</p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Nome Fantasia
                    </p>
                    <p>{info?.nome_fantasia || "Não informado"}</p>
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Atividade Principal
                    </p>
                    <p>{info?.atividade_principal || "Não informado"}</p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Inscrição Estadual
                    </p>
                    <p>{info?.inscri_estadual || "Não informado"}</p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Inscrição Municipal
                    </p>
                    <p>{info?.inscri_municipal || "Não informado"}</p>
                  </div>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Data de Abertura
                    </p>
                    <p>
                      {info?.data_abertura
                        ? formatDate(new Date(info?.data_abertura))
                        : "Não informado"}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Horário de Funcionamento
                    </p>
                    <p>{info?.horario_funcionamento || "Não informado"}</p>
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Porte Empresarial
                    </p>
                    <p>
                      {info?.porte_empresarial
                        ? info?.porte_empresarial.replace(/"/g, "")
                        : "Não informado"}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Capital Social
                    </p>
                    <p>{info?.capital_social || "Não informado"}</p>
                  </div>
                </div>
              </div>
              <Separator className="my-4" />
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  Observações Gerais
                </p>
                <p className="text-sm bg-muted p-3 rounded-md">
                  {info?.observacoes_gerais || "Sem observações"}
                </p>
              </div>
              <Separator className="my-4" />
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  Optante Simples:{" "}
                </p>
                <span className="text-sm font-bold p-3 rounded-md">
                  {credenciado?.optante ? "Sim" : "Não"}
                </span>
              </div>

              {info?.logotipo_empresa && (
                <>
                  <Separator className="my-4" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-2">
                      Logotipo
                    </p>
                    <div className="flex justify-center">
                      <div className="rounded-md overflow-hidden max-w-xs border">
                        <img
                          src={info?.logotipo_empresa}
                          alt="Logotipo da empresa"
                          className="max-w-full h-auto"
                        />
                      </div>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Aba de Contato */}
        <TabsContent value="contato">
          <Card>
            <CardHeader>
              <CardTitle>Informações de Contato</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="border shadow-sm">
                  <CardHeader>
                    <CardTitle className="text-lg">Contatos Gerais</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-primary/10 p-2 rounded-full">
                        <Phone className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">Telefone</p>
                        <p className="text-sm text-muted-foreground">
                          {contato?.telefone || "Não informado"}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <div className="bg-primary/10 p-2 rounded-full">
                        <Phone className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">Celular</p>
                        <p className="text-sm text-muted-foreground">
                          {contato?.celular || "Não informado"}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <div className="bg-primary/10 p-2 rounded-full">
                        <Mail className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">E-mail</p>
                        <p className="text-sm text-muted-foreground break-all">
                          {contato?.email || "Não informado"}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <div className="space-y-4">
                  <Card className="border shadow-sm">
                    <CardHeader className="py-3">
                      <CardTitle className="text-lg">Gerente</CardTitle>
                    </CardHeader>
                    <CardContent className="py-3">
                      <div className="flex items-center gap-3">
                        <div className="bg-primary/10 p-2 rounded-full">
                          <User className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                          <p className="text-sm font-medium">
                            {contato?.nome_gerente || "Não informado"}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {contato?.telefone_gerente || "Sem telefone"}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border shadow-sm">
                    <CardHeader className="py-3">
                      <CardTitle className="text-lg">Proprietário</CardTitle>
                    </CardHeader>
                    <CardContent className="py-3">
                      <div className="flex items-center gap-3">
                        <div className="bg-primary/10 p-2 rounded-full">
                          <User className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                          <p className="text-sm font-medium">
                            {contato?.nome_proprietario || "Não informado"}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {contato?.telefone_proprietario || "Sem telefone"}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Aba de Endereço */}
        <TabsContent value="endereco">
          <Card>
            <CardHeader>
              <CardTitle>Endereço</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        CEP
                      </p>
                      <p>{endereco?.cep || "Não informado"}</p>
                    </div>

                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Logradouro
                      </p>
                      <p>{endereco?.logradouro || "Não informado"}</p>
                    </div>

                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Bairro
                      </p>
                      <p>{endereco?.bairro || "Não informado"}</p>
                    </div>

                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Cidade/Estado
                      </p>
                      <p>
                        {endereco?.cidade || "Não informado"},{" "}
                        {endereco?.estado || ""}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Aba de Estrutura */}
        <TabsContent value="estrutura">
          <Card>
            <CardHeader>
              <CardTitle>Estrutura do Credenciado</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
                <Card className="border">
                  <CardContent className="p-4 text-center">
                    <p className="text-3xl font-bold mb-1">
                      {estrutura?.capacidade_atendimento || "0"}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Capacidade de Atendimento
                    </p>
                  </CardContent>
                </Card>

                <Card className="border">
                  <CardContent className="p-4 text-center">
                    <p className="text-3xl font-bold mb-1">
                      {estrutura?.box_veiculos_leves || "0"}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Box para Veículos Leves
                    </p>
                  </CardContent>
                </Card>

                <Card className="border">
                  <CardContent className="p-4 text-center">
                    <p className="text-3xl font-bold mb-1">
                      {estrutura?.box_veiculos_pesados || "0"}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Box para Veículos Pesados
                    </p>
                  </CardContent>
                </Card>

                <Card className="border">
                  <CardContent className="p-4 text-center">
                    <p className="text-3xl font-bold mb-1">
                      {estrutura?.elevadores_veiculos || "0"}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Elevadores de Veículos
                    </p>
                  </CardContent>
                </Card>

                <Card className="border">
                  <CardContent className="p-4 text-center">
                    <p className="text-3xl font-bold mb-1">
                      {estrutura?.estufas_pintura || "0"}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Estufas de Pintura
                    </p>
                  </CardContent>
                </Card>
              </div>

              <div className="mt-6">
                <h3 className="text-lg font-semibold mb-4">
                  Imagens da Estrutura
                </h3>
                {renderImages(getImages())}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Aba de Contratos */}
        <TabsContent value="contratos">
          <Card>
            <CardHeader>
              <CardTitle>Contratos</CardTitle>
              <CardDescription>
                Contratos vinculados a este credenciado
              </CardDescription>
            </CardHeader>
            <CardContent>
              {credenciado &&
              credenciado.contratos &&
              credenciado.contratos.length > 0 ? (
                <div className="space-y-3">
                  {credenciado.contratos.map((contrato, index) => (
                    <Card
                      key={index}
                      className="border shadow-sm hover:shadow-md transition-shadow"
                    >
                      <CardContent className="p-4">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="font-medium">Contrato #{index + 1}</p>
                            <p className="text-sm text-muted-foreground">
                              {contrato.contrato?.razao_social}
                            </p>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleOpenConfirmDialog(contrato)}
                          >
                            Desvincular Contrato
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="mx-auto h-10 w-10 text-muted-foreground mb-2" />
                  <p className="text-lg font-medium">
                    Nenhum contrato encontrado
                  </p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Não há contratos vinculados a este credenciado.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="servicos">
          <Card>
            <CardHeader>
              <CardTitle>Serviços</CardTitle>
              <CardDescription>
                Serviços vinculados a este credenciado
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul>
                {credenciado.servicos.map(({ descricao, id }) => (
                  <li key={id}>- {descricao}</li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      {/* Dialog de confirmação */}
      <Dialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar desvinculação</DialogTitle>
            <DialogDescription>
              Você está prestes a desvincular este credenciado do contrato{" "}
              <span className="font-semibold">
                {contratoParaDesvincular?.contrato?.razao_social}
              </span>
              . Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>

          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 flex items-start gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-600 flex-shrink-0 mt-0.5" />
            <p className="text-sm text-yellow-800">
              A desvinculação removerá todos os privilégios e acesso deste
              credenciado ao contrato selecionado.
            </p>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsConfirmDialogOpen(false)}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleDesvincularContrato}
              disabled={isDesvinculating}
            >
              {isDesvinculating
                ? "Desvinculando..."
                : "Confirmar desvinculação"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DetalheCredenciado;
