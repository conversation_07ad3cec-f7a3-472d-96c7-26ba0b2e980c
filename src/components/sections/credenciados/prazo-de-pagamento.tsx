"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { PrazoDePagamentoConfig } from "@/components/forms/inputConfig";
import { prazoDePagamentoSchema } from "@/components/forms/schemas";
import { SheetForm } from "@/components/forms/sheet-form";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { Checkbox } from "@/components/ui/checkbox";
import { usePrazoDePagamento } from "@/context/prazo-de-pagamento-context";
import { ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import { toast } from "sonner";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Edit, Delete, MoreHorizontal } from "lucide-react";

interface ActionsCellProps {
  row: {
    original: prazo_de_pagamento;
  };
}

export const ActionsCell: React.FC<ActionsCellProps> = ({ row }) => {
  const { setPrazosDePagamento } = usePrazoDePagamento();
  const [open, setOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<prazo_de_pagamento | null>(null);

  const editItem = async () => {
    setSelectedItem(row.original);
    setOpen(true);
  };

  const updateItem = async (values: z.infer<typeof prazoDePagamentoSchema>) => {
    try {
      const res = await fetch(`/api/prazo-de-pagamento/${row.original.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values),
      });

      if (!res.ok) {
        toast("Erro ao atualizar o prazo de pagamento");
        return;
      }

      const data = await res.json();
      setPrazosDePagamento((prev) =>
        prev.map((item) => (item.id === data.data.id ? data.data : item))
      );

      toast("Prazo de pagamento atualizado com sucesso!");
      setOpen(false);
    } catch (error) {
      toast("Erro ao atualizar o prazo de pagamento");
    }
  };

  const deleteItem = async () => {
    const res = await fetch(`/api/prazo-de-pagamento/${row.original.id}`, {
      method: "DELETE",
    });
    if (!res.ok) {
      toast("Ops, algo deu errado", {
        description: "Houve um erro ao deletar o prazo de pagamento",
      });
      return;
    }
    setPrazosDePagamento((prev) => prev.filter((item) => item.id !== row.original.id));
    toast("Prazo de pagamento deletado com sucesso!");
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Abrir o menu</span>
            <MoreHorizontal />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Ações</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={editItem}>
            Editar
            <Edit />
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => {
              toast("Tem certeza?", {
                description: "Essa ação não pode ser desfeita",
                action: {
                  label: "Tenho certeza!",
                  onClick: deleteItem,
                },
              });
            }}>
            Deletar
            <Delete />
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      {selectedItem && open && (
        <SheetForm
          title="Editar Prazo de Pagamento"
          schema={prazoDePagamentoSchema}
          onSubmit={updateItem}
          triggerLabel="Editar Prazo de Pagamento"
          defaultValues={selectedItem}>
          <GenericFormsInput fieldConfig={PrazoDePagamentoConfig} variants="single" />
        </SheetForm>
      )}
    </>
  );
};

export const prazoDePagamentoColumn: ColumnDef<prazo_de_pagamento>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "descricao",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Descrição" />,
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => <ActionsCell row={row} />,
  },
];

export function PrazoDePagamentoTable() {
  const { prazosDePagamento, setPrazosDePagamento } = usePrazoDePagamento();

  async function onNewPrazoDePagamento(values: z.infer<typeof prazoDePagamentoSchema>) {
    const response = await fetch("/api/prazo-de-pagamento", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });

    if (!response.ok) {
      toast("Ops, algo deu errado", {
        description: "Houve um erro ao criar o prazo de pagamento",
      });
      return;
    }

    const data = await response.json();
    setPrazosDePagamento((prev) => [...prev, data.data]);
  }

  return (
    <DataTable
      data={prazosDePagamento}
      newItem={{
        defaultValues: {
          descricao: "",
        },
        fieldConfig: PrazoDePagamentoConfig,
        schema: prazoDePagamentoSchema,
      }}
      onNewItem={onNewPrazoDePagamento}
      exportTo={true}
      columns={prazoDePagamentoColumn}
    />
  );
}
