"use client";

import { useForm } from "react-hook-form";
import { FormProvider } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { useCredenciado } from "@/context/credenciado-context";
import { useContrato } from "@/context/contrato-context";
import { toast } from "sonner";
import { Combobox } from "@/components/inputs/combo-box";
import { DataTable, DataTableMethods } from "@/components/tables/data-table";
import { DataTableColumnHeader } from "@/components/tables/column-header";
import { Checkbox } from "@/components/ui/checkbox";
import { ColumnDef } from "@tanstack/react-table";
import { useEffect, useRef, useState } from "react";
import FilterCredenciado from "./filtros-credenciado";
import { AlertTriangle } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  desvincularCredenciadoContrato,
  getCredenciadosByContratoId,
} from "@/serverActions/credenciadoAction";
import { useRouter } from "next/navigation";
import { useSession } from "@/components/session/use-session";
import { getContratoByIdAction } from "@/serverActions/contratoAction";

type VincularContratoFormData = {
  contratoId: string;
  credenciadosIds: string[];
};

export function VincularContrato() {
  const { credenciados, setCredenciados } = useCredenciado();
  const { contratos } = useContrato();
  const methods = useForm<VincularContratoFormData>();
  const tableRef = useRef<DataTableMethods<credenciado>>(null);
  const [selectedContrato, setSelectedContrato] = useState<string | null>(null);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [credenciadoParaDesvincular, setCredenciadoParaDesvincular] = useState<
    string | null
  >(null);
  const [isDesvinculating, setIsDesvinculating] = useState(false);

  const router = useRouter();
  const [filteredCredenciados, setFilteredCredenciados] = useState<
    credenciado[]
  >([]);
  const [credenciadoData, setCredenciadoData] = useState<credenciado[]>([]);
  const [filters, setFilters] = useState({
    atividade_principal: "",
    cnpj: "",
    razao_social: "",
    nome_fantasia: "",
    email: "",
    telefone: "",
    celular: "",
    cidade: "",
    estado: "",
    ativo: false,
    orcamentista: false,
    optante: false,
    placa_verde: false,
    concessionaria: false,
    taxa_adm: 0,
  });
  const [showFilterDialog, setShowFilterDialog] = useState(false);
  const { session } = useSession();
  const isAdmin =
    session?.roles.includes("ADMIN") ||
    session?.roles.includes("CREDENCIAMENTO") ||
    session?.roles.includes("GESTOR_FROTAS");
  const [isLoading, setIsLoading] = useState(false);

  // Novos estados para paginação no servidor
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");

  // Initialize filtered credenciados with all credenciados
  useEffect(() => {
    async function fetchCredenciadoData() {
      setIsLoading(true);
      if (session?.contrato && !session.roles.includes("ADMIN")) {
        try {
          const response = await getCredenciadosByContratoId(
            session.contrato.id
          );
          if (response.success && response.data) {
            const credenciadoAction = response.data
              .credenciados as credenciado[];
            const filtered = credenciadoAction.filter((credenciado) =>
              credenciado.contratos.some(
                (contrato) => contrato.contratoId === session?.contratoId
              )
            );
            setCredenciadoData(filtered);
          }
          setIsLoading(false);
        } catch (error) {
          setIsLoading(false);
          console.error("Erro ao buscar credenciados:", error);
        }
      } else {
        // Para usuários admin, usamos a paginação do servidor
        if (session?.roles.includes("ADMIN")) {
          fetchPaginatedData(currentPage, itemsPerPage, searchTerm);
        } else {
          setIsLoading(false);
          setCredenciadoData(credenciados);
        }
      }
    }

    fetchCredenciadoData();
  }, [session, credenciados, currentPage, itemsPerPage, searchTerm]);

  useEffect(() => {
    setFilteredCredenciados(credenciadoData);
  }, [credenciadoData]);

  useEffect(() => {
    const handleContratoChange = async (value: any) => {
      if (value.contratoId) {
        setSelectedContrato(value.contratoId);

        const selectedContratoData = await getContratoByIdAction(
          value.contratoId
        );
        const newCidade = selectedContratoData?.data.contrato.cidade || "";
        const newEstado =
          selectedContratoData?.data.contrato.estado_financeiro || "";

        // Atualiza apenas os filtros de cidade/estado, mantendo os outros filtros existentes
        const updatedFilters = {
          ...filters, // Mantém todos os filtros atuais
          cidade: newCidade,
          estado: "",
        };

        setFilters(updatedFilters);
        await applyFiltersWithFilters(updatedFilters, newEstado);
      } else {
        setSelectedContrato(null);
        setFilteredCredenciados(credenciados); // Mostra todos se nenhum contrato selecionado
      }
    };

    const applyFiltersWithFilters = async (
      filtersToApply: any,
      estadoFallback: string
    ) => {
      // Aplica os filtros imediatamente
      const filtered = await applyFiltersImmediately(filtersToApply);

      // Se não encontrou resultados e tem fallback de estado, tenta com estado
      if (filtered.length === 0 && estadoFallback) {
        const fallbackFilters = {
          ...filtersToApply, // Mantém os outros filtros
          cidade: "",
          estado: estadoFallback,
        };
        setFilters(fallbackFilters);
        await applyFiltersImmediately(fallbackFilters);
      }
    };

    const applyFiltersImmediately = async (filtersToApply: any) => {
      if (isAdmin) {
        // Para admin, usa paginação no servidor com todos os filtros
        await fetchPaginatedData(1, itemsPerPage, searchTerm, filtersToApply);
        return filteredCredenciados;
      } else {
        // Filtro no cliente para não-admin com todos os critérios
        const filtered = credenciados.filter((credenciado) => {
          const info = credenciado.informacoes[0] || {};
          const contato = credenciado.contato || {};
          const endereco = credenciado.endereco || {};

          // Filtro por cidade/estado (contrato)
          const cidadeMatch =
            !filtersToApply.cidade ||
            endereco.cidade
              ?.toLowerCase()
              .includes(filtersToApply.cidade.toLowerCase());
          const estadoMatch =
            !filtersToApply.estado || endereco.estado === filtersToApply.estado;

          // Filtros de texto
          const textFilters = [
            {
              field: info.atividade_principal,
              filter: filtersToApply.atividade_principal,
            },
            { field: info.cnpj, filter: filtersToApply.cnpj },
            { field: info.razao_social, filter: filtersToApply.razao_social },
            { field: info.nome_fantasia, filter: filtersToApply.nome_fantasia },
            { field: contato.email, filter: filtersToApply.email },
            { field: contato.telefone, filter: filtersToApply.telefone },
            { field: contato.celular, filter: filtersToApply.celular },
          ];

          if (
            textFilters.some(
              ({ field, filter }) =>
                filter &&
                (!field || !field.toLowerCase().includes(filter.toLowerCase()))
            )
          ) {
            return false;
          }

          // Filtros booleanos
          const booleanFilters = [
            { field: credenciado.ativo, filter: filtersToApply.ativo },
            {
              field: credenciado.orcamentista,
              filter: filtersToApply.orcamentista,
            },
            { field: credenciado.optante, filter: filtersToApply.optante },
            {
              field: credenciado.atende_placa_verde,
              filter: filtersToApply.placa_verde,
            },
            {
              field: credenciado.concessionaria,
              filter: filtersToApply.concessionaria,
            },
          ];

          if (booleanFilters.some(({ field, filter }) => filter && !field)) {
            return false;
          }

          return cidadeMatch && estadoMatch;
        });

        setFilteredCredenciados(filtered);
        return filtered;
      }
    };

    const subscription = methods.watch(handleContratoChange);

    return () => subscription.unsubscribe();
  }, [methods, isAdmin, credenciados, filters]);

  const handleOpenConfirmDialog = (credenciadoId: string) => {
    setCredenciadoParaDesvincular(credenciadoId);
    setIsConfirmDialogOpen(true);
  };

  const handleDesvincular = async () => {
    if (!selectedContrato || !credenciadoParaDesvincular) {
      toast.error("Dados incompletos para desvincular");
      return;
    }

    setIsDesvinculating(true);
    try {
      const response = await desvincularCredenciadoContrato(
        credenciadoParaDesvincular,
        selectedContrato
      );

      if (response.success) {
        toast.success("Credenciado desvinculado com sucesso!");

        // Atualizar o estado local
        setCredenciados((prevCredenciados) => {
          return prevCredenciados.map((cred) => {
            if (cred.id === credenciadoParaDesvincular) {
              // Cria um novo objeto credenciado com contratos filtrados
              const updatedCredenciado = {
                ...cred,
                // Filtra o contrato removido
                contratos: cred.contratos.filter(
                  (c) => c.contratoId !== selectedContrato
                ) as typeof cred.contratos,
              };
              return updatedCredenciado;
            }
            return cred;
          });
        });
      } else {
        toast.error(`Erro ao desvincular: ${response.error}`);
      }
    } catch (error) {
      console.error("Erro ao desvincular credenciado do contrato:", error);
      toast.error("Não foi possível desvincular o credenciado");
    } finally {
      setIsDesvinculating(false);
      setIsConfirmDialogOpen(false);
      setCredenciadoParaDesvincular(null);
    }
  };

  // Define columns with a conditional "Desvincular" button
  const credenciadoColumns: ColumnDef<credenciado>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "atividade_principal",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Atividade Principal" />
      ),
      accessorFn: (row) =>
        row.informacoes[0]?.atividade_principal || "Não informada",
    },
    {
      accessorKey: "cnpj",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="CNPJ" />
      ),
      accessorFn: (row) => row.informacoes[0]?.cnpj,
      cell: ({ row }) => {
        const cnpj = row.getValue("cnpj") as string;
        return (
          <span>
            {cnpj?.replace(
              /^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/,
              "$1.$2.$3/$4-$5"
            )}
          </span>
        );
      },
    },
    {
      accessorKey: "razao_social",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Razão Social" />
      ),
      accessorFn: (row) => row.informacoes[0]?.razao_social || "Não informada",
    },
    {
      accessorKey: "nome_fantasia",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Nome Fantasia" />
      ),
      accessorFn: (row) => row.informacoes[0]?.nome_fantasia || "N/A",
    },
    {
      accessorKey: "endreco.cidade",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Localidade" />
      ),
      accessorFn: (row) =>
        `${row.endereco?.cidade}/${row.endereco?.estado}` || "N/A",
    },
    {
      accessorKey: "email",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="E-mail" />
      ),
      accessorFn: (row) => row.contato?.email || "Não informado",
    },
    {
      accessorKey: "telefone",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Telefone" />
      ),
      accessorFn: (row) => row.contato?.telefone || "Não informado",
    },
    {
      accessorKey: "celular",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Celular" />
      ),
      accessorFn: (row) => row.contato?.celular || "Não informado",
    },
    {
      id: "acoes",
      header: "Ações",
      cell: ({ row }) => {
        const credenciado = row.original;
        // Verificar se o credenciado está vinculado ao contrato selecionado
        const estaVinculado = credenciado.contratos?.some(
          (contrato: { contratoId: string }) =>
            contrato.contratoId === selectedContrato
        );

        // Só mostrar o botão se estiver vinculado
        return estaVinculado ? (
          <Button
            variant="destructive"
            size="sm"
            onClick={() => handleOpenConfirmDialog(credenciado.id)}
          >
            Desvincular
          </Button>
        ) : null;
      },
    },
  ];

  const handleFilterChange = (field: string, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  async function fetchPaginatedData(
    page = 1,
    limit = 10,
    search = "",
    appliedFilters = filters
  ) {
    setIsLoading(true);
    try {
      let url = `/api/credenciado?page=${page}&limit=${limit}`;
      if (search) url += `&search=${encodeURIComponent(search)}`;

      // Mapeamento de campos para parâmetros da API
      const filterMappings: Record<string, string> = {
        placa_verde: "atende_placa_verde",
        // Adicione outros mapeamentos se necessário
      };

      // Adiciona todos os filtros à URL
      Object.entries(appliedFilters).forEach(([key, value]) => {
        if (
          value !== "" &&
          value !== false &&
          value !== undefined &&
          value !== null
        ) {
          const paramKey = filterMappings[key] || key;
          url += `&${encodeURIComponent(paramKey)}=${encodeURIComponent(
            value
          )}`;
        }
      });

      const response = await fetch(url);
      const result = await response.json();

      if (result.success && result.data) {
        setFilteredCredenciados(result.data.credenciados);
        setTotalItems(result.data.pagination.total);
        setTotalPages(result.data.pagination.pages);
      } else {
        toast.error(result.error || "Erro ao carregar credenciados");
      }
    } catch (error) {
      console.error("Erro ao buscar credenciados:", error);
      toast.error("Falha ao carregar dados");
    } finally {
      setIsLoading(false);
    }
  }

  const applyFilters = () => {
    // Se for admin com paginação no servidor, aplicamos os filtros via API

    if (isAdmin) {
      setCurrentPage(1);
      fetchPaginatedData(1, itemsPerPage, searchTerm, filters);
      setShowFilterDialog(false);
      return;
    }

    // Caso contrário, mantém a lógica de filtro original no cliente
    const filtered = credenciados.filter((credenciado) => {
      // Filtro por texto - Atividade Principal
      if (
        filters.atividade_principal &&
        !credenciado.informacoes[0]?.atividade_principal
          ?.toLowerCase()
          .includes(filters.atividade_principal.toLowerCase())
      ) {
        return false;
      }

      // Filtro por texto - CNPJ
      if (
        filters.cnpj &&
        !credenciado.informacoes[0]?.cnpj
          ?.toLowerCase()
          .includes(filters.cnpj.toLowerCase())
      ) {
        return false;
      }

      // Filtro por texto - Razão Social
      if (
        filters.razao_social &&
        !credenciado.informacoes[0]?.razao_social
          ?.toLowerCase()
          .includes(filters.razao_social.toLowerCase())
      ) {
        return false;
      }

      // Filtro por texto - Nome Fantasia
      if (
        filters.nome_fantasia &&
        !credenciado.informacoes[0]?.nome_fantasia
          ?.toLowerCase()
          .includes(filters.nome_fantasia.toLowerCase())
      ) {
        return false;
      }

      // Filtro por texto - Email
      if (
        filters.email &&
        !credenciado.contato?.email
          ?.toLowerCase()
          .includes(filters.email.toLowerCase())
      ) {
        return false;
      }

      // Filtro por texto - Telefone
      if (
        filters.telefone &&
        !credenciado.contato?.telefone?.includes(filters.telefone)
      ) {
        return false;
      }

      // Filtro por texto - Celular
      if (
        filters.celular &&
        !credenciado.contato?.celular?.includes(filters.celular)
      ) {
        return false;
      }

      if (
        filters.taxa_adm &&
        !credenciado.contratos.find(
          ({ contrato }) =>
            String(contrato?.taxa_admin) === String(filters.taxa_adm)
        )
      ) {
        return false;
      }

      // Filtro por localidade - Cidade
      if (
        filters.cidade &&
        !credenciado.endereco?.cidade
          ?.toLowerCase()
          .includes(filters.cidade.toLowerCase())
      ) {
        return false;
      }

      // Filtro por localidade - Estado
      if (filters.estado && credenciado.endereco?.estado !== filters.estado) {
        return false;
      }

      // Filtros booleanos - Ativo
      if (filters.ativo && !credenciado.ativo) {
        return false;
      }

      // Filtros booleanos - Orçamentista
      if (filters.orcamentista && !credenciado.orcamentista) {
        return false;
      }

      // Filtros booleanos - Optante
      if (filters.optante && !credenciado.optante) {
        return false;
      }

      // Filtros booleanos - Placa Verde
      if (filters.placa_verde && !credenciado.atende_placa_verde) {
        return false;
      }

      // Filtros booleanos - Concessionária
      if (filters.concessionaria && !credenciado.concessionaria) {
        return false;
      }

      // Se passou por todos os filtros, inclui no resultado
      return true;
    });

    console.log("filtered", filtered);

    setFilteredCredenciados(filtered);
    toast.success(`${filtered.length} credenciados encontrados`);
    setShowFilterDialog(false);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    if (isAdmin) {
      fetchPaginatedData(page, itemsPerPage, searchTerm);
    }
  };

  const handlePerPageChange = (perPage: number) => {
    setItemsPerPage(perPage);
    setCurrentPage(1); // Reset para primeira página
    if (isAdmin) {
      fetchPaginatedData(1, perPage, searchTerm);
    }
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset para primeira página
    if (isAdmin) {
      fetchPaginatedData(1, itemsPerPage, term);
    }
  };

  const clearFilters = () => {
    setFilters({
      atividade_principal: "",
      cnpj: "",
      razao_social: "",
      nome_fantasia: "",
      email: "",
      telefone: "",
      celular: "",
      cidade: "",
      estado: "",
      ativo: false,
      optante: false,
      orcamentista: false,
      placa_verde: false,
      concessionaria: false,
      taxa_adm: 0,
    });
    setFilteredCredenciados(credenciados);
    setShowFilterDialog(false);
    router.refresh();
  };

  const onSubmit = async (data: VincularContratoFormData) => {
    try {
      if (!tableRef.current) {
        toast.error("Erro ao acessar a tabela");
        return;
      }

      const selectedCredenciados = tableRef.current.getSelectedRows();
      const credenciadosIds = selectedCredenciados.map(
        (credenciado) => credenciado.id
      );

      if (credenciadosIds.length === 0) {
        toast.warning("Selecione pelo menos um credenciado");
        return;
      }

      if (!data.contratoId) {
        toast.warning("Selecione um contrato");
        return;
      }

      const response = await fetch("/api/credenciado/vincular-contrato", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contratoId: data.contratoId,
          credenciadosIds,
        }),
      });

      if (!response.ok) {
        throw new Error("Falha ao vincular credenciado ao contrato");
      }

      toast.success("Credenciados vinculados com sucesso!");
      tableRef.current.resetRowSelection();
      methods.reset();
      window.location.href =
        "/dashboard/rede-credenciada/consultar-credenciados";
    } catch (error) {
      console.error("Erro ao vincular credenciados:", error);
      toast.error("Erro ao vincular credenciados");
    }
  };

  // Encontrar o nome do contrato selecionado para exibir no dialog
  const contratoSelecionado = contratos?.find((c) => c.id === selectedContrato);
  // Encontrar o nome do credenciado selecionado para exibir no dialog
  const credenciadoSelecionado = credenciados?.find(
    (c) => c.id === credenciadoParaDesvincular
  );
  const nomeCredenciado =
    credenciadoSelecionado?.informacoes[0]?.nome_fantasia ||
    credenciadoSelecionado?.informacoes[0]?.razao_social ||
    "este credenciado";

  return (
    <div className="p-8">
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 gap-4">
            <Combobox
              datas={contratos}
              title="Selecione o Contrato"
              placeholder="Selecione um contrato"
              referenceId="contratoId"
              chave="nome_contrato"
              name="contratoId"
              description="Contrato que será vinculado"
            />

            {selectedContrato && (
              <>
                <h2 className="text-xl font-bold py-4">
                  Selecione Credenciados que serão vinculados
                </h2>
                <div className="flex justify-end gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full sm:w-auto"
                    onClick={() => setShowFilterDialog(true)}
                  >
                    Filtros
                  </Button>
                  <Button type="submit" className="w-full sm:w-auto">
                    Vincular
                  </Button>
                </div>
                <DataTable
                  data={filteredCredenciados}
                  columns={credenciadoColumns}
                  ref={tableRef}
                  showSearch={false}
                  exportTo={true}
                  serverSidePagination={{
                    enabled: true,
                    currentPage: currentPage,
                    totalPages: totalPages,
                    totalItems: totalItems,
                    perPage: itemsPerPage, // Adicione esta propriedade
                    onPageChange: handlePageChange,
                    onPerPageChange: handlePerPageChange,
                    onSearch: handleSearch,
                  }}
                />
              </>
            )}
          </div>
        </form>
      </FormProvider>

      <FilterCredenciado
        showFilterDialog={showFilterDialog}
        setShowFilterDialog={setShowFilterDialog}
        filters={filters}
        handleFilterChange={handleFilterChange}
        clearFilters={clearFilters}
        applyFilters={applyFilters}
      />

      {/* Dialog de confirmação para desvincular */}
      <Dialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar desvinculação</DialogTitle>
            <DialogDescription>
              Você está prestes a desvincular {nomeCredenciado} do contrato{" "}
              <span className="font-semibold">
                {contratoSelecionado?.razao_social || "selecionado"}
              </span>
              . Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>

          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 flex items-start gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-600 flex-shrink-0 mt-0.5" />
            <p className="text-sm text-yellow-800">
              A desvinculação removerá todos os privilégios e acesso deste
              credenciado ao contrato selecionado.
            </p>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsConfirmDialogOpen(false)}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleDesvincular}
              disabled={isDesvinculating}
            >
              {isDesvinculating
                ? "Desvinculando..."
                : "Confirmar desvinculação"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
