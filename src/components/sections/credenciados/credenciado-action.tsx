"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Trash2 } from "lucide-react";
import { toast } from "sonner";
import ConfirmModal from "@/components/modal/confirm-modal-user";
import {
  bloquearCredenciado,
  deleteCredenciado,
} from "@/serverActions/credenciadoAction";
import { useSession } from "@/components/session/use-session";
import { hasOsByCredenciadoId } from "@/serverActions/orcamentoAction";

interface CredenciadoActionsProps {
  credenciado: credenciado;
  onActionClick?: (e: React.MouseEvent) => void;
}

export function CredenciadoActions({ credenciado, onActionClick }: CredenciadoActionsProps) {
  const { session } = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);

  const handleToggleStatus = async () => {
    try {
      setIsLoading(true);
      const action = await bloquearCredenciado(credenciado.id);

      if (action.success) {
        toast.success(
          credenciado.ativo
            ? "Credenciado bloqueado com sucesso"
            : "Credenciado ativado com sucesso"
        );
        window.location.reload();
      } else {
        throw new Error(action.error || "Erro ao alterar status do credenciado");
      }
    } catch (error: any) {
      toast.error(error.message || "Erro ao alterar status do credenciado");
      console.error(error);
    } finally {
      setIsLoading(false);
      setConfirmModalOpen(false);
    }
  };

  const handleDeleteRequest = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onActionClick) onActionClick(e);

    try {
      setIsLoading(true);
      const hasOs = await hasOsByCredenciadoId(credenciado.id);
      if (hasOs.data.hasAssociatedOS) {
        toast.error(
          "Não é possível excluir este credenciado pois há Ordens de Serviço vinculadas a ele."
        );
        return;
      }

      setDeleteModalOpen(true);
    } catch (error: any) {
      toast.error("Erro ao verificar Ordens de Serviço");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      setIsLoading(true);
      const result = await deleteCredenciado(credenciado.id);

      if (result.success) {
        toast.success("Credenciado excluído com sucesso!");
        window.location.reload();
      } else {
        throw new Error(result.error || "Erro ao excluir o credenciado");
      }
    } catch (error: any) {
      toast.error(error.message || "Erro ao excluir o credenciado");
      console.error(error);
    } finally {
      setIsLoading(false);
      setDeleteModalOpen(false);
    }
  };

  const openConfirmModal = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onActionClick) onActionClick(e);
    setConfirmModalOpen(true);
    return false;
  };

  return (
    <div onClick={(e) => e.stopPropagation()} className="relative" style={{ zIndex: 50 }}>
      <div className="flex gap-1">
      {session?.roles?.includes("ADMIN") && (
          <Button
            variant="ghost"
            size="icon"
            onClick={openConfirmModal}
            disabled={isLoading}
            className={credenciado.ativo ? "text-destructive" : "text-green-600"}>
            {credenciado.ativo ? (
              <ShieldAlert className="h-4 w-4" />
            ) : (
              <ShieldCheck className="h-4 w-4" />
            )}
          </Button>
      )}
      {/* {(session?.roles.includes("ADMIN") || session?.roles.includes("CREDENCIAMENTO")) && (
        <Button
        variant="ghost"
        size="icon"
        onClick={handleDeleteRequest}
        disabled={isLoading}
        className="text-destructive hover:bg-destructive/10">
          <Trash2 className="h-4 w-4" />
        </Button>
      )} */}
      </div>

      <ConfirmModal
        open={confirmModalOpen}
        title={credenciado.ativo ? "Confirmar Bloqueio" : "Confirmar Ativação"}
        message={
          credenciado.ativo
            ? "Deseja bloquear este credenciado? Ele ficará inacessível para uso no sistema."
            : "Deseja ativar este credenciado? Ele ficará disponível para uso no sistema."
        }
        onConfirm={(e) => {
          handleToggleStatus();
        }}
        onCancel={() => setConfirmModalOpen(false)}
      />

      <ConfirmModal
        open={deleteModalOpen}
        title="Confirmar Exclusão"
        message="Tem certeza que deseja excluir este credenciado? Esta ação não pode ser desfeita."
        onConfirm={(e) => {
          handleDelete();
        }}
        onCancel={() => setDeleteModalOpen(false)}
      />
    </div>
  );
}
