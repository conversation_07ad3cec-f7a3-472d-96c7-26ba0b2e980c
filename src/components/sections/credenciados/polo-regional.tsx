"use client";

import { CidadeConfig, GetCidadeConfig, PoloRegionalConfig } from "@/components/forms/inputConfig";
import { cidadeSchema, poloRegionalSchema } from "@/components/forms/schemas";
import { SheetForm } from "@/components/forms/sheet-form";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle } from "@/components/ui/drawer";
import { usePoloRegional } from "@/context/polo-regional-context";
import { ColumnDef } from "@tanstack/react-table";
import { Delete, Edit, MoreHorizontal, MapPin } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { z } from "zod";

export const cidadeColumn: ColumnDef<cidade>[] = [
  {
    accessorKey: "cidade",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Cidade" />,
  },
  {
    accessorKey: "estado",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Estado" />,
  },
];

export const poloRegionalColumn: ColumnDef<polo_regional>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "descricao",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Descrição" />,
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const { setPolosRegionais } = usePoloRegional();
      const [open, setOpen] = useState(false);
      const [selectedItem, setSelectedItem] = useState<polo_regional | null>(null);
      const [drawerOpen, setDrawerOpen] = useState(false);

      const editItem = async () => {
        setSelectedItem(row.original);
        setOpen(true);
      };

      const updateItem = async (values: z.infer<typeof poloRegionalSchema>) => {
        try {
          const res = await fetch(`/api/polo-regional/${row.original.id}`, {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(values),
          });

          if (!res.ok) {
            toast("Erro ao atualizar o polo regional");
            return;
          }

          const data = await res.json();
          setPolosRegionais((prev) =>
            prev.map((item) => (item.id === data.data.id ? data.data : item))
          );

          toast("Polo regional atualizado com sucesso!");
          setOpen(false);
        } catch (error) {
          toast("Erro ao atualizar o polo regional");
        }
      };

      const deleteItem = async () => {
        const res = await fetch(`/api/polo-regional/${row.original.id}`, {
          method: "DELETE",
        });
        if (!res.ok) {
          toast("Ops, algo deu errado", {
            description: "Houve um erro ao deletar o polo regional",
          });
          return;
        }
        setPolosRegionais((prev) => prev.filter((item) => item.id !== row.original.id));
        toast("Polo regional deletado com sucesso!");
      };
      const onUpdateCity = async (values: z.infer<typeof cidadeSchema>) => {
        try {
          const res = await fetch(`/api/polo_regional/${row.original.id}`, {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(values),
          });

          if (!res.ok) {
            toast("Erro ao atualizar o polo regional");
            return;
          }

          const data = await res.json();
          setPolosRegionais((prev) =>
            prev.map((item) => (item.id === data.data.id ? data.data : item))
          );

          toast("Polo regional atualizado com sucesso!");
          setOpen(false);
        } catch (error) {
          toast("Erro ao atualizar o polo regional");
        }
      };

      return (
        <>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Abrir o menu</span>
                <MoreHorizontal />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Ações</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={editItem}>
                Editar
                <Edit />
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  toast("Tem certeza?", {
                    description: "Essa ação não pode ser desfeita",
                    action: {
                      label: "Tenho certeza!",
                      onClick: deleteItem,
                    },
                  });
                }}>
                Deletar
                <Delete />
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setDrawerOpen(true)}>
                Cidades do Polo
                <MapPin />
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          {selectedItem && open && (
            <SheetForm
              title="Editar Polo Regional"
              schema={poloRegionalSchema}
              onSubmit={updateItem}
              triggerLabel="Editar Polo Regional"
              defaultValues={selectedItem}>
              <GenericFormsInput fieldConfig={PoloRegionalConfig} variants="single" />
            </SheetForm>
          )}
          <Drawer open={drawerOpen} onOpenChange={setDrawerOpen}>
            <DrawerContent>
              <DrawerHeader>
                <DrawerTitle>Cidades do Polo</DrawerTitle>
              </DrawerHeader>
              <div className="p-4">
                <DataTable
                  data={row.original.cidades}
                  columns={cidadeColumn}
                  newItem={{
                    defaultValues: { estado: "", cidade: "" },
                    fieldConfig: CidadeConfig,
                    schema: cidadeSchema,
                  }}
                  onNewItem={onUpdateCity}
                />
              </div>
            </DrawerContent>
          </Drawer>
        </>
      );
    },
  },
];

export function PoloRegionalTable() {
  const { polosRegionais, setPolosRegionais } = usePoloRegional();

  async function onNewPoloRegional(values: z.infer<typeof poloRegionalSchema>) {
    if (values.descricao === null || values.descricao === "") {
      toast("Por favor, preencha todos os campos");
      return;
    }
    const response = await fetch("/api/polo-regional", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });

    if (!response.ok) {
      toast("Ops, algo deu errado", {
        description: "Houve um erro ao criar o polo regional",
      });
      return;
    }
    const data = await response.json();
    setPolosRegionais((prev) => [...prev, data.data]);
    window.location.reload();
  }

  return (
    <DataTable
      data={polosRegionais}
      newItem={{
        defaultValues: {
          descricao: "",
        },
        fieldConfig: PoloRegionalConfig,
        schema: poloRegionalSchema,
      }}
      onNewItem={onNewPoloRegional}
      exportTo={true}
      columns={poloRegionalColumn}
    />
  );
}
