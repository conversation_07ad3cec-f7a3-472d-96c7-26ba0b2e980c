"use client";

import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { Checkbox } from "@/components/ui/checkbox";
import { useCredenciado } from "@/context/credenciado-context";
import { getServerSession } from "@/lib/auth/server-session";
import { Session } from "@/lib/auth/types";
import { ColumnDef } from "@tanstack/react-table";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { CredenciadoActions } from "./credenciado-action";
import FilterCredenciado from "./filtros-credenciado";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { useSession } from "@/components/session/use-session";
import { useUsuario } from "@/context/usuario-context";
import { getCredenciadosByContratoId } from "@/serverActions/credenciadoAction";

export const credenciadoColumns: ColumnDef<credenciado>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "atividade_principal",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Atividade Principal" />
    ),
    accessorFn: (row) =>
      row.informacoes[0]?.atividade_principal || "Não informada",
  },
  {
    accessorKey: "cnpj",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="CNPJ" />
    ),
    accessorFn: (row) => row.informacoes[0]?.cnpj,
    cell: ({ row }) => {
      const cnpj = row.getValue("cnpj") as string;
      return (
        <span>
          {cnpj?.replace(
            /^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/,
            "$1.$2.$3/$4-$5"
          )}
        </span>
      );
    },
  },
  {
    accessorKey: "razao_social",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Razão Social" />
    ),
    accessorFn: (row) => row.informacoes[0]?.razao_social || "Não informada",
  },
  {
    accessorKey: "nome_fantasia",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Nome Fantasia" />
    ),
    accessorFn: (row) => row.informacoes[0]?.nome_fantasia || "N/A",
  },
  {
    accessorKey: "endreco.cidade",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Localidade" />
    ),
    accessorFn: (row) =>
      `${row.endereco?.cidade}/${row.endereco?.estado}` || "N/A",
  },
  {
    accessorKey: "email",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="E-mail" />
    ),
    accessorFn: (row) => row.contato?.email || "Não informado",
  },
  {
    accessorKey: "senha",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Senha" />
    ),
    accessorFn: (row) => {
      const { session } = useSession();
      const { usuarios } = useUsuario();
      const usuario = usuarios.find(
        (user) =>
          user.nome === row.informacoes[0]?.razao_social ||
          user.email === row.contato.email
      );
      const isAdmin =
        session?.roles.includes("ADMIN") ||
        session?.roles.includes("CREDENCIAMENTO");

      return isAdmin ? usuario?.senha : "";
    },
  },
  {
    accessorKey: "telefone",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Telefone" />
    ),
    accessorFn: (row) => row.contato?.telefone || "Não informado",
  },
  {
    accessorKey: "celular",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Celular" />
    ),
    accessorFn: (row) => row.contato?.celular || "Não informado",
  },
  {
    accessorKey: "contratos",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Vinculado" />
    ),
    cell: ({ row }) => {
      const contratos = row.getValue("contratos") as any[];
      return <span>{contratos?.length > 0 ? "Sim" : "Não"}</span>;
    },
  },
  {
    id: "actions",
    header: "Ações",
    cell: ({ row }) => <CredenciadoActions credenciado={row.original} />,
  },
];

export function CredenciadoTable() {
  const router = useRouter();
  const { credenciados } = useCredenciado();
  const [filteredCredenciados, setFilteredCredenciados] = useState<
    credenciado[]
  >([]);
  const [credenciadoData, setCredenciadoData] = useState<credenciado[]>([]);
  const [filters, setFilters] = useState({
    atividade_principal: "",
    cnpj: "",
    razao_social: "",
    nome_fantasia: "",
    email: "",
    telefone: "",
    celular: "",
    cidade: "",
    estado: "",
    ativo: false,
    orcamentista: false,
    optante: false,
    placa_verde: false,
    concessionaria: false,
    taxa_adm: 0,
  });
  const [showFilterDialog, setShowFilterDialog] = useState(false);
  const [visibleColumns, setVisibleColumns] = useState<
    ColumnDef<credenciado>[]
  >([]);
  const { session } = useSession();
  const isAdmin =
    session?.roles.includes("ADMIN") ||
    session?.roles.includes("CREDENCIAMENTO") ||
    session?.roles.includes("GESTOR_FROTAS");
  const [isLoading, setIsLoading] = useState(false);

  // Novos estados para paginação no servidor
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    // Filter out the password column for non-admin users
    const filteredColumns = isAdmin
      ? credenciadoColumns
      : credenciadoColumns.filter(
          (column) =>
            !("accessorKey" in column && column.accessorKey === "senha")
        );

    setVisibleColumns(filteredColumns);
  }, [session, isAdmin]);

  const handleRowClick = ({ id }: credenciado) => {
    router.push(`/dashboard/rede-credenciada/detalhes/${id}`);
  };

  async function fetchPaginatedData(page = 1, limit = 10, search = "") {
    setIsLoading(true);

    try {
      let url = `/api/credenciado?page=${page}&limit=${limit}`;
      if (search) url += `&search=${encodeURIComponent(search)}`;

      Object.entries(filters).forEach(([key, value]) => {
        if (
          value !== "" &&
          value !== false &&
          value !== undefined &&
          value !== null
        ) {
          let paramKey = key;
          if (key === "placa_verde") paramKey = "atende_placa_verde";
          url += `&${encodeURIComponent(paramKey)}=${encodeURIComponent(
            value
          )}`;
        }
      });

      const response = await fetch(url);
      const result = await response.json();

      if (result.success && result.data) {
        setFilteredCredenciados(result.data.credenciados);
        setTotalItems(result.data.pagination.total);
        setTotalPages(result.data.pagination.pages);
      } else {
        toast.error("Erro ao carregar credenciados");
      }
    } catch (error) {
      console.error("Erro ao buscar credenciados:", error);
      toast.error("Falha ao carregar dados");
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    async function fetchCredenciadoData() {
      setIsLoading(true);
      // if (session?.contrato && !session.roles.includes("ADMIN")) {
      //   try {
      //     const response = await getCredenciadosByContratoId(session.contrato.id);
      //     if (response.success && response.data) {
      //       const credenciadoAction = response.data.credenciados as credenciado[];
      //       const filtered = credenciadoAction.filter((credenciado) =>
      //         credenciado.contratos.some((contrato) => contrato.contratoId === session?.contratoId)
      //       );
      //       setCredenciadoData(filtered);
      //     }
      //     setIsLoading(false);
      //   } catch (error) {
      //     setIsLoading(false);
      //     console.error("Erro ao buscar credenciados:", error);
      //   }
      // }

      // Para usuários admin, usamos a paginação do servidor
      if (isAdmin) {
        fetchPaginatedData(currentPage, itemsPerPage, searchTerm);
      } else {
        setIsLoading(false);
        setCredenciadoData(credenciados);
      }
    }

    fetchCredenciadoData();
  }, [session, credenciados, currentPage, itemsPerPage, searchTerm]);

  useEffect(() => {
    setFilteredCredenciados(credenciadoData);
  }, [credenciadoData]);

  // Handlers para paginação no servidor
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    if (isAdmin) {
      fetchPaginatedData(page, itemsPerPage, searchTerm);
    }
  };

  const handlePerPageChange = (perPage: number) => {
    setItemsPerPage(perPage);
    setCurrentPage(1); // Reset para primeira página
    if (isAdmin) {
      fetchPaginatedData(1, perPage, searchTerm);
    }
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset para primeira página
    if (isAdmin) {
      fetchPaginatedData(1, itemsPerPage, term);
    }
  };

  // Manter as funções de filtro originais
  const clearFilters = () => {
    setFilters({
      atividade_principal: "",
      cnpj: "",
      razao_social: "",
      nome_fantasia: "",
      email: "",
      telefone: "",
      celular: "",
      cidade: "",
      estado: "",
      ativo: false,
      optante: false,
      orcamentista: false,
      placa_verde: false,
      concessionaria: false,
      taxa_adm: 0,
    });
    setFilteredCredenciados(credenciadoData);
    setShowFilterDialog(false);
    router.refresh();
  };

  const applyFilters = () => {
    // Se for admin com paginação no servidor, aplicamos os filtros via API
    if (isAdmin) {
      setCurrentPage(1);
      fetchPaginatedData(1, itemsPerPage, searchTerm);
      setShowFilterDialog(false);
      return;
    }

    const filtered = credenciados.filter((credenciado) => {
      // Filter by text fields
      if (
        filters.atividade_principal &&
        !credenciado.informacoes[0]?.atividade_principal
          ?.toLowerCase()
          .includes(filters.atividade_principal.toLowerCase())
      ) {
        return false;
      }

      if (
        filters.cnpj &&
        !credenciado.informacoes[0]?.cnpj
          ?.toLowerCase()
          .includes(filters.cnpj.toLowerCase())
      ) {
        return false;
      }

      if (
        filters.razao_social &&
        !credenciado.informacoes[0]?.razao_social
          ?.toLowerCase()
          .includes(filters.razao_social.toLowerCase())
      ) {
        return false;
      }

      if (
        filters.nome_fantasia &&
        !credenciado.informacoes[0]?.nome_fantasia
          ?.toLowerCase()
          .includes(filters.nome_fantasia.toLowerCase())
      ) {
        return false;
      }

      if (
        filters.email &&
        !credenciado.contato?.email
          ?.toLowerCase()
          .includes(filters.email.toLowerCase())
      ) {
        return false;
      }

      if (
        filters.telefone &&
        !credenciado.contato?.telefone?.includes(filters.telefone)
      ) {
        return false;
      }

      if (
        filters.taxa_adm &&
        !credenciado.contratos.some(
          ({ contrato }) =>
            String(contrato?.taxa_admin) === String(filters.taxa_adm)
        )
      ) {
        return false;
      }

      if (
        filters.celular &&
        !credenciado.contato?.celular?.includes(filters.celular)
      ) {
        return false;
      }

      if (
        filters.cidade &&
        !credenciado.endereco?.cidade
          ?.toLowerCase()
          .includes(filters.cidade.toLowerCase())
      ) {
        return false;
      }

      if (filters.estado && credenciado.endereco?.estado !== filters.estado) {
        return false;
      }

      // Filter by boolean fields
      // Only filter if the checkbox is checked (true)
      if (filters.ativo && !credenciado.ativo) {
        return false;
      }

      if (filters.orcamentista && !credenciado.orcamentista) {
        return false;
      }

      if (filters.placa_verde && !credenciado.atende_placa_verde) {
        return false;
      }

      if (filters.concessionaria && !credenciado.concessionaria) {
        return false;
      }

      return true;
    });

    setFilteredCredenciados(filtered);
    toast.success(`${filtered.length} credenciados encontrados`);
    setShowFilterDialog(false);
  };

  const handleFilterChange = (field: string, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <>
      <DataTable
        data={filteredCredenciados}
        exportTo={true}
        columns={visibleColumns}
        handleRowClick={handleRowClick}
        isLoading={isLoading}
        showMapButton={true}
        showReportButton={true}
        onClick={() =>
          router.push("/dashboard/rede-credenciada/novo-credenciado")
        }
        reportType="credenciados"
        showSearch={false}
        serverSidePagination={{
          enabled: true,
          currentPage: currentPage,
          totalPages: totalPages,
          totalItems: totalItems,
          perPage: itemsPerPage,
          onPageChange: handlePageChange,
          onPerPageChange: handlePerPageChange,
          onSearch: handleSearch,
        }}
        showFilterButton={true}
        setShowFilterDialog={setShowFilterDialog}
      />
      <FilterCredenciado
        showFilterDialog={showFilterDialog}
        setShowFilterDialog={setShowFilterDialog}
        filters={filters}
        handleFilterChange={handleFilterChange}
        clearFilters={clearFilters}
        applyFilters={applyFilters}
      />
    </>
  );
}
