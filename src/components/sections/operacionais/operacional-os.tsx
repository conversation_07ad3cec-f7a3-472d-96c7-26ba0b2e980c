"use client";

import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { Checkbox } from "@/components/ui/checkbox";

import { useOS } from "@/context/os-context";
import { ColumnDef } from "@tanstack/react-table";
import { useRouter } from "next/navigation";

export const osColumns: ColumnDef<OS>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  // Centro de Custo (via veículo)
  {
    accessorKey: "centro_de_custo",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Centro de Custo" />,
    accessorFn: (row) => row.veiculo?.lotacao_veiculos?.centro_custo?.descricao || "Não vinculado",
  },
  // Placa do Veículo
  {
    accessorKey: "placa",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Placa" />,
    accessorFn: (row) => row.veiculo?.matricula || "Não informada",
  },
  // Marca do Veículo
  {
    accessorKey: "marca",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Marca" />,
    accessorFn: (row) => row.veiculo?.marca?.descricao || "Não especificada",
  },
  // Modelo do Veículo
  {
    accessorKey: "modelo",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Modelo" />,
    accessorFn: (row) => row.veiculo?.modelo?.descricao || "Não especificado",
  },
  // // Versão do Veículo
  // {
  //   accessorKey: "versao",
  //   header: ({ column }) => <DataTableColumnHeader column={column} title="Versão" />,
  //   accessorFn: (row) => row.veiculo?.versao?.descricao || "Não informada",
  // },
  // Valor de Peças Aprovado (via OS)
  {
    accessorKey: "valor_pecas_aprovado",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Valor Peças (R$)" />,
    accessorFn: (row) => row.minimun_orcament || 0, // Usando o campo minimun_orcamento da OS
    cell: ({ row }) => {
      const valor = parseFloat(row.getValue("valor_pecas_aprovado"));
      return valor.toLocaleString("pt-BR", {
        style: "currency",
        currency: "BRL",
      });
    },
  },
  // Valor de Serviços Aprovados (via OS)
  {
    accessorKey: "valor_servicos_aprovados",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Valor Serviços (R$)" />,
    accessorFn: (row) => (row.orcamento_individual ? row.valor_autorizado : 0), // Ajuste conforme a lógica de orçamento
    cell: ({ row }) => {
      const valor = parseFloat(row.getValue("valor_servicos_aprovados"));
      return valor.toLocaleString("pt-BR", {
        style: "currency",
        currency: "BRL",
      });
    },
  },
];

export function OperacionalOSTable() {
  const router = useRouter();
  const { ordensDeServico } = useOS();
  return (
    <DataTable
      data={ordensDeServico}
      exportTo={true}
      columns={osColumns}
      onClick={() => router.push("/dashboard/ordens-de-servico/nova-ordem")}
    />
  );
}
