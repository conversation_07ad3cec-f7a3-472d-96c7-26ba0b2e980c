"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { GetModeloVeiculoConfig } from "@/components/forms/inputConfig";
import { modeloSchema } from "@/components/forms/schemas";

import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { Checkbox } from "@/components/ui/checkbox";
import { useMarca } from "@/context/marca-context";
import { useModelo } from "@/context/modelo-context";
import { ColumnDef } from "@tanstack/react-table";
import { toast } from "sonner";
import { z } from "zod";

export const modeloColumn: ColumnDef<modelo>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "descricao",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Descrição" />
    ),
  },
  {
    accessorKey: "marca.descricao",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Marca" />
    ),
  },
  {
    accessorKey: "codigo_fipe",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Código FIPE" />
    ),
  },
  {
    accessorKey: "codigo_suiv",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Código SUIV" />
    ),
  },
];

export function ModeloTable() {
  const { modelos, setModelos } = useModelo();
  const { marcas } = useMarca();

  async function onNewModelo(values: z.infer<typeof modeloSchema>) {
    const response = await fetch("/api/modelo", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });

    if (!response.ok) {
      toast("Ops, algo deu errado", {
        description: "Houve um erro ao criar o modelo",
      });
      return;
    }

    const data = await response.json();
    setModelos((prev) => [...prev, data.data]);
    window.location.reload();
  }

  return (
    <DataTable
      data={modelos}
      newItem={{
        defaultValues: {
          marca: "",
          descricao: "",
          codigo_fipe: "",
          codigo_suiv: "",
        },
        fieldConfig: GetModeloVeiculoConfig([marcas], ['descricao']),
        schema: modeloSchema,
        name: "modelo",
      }}
      onNewItem={onNewModelo}
      exportTo={true}
      columns={modeloColumn}
    />
  );
}
