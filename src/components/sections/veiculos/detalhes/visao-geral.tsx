"use client";

import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { getMarkerLogoByPlate } from "@/serverActions/suivAction";
interface VisaoGeralProps {
  veiculo: veiculo | null;
}

export function VisaoGeral({ veiculo }: VisaoGeralProps) {

  if (!veiculo) {
    return <div>Carregando...</div>;
  }
  return (
    <div className="grid grid-cols-1 md:grid-cols-[70%_30%] gap-4 p-4 w-full">
      <div className="flex flex-col gap-4 w-full">
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Tipo de veículo</p>
                <p>{veiculo.tiposVeiculos?.descricao || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Placa</p>
                <p>{veiculo.placa || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Marca</p>
                <p>{veiculo.marca?.descricao || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Modelo</p>
                <p>{veiculo.modelo?.descricao || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Versão</p>
                <p>{veiculo.versao?.descricao ? veiculo.versaoId : "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">VIN (Chassi)</p>
                <p>{veiculo.vin || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">
                  Ano de fabricação
                </p>
                <p>{veiculo.ano_fab || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Ano do modelo</p>
                <p>{veiculo.ano_modelo || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Tipo de frota</p>
                <p>{veiculo.tipo_de_frota?.descricao || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Cor</p>
                <p>{veiculo.cor || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Odômetro</p>
                <p>{veiculo.odometro_atual || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Tag RFID</p>
                <p>{veiculo.tag_rfid || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Número do motor</p>
                <p>{veiculo.numero_do_motor || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Matrícula</p>
                <p>{veiculo.matricula || "N/A"}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <h3 className="text-lg font-semibold mb-4">Definições</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">
                  Número do câmbio
                </p>
                <p>{veiculo.definicoes?.numero_de_cambio || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Cilindradas</p>
                <p>{veiculo.definicoes?.cilindradas || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Potência</p>
                <p>{veiculo.definicoes?.potencia || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Carroceria</p>
                <p>{veiculo.definicoes?.carroceria || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Transmissão</p>
                <p>{veiculo.definicoes?.transmissao || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">
                  Quantidade de portas
                </p>
                <p>
                  {veiculo.definicoes?.quantidade_de_portas || "N/A"}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">
                  Quantidade de assentos
                </p>
                <p>
                  {veiculo.definicoes?.quantidade_de_assentos || "N/A"}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">
                  Quantidade de eixos
                </p>
                <p>
                  {veiculo.definicoes?.quantidade_de_eixos || "N/A"}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Litragem</p>
                <p>{veiculo.definicoes?.litragem || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Combustível</p>
                <p>
                  {veiculo.definicoes?.combustivel?.toString() ||
                    veiculo.combustivel?.toString() ||
                    "N/A"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-col gap-4">
        <Card>
          <CardContent className="p-4">
            <h3 className="text-lg font-semibold mb-4">Faturamento</h3>
            <div className="grid grid-cols-1 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Centro de custo</p>
                <p>
                  {veiculo.lotacao_veiculos?.centro_custo?.descricao || "N/A"}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Empenho</p>
                <p>{veiculo.faturamentoVeiculo?.empenho?.nota_empenho || "N/A"}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <h3 className="text-lg font-semibold mb-4">Fipe</h3>
            <div className="grid grid-cols-1 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Código Fipe</p>
                <p>{veiculo.codigo_fipe?.codigo_fipe || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Valor venal</p>
                <p>
                  R${" "}
                  {veiculo.valor_venal ||
                    veiculo.codigo_fipe?.valor_venal ||
                    "N/A"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card></Card>

        <Card>
          <CardContent className="p-4">
            <h3 className="text-lg font-semibold mb-4">Configurações</h3>
            <div className="grid grid-cols-1 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">
                  Status do veículo
                </p>
                <p>{veiculo.status || "N/A"}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
