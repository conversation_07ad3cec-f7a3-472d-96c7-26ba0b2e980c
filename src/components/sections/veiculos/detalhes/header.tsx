"use client"

import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { getMarkerLogoByPlate } from "@/serverActions/suivAction"
import Image from "next/image"
import { useEffect, useState } from "react"

interface HeaderProps {
  veiculoId: string
}

export function Header({ veiculoId }: HeaderProps) {
  const [veiculo, setVeiculo] = useState<veiculo | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [vehicleLogo, setVehicleLogo] = useState<string | null>(null);
  useEffect(() => {
    const fetchVehicleLogo = async () => {
      if (veiculo?.placa) {
        const cleanPlate = veiculo.placa.replace(/[^a-zA-Z0-9]/g, "");
        const response = await getMarkerLogoByPlate(cleanPlate);
        if (response) {
          setVehicleLogo(response.makerLogoUrl);
        }
      }
    };

    fetchVehicleLogo();
  }, [veiculo?.placa]);

  useEffect(() => {
    async function fetchVeiculo() {
      try {
        const response = await fetch(`/api/veiculos/${veiculoId}`)
        if (!response.ok) {
          throw new Error("Erro ao carregar veículo")
        }
        const data = await response.json()
        setVeiculo(data.veiculo)
      } catch (error) {
        setError("Não foi possível carregar os dados do veículo")
        console.error(error)
      } finally {
        setLoading(false)
      }
    }

    fetchVeiculo()
  }, [veiculoId])

  if (loading) {
    return (
      <Card className="p-6 animate-pulse">
        <div className="h-16 bg-muted rounded-md" />
      </Card>
    )
  }

  if (error || !veiculo) {
    return (
      <Card className="p-6">
        <div className="text-muted-foreground">
          {error || "Erro ao carregar dados do veículo"}
        </div>
      </Card>
    )
  }

  return (
    <Card className="p-6">
      <div className="flex items-start gap-6">
        <div className="relative w-16 h-16 flex-shrink-0">
            {vehicleLogo ? (
              <img
                src={vehicleLogo}
                alt={veiculo.marca?.descricao || "Logo"}
                className="w-full h-full object-contain rounded-md"
                width={64}
                height={64}
              />
            ) : (
              <div className="w-full h-full bg-muted rounded-md flex items-center justify-center text-muted-foreground text-4xl">
                {veiculo.marca?.descricao.charAt(0)}
              </div>
            )}
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-3 mb-2">
            <h1 className="text-2xl font-semibold truncate">
              {veiculo.modelo?.descricao} ({veiculo.ano_fab})
            </h1>
            <Badge variant={veiculo.status === "Ativo" ? "default" : "secondary"}>
              {veiculo.status}
            </Badge>
          </div>
        </div>
      </div>
    </Card>
  )
} 