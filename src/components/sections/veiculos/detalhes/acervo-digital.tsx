"use client";

import { Card } from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { useEffect, useState } from "react"

interface AcervoDigitalProps {
  veiculoId: string
}

interface Arquivo {
  id: string
  nome: string
  tipo: string
  tamanho: number
  dataUpload: string
}

export function AcervoDigital({ veiculoId }: AcervoDigitalProps) {
  const [arquivos, setArquivos] = useState<Arquivo[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchArquivos() {
      try {
        const response = await fetch(`/api/veiculos/${veiculoId}/arquivos`)
        if (!response.ok) {
          throw new Error("Erro ao carregar arquivos")
        }
        const data = await response.json()
        setArquivos(data.veiculo)
      } catch (error) {
        setError("Não foi possível carregar os arquivos")
        console.error(error)
      } finally {
        setLoading(false)
      }
    }

    fetchArquivos()
  }, [veiculoId])

  function formatFileSize(bytes: number) {
    if (bytes === 0) return "0 B"
    const k = 1024
    const sizes = ["B", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
  }

  function formatDate(date: string) {
    return new Date(date).toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    })
  }

 

  return (
    <div className="space-y-4">
      <Card className="p-6">

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nome do arquivo</TableHead>
                <TableHead>Tipo</TableHead>
                <TableHead>Tamanho</TableHead>
                <TableHead>Data de upload</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center">
                    Carregando...
                  </TableCell>
                </TableRow>
              ) : error ? (
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center text-muted-foreground">
                    {error}
                  </TableCell>
                </TableRow>
              ) : arquivos.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center text-muted-foreground">
                    Nenhum arquivo encontrado.
                  </TableCell>
                </TableRow>
              ) : (
                arquivos.map((arquivo) => (
                  <TableRow key={arquivo.id}>
                    <TableCell>{arquivo.nome}</TableCell>
                    <TableCell>{arquivo.tipo}</TableCell>
                    <TableCell>{formatFileSize(arquivo.tamanho)}</TableCell>
                    <TableCell>{formatDate(arquivo.dataUpload)}</TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </Card>
    </div>
  )
} 