"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import Link from "next/link";

interface PlanoRevisaoProps {
  veiculo: veiculo;
}

export function PlanoRevisao({ veiculo }: PlanoRevisaoProps) {
  if (!veiculo) {
    return <div className="w-full p-4">Carregando plano de revisão...</div>;
  }

  return (
    <div className="w-full space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Plano de Revisão</h2>
        <Button asChild>
          <Link href={`/dashboard/veiculos/${veiculo.id}/adicionar-revisao`}>
            Adicionar Revisão
          </Link>
        </Button>
      </div>

      {veiculo.manutencoes.length === 0 ? (
        <div className="text-center p-8 text-muted-foreground">Nenhuma revisão cadastrada</div>
      ) : (
        <Accordion type="multiple" className="space-y-2">
          {veiculo.manutencoes.map((manutencao: any) => (
            <AccordionItem key={manutencao.id} value={manutencao.id.toString()}>
              <AccordionTrigger className="flex justify-between items-center p-4 rounded-lg border-2">
                <div className="font-medium text-lg">
                  Revisão de {new Intl.NumberFormat("pt-BR").format(manutencao.km)}km ou{" "}
                  {manutencao.periodo} meses
                </div>
                <div className="flex flex-col items-end">
                  <div className="font-medium text-lg">
                    Valor estimado:{" "}
                    {new Intl.NumberFormat("pt-BR", {
                      style: "currency",
                      currency: "BRL",
                    }).format(manutencao.valor / 100)}
                  </div>
                  <Button size="sm" variant="outline" asChild>
                    <Link href={`/dashboard/veiculos/editar-revisao/${manutencao.id}`}>Editar</Link>
                  </Button>
                </div>
              </AccordionTrigger>
              <AccordionContent className="p-4">
                <h4 className="font-medium mb-2">Itens a serem inspecionados:</h4>
                <ul className="list-disc list-inside text-sm ">
                  {manutencao.itens.split(";").map((item: string, i: number) => (
                    <li key={i}>{item.trim()}</li>
                  ))}
                </ul>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      )}
    </div>
  );
}
