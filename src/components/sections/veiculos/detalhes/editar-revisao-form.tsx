"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";

import { updateManutencaoAction, getManutencaoByIdAction } from "@/serverActions/manutencaoAction";
import { CurrencyInput } from "@/components/inputs/currency-input";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

const formSchema = z.object({
  periodo: z.string().min(1, "O período é obrigatório").regex(/^\d+$/, "Deve ser um número inteiro"),
  km: z.string().min(1, "A quilometragem é obrigatória").regex(/^\d+$/, "Deve ser um número inteiro"),
  itens: z.string().min(1, "Liste os itens que serão inspecionados"),
  valor: z.string().min(1, "Valor estimado é obrigatório"),
});

interface FormValues extends z.infer<typeof formSchema> {}

interface EditarRevisaoFormProps {
  manutencaoId: string;
}

export function EditarRevisaoForm({ manutencaoId }: EditarRevisaoFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [veiculoId, setVeiculoId] = useState<string>("");

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      periodo: "",
      km: "",
      itens: "",
      valor: "",
    },
  });

  const { reset } = form;

  useEffect(() => {
    async function loadManutencao() {
      setLoading(true);
      const res = await getManutencaoByIdAction(manutencaoId);
      console.log(res);
      if (!res.success) {
        toast.error(res.error || "Erro ao carregar revisão");
        setLoading(false);
        return;
      }
          const m = res.data.data;
          setVeiculoId(m.veiculoId);
          reset({
            periodo: String(m.periodo),
            km: String(m.km),
            itens: (m.itens ?? "").replace(/;/g, "\n"),
            valor: m.valor != null 
              ? m.valor.toFixed(2).replace(".", ",") 
              : "",
          });
          setLoading(false);
    }
    loadManutencao();
  }, [manutencaoId, reset]);

  async function onSubmit(values: FormValues) {
    try {
      const result = await updateManutencaoAction(manutencaoId, {
        periodo: parseInt(values.periodo),
        km: parseInt(values.km),
        itens: values.itens.replace(/\n/g, ";"),
        valor: parseFloat(values.valor.replace(",", ".")),
        veiculoId,
      });
      if (!result.success) throw new Error(result.error);
      toast.success("Manutenção atualizada com sucesso!");
    window.location.href = `/dashboard/veiculos/${veiculoId}/detalhes`;
      router.refresh();
    } catch (err) {
      console.error(err);
      toast.error("Falha ao atualizar revisão. Tente novamente.");
    }
  }

  if (loading) {
    return <div className="w-full p-4 text-center">Carregando revisão...</div>;
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="periodo"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Período (meses)</FormLabel>
                <FormControl>
                  <Input {...field} type="number" min="1" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="km"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Quilometragem</FormLabel>
                <FormControl>
                  <Input {...field} type="number" min="0" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="valor"
          render={({ field: { value, onChange, ...rest } }) => (
            <FormItem>
              <FormLabel>Valor estimado (R$)</FormLabel>
              <FormControl>
                <CurrencyInput
                  {...rest}
                  value={value ? Number(value.replace(",", ".")) : null}
                  onChange={(val) =>
                    onChange(val !== null ? String(val).replace(".", ",") : "")
                  }
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="itens"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Itens a serem inspecionados</FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  className="min-h-[100px]"
                  placeholder="Um item por linha"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-4">
          <Button variant="outline" onClick={() => router.back()} type="button">
            Cancelar
          </Button>
          <Button type="submit">Atualizar Revisão</Button>
        </div>
      </form>
    </Form>
  );
}