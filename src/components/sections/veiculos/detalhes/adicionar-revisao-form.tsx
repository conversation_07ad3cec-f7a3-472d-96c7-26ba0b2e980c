"use client";

import { CurrencyInput } from "@/components/inputs/currency-input";
import { DateInput } from "@/components/inputs/date-input";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import * as z from "zod";

const formSchema = z.object({
  periodo: z
    .string()
    .min(1, "O período é obrigatório")
    .regex(/^\d+$/, "Deve ser um número inteiro"),
  km: z
    .string()
    .min(1, "A quilometragem é obrigatória")
    .regex(/^\d+$/, "Deve ser um número inteiro"),
  itens: z.string().min(1, "Liste os itens que serão inspecionados"),
  valor: z.string().min(1, "Valor estimado é obrigatório"),
});

interface AdicionarRevisaoFormProps {
  veiculoId: string;
}

export function AdicionarRevisaoForm({ veiculoId }: AdicionarRevisaoFormProps) {
  const router = useRouter();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      periodo: "",
      km: "",
      itens: "",
      valor: "",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      const response = await fetch("/api/manutencao", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          periodo: parseInt(values.periodo),
          km: parseInt(values.km),
          itens: values.itens.replace(/\n/g, ";"),
          valor: parseFloat(values.valor.replace(",", ".")),
          veiculoId,
        }),
      });

      if (!response.ok) {
        throw new Error("Erro ao salvar manutenção");
      }

      toast.success("Manutenção adicionada com sucesso!");
      router.push(`/dashboard/veiculos/veiculos/${veiculoId}/detalhes`);
      router.refresh();
    } catch (error) {
      toast.error("Erro ao adicionar manutenção. Tente novamente.");
      console.error(error);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="periodo"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Período (meses)</FormLabel>
                <FormControl>
                  <Input placeholder="Ex: 12 (para 12 meses)" type="number" min="1" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="km"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Quilometragem</FormLabel>
                <FormControl>
                  <Input placeholder="Ex: 10000" type="number" min="0" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="valor"
          render={({ field: { value, onChange, ...rest } }) => (
            <FormItem>
              <FormLabel>Valor estimado (R$)</FormLabel>
              <FormControl>
                <CurrencyInput
                  placeholder="Ex: 1595,00"
                  value={value ? Number(value) : null}
                  onChange={(val: number | null) => onChange(val ? String(val) : "")}
                  {...rest}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="itens"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Itens a serem inspecionados</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Digite um item por linha (serão separados por ponto e vírgula)"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Cancelar
          </Button>
          <Button type="submit">Salvar Revisão</Button>
        </div>
      </form>
    </Form>
  );
}
