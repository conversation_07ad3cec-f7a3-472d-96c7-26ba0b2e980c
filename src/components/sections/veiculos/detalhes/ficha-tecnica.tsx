"use client";

import { useEffect, useState } from "react";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import api from "@/service/api";

interface FichaTecnicaProps {
  veiculoId: string;
}

interface EspecificacoesTecnicas {
  aerodinamica: {
    area_frontal: string;
    area_frontal_corrigida: string;
    coeficiente_arrasto: string;
  };
  bateria: {
    capacidade: string;
    lado_polo_positivo: string;
    Kg: string;
  };
  consumo: {
    rodoviario: string;
    urbano: string;
  };
  desempenho: {
    velocidade_maxima: string;
    aceleracao_0_100: string;
  };
  dimensoes: {
    altura: string;
    angulo_central: string;
    angulo_entrada: string;
    angulo_saida: string;
    bitola_dianteira: string;
    bitola_traseira: string;
    carga_util: string;
    comprimento: string;
    distancia_entre_eixos: string;
    inclinacao_lateral_maxima: string;
    largura: string;
    peso: string;
    porta_malas: string;
    rampa_maxima: string;
    reboque_com_freio: string;
    reboque_sem_freio: string;
    tanque_combustivel: string;
    travessia_agua: string;
    vao_livre_solo: string;
  };
  direcao: {
    assistencia: string;
    diametro_minimo_giro: string;
    pneus_dianteiros: string;
    pneus_traseiros: string;
  };
  freios: {
    dianteiros: string;
    traseiros: string;
  };
  geral: {
    configuracao: string;
    garantia: string;
    ocupantes: string;
    portas: string;
    porte: string;
    procedencia: string;
    geracao: string;
    observacoes: string;
  };
  iluminacao: {
    farol_baixo: string;
    farol_alto: string;
    farol_auxiliar: string;
  };
  lubrificante: {
    capacidade_carter: string;
    viscosidade: string;
    tipo_oleo: string;
  };
  motor: {
    alimentacao: string;
    aspiracao: string;
    cilindrada: string;
    cilindros: string;
    codigo_motor: string;
    comando_valvulas: string;
    combustivel: string;
    curso__pistoes: string;
    diametro_cilindro: string;
    disposicao: string;
    instalacao: string;
    peso_potencia: string;
    razao_comresao: string;
    peso_torque: string;
    potencia_especifica: string;
    potencia_maxima: string;
    torque_especifico: string;
    torque_maximo: string;
    valvulas_por_cilindro: string;
    variacao_comando: string;


  };
  suspensao: {
    dianteira: string;
    traseira: string;
  };
  transmissao: {
    cambio: string;
    tracao: string;
    acoplamento: string;
    velocidades: string;
  };
}

export function FichaTecnica({ veiculoId }: FichaTecnicaProps) {
  const [especificacoes, setEspecificacoes] = useState<EspecificacoesTecnicas | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    async function fetchEspecificacoes() {
      try {
        const response = await api.get(`/veiculos/${veiculoId}/ficha-tecnica`);
        setEspecificacoes(response.data);
      } catch (error) {
        console.error("Erro ao buscar especificações técnicas:", error);
      }
    }

    fetchEspecificacoes();
  }, [veiculoId]);

  if (!especificacoes) {
    return <div>Carregando...</div>;
  }

  const renderEspecificacao = (label: string, value: string) => (
    <div className="flex justify-between py-2 border-b">
      <span className="text-muted-foreground">{label}</span>
      <span>{value}</span>
    </div>
  );

  return (
    <div className="w-full p-4">
      <div className="flex items-center gap-2 mb-6">
        <div className="relative flex-1">
          <Input
            type="text"
            placeholder="Busca rápida"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
          <Search className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
        </div>
      </div>

      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value="aerodinamica">
          <AccordionTrigger>Aerodinâmica</AccordionTrigger>
          <AccordionContent>
            {renderEspecificacao("Área frontal (A)", especificacoes.aerodinamica.area_frontal)}
            {renderEspecificacao("Área frontal corrigida", especificacoes.aerodinamica.area_frontal_corrigida)}
            {renderEspecificacao("Coeficiente de arrasto (Cx)", especificacoes.aerodinamica.coeficiente_arrasto)}
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="consumo">
          <AccordionTrigger>Consumo</AccordionTrigger>
          <AccordionContent>
            {renderEspecificacao("Rodoviário", especificacoes.consumo.rodoviario)}
            {renderEspecificacao("Urbano", especificacoes.consumo.urbano)}
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="desempenho">
          <AccordionTrigger>Desempenho</AccordionTrigger>
          <AccordionContent>
            {renderEspecificacao("Velocidade máxima", especificacoes.desempenho.velocidade_maxima)}
            {renderEspecificacao("Aceleração 0-100 km/h", especificacoes.desempenho.aceleracao_0_100)}
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="dimensoes">
          <AccordionTrigger>Dimensões</AccordionTrigger>
          <AccordionContent>
            {renderEspecificacao("Altura", especificacoes.dimensoes.altura)}
            {renderEspecificacao("Ângulo central", especificacoes.dimensoes.angulo_central)}
            {renderEspecificacao("Ângulo de entrada", especificacoes.dimensoes.angulo_entrada)}
            {renderEspecificacao("Ângulo de saída", especificacoes.dimensoes.angulo_saida)}
            {renderEspecificacao("Bitola dianteira", especificacoes.dimensoes.bitola_dianteira)}
            {renderEspecificacao("Bitola traseira", especificacoes.dimensoes.bitola_traseira)}
            {renderEspecificacao("Carga útil", especificacoes.dimensoes.carga_util)}
            {renderEspecificacao("Comprimento", especificacoes.dimensoes.comprimento)}
            {renderEspecificacao("Distância entre-eixos", especificacoes.dimensoes.distancia_entre_eixos)}
            {renderEspecificacao("Inclinação lateral máxima", especificacoes.dimensoes.inclinacao_lateral_maxima)}
            {renderEspecificacao("Largura", especificacoes.dimensoes.largura)}
            {renderEspecificacao("Peso", especificacoes.dimensoes.peso)}
            {renderEspecificacao("Porta-malas", especificacoes.dimensoes.porta_malas)}
            {renderEspecificacao("Rampa máxima", especificacoes.dimensoes.rampa_maxima)}
            {renderEspecificacao("Reboque com freio", especificacoes.dimensoes.reboque_com_freio)}
            {renderEspecificacao("Reboque sem freio", especificacoes.dimensoes.reboque_sem_freio)}
            {renderEspecificacao("Tanque de combustível", especificacoes.dimensoes.tanque_combustivel)}
            {renderEspecificacao("Travessia de água", especificacoes.dimensoes.travessia_agua)}
            {renderEspecificacao("Vão livre solo", especificacoes.dimensoes.vao_livre_solo)}
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="direcao">
          <AccordionTrigger>Direção</AccordionTrigger>
          <AccordionContent>
            {renderEspecificacao("Assistência", especificacoes.direcao.assistencia)}
            {renderEspecificacao("Diâmetro mínimo de giro", especificacoes.direcao.diametro_minimo_giro)}
            {renderEspecificacao("Pneus dianteiros", especificacoes.direcao.pneus_dianteiros)}
            {renderEspecificacao("Pneus traseiros", especificacoes.direcao.pneus_traseiros)}
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="freios">
          <AccordionTrigger>Freios</AccordionTrigger>
          <AccordionContent>
            {renderEspecificacao("Dianteiros", especificacoes.freios.dianteiros)}
            {renderEspecificacao("Traseiros", especificacoes.freios.traseiros)}
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="geral">
          <AccordionTrigger>Geral</AccordionTrigger>
          <AccordionContent>
            {renderEspecificacao("Configuração", especificacoes.geral.configuracao)}
            {renderEspecificacao("Garantia", especificacoes.geral.garantia)}
            {renderEspecificacao("Ocupantes", especificacoes.geral.ocupantes)}
            {renderEspecificacao("Portas", especificacoes.geral.portas)}
            {renderEspecificacao("Porte", especificacoes.geral.porte)}
            {renderEspecificacao("Procedência", especificacoes.geral.procedencia)}
            {renderEspecificacao("Geração", especificacoes.geral.geracao)}
            {renderEspecificacao("Observações", especificacoes.geral.observacoes)}
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="iluminacao">
          <AccordionTrigger>Iluminação</AccordionTrigger>
          <AccordionContent>
            {renderEspecificacao("Farol Baixo", especificacoes.iluminacao.farol_baixo)}
            {renderEspecificacao("Farol Alto", especificacoes.iluminacao.farol_alto)}
            {renderEspecificacao("Farol Auxiliar", especificacoes.iluminacao.farol_auxiliar)}
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="lubrificante">
          <AccordionTrigger>Lubrificante</AccordionTrigger>
          <AccordionContent>
            {renderEspecificacao("Capacidade de Cárter", especificacoes.lubrificante.capacidade_carter)}
            {renderEspecificacao("Viscosidade", especificacoes.lubrificante.viscosidade)}
            {renderEspecificacao("Tipo de Óleo", especificacoes.lubrificante.tipo_oleo)}
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="motor">
          <AccordionTrigger>Motor</AccordionTrigger>
          <AccordionContent>
            {renderEspecificacao("Alimentação", especificacoes.motor.alimentacao)}
            {renderEspecificacao("Aspiração", especificacoes.motor.aspiracao)}
            {renderEspecificacao("Cilindrada", especificacoes.motor.cilindrada)}
            {renderEspecificacao("Cilindros", especificacoes.motor.cilindros)}
            {renderEspecificacao("Código do motor", especificacoes.motor.codigo_motor)}
            {renderEspecificacao("Comando de válvulas", especificacoes.motor.comando_valvulas)}
            {renderEspecificacao("Combustível", especificacoes.motor.combustivel)}
            {renderEspecificacao("Variação do comando", especificacoes.motor.variacao_comando)}
            {renderEspecificacao("Curso dos pistões", especificacoes.motor.curso__pistoes)}
            {renderEspecificacao("Diâmetro do cilindro", especificacoes.motor.diametro_cilindro)}
            {renderEspecificacao("Disposição", especificacoes.motor.disposicao)}
            {renderEspecificacao("Instalação", especificacoes.motor.instalacao)}
            {renderEspecificacao("Peso/potência", especificacoes.motor.peso_potencia)}
            {renderEspecificacao("Razão de compressão", especificacoes.motor.razao_comresao)}
            {renderEspecificacao("Peso/torque", especificacoes.motor.peso_torque)}
            {renderEspecificacao("Potência específica", especificacoes.motor.potencia_especifica)}
            {renderEspecificacao("Potência máxima", especificacoes.motor.potencia_maxima)}
            {renderEspecificacao("Torque específico", especificacoes.motor.torque_especifico)}
            {renderEspecificacao("Torque máximo", especificacoes.motor.torque_maximo)}
            {renderEspecificacao("Valvulas por cilindro", especificacoes.motor.valvulas_por_cilindro)}
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="suspensao">
          <AccordionTrigger>Suspensão</AccordionTrigger>
          <AccordionContent>
            {renderEspecificacao("Dianteira", especificacoes.suspensao.dianteira)}
            {renderEspecificacao("Traseira", especificacoes.suspensao.traseira)}
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="transmissao">
          <AccordionTrigger>Transmissão</AccordionTrigger>
          <AccordionContent>
            {renderEspecificacao("Câmbio", especificacoes.transmissao.cambio)}
            {renderEspecificacao("Tração", especificacoes.transmissao.tracao)}
            {renderEspecificacao("Acoplamento", especificacoes.transmissao.acoplamento)}
            {renderEspecificacao("Velocidades", especificacoes.transmissao.velocidades)}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
} 