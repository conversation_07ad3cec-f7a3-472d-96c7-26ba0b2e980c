"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { formatDistanceToNow } from "date-fns"
import { ptBR } from "date-fns/locale"
import { useEffect, useState } from "react"
import { Loader2 } from "lucide-react"

interface AtividadesProps {
  veiculoId: string
}

interface Atividade {
  id: string
  usuario: {
    nome: string
    avatar?: string
  }
  descricao: string
  data: string
  tipo: "plano_revisao" | "ficha_tecnica" | "veiculo"
}

export function Atividades({ veiculoId }: AtividadesProps) {
  const [atividades, setAtividades] = useState<Atividade[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchAtividades() {
      try {
        const response = await fetch(`/api/veiculos/${veiculoId}/atividades`)
        if (!response.ok) {
          throw new Error("Erro ao carregar atividades")
        }
        const data = await response.json()
        setAtividades(data)
      } catch (error) {
        setError("Não foi possível carregar as atividades")
        console.error(error)
      } finally {
        setLoading(false)
      }
    }

    fetchAtividades()
  }, [veiculoId])

  function getInitials(name: string) {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
  }

  return (
    <div className="space-y-4">
      <Card className="p-6">
        
        {loading ? (
          <div className="flex items-center justify-center h-[400px]">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-[400px] text-muted-foreground">
            {error}
          </div>
        ) : atividades.length === 0 ? (
          <div className="flex items-center justify-center h-[400px] text-muted-foreground">
            Nenhuma atividade registrada
          </div>
        ) : (
          <ScrollArea className="h-[400px] pr-4">
            <div className="space-y-8">
              {atividades.map((atividade) => (
                <div key={atividade.id} className="flex gap-4">
                  <Avatar className="h-10 w-10">
                    {atividade.usuario.avatar && (
                      <AvatarImage src={atividade.usuario.avatar} alt={atividade.usuario.nome} />
                    )}
                    <AvatarFallback>{getInitials(atividade.usuario.nome)}</AvatarFallback>
                  </Avatar>
                  <div className="flex flex-col">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{atividade.usuario.nome}</span>
                      <span className="text-sm text-muted-foreground">
                        {formatDistanceToNow(new Date(atividade.data), {
                          addSuffix: true,
                          locale: ptBR
                        })}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground">{atividade.descricao}</p>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </Card>
    </div>
  )
} 