"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { GetVeiculoConfig } from "@/components/forms/inputConfig";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { useMarca } from "@/context/marca-context";
import { useModelo } from "@/context/modelo-context";
import { useTipoDeFrota } from "@/context/tipo-de-frota-context";
import { useTipoDeVeiculo } from "@/context/tipo-de-veiculo-context";
import { useVeiculos } from "@/context/veiculos-context";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { veiculoSchema } from "@/components/forms/schemas";
import { useVersao } from "@/context/versao-context";
import { Card } from "@/components/ui/card";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { useEmpenho } from "@/context/empenho-context";
import { useEffect, useMemo, useState } from "react";
import { getServerSession } from "@/lib/auth/server-session";
import { Session } from "@/lib/auth/types";
import { Loader2 } from "lucide-react";
import { getRevisionPlan } from "@/serverActions/suivAction";
import { getVehicleByPlateWithCache } from "@/service/suivCache.service";

export function VeiculoForm({ veiculoId }: { veiculoId?: string }) {
  const router = useRouter();
  const { veiculos, setVeiculos } = useVeiculos();
  const { versoes } = useVersao();
  const { marcas } = useMarca();
  const { modelos } = useModelo();
  const { tiposDeVeiculo } = useTipoDeVeiculo();
  const { tiposDeFrota } = useTipoDeFrota();
  const { centrosDeCusto } = useCentroDeCusto();
  const { empenhos } = useEmpenho();

  const [selectedMarcaId, setSelectedMarcaId] = useState<string | null>(null);
  const [selectedModeloId, setSelectedModeloId] = useState<string | null>(null);
  const [sessionData, setSessionData] = useState<Session | undefined>();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingVeiculo, setIsLoadingVeiculo] = useState(!!veiculoId);

  const isEditMode = !!veiculoId;
  const form = useForm<z.infer<typeof veiculoSchema>>({
    resolver: zodResolver(veiculoSchema),
    defaultValues: defaultValues.veiculoSchema as any,
  });

  useEffect(() => {
    async function fetchSessionData() {
      const response = await getServerSession();
      setSessionData(response);
    }
    fetchSessionData();
  }, []);

  // Fetch veículo data when in edit mode
  useEffect(() => {
    if (isEditMode) {
      const fetchVeiculoData = async () => {
        try {
          setIsLoadingVeiculo(true);
          const res = await fetch(`/api/veiculos/${veiculoId}`);
          if (!res.ok) {
            throw new Error("Erro ao buscar dados do veículo");
          }

          const data = await res.json();
          const veiculo = data.veiculo;

          // Preencher formulário com dados existentes
          form.reset({
            // Campos principais
            marcaId: veiculo.marcaId || "",
            modeloId: veiculo.modeloId || "",
            versaoId: veiculo.versaoId || "",
            tipo_de_veiculoId: veiculo.tipoDeVeiculoId || "",
            tipo_de_frotaId: veiculo.tipoDeFrotaId || "",
            renovam: veiculo.renavam || "",
            vin: veiculo.chassi || "",
            numero_do_motor: veiculo.numero_do_motor || "",
            ano_de_fabricacao: veiculo.ano_fab || "",
            ano_do_modelo: veiculo.ano_modelo || "",
            cor: veiculo.cor || "",
            odometro_atual: veiculo.odometro_atual || 0,
            matricula: veiculo.matricula || "",
            tag_rfid: veiculo.tag_rfid || null,
            data_compra: veiculo.data_compra || "",
            data_cedencia: veiculo.data_cedencia || "",
            placa: veiculo.placa || "",
            status: veiculo.status || "Ativo",

            // Objeto de combustível
            combustivel: {
              capacidade_do_tanque: veiculo.combustivel?.capacidade_do_tanque || "",
              tipos_de_combustiveis: veiculo.combustivel?.tipos_de_combustiveis || ["Gasolina"],
            },

            // Objeto de definições
            definicoes: {
              numero_de_cambio: veiculo.definicoes?.numero_de_cambio || "",
              cilindradas: veiculo.definicoes?.cilindradas || undefined,
              potencia: veiculo.definicoes?.potencia || "",
              segmento: veiculo.definicoes?.segmento || "",
              carroceria: veiculo.definicoes?.carroceria || "",
              transmissao: veiculo.definicoes?.transmissao || "",
              quantidade_de_portas: veiculo.definicoes?.quantidade_de_portas || undefined,
              quantidade_de_assentos: veiculo.definicoes?.quantidade_de_assentos || undefined,
              quantidade_de_eixos: veiculo.definicoes?.quantidade_de_eixos || undefined,
              numero_de_valvulas: veiculo.definicoes?.numero_de_valvulas || "",
              litragem: veiculo.definicoes?.litragem || undefined,
              combustivel: veiculo.definicoes?.combustivel || "",
              origem_do_veiculo: veiculo.definicoes?.origem_do_veiculo || "nacional",
            },

            // Objeto de lotação
            lotacao: {
              centro_de_custoId: veiculo.lotacao_veiculos?.centro_custoID || "",
              estado: veiculo.lotacao_veiculos?.estado || "",
              cidade: veiculo.lotacao_veiculos?.cidade || "",
            },

            // Objeto de faturamento
            faturamento: {
              centro_custoId: veiculo.lotacao_veiculos?.centro_custoID || "",
              empenhoId: veiculo.empenhoId || "",
            },

            // Código FIPE e valor venal
            fipe: {
              codigo_fipe: veiculo.codigo_fipe?.codigo_fipe || "",
              valor_venal: veiculo.codigo_fipe?.valor_venal?.toString() || "",
            },
          });

          // Atualizar IDs selecionados para filtros
          setSelectedMarcaId(veiculo.marcaId);
          setSelectedModeloId(veiculo.modeloId);
        } catch (error) {
          console.error("Erro ao buscar dados do veículo:", error);
          toast.error("Erro ao carregar dados do veículo");
        } finally {
          setIsLoadingVeiculo(false);
        }
      };

      fetchVeiculoData();
    }
  }, [isEditMode, veiculoId, form]);

  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "marcaId") {
        setSelectedMarcaId(value.marcaId as string);
        // Limpar o modelo quando a marca mudar
        if (!isLoadingVeiculo) {
          form.setValue("modeloId", "");
          form.setValue("versaoId", "");
        }
      } else if (name === "modeloId") {
        setSelectedModeloId(value.modeloId as string);
        // Limpar a versão quando o modelo mudar
        if (!isLoadingVeiculo) {
          form.setValue("versaoId", "");
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form, isLoadingVeiculo]);

  const filteredModelos = useMemo(() => {
    return selectedMarcaId
      ? modelos.filter((modelo) => modelo.marcaId === selectedMarcaId)
      : modelos;
  }, [selectedMarcaId, modelos]);

  const filteredVersoes = useMemo(() => {
    if (!Array.isArray(versoes)) return [];

    return selectedModeloId
      ? versoes.filter((versao) => versao.modeloId === selectedModeloId)
      : versoes;
  }, [selectedModeloId, versoes]);

  const undo = async (id: string) => {
    const res = await fetch(`/api/veiculos/${id}`, { method: "DELETE" });
    if (!res.ok) {
      toast("Ops, houve um erro", {
        description: "Não foi possível cancelar a criação do veículo",
      });
      return;
    }
    toast("Tudo certo!", {
      description: "O veículo foi removido com sucesso",
    });
  };

  async function onSubmit(values: z.infer<typeof veiculoSchema>) {
    if (!values.tipo_de_veiculoId) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Tipo de veículo",
      });
      return;
    }
    if (!values.marcaId) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Marca",
      });
      return;
    }
    if (!values.modeloId) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Modelo",
      });
      return;
    }
    // if (!values.versaoId) {
    //   toast("Preencha todos os campos obrigatórios!", {
    //     description: "Campo vazio: Versão",
    //   });
    //   return;
    // }

    if (!values.tipo_de_frotaId) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Tipo de frota",
      });
      return;
    }
    if (!values.lotacao.centro_de_custoId) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Centro de Custo",
      });
      return;
    }
    if (!values.lotacao.estado) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Estado",
      });
      return;
    }
    if (!values.lotacao.cidade) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Cidade",
      });
      return;
    }
    console.log("passou aqui 7");
    setIsLoading(true);

    try {
      const url = isEditMode ? `/api/veiculos/${veiculoId}` : "/api/veiculos";
      const method = isEditMode ? "PUT" : "POST";
      const res = await fetch(url, {
        method,
        headers: {
          "Content-type": "application/json",
        },
        body: JSON.stringify({ ...values, tag_rfid: values.tag_rfid || null }),
      });
      console.log("Resposta:", res);
      if (!res.ok) {
        toast("Ops, alguma coisa deu errado", {
          description: `Houve um erro na ${isEditMode ? "atualização" : "criação"} do veículo`,
        });
        return;
      }

      const data = await res.json();

      const veiculo = data.data as veiculo;

      if (isEditMode) {
        setVeiculos((prev) => prev.map((v) => (v.id === veiculoId ? data.data : v)));
        toast("Sucesso!", {
          description: "O veículo foi atualizado com sucesso",
        });
      } else {
        setVeiculos((prev) => [...prev, data.data]);

        if (!veiculo?.placa) return;

        try {
          const cleanPlate = veiculo.placa.replace(/[^a-zA-Z0-9]/g, "");
          const response = await getVehicleByPlateWithCache(cleanPlate);
          const revisionPlan = await getRevisionPlan(
            response.suivDataCollection[0].versionId,
            response.yearModel
          );
          if (revisionPlan) {
            // Persistir cada item do plano de revisão na nossa API, evitando duplicados por km
            for (const plan of revisionPlan) {
              const alreadyExists = veiculo.manutencoes.some((m: any) => m.km === plan.kilometers);
              if (alreadyExists) continue;

              try {
                await fetch("/api/manutencao", {
                  method: "POST",
                  headers: { "Content-Type": "application/json" },
                  body: JSON.stringify({
                    periodo: plan.months,
                    km: plan.kilometers,
                    itens: plan.inspections.map((i: any) => i.description).join(";"),
                    valor: 0,
                    veiculoId: veiculo.id,
                  }),
                });
              } catch (err) {
                console.error("Erro ao persistir revisão:", err);
                // Log the error but do not block the form submission
              }
            }
          }
        } catch (error) {
          console.error("Erro ao buscar plano de revisão:", error);
          // Log the error but do not block the form submission
        }

        toast("Sucesso!", {
          description: "O veículo foi criado com sucesso",
          action: <Button onClick={async () => await undo(data.data.id)}>Cancelar criação</Button>,
        });
      }

      window.location.href = "/dashboard/veiculos/veiculos";
    } catch (error) {
      console.error("Erro ao processar formulário:", error);
      toast.error("Ocorreu um erro inesperado");
    } finally {
      setIsLoading(false);
      window.location.href = "/dashboard/veiculos/veiculos";
    }
  }
  let centrosCustos: centro_de_custo[] = [];
  if (sessionData && sessionData.unidade_filha_id) {
    centrosCustos = centrosDeCusto
      .flatMap((centro) => centro.centro_custos_filhos)
      .filter((centro) => {
        return centro.id === sessionData.unidade_filha_id;
      });
  } else {
    centrosCustos = centrosDeCusto.flatMap((centro) => centro.centro_custos_filhos);
  }

  let empenhoData: empenho[] = [];
  if (sessionData && sessionData.unidade_filha_id) {
    empenhoData = empenhos.filter(
      (empenho) => empenho.centroCustoId === sessionData?.unidade_filha_id
    );
  } else {
    empenhoData = empenhos;
  }

  const fieldConfig = useMemo(() => {
    return GetVeiculoConfig(
      [
        tiposDeVeiculo,
        marcas,
        filteredModelos,
        filteredVersoes,
        tiposDeFrota,
        centrosCustos,
        empenhoData,
      ],
      ["descricao", "descricao", "descricao", "descricao", "descricao", "descricao", "nota_empenho"]
    );
  }, [
    tiposDeVeiculo,
    marcas,
    filteredModelos,
    filteredVersoes,
    tiposDeFrota,
    centrosCustos,
    empenhoData,
  ]);

  if (isEditMode && isLoadingVeiculo) {
    return (
      <div className="flex flex-col p-4 items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <p className="mt-2">Carregando dados do veículo...</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col p-4 gap-4 w-full">
      <Card>
        <div className="p-4">
          <Form {...form}>
            <form className="space-y-4 py-4">
              <GenericFormsInput
                fieldConfig={fieldConfig}
                className="grid md:grid-cols-6 md:gap-4"
              />
              <div className="w-full flex justify-end gap-2">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => router.push("/dashboard/veiculos/veiculos")}>
                  Cancelar
                </Button>
                <Button
                  type="button"
                  onClick={async () => {
                    const values = form.getValues();
                    await onSubmit(values);
                  }}
                  disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {isEditMode ? "Atualizando..." : "Criando..."}
                    </>
                  ) : isEditMode ? (
                    "Atualizar"
                  ) : (
                    "Criar"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </Card>
    </div>
  );
}
