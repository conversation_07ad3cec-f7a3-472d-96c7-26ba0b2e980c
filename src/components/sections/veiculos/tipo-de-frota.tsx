"use client";
import { TipoDeFrotaConfig } from "@/components/forms/inputConfig";
import { tipoDeFrotaSchema } from "@/components/forms/schemas";
import { SheetForm } from "@/components/forms/sheet-form";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useTipoDeFrota } from "@/context/tipo-de-frota-context";
import { ColumnDef } from "@tanstack/react-table";
import { Delete, Edit, MoreHorizontal } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { z } from "zod";

export const tipoDeFrotaColumn: ColumnDef<tipo_de_frota>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "descricao",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Descrição" />,
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const { setTiposDeFrota } = useTipoDeFrota();
      const [open, setOpen] = useState(false);
      const [selectedItem, setSelectedItem] = useState<tipo_de_frota | null>(null);

      const editItem = async () => {
        setSelectedItem(row.original);
        setOpen(true);
      };

      const updateItem = async (values: z.infer<typeof tipoDeFrotaSchema>) => {
        try {
          const res = await fetch(`/api/tipo-de-frota/${row.original.id}`, {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(values),
          });

          if (!res.ok) {
            toast("Erro ao atualizar o tipo de frota");
            return;
          }

          const data = await res.json();
          setTiposDeFrota((prev) =>
            prev.map((item) => (item.id === data.data.id ? data.data : item))
          );

          toast("Tipo de frota atualizado com sucesso!");
          setOpen(false);
        } catch (error) {
          toast("Erro ao atualizar o tipo de frota");
        }
      };

      const deleteItem = async () => {
        const res = await fetch(`/api/tipo-de-frota/${row.original.id}`, {
          method: "DELETE",
        });
        if (!res.ok) {
          toast("Ops, algo deu errado", {
            description: "Houve um erro ao deletar o tipo de frota",
          });
          return;
        }
        setTiposDeFrota((prev) => prev.filter((item) => item.id !== row.original.id));
        toast("Tipo de frota deletado com sucesso!");
      };

      return (
        <>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Abrir o menu</span>
                <MoreHorizontal />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Ações</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={editItem}>
                Editar
                <Edit />
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  toast("Tem certeza?", {
                    description: "Essa ação não pode ser desfeita",
                    action: {
                      label: "Tenho certeza!",
                      onClick: deleteItem,
                    },
                  });
                }}>
                Deletar
                <Delete />
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          {selectedItem && open && (
            <SheetForm
              title="Editar Tipo de Frota"
              schema={tipoDeFrotaSchema}
              onSubmit={updateItem}
              triggerLabel="Editar Tipo de Frota"
              defaultValues={selectedItem}>
              <GenericFormsInput fieldConfig={TipoDeFrotaConfig} variants="single" />
            </SheetForm>
          )}
        </>
      );
    },
  },
];

export function TipoDeFrotaTable() {
  const { tiposDeFrota, setTiposDeFrota } = useTipoDeFrota();

  async function onNewTipoDeFrota(values: z.infer<typeof tipoDeFrotaSchema>) {
    const response = await fetch("/api/tipo-de-frota", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });

    if (!response.ok) {
      toast("Ops, algo deu errado", {
        description: "Houve um erro ao criar o tipo de frota",
      });
      return;
    }

    const data = await response.json();
    setTiposDeFrota((prev) => [...prev, data.data]);
    window.location.reload();
  }

  return (
    <DataTable
      data={tiposDeFrota}
      columns={tipoDeFrotaColumn}
      exportTo={true}
      newItem={{
        name: "Tipo de frota",
        defaultValues: { descricao: "" },
        fieldConfig: TipoDeFrotaConfig,
        schema: tipoDeFrotaSchema,
      }}
      onNewItem={onNewTipoDeFrota}
    />
  );
}
