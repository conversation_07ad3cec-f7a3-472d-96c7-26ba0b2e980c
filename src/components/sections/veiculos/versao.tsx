"use client";

import { VersaoConfig } from "@/components/forms/inputConfig";
import { versaoSchema } from "@/components/forms/schemas";

import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { Checkbox } from "@/components/ui/checkbox";
import { useVersao } from "@/context/versao-context";
import { ColumnDef } from "@tanstack/react-table";
import { toast } from "sonner";
import { z } from "zod";

export const versaoColumn: ColumnDef<versao>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "descricao",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Descrição" />
    ),
  },
];

export function VersaoTable() {
  const { versoes, setVersoes } = useVersao();

  async function onNewVersao(values: z.infer<typeof versaoSchema>) {
    const response = await fetch("/api/versao", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });

    if (!response.ok) {
      toast("Ops, algo deu errado", {
        description: "Houve um erro ao criar a versão",
      });
      return;
    }

    const data = await response.json();
    setVersoes((prev) => [...prev, data.data]);
  }

  return (
    <DataTable
      data={versoes}
      newItem={{
        defaultValues: { descricao: "" },
        fieldConfig: VersaoConfig,
        schema: versaoSchema,
        name: "versão",
      }}
      onNewItem={onNewVersao}
      exportTo={true}
      columns={versaoColumn}
    />
  );
}
