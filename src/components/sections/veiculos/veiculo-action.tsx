import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog";
import { Edit, Power, PencilLine } from "lucide-react";
import { toast } from "sonner";
import { DateInput } from "@/components/inputs/date-input";
import { useRouter } from "next/navigation";

const VeiculoActions = ({ row }: { row: any }) => {
  const router = useRouter();
  // Modal para atualizar odômetro
  const [isOdometroDialogOpen, setIsOdometroDialogOpen] = useState(false);
  const [novoOdometroInput, setNovoOdometroInput] = useState("");

  // Modal para inativação
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isTemporaria, setIsTemporaria] = useState(false);
  // Para período temporário, usaremos um range de datas.
  const [inativacaoInicio, setInativacaoInicio] = useState("");
  const [inativacaoFim, setInativacaoFim] = useState("");

  // Navegar para a página de edição
  const handleNavToEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    router.push(`/dashboard/veiculos/editar-veiculo/${row.original.id}`);
  };

  // Abre o modal de atualização do odômetro
  const openOdometroDialog = (e: React.MouseEvent) => {
    e.stopPropagation();
    setNovoOdometroInput(row.original.odometro_atual ? String(row.original.odometro_atual) : "");
    setIsOdometroDialogOpen(true);
  };

  // Confirma a atualização do odômetro via fetch
  const handleConfirmOdometro = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      const res = await fetch(`/api/veiculos/${row.original.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ odometro_atual: Number(novoOdometroInput) }),
      });
      if (!res.ok) throw new Error("Falha ao atualizar odômetro");
      toast.success("Odômetro atualizado com sucesso!");
      window.location.reload();
      setIsOdometroDialogOpen(false);
    } catch (err) {
      console.error(err);
      toast.error("Erro ao atualizar odômetro");
    }
  };

  // Abre o modal de inativação e recupera o período salvo no localStorage
  // Ou preenche com a data atual se não tiver dados salvos
  const openInativarDialog = (e: React.MouseEvent) => {
    e.stopPropagation();
    const savedPeriodo = localStorage.getItem("inativacao_periodo_" + row.original.id) || "";
    
    // Formata data atual no formato YYYY-MM-DD
    const dataAtual = new Date().toISOString().split('T')[0];
    
    if (savedPeriodo) {
      // Assume o formato "YYYY-MM-DD à YYYY-MM-DD"
      const partes = savedPeriodo.split(" à ");
      setInativacaoInicio(partes[0] || dataAtual);
      setInativacaoFim(partes[1] || "");
    } else {
      // Define a data inicial como a data atual
      setInativacaoInicio(dataAtual);
      setInativacaoFim("");
    }
    
    setIsDialogOpen(true);
  };

  // Confirma a inativação via fetch; o body enviado possui somente o status "Inativo"
  // Se a inativação for temporária, o range de datas é salvo no localStorage para exibição na tabela
  const handleConfirmarInativacao = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      const body = { status: "Inativo" };
      const res = await fetch(`/api/veiculos/${row.original.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(body),
      });
      if (!res.ok) throw new Error("Falha ao inativar veículo");
      toast.success("Veículo inativado com sucesso!");
      if (isTemporaria) {
        // Salva o range de datas no formato "início à fim"
        localStorage.setItem("inativacao_periodo_" + row.original.id, `${inativacaoInicio} à ${inativacaoFim}`);
      } else {
        localStorage.removeItem("inativacao_periodo_" + row.original.id);
      }
      window.location.reload();
      setIsDialogOpen(false);
    } catch (err) {
      console.error(err);
      toast.error("Erro ao inativar veículo");
    }
  };

  // Handler para reativar veículo
  const handleReativar = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      const res = await fetch(`/api/veiculos/${row.original.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ status: "Ativo" }),
      });
      if (!res.ok) throw new Error("Falha ao reativar veículo");
      toast.success("Veículo reativado com sucesso!");
      localStorage.removeItem("inativacao_periodo_" + row.original.id);
      window.location.reload();
    } catch (err) {
      console.error(err);
      toast.error("Erro ao reativar veículo");
    }
  };

  return (
    <>
      <div className="flex gap-2">
        {/* Botão para editar o veículo */}
        <Button variant="ghost" size="icon" onClick={handleNavToEdit}>
          <PencilLine className="h-4 w-4 text-green-500" />
        </Button>
        
        {/* Botão para atualizar o odômetro */}
        <Button variant="ghost" size="icon" onClick={openOdometroDialog}>
          <Edit className="h-4 w-4 text-blue-500" />
        </Button>
        
        {/* Botão para inativar ou reativar o veículo */}
        {row.original.status === "Ativo" ? (
          <Button variant="ghost" size="icon" onClick={openInativarDialog}>
            <Power className="h-4 w-4 text-orange-500" />
          </Button>
        ) : (
          <Button variant="ghost" size="icon" onClick={handleReativar}>
            <Power className="h-4 w-4 text-green-500" />
          </Button>
        )}
      </div>

      {/* Modal para atualizar odômetro */}
      <Dialog open={isOdometroDialogOpen} onOpenChange={setIsOdometroDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Atualizar Odômetro</DialogTitle>
            <DialogDescription>
              Digite o novo valor do odômetro para o veículo.
            </DialogDescription>
          </DialogHeader>
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">
              Odômetro Atual
            </label>
            <input
              type="number"
              placeholder="Digite o novo valor"
              className="border p-2 rounded w-full"
              value={novoOdometroInput}
              onClick={(e) => e.stopPropagation()}
              onChange={(e) => setNovoOdometroInput(e.target.value)}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={(e) => { e.stopPropagation(); setIsOdometroDialogOpen(false); }}>
              Cancelar
            </Button>
            <Button onClick={handleConfirmOdometro}>
              Confirmar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de inativação */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Inativar Veículo</DialogTitle>
            <DialogDescription>
              Selecione se deseja inativar temporariamente ou permanentemente.
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex items-center space-x-2 my-4">
            <input
              type="checkbox"
              checked={isTemporaria}
              onClick={(e) => e.stopPropagation()}
              onChange={(e) => setIsTemporaria(e.target.checked)}
            />
            <span>Inativar temporariamente</span>
          </div>
          
          {isTemporaria && (
            <>
              <div className="mb-4">
                <label className="block text-sm font-medium mb-1">
                  Data de Início
                </label>
                <DateInput
                  value={inativacaoInicio}
                  onChange={setInativacaoInicio}
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
              <div className="mb-4">
                <label className="block text-sm font-medium mb-1">
                  Data de Término
                </label>
                <DateInput
                  value={inativacaoFim}
                  onChange={setInativacaoFim}
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
            </>
          )}
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={(e) => { e.stopPropagation(); setIsDialogOpen(false); }}
            >
              Cancelar
            </Button>
            <Button onClick={handleConfirmarInativacao}>
              Confirmar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default VeiculoActions;