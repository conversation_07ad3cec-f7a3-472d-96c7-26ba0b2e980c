"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { MarcaConfig } from "@/components/forms/inputConfig";
import { marcaSchema } from "@/components/forms/schemas";
import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { Checkbox } from "@/components/ui/checkbox";
import { useMarca } from "@/context/marca-context";
import { ColumnDef } from "@tanstack/react-table";
import { toast } from "sonner";
import { z } from "zod";
import {useRouter} from "next/navigation";

export const marcaColumn: ColumnDef<marca>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "descricao",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Marca" />
    ),
  },
  {
    accessorKey: "codigo_fipe",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Código FIPE" />
    ),
  },
  {
    accessorKey: "codigo_suiv",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Código SUIV" />
    ),
  },
];

export function MarcaTable() {
  const router = useRouter();
  const { marcas, setMarcas } = useMarca();

  async function onNewMarca(values: z.infer<typeof marcaSchema>) {
    const response = await fetch("/api/marca", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });

    if (!response.ok) {
      toast("Ops, algo deu errado", {
        description: "Houve um erro ao criar a marca",
      });
      return;
    }

    const data = await response.json();
    setMarcas((prev) => [...prev, data.data]);
    window.location.reload();
  }

  return (
    <DataTable
      data={marcas}
      newItem={{
        defaultValues: { marca: "", codigo_fipe: "", codigo_suiv: "" },
        fieldConfig: MarcaConfig,
        schema: marcaSchema,
        name: "marca",
      }}
      onNewItem={onNewMarca}
      exportTo={true}
      columns={marcaColumn}
    />
  );
}
