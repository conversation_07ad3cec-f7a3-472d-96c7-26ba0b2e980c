import React from "react";
import { useOS } from "@/context/os-context";
import { useCredenciado } from "@/context/credenciado-context";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

interface VeiculoHistoricoManutencaoProps {
  veiculoId: string;
}

export function VeiculoHistoricoManutencao({ veiculoId }: VeiculoHistoricoManutencaoProps) {
  const { ordensDeServico } = useOS();
  const { credenciados } = useCredenciado();
  const osVeiculo = ordensDeServico.filter(
    (os) => os.veiculo?.id === veiculoId && os.status === "faturada"
  );

  const sortedOS = [...osVeiculo].sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  if (sortedOS.length === 0) {
    return (
      <div className="text-sm text-muted-foreground py-2">
        Nenhum histórico de manutenção encontrado.
      </div>
    );
  }

  const valorTotal = sortedOS.reduce((total, os) => {
    const osTotal =
      os.orcamentos?.reduce((orcTotal, orc) => {
        if (orc.status === "faturada") {
          return orcTotal + (orc.valorTotal / 100 || 0);
        }
        return orcTotal;
      }, 0) || 0;
    return total + osTotal;
  }, 0);

  return (
    <Accordion type="single" collapsible className="w-full">
      <AccordionItem value="historico-manutencao">
        <AccordionTrigger className="font-medium">
          <div className="flex justify-between w-full pr-4">
            <span>Histórico de Manutenção ({sortedOS.length} registros)</span>
            <span className="text-sm">
              Total:{" "}
              {new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(
                valorTotal
              )}
            </span>
          </div>
        </AccordionTrigger>
        <AccordionContent>
          {sortedOS.map((os) => (
            <div key={os.id} className="mb-6 border rounded-md p-4">
              <div className="mb-4 flex justify-between">
                <div>
                  <h3 className="font-bold">OS #{os.osNumber}</h3>
                  <p className="text-sm text-muted-foreground">
                    Data: {format(new Date(os.createdAt), "dd/MM/yyyy", { locale: ptBR })}
                  </p>
                </div>
                <Badge>Finalizada</Badge>
              </div>

              {os.orcamentos
                ?.filter((orc) => orc.status === "faturada")
                .map((orcamento) => {
                  const credenciadoData = credenciados.find(
                    (cred) => cred.id === orcamento.credenciadoId
                  );

                  return (
                    <div key={orcamento.id} className="mb-4 border-t pt-4">
                      <div className="flex justify-between mb-2">
                        <span className="font-medium">Orçamento #{orcamento.numeroOrcamento}</span>
                        <span>
                          Total:{" "}
                          {new Intl.NumberFormat("pt-BR", {
                            style: "currency",
                            currency: "BRL",
                          }).format(orcamento.valorTotal / 100)}
                        </span>
                      </div>
                      <p className="text-sm mb-4">
                        <strong>Credenciado:</strong>{" "}
                        {credenciadoData ? credenciadoData.informacoes[0].razao_social : "N/A"} |{" "}
                        <strong>Local:</strong>{" "}
                        {credenciadoData
                          ? `${credenciadoData.endereco.cidade}-${credenciadoData.endereco.estado}`
                          : "N/A"}
                      </p>

                      {/* Peças */}
                      {orcamento.processedPecas && orcamento.processedPecas.length > 0 && (
                        <>
                          <h4 className="font-medium mb-2">Peças</h4>
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Descrição</TableHead>
                                <TableHead>Marca</TableHead>
                                <TableHead>Qtd</TableHead>
                                <TableHead>Valor</TableHead>
                                <TableHead>Total</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {orcamento.processedPecas.map((peca) => (
                                <TableRow key={peca.id}>
                                  <TableCell>{peca.descricao}</TableCell>
                                  <TableCell>{peca.marca}</TableCell>
                                  <TableCell>{peca.quantidade}</TableCell>
                                  <TableCell>
                                    {new Intl.NumberFormat("pt-BR", {
                                      style: "currency",
                                      currency: "BRL",
                                    }).format(peca.valorNegociado / 100)}
                                  </TableCell>
                                  <TableCell>
                                    {new Intl.NumberFormat("pt-BR", {
                                      style: "currency",
                                      currency: "BRL",
                                    }).format((peca.valorNegociado * peca.quantidade) / 100)}
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </>
                      )}

                      {/* Serviços */}
                      {orcamento.processedServicos && orcamento.processedServicos.length > 0 && (
                        <>
                          <h4 className="font-medium mb-2 mt-4">Serviços</h4>
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Descrição</TableHead>
                                <TableHead>Tipo</TableHead>
                                <TableHead>Valor</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {orcamento.processedServicos.map((servico) => (
                                <TableRow key={servico.id}>
                                  <TableCell>{servico.descricao}</TableCell>
                                  <TableCell>{servico.tipoServico || "N/A"}</TableCell>
                                  <TableCell>
                                    {new Intl.NumberFormat("pt-BR", {
                                      style: "currency",
                                      currency: "BRL",
                                    }).format(servico.valor / 100)}
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </>
                      )}
                    </div>
                  );
                })}
            </div>
          ))}
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
