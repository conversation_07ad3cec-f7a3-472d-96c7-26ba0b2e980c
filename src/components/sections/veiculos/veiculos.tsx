"use client";

import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable, DataTableMethods } from "@/components/tables/data-table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { useVeiculos } from "@/context/veiculos-context";
import { getServerSession } from "@/lib/auth/server-session";
import { Session } from "@/lib/auth/types";
import { BatchUpdateModal, Veiculo } from "@/components/modal/batch-veiculo-update-modal";

import { ColumnDef } from "@tanstack/react-table";
import { Upload, Download, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import VeiculoActions from "./veiculo-action";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { VehicleFiltersDialog, ActiveFilters, VehicleFilters } from "@/components/filters/VehicleFilters";

export const veiculoColumns: ColumnDef<veiculo>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  // Campos principais de identificação
  {
    accessorKey: "placa",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Placa" />,
  },
  {
    accessorKey: "marca",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Marca" />,
    accessorFn: (row) => row.marca?.descricao || "Não especificada",
  },
  {
    accessorKey: "modelo",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Modelo" />,
    accessorFn: (row) => row.modelo?.descricao || "Não especificado",
  },
  {
    accessorKey: "tipo_de_veiculo",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Tipo de Veículo" />,
    accessorFn: (row) => row.tipo_de_veiculo?.descricao || "Não especificado",
  },
  {
    accessorKey: "ano_de_fabricacao",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Ano Fabricação" />,
    accessorFn: (row) => row.ano_fab || "N/A",
  },
  {
    accessorKey: "ano_modelo",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Ano Modelo" />,
    accessorFn: (row) => row.ano_modelo || "N/A",
  },
  {
    accessorKey: "cor",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Cor" />,
    accessorFn: (row) => row.cor || "N/A",
  },
  // Campos de identificação técnica
  {
    accessorKey: "renovam",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Renavam" />,
    accessorFn: (row) => row.renovam || "N/A",
  },
  {
    accessorKey: "vin",
    header: ({ column }) => <DataTableColumnHeader column={column} title="VIN/Chassi" />,
    accessorFn: (row) => row.vin || "N/A",
  },
  {
    accessorKey: "numero_do_motor",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Número do Motor" />,
    accessorFn: (row) => row.numero_do_motor || "N/A",
  },
  {
    accessorKey: "matricula",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Matrícula" />,
    accessorFn: (row) => row.matricula || "N/A",
  },
  // Campos de localização e centro de custo
  {
    accessorKey: "centro_de_custo",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Centro de Custo" />,
    accessorFn: (row) => row.lotacao_veiculos?.centro_custo?.descricao || "Não vinculado",
  },
  {
    accessorKey: "cidade",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Cidade" />,
    accessorFn: (row) => row.lotacao_veiculos?.cidade || "Não informada",
  },
  // Campos de datas
  {
    accessorKey: "data_compra",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Data de Compra" />,
    accessorFn: (row) => row.data_compra ? new Date(row.data_compra).toLocaleDateString("pt-BR") : "N/A",
  },
  {
    accessorKey: "data_cedencia",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Data de Cedência" />,
    accessorFn: (row) => row.data_cedencia ? new Date(row.data_cedencia).toLocaleDateString("pt-BR") : "N/A",
  },
  // Campos técnicos e especificações
  {
    accessorKey: "tipo_de_frota",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Tipo de Frota" />,
    accessorFn: (row) => row.tipo_de_frota?.descricao || "N/A",
  },
  {
    accessorKey: "combustivel",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Combustível" />,
    accessorFn: (row) => {
      if (row.combustivel?.tipos_de_combustiveis) {
        return Array.isArray(row.combustivel.tipos_de_combustiveis)
          ? row.combustivel.tipos_de_combustiveis.join(", ")
          : row.combustivel.tipos_de_combustiveis;
      }
      return "N/A";
    },
  },
  {
    accessorKey: "potencia",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Potência" />,
    accessorFn: (row) => row.definicoes?.potencia || "N/A",
  },
  {
    accessorKey: "cilindradas",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Cilindradas" />,
    accessorFn: (row) => row.definicoes?.cilindradas || "N/A",
  },
  {
    accessorKey: "transmissao",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Transmissão" />,
    accessorFn: (row) => row.definicoes?.transmissao || "N/A",
  },
  {
    accessorKey: "quantidade_de_portas",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Portas" />,
    accessorFn: (row) => row.definicoes?.quantidade_de_portas || "N/A",
  },
  {
    accessorKey: "quantidade_de_assentos",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Assentos" />,
    accessorFn: (row) => row.definicoes?.quantidade_de_assentos || "N/A",
  },
  // Campos de controle e valores
  {
    accessorKey: "odometro_atual",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Odômetro" />,
    accessorFn: (row) => row.odometro_atual || "N/A",
  },
  {
    accessorKey: "valor_venal",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Valor Venal (R$)" />,
    accessorFn: (row) => row.valor_venal || 0,
    cell: ({ row }) => {
      const valor = parseFloat(row.getValue("valor_venal"));
      return valor.toLocaleString("pt-BR", {
        style: "currency",
        currency: "BRL",
      });
    },
  },
  {
    accessorKey: "codigo_fipe",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Código FIPE" />,
    accessorFn: (row) => row.codigo_fipe?.codigo_fipe || "N/A",
  },
  {
    accessorKey: "tag_rfid",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Tag RFID" />,
    accessorFn: (row) => row.tag_rfid || "N/A",
  },
  {
    accessorKey: "status",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => (
      <div
        className={`font-medium ${
          row.original.status === "Ativo" ? "text-green-600" : "text-red-600"
        }`}>
        {row.original.status}
      </div>
    ),
  },
  {
    id: "actions",
    header: "Ações",
    cell: ({ row }) => (
      <div onClick={(e) => e.stopPropagation()}>
        <VeiculoActions row={row} />
      </div>
    ),
  },
];

export function VeiculoTable() {
  const tableRef = useRef<DataTableMethods<veiculo>>(null);

  const router = useRouter();
  const { veiculos } = useVeiculos();
  const [isUploading, setIsUploading] = useState(false);
  const [sessionData, setSessionData] = useState<Session>();
  const [selectedRows, setSelectedRows] = useState<veiculo[]>([]);
  const { centrosDeCusto } = useCentroDeCusto();
  const [showFilterDialog, setShowFilterDialog] = useState(false);
  const [activeFilters, setActiveFilters] = useState<VehicleFilters>({});

  useEffect(() => {
    // Set up an interval to check for selection changes
    const interval = setInterval(() => {
      if (tableRef.current) {
        const currentSelectedRows = tableRef.current.getSelectedRows();
        // Only update state if selection has actually changed
        if (JSON.stringify(currentSelectedRows) !== JSON.stringify(selectedRows)) {
          setSelectedRows(currentSelectedRows);
        }
      }
    }, 200); // Check every 200ms

    return () => clearInterval(interval);
  }, [selectedRows]);

  useEffect(() => {
    async function fetchSessionData() {
      const sessionData = await getServerSession();
      setSessionData(sessionData);
    }
    fetchSessionData();
  }, []);

  const handleDownloadModeloVeiculo = async () => {
    try {
      const response = await fetch("/modelos/atualizar-veiculos.xlsx", {
        method: "GET",
      });

      if (!response.ok) {
        throw new Error("Erro ao baixar modelo");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "atualizar-veiculos.xlsx";
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error("Erro ao baixar modelo:", error);
      toast.error("Erro ao baixar modelo da planilha");
    }
  };

  const handleDownloadModeloOdometro = async () => {
    try {
      const response = await fetch("/modelos/atualizar-odometro.xlsx", {
        method: "GET",
      });

      if (!response.ok) {
        throw new Error("Erro ao baixar modelo");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "atualizar-odometro.xlsx";
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error("Erro ao baixar modelo:", error);
      toast.error("Erro ao baixar modelo da planilha");
    }
  };

  const handleUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    const formData = new FormData();
    formData.append("file", file);

    try {
      const res = await fetch("/api/veiculos/importar-odometros", {
        method: "POST",
        body: formData,
        credentials: "include",
      });
      const result = await res.json();

      if (!res.ok) {
        if (result.erros) {
          toast.error("Erros na planilha", {
            description: result.erros.join("\n"),
          });
        } else {
          toast.error(result.message || "Erro ao importar planilha");
        }
        return;
      }

      toast.success("Veículos importados com sucesso!");
      window.location.reload();
    } catch (error) {
      console.error("Erro no upload:", error);
      toast.error("Erro ao importar planilha");
    } finally {
      setIsUploading(false);
    }
  };

  const handleUpload2 = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    const formData = new FormData();
    formData.append("file", file);

    try {
      const token = localStorage.getItem("token");
      const res = await fetch("/api/veiculos/importar-veiculos", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      });

      const result = await res.json();

      if (!res.ok) {
        if (result.erros) {
          toast.error("Erros na planilha", {
            description: result.erros.join("\n"),
          });
        } else {
          toast.error(result.message || "Erro ao importar planilha");
        }
        return;
      }

      toast.success("Veículos importados com sucesso!");
      window.location.reload();
    } catch (error) {
      console.error("Erro no upload:", error);
      toast.error("Erro ao importar planilha");
    } finally {
      setIsUploading(false);
    }
  };



  // Função para aplicar filtros
  const applyFilters = (data: veiculo[], filters: VehicleFilters): veiculo[] => {
    return data.filter(veiculo => {
      // Filtro por placa
      if (filters.placa && !veiculo.placa?.toLowerCase().includes(filters.placa.toLowerCase())) {
        return false;
      }

      // Filtro por marca
      if (filters.marca && veiculo.marca?.descricao !== filters.marca) {
        return false;
      }

      // Filtro por modelo
      if (filters.modelo && veiculo.modelo?.descricao !== filters.modelo) {
        return false;
      }

      // Filtro por tipo de veículo
      if (filters.tipoDeVeiculo && veiculo.tipo_de_veiculo?.descricao !== filters.tipoDeVeiculo) {
        return false;
      }

      // Filtro por ano de fabricação
      if (filters.anoFabricacao && veiculo.ano_fab !== filters.anoFabricacao) {
        return false;
      }

      // Filtro por ano do modelo
      if (filters.anoModelo && veiculo.ano_modelo !== filters.anoModelo) {
        return false;
      }

      // Filtro por cor
      if (filters.cor && veiculo.cor !== filters.cor) {
        return false;
      }

      // Filtro por renavam
      if (filters.renovam && !veiculo.renovam?.toLowerCase().includes(filters.renovam.toLowerCase())) {
        return false;
      }

      // Filtro por VIN
      if (filters.vin && !veiculo.vin?.toLowerCase().includes(filters.vin.toLowerCase())) {
        return false;
      }

      // Filtro por número do motor
      if (filters.numeroDoMotor && !veiculo.numero_do_motor?.toLowerCase().includes(filters.numeroDoMotor.toLowerCase())) {
        return false;
      }

      // Filtro por matrícula
      if (filters.matricula && !veiculo.matricula?.toLowerCase().includes(filters.matricula.toLowerCase())) {
        return false;
      }

      // Filtro por centro de custo
      if (filters.centroCusto && veiculo.lotacao_veiculos?.centro_custo?.descricao !== filters.centroCusto) {
        return false;
      }

      // Filtro por cidade
      if (filters.cidade && veiculo.lotacao_veiculos?.cidade !== filters.cidade) {
        return false;
      }

      // Filtro por tipo de frota
      if (filters.tipoDeFrota && veiculo.tipo_de_frota?.descricao !== filters.tipoDeFrota) {
        return false;
      }

      // Filtro por combustível
      if (filters.combustivel) {
        const combustiveis = Array.isArray(veiculo.combustivel?.tipos_de_combustiveis)
          ? veiculo.combustivel.tipos_de_combustiveis
          : [veiculo.combustivel?.tipos_de_combustiveis];
        if (!combustiveis.includes(filters.combustivel as any)) {
          return false;
        }
      }

      // Filtro por potência
      if (filters.potencia && veiculo.definicoes?.potencia !== filters.potencia) {
        return false;
      }

      // Filtro por transmissão
      if (filters.transmissao && veiculo.definicoes?.transmissao !== filters.transmissao) {
        return false;
      }

      // Filtro por status
      if (filters.status && veiculo.status !== filters.status) {
        return false;
      }

      // Filtro por valor venal mínimo
      if (filters.valorVenalMin) {
        const valorVenal = parseFloat(veiculo.valor_venal || '0');
        const valorMin = parseFloat(filters.valorVenalMin);
        if (valorVenal < valorMin) {
          return false;
        }
      }

      // Filtro por valor venal máximo
      if (filters.valorVenalMax) {
        const valorVenal = parseFloat(veiculo.valor_venal || '0');
        const valorMax = parseFloat(filters.valorVenalMax);
        if (valorVenal > valorMax) {
          return false;
        }
      }

      return true;
    });
  };

  const handleFiltersChange = (filters: VehicleFilters) => {
    setActiveFilters(filters);
  };

  const handleRemoveFilter = (key: keyof VehicleFilters) => {
    const newFilters = { ...activeFilters };
    delete newFilters[key];
    setActiveFilters(newFilters);
  };

  const handleClearAllFilters = () => {
    setActiveFilters({});
  };

  // Aplicar filtros de sessão primeiro
  let baseVeiculoData: veiculo[] = [];
  if (sessionData && !sessionData.roles.includes("ADMIN") && sessionData.centro_de_custoId) {
    if (sessionData.unidade_filha_id) {
      baseVeiculoData = (() => {
        let filteredData = veiculos.filter(
          (veiculo) => veiculo.lotacao_veiculos?.centro_custoID === sessionData.unidade_filha_id
        );
        if (filteredData.length === 0) {

          filteredData = veiculos.filter(
            (veiculo) => veiculo.lotacao_veiculos?.centro_custoID === sessionData.centro_de_custoId
          );
        }
        return filteredData;
      })();
    } else if (sessionData.centro_de_custoId) {
      const unidadesFilhas = centrosDeCusto
        .flatMap((centro) => centro.centro_custos_filhos)
        .filter((uniFilho) => uniFilho.centro_custo_ascdID === sessionData.centro_de_custoId);
      const filteredVeiculos = veiculos.filter((veiculo) =>
        unidadesFilhas.some((centro) => centro.id === veiculo?.lotacao_veiculos?.centro_custoID)
      );
      if (filteredVeiculos.length > 0) {
        baseVeiculoData = filteredVeiculos;
      } else {
        baseVeiculoData = veiculos.filter(
          (veiculo) =>
            veiculo.lotacao_veiculos?.centro_custo?.id ===
            sessionData.centro_de_custoId
        );
      }
    } else {
      baseVeiculoData = veiculos.filter(
        (veiculo) =>
          veiculo.lotacao_veiculos?.centro_custo?.centro_custo_ascdID ===
          sessionData.centro_de_custoId
      );
    }
  } else {
    baseVeiculoData = veiculos;
  }

  // Aplicar filtros de usuário
  const veiculoData = applyFilters(baseVeiculoData, activeFilters);

  // Debug: Log dos dados para verificar estrutura
  useEffect(() => {
    if (veiculoData.length > 0) {
      console.log("🚗 Dados de veículos:", veiculoData[0]);
      console.log("📊 Total de veículos:", veiculoData.length);
      console.log("🔍 Filtros ativos:", activeFilters);
    }
  }, [veiculoData, activeFilters]);

  const mapSelectedVehiclesToBatchFormat = (vehicles: veiculo[]): Veiculo[] => {
    return vehicles.map((vehicle) => ({
      id: vehicle.id,
      placa: vehicle.placa || "",
      odometro_atual: Number(vehicle.odometro_atual) || 0,
      cor: vehicle.cor || "",
      ano_modelo: Number(vehicle.ano_modelo) || 0,
      status: vehicle.status || "",
      combustivel: vehicle.combustivel?.tipos_de_combustiveis?.[0] || "",
      renavam: vehicle.renovam || "",
      vin: vehicle.vin || "",
      numero_motor: vehicle.numero_do_motor || "",
      ano_fab: Number(vehicle.ano_fab) || 0,
      matricula: vehicle.matricula || "",
      tag_rfid: vehicle.tag_rfid || "",
      valor_venal: Number(vehicle.codigo_fipe?.valor_venal) || 0,
    }));
  };
  return (
    <>
      {/* Filtros Ativos */}
      <ActiveFilters
        filters={activeFilters}
        onRemoveFilter={handleRemoveFilter}
        onClearAll={handleClearAllFilters}
      />

      <div className="flex justify-between mb-4">
        <div className="flex gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <Download className="w-4 h-4" />
                Download Modelo de Planilha
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={handleDownloadModeloVeiculo}>
                Modelo Atualização Veículo
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDownloadModeloOdometro}>
                Modelo Atualização Odômetro
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <label className="cursor-pointer">
            <div
              className={`flex items-center gap-2 border border-gray-300 rounded-md px-4 py-2 text-sm hover:bg-gray-700 transition ${
                isUploading ? "opacity-50 cursor-not-allowed" : ""
              }`}>
              {isUploading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Upload className="w-4 h-4" />
              )}
              <span>{isUploading ? "Atualizando..." : "Atualizar odômetro"}</span>
            </div>
            <input
              type="file"
              accept=".xlsx, .xls"
              className="hidden"
              onChange={handleUpload}
              disabled={isUploading}
            />
          </label>
          <label className="cursor-pointer">
            <div
              className={`flex items-center gap-2 border border-gray-300 rounded-md px-4 py-2 text-sm hover:bg-gray-400 transition ${
                isUploading ? "opacity-50 cursor-not-allowed" : ""
              }`}>
              {isUploading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Upload className="w-4 h-4" />
              )}
              <span>{isUploading ? "Atualizando..." : "Atualizar Veiculos"}</span>
            </div>
            <input
              type="file"
              accept=".xlsx, .xls"
              className="hidden"
              onChange={handleUpload2}
              disabled={isUploading}
            />
          </label>

          <BatchUpdateModal selectedVehicles={mapSelectedVehiclesToBatchFormat(selectedRows)} />
        </div>
      </div>

      <DataTable
        ref={tableRef}
        data={veiculoData}
        onClick={() => router.push("/dashboard/veiculos/novo-veiculo")}
        exportTo={true}
        columns={veiculoColumns}
        handleRowClick={(row: any) => {
          router.push(`/dashboard/veiculos/${row.id}/detalhes`);
        }}
        showReportButton={true}
        reportType="veiculos"
        showFilterButton={true}
        setShowFilterDialog={setShowFilterDialog}
      />

      {/* Dialog de Filtros */}
      <VehicleFiltersDialog
        open={showFilterDialog}
        onOpenChange={setShowFilterDialog}
        onFiltersChange={handleFiltersChange}
        currentFilters={activeFilters}
      />
    </>
  );
}
