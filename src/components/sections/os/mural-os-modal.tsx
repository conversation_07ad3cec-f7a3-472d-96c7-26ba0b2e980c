"use client";
import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Download, Plus } from "lucide-react";
import { getMuralOsLogsAction } from "@/serverActions/orcamentoAction";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { toast } from "sonner";
import { Pagination } from "@/components/ui/pagination";
import { Textarea } from "@/components/ui/textarea";
import { createManualMuralLog } from "@/serverActions/muralAction";
import { useUsuario } from "@/context/usuario-context";
import { useContrato } from "@/context/contrato-context";
import { Session } from "@/lib/auth/types";
import { getServerSession } from "@/lib/auth/server-session";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { MultipleSelection } from "@/components/inputs/multi-selector-input";
import { FormProvider, useForm } from "react-hook-form";
import { useCredenciado } from "@/context/credenciado-context";

interface MuralOsModalProps {
  isOpen: boolean;
  onClose: () => void;
  osId: string;
  osNumber: string | number;
  osYear?: string | number;
}

interface MessageModalProps {
  isOpen: boolean;
  onClose: () => void;
  osId: string;
}

function MessageModal({ isOpen, onClose, osId }: MessageModalProps) {
  const { usuarios, setUsuarios } = useUsuario();
  const { contratos } = useContrato();
  const { credenciados } = useCredenciado();
  const methods = useForm();

  const [message, setMessage] = useState("");
  const [filteredUsuarios, setFilteredUsuarios] = useState<any>([]);
  const [sessionData, setSessionData] = useState<Session>();
  const [emails, setEmails] = useState<any>([]);

  useEffect(() => {
    const fetchSessionAndContrato = async () => {
      const sessionData = await getServerSession();
      setSessionData(sessionData);
    };

    fetchSessionAndContrato();
  }, []);

  useEffect(() => {
    if (typeof window !== "undefined") {
      if (sessionData && sessionData.contrato) {
        const credenciadosVinculados = credenciados.filter((credenciado) =>
          credenciado.contratos.some(
            (contrato) => contrato.contratoId === sessionData?.contratoId && credenciado.ativo
          )
        );
        setFilteredUsuarios([
          ...usuarios.filter((usuario) =>
            usuario?.contratos?.some((contrato) => contrato.id === sessionData.contratoId)
          ),
          ...credenciadosVinculados.map((credenciado) => ({
            email: credenciado.contato.email,
          })),
        ]);
      }
    } else {
      setFilteredUsuarios(usuarios);
    }
  }, [sessionData]);

  const handleMessage = async () => {
    await createManualMuralLog(osId, message, emails)
      .then(() => {
        toast("Mensagem registrada com sucesso!");
        window.location.reload();
      })
      .catch(() => toast.error("Erro ao registrar mensagem!"));
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => onClose()}>
      <DialogContent
        className="sm:max-w-4xl lg:max-w-3xl overflow-y-auto"
        style={{ maxHeight: "80vh" }}>
        <DialogHeader>
          <DialogTitle>Adicione uma mensagem</DialogTitle>
          <DialogDescription>
            Preencha os campos abaixo para adicionar uma mensagem ao mural
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="message" className="text-sm font-medium">
              Descrição
            </Label>
            <Textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Digite sua mensagem aqui..."
              className="min-h-[120px]"
              style={{ resize: "none" }}
            />
          </div>

          <FormProvider {...methods}>
            <div className="space-y-2">
              <Label htmlFor="emails" className="text-sm font-medium">
                Emails (opcional)
              </Label>
              <MultipleSelection
                name="emails"
                options={
                  filteredUsuarios.map(({ email }: any) => ({
                    value: email,
                    label: email,
                  })) || []
                }
                placeHolder="Escolha os emails"
                onChange={(value, e) => {
                  e.stopPropagation();
                  if (!value || value.length === 0) return;
                  const lastSelected = value[value.length - 1].value;
                  setEmails((prev: string[]) => [...prev, lastSelected]);
                }}
              />
              {emails.length > 0 && (
                <p className="text-xs text-muted-foreground mt-2">
                  {emails.length} email(s) selecionado(s)
                </p>
              )}
            </div>
          </FormProvider>
        </div>

        <DialogFooter className="sm:justify-end">
          <Button onClick={onClose} variant="outline" className="mr-2">
            Cancelar
          </Button>
          <Button disabled={message.length === 0} onClick={handleMessage} className="min-w-[100px]">
            Adicionar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export function MuralOsModal({
  isOpen,
  onClose,
  osId,
  osNumber,
  osYear = new Date().getFullYear(),
}: MuralOsModalProps) {
  const [logs, setLogs] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredLogs, setFilteredLogs] = useState<any[]>([]);
  const [messageModalIsOpen, setMessageModalIsOpen] = useState(false);

  const fetchLogs = async (page = 1) => {
    setLoading(true);
    try {
      const response = await getMuralOsLogsAction(osId, page);
      if (response.success) {
        const logsData = response.data?.data || [];
        setLogs(logsData);
        setFilteredLogs(logsData);
        setPagination(response.data?.pagination);
      } else {
        toast.error(`Erro ao carregar logs: ${response.error}`);
      }
    } catch (error) {
      console.error("Erro ao carregar logs da OS:", error);
      toast.error("Falha ao carregar histórico da OS");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen && osId) {
      fetchLogs(1);
    }
  }, [isOpen, osId]);

  useEffect(() => {
    if (logs.length > 0 && searchQuery) {
      const filtered = logs.filter((log) =>
        JSON.stringify(log).toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredLogs(filtered);
    } else {
      setFilteredLogs(logs);
    }
  }, [searchQuery, logs]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchLogs(page);
  };

  const handleExport = () => {
    // Implementação básica para exportar para CSV
    if (logs.length === 0) return;

    const headers = ["Data", "Usuário", "Descrição"];

    const csvContent = [
      headers.join(","),
      ...filteredLogs.map((log) =>
        [
          format(new Date(log.data), "dd/MM/yyyy HH:mm:ss"),
          log.usuario || "Sistema",
          log.descricao,
        ].join(",")
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", `mural-os-${osNumber}-${osYear}.csv`);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => onClose()}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle>Mural da ordem de serviço</DialogTitle>
          <DialogDescription>
            Ordem de serviço: #{osNumber} - {osYear}
          </DialogDescription>
        </DialogHeader>

        <div className="flex justify-between items-center mb-4">
          <div className="relative w-64">
            <Input
              placeholder="Busca rápida"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          </div>

          <div>
            {" "}
            <Button variant="outline" onClick={() => setMessageModalIsOpen(true)} className="mr-2">
              <Plus className="h-4 w-4 mr-2" />
              Adicionar
            </Button>
            {/* <Button variant="outline" onClick={handleExport} disabled={logs.length === 0}>
              <Download className="h-4 w-4 mr-2" />
              Exportar
            </Button> */}
          </div>
        </div>

        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[180px]">Data</TableHead>
                <TableHead>Usuário</TableHead>
                <TableHead>Descrição</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={3} className="text-center py-8">
                    Carregando logs...
                  </TableCell>
                </TableRow>
              ) : filteredLogs.length > 0 ? (
                filteredLogs.map((log, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">
                      {format(new Date(log.data), "dd/MM/yyyy HH:mm:ss", {
                        locale: ptBR,
                      })}
                    </TableCell>
                    <TableCell>{log.usuario || "Sistema"}</TableCell>
                    <TableCell>{log.descricao}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={3} className="text-center py-8">
                    Nenhum registro encontrado
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {pagination && pagination.totalPages > 1 && (
          <div className="flex items-center justify-center space-x-2 mt-4">
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => handlePageChange(1)}>
              &lt;&lt;
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => handlePageChange(currentPage - 1)}>
              &lt;
            </Button>

            <span className="text-sm">
              Página {currentPage} de {pagination.totalPages}
            </span>

            <Button
              variant="outline"
              size="sm"
              disabled={currentPage === pagination.totalPages}
              onClick={() => handlePageChange(currentPage + 1)}>
              &gt;
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage === pagination.totalPages}
              onClick={() => handlePageChange(pagination.totalPages)}>
              &gt;&gt;
            </Button>
          </div>
        )}

        {pagination && (
          <div className="text-sm text-center text-muted-foreground mt-2">
            Exibindo {filteredLogs.length} de {pagination.total} registros
          </div>
        )}
      </DialogContent>
      <MessageModal
        isOpen={messageModalIsOpen}
        onClose={() => setMessageModalIsOpen(false)}
        osId={osId}
      />
    </Dialog>
  );
}
