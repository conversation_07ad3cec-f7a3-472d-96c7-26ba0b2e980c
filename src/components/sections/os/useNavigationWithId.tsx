"use client";
import { useRouter, useSearchParams } from "next/navigation";

export function useNavigationWithId() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const osId = searchParams.get("id");
  console.log("osId -->", osId);
  const goToChat = () => {
    if (osId) {
      router.push(`/dashboard/ordens-de-servico/${osId}/chat`);
    }
  };

  return { router, osId, goToChat };
}
