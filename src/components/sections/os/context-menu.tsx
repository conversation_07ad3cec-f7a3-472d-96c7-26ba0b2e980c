import React, { useEffect, useRef, useState } from 'react';

interface ContextMenuItem {
  label: string;
  onClick: () => void;
  icon?: React.ReactNode;
}

interface OSContextMenuProps {
  x: number;
  y: number;
  items: ContextMenuItem[];
  onClose: () => void;
}

export function OSContextMenu({ x, y, items, onClose }: OSContextMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({ x, y });
  
  // Ajustar posição apenas uma vez após a renderização inicial
  useEffect(() => {
    if (!menuRef.current) return;
    
    // Obter dimensões da janela e do menu
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    const menuRect = menuRef.current.getBoundingClientRect();
    
    // Obter posição de scroll da página
    const scrollX = window.scrollX;
    const scrollY = window.scrollY;
    
    // Calcular posições finais
    let finalX = x;
    let finalY = y;
    
    // Verificar se o menu sai da tela horizontalmente
    if (x + menuRect.width > windowWidth) {
      finalX = x - menuRect.width;
    }
    
    // Verificar se o menu sai da tela verticalmente
    if (y + menuRect.height > windowHeight) {
      finalY = y - menuRect.height;
    }
    
    // Garantir que o menu não fique fora da tela nas bordas superiores
    finalX = Math.max(finalX, 0);
    finalY = Math.max(finalY, 0);
    
    // Atualizar posição
    setPosition({ x: finalX, y: finalY });
  }, [x, y]);
  
  useEffect(() => {
    const handleOutsideClick = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };
    
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };
    
    // Registrar evento para fechar o menu ao clicar fora
    document.addEventListener('mousedown', handleOutsideClick);
    // Registrar evento para fechar o menu ao pressionar Escape
    document.addEventListener('keydown', handleEscape);
    
    return () => {
      document.removeEventListener('mousedown', handleOutsideClick);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [onClose]);

  return (
    <div
      ref={menuRef}
      className="fixed bg-white text-gray-800 border rounded-md shadow-lg p-1 z-50 w-48 min-w-[180px]"
      style={{
        top: `${position.y}px`,
        left: `${position.x}px`,
      }}
    >
      <ul className="py-1">
        {items.map((item, index) => (
          <li
            key={index}
            onClick={() => {
              item.onClick();
              onClose();
            }}
            className="px-3 py-2 text-sm flex items-center gap-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer rounded-sm"
          >
            {item.icon && <span className="text-muted-foreground">{item.icon}</span>}
            <span>{item.label}</span>
          </li>
        ))}
      </ul>
    </div>
  );
}