"use client";

import { Form<PERSON>rovider, useForm } from "react-hook-form";
import { defaultValues } from "@/components/forms/defaultValues";
import { osSchema } from "@/components/forms/schemas";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useCondutor } from "@/context/condutor-context";
import { useVeiculos } from "@/context/veiculos-context";
import { useTiposDeOs } from "@/context/tipos-de-os-context";
import { toast } from "sonner";
import { z } from "zod";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card } from "@/components/ui/card";
import { useCredenciado } from "@/context/credenciado-context";
import { useRouter } from "next/navigation";
import { Combobox } from "@/components/inputs/combo-box";
import { getServerSession } from "@/lib/auth/server-session";
import { Session } from "@/lib/auth/types";
import { useOS } from "@/context/os-context";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { AlertCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { getCredenciadoById, getCredenciadosByContratoId } from "@/serverActions/credenciadoAction";
import { checkSessionActive } from "@/serverActions/checkSession";

const uploadFile = async (file: File) => {
  const formData = new FormData();
  formData.append("file", file);

  const res = await fetch("/api/upload", {
    method: "POST",
    body: formData,
  });

  if (!res.ok) {
    throw new Error("Erro ao fazer upload do arquivo");
  }

  return await res.json();
};

// Component for displaying warranty parts
function VehicleWarrantyPartsAccordion({ vehicleId }: { vehicleId: string }) {
  const { ordensDeServico } = useOS();
  const [warrantyParts, setWarrantyParts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!vehicleId) {
      setWarrantyParts([]);
      setLoading(false);
      return;
    }

    setLoading(true);

    // Find all completed service orders for this vehicle
    const vehicleOrders = ordensDeServico.filter(
      (os) => os.status === "faturada" && os.veiculoId === vehicleId
    );

    const partsWithWarranty: any[] = [];

    // Extract parts with warranty from these orders
    vehicleOrders.forEach((os) => {
      const finishedQuotes = os.orcamentos?.filter((orc) => orc.status === "faturada") || [];

      finishedQuotes.forEach((orc) => {
        if (orc.processedPecas && orc.processedPecas.length > 0) {
          orc.processedPecas.forEach((peca) => {
            if (peca.garantia) {
              const garantiaDate = new Date(peca.garantia);
              const today = new Date();
              const statusGarantia = garantiaDate > today ? "Válida" : "Vencida";
              const diasRestantes =
                statusGarantia === "Válida"
                  ? Math.ceil((garantiaDate.getTime() - today.getTime()) / (1000 * 3600 * 24))
                  : 0;

              partsWithWarranty.push({
                id: peca.id,
                osNumber: os.osNumber,
                descricao: peca.descricao,
                codigo: peca.codigo || "",
                marca: peca.marca,
                garantiaDate: garantiaDate,
                statusGarantia,
                diasRestantes,
                dataManutencao: new Date(os.createdAt).toLocaleDateString("pt-BR"),
              });
            }
          });
        }
      });
    });

    setWarrantyParts(partsWithWarranty);
    setLoading(false);
  }, [vehicleId, ordensDeServico]);

  if (loading) {
    return <div className="text-sm text-gray-600">Carregando garantias...</div>;
  }

  if (warrantyParts.length === 0) {
    return (
      <div className="flex items-center gap-2 text-sm text-gray-600 py-2">
        <AlertCircle size={16} />
        <span>Este veículo não possui peças em garantia.</span>
      </div>
    );
  }

  // Count valid and expired warranties
  const validWarranties = warrantyParts.filter((part) => part.statusGarantia === "Válida").length;
  const expiredWarranties = warrantyParts.filter(
    (part) => part.statusGarantia === "Vencida"
  ).length;

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2 mb-3">
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
          Válidas: {validWarranties}
        </Badge>
        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
          Vencidas: {expiredWarranties}
        </Badge>
      </div>

      <div className="max-h-64 overflow-y-auto pr-1">
        {warrantyParts.map((part) => (
          <div key={part.id} className="mb-3 bg-muted/30 p-3 rounded-md">
            <div className="flex justify-between">
              <span className="font-medium">{part.descricao}</span>
              <div>
                {part.statusGarantia === "Válida" ? (
                  <Badge className="bg-green-500 text-white">{part.diasRestantes} dias</Badge>
                ) : (
                  <Badge className="bg-red-500 text-white">Vencida</Badge>
                )}
              </div>
            </div>
            <div className="text-sm text-muted-foreground mt-1">
              <div className="flex justify-between">
                <span>Código: {part.codigo || "N/A"}</span>
                <span>Marca: {part.marca}</span>
              </div>
              <div className="flex justify-between mt-1">
                <span>OS: #{part.osNumber}</span>
                <span>Data: {part.dataManutencao}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export function OSForm() {
  const methods = useForm<z.infer<typeof osSchema>>({
    resolver: zodResolver(osSchema),
    defaultValues: defaultValues.osSchema,
  });

  const { handleSubmit, setValue, watch } = methods;
  const [loading, setLoading] = useState(false);
  const [estados, setEstados] = useState<{ id: number; sigla: string; nome: string }[]>([]);
  const [cidades, setCidades] = useState<{ id: number; nome: string }[]>([]);
  const [session, setSessionData] = useState<Session | null>();
  const [credenciadosByContract, setCredenciadosByContract] = useState<
    credenciado[]
  >([]);
  const [files, setFiles] = useState<File[]>([]);

  const { condutores = [] } = useCondutor();
  const { veiculos = [] } = useVeiculos();
  const { tiposDeOs = [] } = useTiposDeOs();
  const { credenciados = [] } = useCredenciado();
  const { ordensDeServico } = useOS();
  const { centrosDeCusto } = useCentroDeCusto();

  const route = useRouter();

  const checkEmpenhoAvailable = (veiculo: veiculo) => {
    if (!veiculo?.faturamentoVeiculo?.empenho) {
      toast.error("Veículo não possui empenho cadastrado");
      return false;
    }

    const empenho = veiculo.faturamentoVeiculo.empenho;
    console.log("checkEmpenhoAvailable", empenho);
    const totalEmpenho = (empenho.valor_pecas || 0) + (empenho.valor_servicos || 0);
    console.log("totalEmpenho", totalEmpenho);
    if (totalEmpenho <= 0) {
      toast.error("Veículo não possui saldo de empenho disponível");
      return false;
    }

    return true;
  };

  useEffect(() => {
    async function carregarEstados() {
      const sessionData = await getServerSession();
      setSessionData(sessionData);
      try {
        const response = await fetch("https://servicodados.ibge.gov.br/api/v1/localidades/estados");
        if (response.ok) {
          const data = await response.json();
          setEstados(data.sort((a: any, b: any) => a.nome.localeCompare(b.nome)));
        }
      } catch (error) {
        console.error("Erro ao carregar estados:", error);
      }
    }
    carregarEstados();
  }, []);

  useEffect(() => {
    async function fetchCredenciadosByContract() {
      if (session?.contratoId) {
        try {
          const response = await getCredenciadosByContratoId(session.contratoId);
          if (response.success && response.data && Array.isArray(response.data.credenciados)) {
            setCredenciadosByContract(response.data.credenciados);
          }
        } catch (error) {
          console.error("Erro ao buscar credenciados pelo contrato:", error);
        }
      }
    }

    fetchCredenciadosByContract();
  }, [session?.contratoId]);

  useEffect(() => {
    async function carregarCidades() {
      if (!watch("estado_de_localizacao")) {
        setCidades([]);
        return;
      }

      try {
        const response = await fetch(
          `https://servicodados.ibge.gov.br/api/v1/localidades/estados/${watch(
            "estado_de_localizacao"
          )}/municipios`
        );
        if (response.ok) {
          const data = await response.json();
          setCidades(data.sort((a: any, b: any) => a.nome.localeCompare(b.nome)));
        }
      } catch (error) {
        console.error("Erro ao carregar cidades:", error);
      }
    }
    carregarCidades();
  }, [watch("estado_de_localizacao")]);

  const undo = async (id: string) => {
    const res = await fetch(`/api/os/${id}`, { method: "DELETE" });
    if (!res.ok) {
      toast("Ops, houve um erro", {
        description: "Não foi possível cancelar a criação da ordem de serviço",
      });
    }
    toast("Tudo certo!", {
      description: "Sua ordem de serviço não foi criada",
    });
  };
  async function onSubmit(values: z.infer<typeof osSchema>) {
    const isSessionActive = await checkSessionActive();

    if (!isSessionActive) {
      toast.info("Sua sessão expirou. Por favor, faça login novamente.");
      setTimeout(() => {
        window.location.href = "/login";
      }, 1000);
      return;
    }
    const selectedVehicle = veiculos.find((v) => v.id === values.veiculoId);

    if (!selectedVehicle || !checkEmpenhoAvailable(selectedVehicle)) {
      return;
    }
    setLoading(true);
    let arquivosLocations: any[] = [];

    if (files.length > 0) {
      arquivosLocations = await Promise.all(
        files.map((file) => uploadFile(file))
      );
    }

    const quoteExpirationDate = new Date(Date.now() + values.quoteExpiration * 3600000);

    const dataOs = {
      ...values,
      quoteExpirationDate:quoteExpirationDate,
      status: "lançada",
      gestorCriador: session?.name,
      arquivos: JSON.stringify(arquivosLocations),
    };
    try {
      const res = await fetch("/api/os", {
        method: "POST",
        headers: {
          "Content-type": "application/json",
        },
        body: JSON.stringify(dataOs),
      });

      if (!res.ok) {
        throw new Error("Ops, alguma coisa deu errado");
      }

      const data = await res.json();
      toast("Ordem de serviço criada com sucesso!", {
        description: "Sua ordem de serviço foi criada com sucesso",
        action: <Button onClick={async () => await undo(data.data.id)}>Cancelar criação</Button>,
      });
      window.location.href = "/dashboard/ordens-de-servico/ordens";
    } catch (error: any) {
      if (error.response?.status === 401) route.push("/login");
      console.log("Erro ao criar ordem de serviço:", error.message);
      toast("Erro ao criar ordem de serviço", {
        description: "Houve um problema ao processar sua solicitação",
      });
    } finally {
      setLoading(false);
    }
  }

  const isMobilizado = watch("mobilizado");
  const veiculoId = watch("veiculoId");

  const veiculoSelecionado = veiculos.find((v) => v.id === veiculoId);

  // Update fleet type display when vehicle changes
  useEffect(() => {
    if (veiculoSelecionado?.tipo_de_frota?.descricao) {
      setValue("tipo_de_frota_display", veiculoSelecionado.tipo_de_frota.descricao);
    } else {
      setValue("tipo_de_frota_display", "");
    }
  }, [veiculoSelecionado, setValue]);

  const [credenciadoOficina, setCredenciadoOficina] = useState<any>(null);

  useEffect(() => {
    async function fetchCredenciadoOficina() {
      if (session?.roles.includes("ORCAMENTISTA_OFICINA") && session.credenciadoId) {
        try {
          const response = await getCredenciadoById(session.credenciadoId as string);
          if (response?.data?.credenciado) {
            setCredenciadoOficina(response.data.credenciado);
          }
        } catch (error) {
          console.error("Erro ao buscar credenciado oficina:", error);
        }
      }
    }
    fetchCredenciadoOficina();
  }, [session?.roles, session?.credenciadoId]);

  const credenciadosVinculados = session?.roles.includes("ORCAMENTISTA_OFICINA")
    ? credenciadoOficina
      ? [credenciadoOficina]
      : []
    : Array.isArray(credenciadosByContract)
      ? credenciadosByContract.filter((credenciado) => credenciado.ativo)
      : [];

  const condutoresAtivos = condutores.filter((condutor) => condutor.status);
  //
  const veiculosAtivosSemOSAutorizada = session?.contrato?.restringir_veiculos
    ? veiculos.filter(
      (veiculo) =>
        veiculo.status === "Ativo" &&
        !veiculo.os.some((os) => os.status === "autorizada" || os.status === "execucao")
    )
    : veiculos.filter((veiculo) => veiculo.status === "Ativo");

  const complementOS = ordensDeServico.filter((os) => String(os.veiculoId) === String(veiculoId));

  let veiculoData: veiculo[] = [];
  if (session && !session.roles.includes("ADMIN") && session.centro_de_custoId) {
    if (session.unidade_filha_id) {
      veiculoData = (() => {
        let filteredData = veiculosAtivosSemOSAutorizada.filter(
          (veiculo) => veiculo.lotacao_veiculos?.centro_custoID === session.unidade_filha_id
        );
        if (filteredData.length === 0) {
          filteredData = veiculosAtivosSemOSAutorizada.filter(
            (veiculo) => veiculo.lotacao_veiculos?.centro_custoID === session.centro_de_custoId
          );
        }
        return filteredData;
      })();
    } else if (session.centro_de_custoId) {
      const unidadesFilhas = centrosDeCusto
        .flatMap((centro) => centro.centro_custos_filhos)
        .filter((uniFilho) => uniFilho.centro_custo_ascdID === session.centro_de_custoId);
      const filteredVeiculos = veiculosAtivosSemOSAutorizada.filter((veiculo) =>
        unidadesFilhas.some((centro) => centro.id === veiculo?.lotacao_veiculos?.centro_custoID)
      );
      if (filteredVeiculos.length > 0) {
        veiculoData = filteredVeiculos;
      } else {
        veiculoData = veiculosAtivosSemOSAutorizada.filter(
          (veiculo) => veiculo.lotacao_veiculos?.centro_custo?.id === session.centro_de_custoId
        );
      }
    } else {
      veiculoData = veiculosAtivosSemOSAutorizada.filter(
        (veiculo) => veiculo.lotacao_veiculos?.centro_custo_ascdID === session.centro_de_custoId
      );
    }
  } else {
    veiculoData = veiculosAtivosSemOSAutorizada;
  }

  let condutorsAtivosData: condutor[] = [];
  if (session && !session.roles.includes("ADMIN") && session.centro_de_custoId) {
    if (session.unidade_filha_id) {
      let filteredData = condutores.filter(
        (condutor) => condutor.lotacao_condutor?.centro_custoID === session.unidade_filha_id
      );
      if (filteredData.length === 0) {
        filteredData = condutores.filter(
          (condutor) => condutor.lotacao_condutor?.centro_custoID === session.centro_de_custoId
        );
      }
      condutorsAtivosData = filteredData;
    } else if (session.centro_de_custoId) {
      const unidadesFilhas = centrosDeCusto
        .flatMap((centro) => centro.centro_custos_filhos)
        .filter((uniFilho) => uniFilho.centro_custo_ascdID === session.centro_de_custoId);
      const filteredCondultores = condutoresAtivos.filter((condutor) =>
        unidadesFilhas.some((centro) => centro.id === condutor?.lotacao_condutor?.centro_custoID)
      );
      if (filteredCondultores.length > 0) {
        condutorsAtivosData = filteredCondultores;
      } else {
        condutorsAtivosData = condutoresAtivos.filter(
          (condutor) => condutor.lotacao_condutor?.centro_custoID === session.centro_de_custoId
        );
      }
    } else {
      condutorsAtivosData = condutoresAtivos.filter(
        (condutor) => condutor.lotacao_condutor?.centro_custoID === session.centro_de_custoId
      );
    }
  } else {
    condutorsAtivosData = condutoresAtivos;
  }

  return (
    <FormProvider {...methods}>
      <Card>
        <form onSubmit={handleSubmit(onSubmit)} className="w-full">
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Coluna esquerda */}
            <div className="w-full lg:flex-1 space-y-4">
              {/* Seção 1: Informações do Veículo e Condutor */}
              <div className="rounded-md border p-4 space-y-4">
                <h2 className="text-lg font-medium text-gray-900 border-b pb-2">
                  Informações Básicas
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Combobox
                    datas={veiculoData}
                    title="Veículo *"
                    placeholder="Selecione um veículo"
                    referenceId="veiculoId"
                    name="veiculoId"
                    chave="placa+marca.descricao+modelo.descricao"
                    description="Veículo para a ordem de serviço"
                    fieldClassName="w-full"
                  />

                  <Combobox
                    datas={condutorsAtivosData}
                    title="Condutor *"
                    placeholder="Selecione um condutor"
                    referenceId="condutorId"
                    chave="nome"
                    name="condutorId"
                    description="Condutor responsável"
                  />
                </div>

                {/* Informações do Veículo Selecionado */}
                {veiculoSelecionado && (
                  <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                      <div>
                        <span className="font-medium text-gray-600 dark:text-gray-400">Tipo de Frota:</span>
                        <div className="text-gray-900 dark:text-gray-100">
                          {veiculoSelecionado.tipo_de_frota?.descricao || "N/A"}
                        </div>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600 dark:text-gray-400">Tipo de Veículo:</span>
                        <div className="text-gray-900 dark:text-gray-100">
                          {veiculoSelecionado.tipo_de_veiculo?.descricao || "N/A"}
                        </div>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600 dark:text-gray-400">Marca/Modelo:</span>
                        <div className="text-gray-900 dark:text-gray-100">
                          {veiculoSelecionado.marca?.descricao} {veiculoSelecionado.modelo?.descricao}
                        </div>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600 dark:text-gray-400">Placa:</span>
                        <div className="text-gray-900 dark:text-gray-100 font-mono">
                          {veiculoSelecionado.placa}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Seção 2: Tipo de Serviço */}
              <div className="rounded-md border p-4 space-y-4">
                <h2 className="text-lg font-medium text-gray-900 border-b pb-2">
                  Tipo de Serviço
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Combobox
                    datas={tiposDeOs}
                    title="Tipo de serviço *"
                    placeholder="Selecione um tipo de serviço"
                    referenceId="tipo_de_osId"
                    chave="descricao"
                    name="tipo_de_osId"
                    description="Categoria do serviço a ser realizado"
                  />

                  <div className="space-y-2">
                    <Label>Tipo de manutenção *</Label>
                    <Select {...methods.register("tipo_de_manutencao")}>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o tipo de manutenção" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Corretiva">Corretiva</SelectItem>
                        <SelectItem value="Preventiva">Preventiva</SelectItem>
                        <SelectItem value="Preditiva">Preditiva</SelectItem>
                        <SelectItem value="Sinistro">Sinistro</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Seção 3: Localização da Criação */}
              <div className="rounded-md border p-4 space-y-4">
                <h2 className="text-lg font-medium text-gray-900 border-b pb-2">
                  Localização da Criação
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Estado de localização *</Label>
                    <Select
                      {...methods.register("estado_de_localizacao")}
                      onValueChange={(value) => {
                        setValue("estado_de_localizacao", value);
                        setValue("cidade_de_localizacao", "");
                      }}>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o estado" />
                      </SelectTrigger>
                      <SelectContent>
                        {estados.map((estado) => (
                          <SelectItem key={estado.id} value={estado.sigla}>
                            {estado.nome}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Cidade de localização *</Label>
                    <Select
                      onValueChange={(value) => {
                        setValue("cidade_de_localizacao", value);
                      }}
                      {...methods.register("cidade_de_localizacao")}
                      disabled={!watch("estado_de_localizacao") || cidades.length === 0}>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione uma cidade" />
                      </SelectTrigger>
                      <SelectContent>
                        {cidades.map((cidade) => (
                          <SelectItem key={cidade.id} value={cidade.nome}>
                            {cidade.nome}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Seção 4: Detalhes do Veículo e Configurações */}
              <div className="rounded-md border p-4 space-y-4">
                <h2 className="text-lg font-medium text-gray-900 border-b pb-2">
                  Detalhes do Veículo e Configurações
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>Odômetro atual (Km)</Label>
                    <Input
                      type="number"
                      placeholder="Ex: 123456"
                      {...methods.register("odometro_atual", {
                        valueAsNumber: true,
                      })}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Veículo imobilizado *</Label>
                    <div className="flex items-center gap-2">
                      <Button
                        type="button"
                        variant={isMobilizado === true ? "default" : "outline"}
                        onClick={() => setValue("mobilizado", true)}
                        className="flex-1">
                        Sim
                      </Button>
                      <Button
                        type="button"
                        variant={isMobilizado === false ? "default" : "outline"}
                        onClick={() => setValue("mobilizado", false)}
                        className="flex-1">
                        Não
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Prazo para cotação (horas) *</Label>
                    <Input
                      type="number"
                      placeholder="24"
                      min={24}
                      max={72}
                      {...methods.register("quoteExpiration", {
                        valueAsNumber: true,
                      })}
                    />
                  </div>
                </div>
              </div>
              {/* Seção 5: Descrição e Arquivos */}
              <div className="rounded-md border p-4 space-y-4">
                <h2 className="text-lg font-medium text-gray-900 border-b pb-2">
                  Descrição e Documentos
                </h2>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Descrição do serviço ou problema *</Label>
                    <Textarea
                      placeholder="Descreva detalhadamente o problema ou serviço necessário..."
                      className="min-h-32"
                      {...methods.register("descricao")}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Arquivos (Fotos e documentos)</Label>
                    <Input
                      type="file"
                      multiple
                      accept="image/*,.pdf,.doc,.docx"
                      onChange={(e) => {
                        if (e.target.files) {
                          setFiles(Array.from(e.target.files));
                        }
                      }}
                      className="mt-1"
                    />
                    {files.length > 0 && (
                      <div className="mt-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                        <p className="text-sm font-medium mb-2">
                          Arquivos selecionados ({files.length}):
                        </p>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          {files.map((file, index) => (
                            <li key={index} className="flex items-center gap-2">
                              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                              {file.name}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Coluna direita */}
            <div className="w-full lg:w-80 xl:w-96 space-y-4">
              {/* Seção Lotação e Faturamento */}
              <div className="rounded-md border p-4 space-y-4">
                <h2 className="text-lg font-medium text-gray-900 border-b pb-2">
                  Lotação e Faturamento
                </h2>
                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-md">
                  {veiculoSelecionado ? (
                    <div className="space-y-4">
                      {/* Lotação */}
                      <div>
                        <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                          LOTAÇÃO
                        </h3>
                        <div className="space-y-1 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Centro de custo:</span>
                            <span className="text-gray-900 dark:text-gray-100 font-medium">
                              {veiculoSelecionado.lotacao_veiculos?.centro_custo?.descricao || "N/A"}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Dotação orçamentária:</span>
                            <span className="text-gray-900 dark:text-gray-100">
                              {veiculoSelecionado.lotacao_veiculos?.centro_custo?.dotacao_orcamentista || "N/A"}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Empenho */}
                      <div>
                        <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                          EMPENHO
                        </h3>
                        <div className="space-y-1 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Centro de custo:</span>
                            <span className="text-gray-900 dark:text-gray-100 font-medium">
                              {veiculoSelecionado.faturamentoVeiculo?.centro_custo?.descricao || "N/A"}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Nota de empenho:</span>
                            <span className="text-gray-900 dark:text-gray-100">
                              {veiculoSelecionado.faturamentoVeiculo?.empenho?.nota_empenho || "N/A"}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Saldo disponível:</span>
                            <span className="text-gray-900 dark:text-gray-100 font-medium">
                              {veiculoSelecionado?.faturamentoVeiculo?.empenho?.saldo_total != null
                                ? (
                                  veiculoSelecionado.faturamentoVeiculo.empenho.saldo_total / 100
                                ).toLocaleString("pt-BR", {
                                  style: "currency",
                                  currency: "BRL",
                                })
                                : "N/A"}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Selecione um veículo para visualizar informações de lotação e faturamento
                      </p>
                    </div>
                  )}
                </div>
              </div>
              {/* Seção de Peças em Garantia */}
              <div className="rounded-md border p-4 space-y-4">
                <Accordion type="single" collapsible className="w-full">
                  <AccordionItem value="warranty-parts" className="border-none">
                    <AccordionTrigger className="text-lg font-medium text-gray-900 hover:no-underline">
                      Peças em Garantia
                    </AccordionTrigger>
                    <AccordionContent className="pt-2">
                      {veiculoId ? (
                        <VehicleWarrantyPartsAccordion vehicleId={veiculoId} />
                      ) : (
                        <div className="text-center py-4">
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Selecione um veículo para visualizar peças em garantia
                          </p>
                        </div>
                      )}
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </div>

              {/* Seção Orçamentação */}
              <div className="rounded-md border p-4 space-y-4">
                <h2 className="text-lg font-medium text-gray-900 border-b pb-2">
                  Configuração de Orçamentação
                </h2>

                <div className="space-y-4">
                  <Combobox
                    datas={credenciadosVinculados}
                    title="Credenciado orçamentista *"
                    placeholder="Selecione um credenciado"
                    referenceId="credenciadoId"
                    chave="informacoes.0.razao_social+informacoes.0.cnpj"
                    name="credenciadoId"
                    description="Credenciado responsável pelo orçamento"
                  />

                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                      <div className="space-y-1">
                        <h3 className="font-medium text-gray-900 dark:text-gray-100">
                          Orçamento individual?
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Define se o orçamento será exclusivo para o orçamentista selecionado
                        </p>
                      </div>
                      <Switch {...methods.register("orcamento_individual")} />
                    </div>

                    <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                      <div className="flex-1">
                        <Label className="font-medium text-gray-900 dark:text-gray-100">
                          Mínimo de orçamentos *
                        </Label>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Quantidade mínima necessária para aprovação
                        </p>
                      </div>
                      <Input
                        type="number"
                        min="1"
                        max="10"
                        className="w-20"
                        defaultValue={3}
                        {...methods.register("minimun_orcamento", {
                          valueAsNumber: true,
                        })}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Botão de submissão */}
          <div className="flex justify-center lg:justify-end my-6 px-4">
            <Button
              type="submit"
              disabled={loading}
              className="bg-teal-500 hover:bg-teal-600 text-white font-medium px-8 py-3 w-full sm:w-auto lg:mr-[400px]">
              {loading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Cadastrando...
                </div>
              ) : (
                "Cadastrar Ordem de Serviço"
              )}
            </Button>
          </div>
        </form>
      </Card>
    </FormProvider>
  );
}
