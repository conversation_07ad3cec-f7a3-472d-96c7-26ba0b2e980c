"use client";

import { TipoDeOsConfig } from "@/components/forms/inputConfig";
import { tipoDeOsSchema } from "@/components/forms/schemas";
import { SheetForm } from "@/components/forms/sheet-form";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useTiposDeOs } from "@/context/tipos-de-os-context";
import { ColumnDef } from "@tanstack/react-table";
import { Delete, Edit, MoreHorizontal } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { z } from "zod";

interface ActionsCellProps {
  row: {
    original: tipo_de_os;
  };
}

export const ActionsCell: React.FC<ActionsCellProps> = ({ row }) => {
  const { setTiposDeOs } = useTiposDeOs();
  const [open, setOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<tipo_de_os | null>(null);

  const editItem = async () => {
    setSelectedItem(row.original);
    setOpen(true);
  };

  const updateItem = async (values: z.infer<typeof tipoDeOsSchema>) => {
    try {
      const res = await fetch(`/api/tipos_de_os/${row.original.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values),
      });

      if (!res.ok) {
        toast("Erro ao atualizar o tipo de OS");
        return;
      }

      const data = await res.json();
      setTiposDeOs((prev) => prev.map((item) => (item.id === data.data.id ? data.data : item)));

      toast("Tipo de OS atualizado com sucesso!");
      setOpen(false);
    } catch (error) {
      toast("Erro ao atualizar o tipo de OS");
    }
  };

  const deleteItem = async () => {
    const res = await fetch(`/api/tipos_de_os/${row.original.id}`, {
      method: "DELETE",
    });
    if (!res.ok) {
      toast("Ops, algo deu errado", {
        description: "Houve um erro ao deletar o tipo de OS",
      });
      return;
    }
    setTiposDeOs((prev) => prev.filter((item) => item.id !== row.original.id));
    toast("Tipo de OS deletado com sucesso!");
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Abrir o menu</span>
            <MoreHorizontal />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Ações</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={editItem}>
            Editar
            <Edit />
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => {
              toast("Tem certeza?", {
                description: "Essa ação não pode ser desfeita",
                action: {
                  label: "Tenho certeza!",
                  onClick: deleteItem,
                },
              });
            }}>
            Deletar
            <Delete />
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      {selectedItem && open && (
        <SheetForm
          title="Editar Tipo de OS"
          schema={tipoDeOsSchema}
          onSubmit={updateItem}
          triggerLabel="Editar Tipo de OS"
          defaultValues={selectedItem}>
          <GenericFormsInput fieldConfig={TipoDeOsConfig} variants="single" />
        </SheetForm>
      )}
    </>
  );
};

export const osTypesColumn: ColumnDef<tipo_de_os>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "descricao",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Descricao do tipo" />,
  },
  {
    accessorKey: "obs",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Observações" />,
  },
  {
    accessorKey: "prazo_para_execucao",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Prazo (dias)" />,
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => <ActionsCell row={row} />,
  },
];

export function OSTypes() {
  const { tiposDeOs, setTiposDeOs } = useTiposDeOs();

  async function onNewOsType(values: z.infer<typeof tipoDeOsSchema>) {
    const response = await fetch("/api/tipos-de-os", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });
    if (!response.ok) {
      toast("Oops! Alguma coisa deu errado", {
        description: "Não foi possivel criar o tipo",
      });
      return;
    }
    const data = await response.json();
    setTiposDeOs((prev) => [...prev, data.data]);
    window.location.reload();
  }

  return (
    <DataTable
      exportTo={true}
      data={tiposDeOs}
      onNewItem={onNewOsType}
      columns={osTypesColumn}
      newItem={{
        name: "tipo de OS",
        defaultValues: { descricao: "", obs: "", prazo_para_execucao: "" },
        fieldConfig: TipoDeOsConfig,
        schema: tipoDeOsSchema,
      }}
    />
  );
}
