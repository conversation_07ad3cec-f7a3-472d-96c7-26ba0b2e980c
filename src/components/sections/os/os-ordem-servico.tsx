"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useParams } from "next/navigation";

import { CardOrcamento } from "./card-orcamento";
import ServiceOrderDetails from "../orcamento/edit-orcamento/service-order-details";
import TechnicalInfo from "../orcamento/edit-orcamento/technical-info";
import FileUploader from "../orcamento/edit-orcamento/file-upload";
import { Detalhes } from "./detalhes";
import {
  getOrcamentoByVehicleId,
  updateOrcamentoStatus,
  updateOsStatus,
} from "@/serverActions/orcamentoAction";
import { useSession } from "@/components/session/use-session";
import { updateEmpenhoSaldosBloqueadosAction } from "@/serverActions/empenhoAction";
import { useContrato } from "@/context/contrato-context";
import { VeiculoHistoricoManutencao } from "../veiculos/veiculo-historico-manutencao";

export function OSOrdemServico({
  os,
  currentStatus,
}: {
  os: OS | null | undefined;
  currentStatus?: string;
}) {
  const { session } = useSession();
  const { contratos } = useContrato();
  const [expanded, setExpanded] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedOrcamentos, setSelectedOrcamentos] = useState<Orcamento[]>([]);
  const [activeTab, setActiveTab] = useState("ordem-de-servico");
  const [loading, setLoading] = useState(true);
  const [ordemServicoData, setOrdemServicoData] = useState<any>(null);
  const [veiculoData, setVeiculoData] = useState<veiculo | undefined>(
    undefined
  );
  const [orcamentos, setOrcamentos] = useState<Orcamento[]>([]);
  const [fichaTecnicaOpen, setFichaTecnicaOpen] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showExcessiveSpendingModal, setShowExcessiveSpendingModal] =
    useState(false);
  const [spendingStatus, setSpendingStatus] = useState("");
  const [selectedOrcamento, setSelectedOrcamento] = useState<
    Orcamento | undefined
  >();

  const handleFilesChange = (files: File[]) => {
    setUploadedFiles(files);
  };

  const handleSelectOrcamento = (orc: Orcamento) => {
    // Verifica se o orçamento já está selecionado
    if (selectedOrcamentos.some((item) => item.id === orc.id)) {
      // Se estiver, remove da seleção
      setSelectedOrcamentos(
        selectedOrcamentos.filter((item) => item.id !== orc.id)
      );
    } else {
      // Se não estiver, adiciona à seleção
      setSelectedOrcamentos([...selectedOrcamentos, orc]);
    }
    // Mantém compatibilidade com o código existente
    setSelectedOrcamento(orc);
  };

  const checkEmpenhoLimits = (pecas: number, servicos: number) => {
    if (!os?.veiculo?.faturamentoVeiculo?.empenho) {
      toast.error("Não foi possível verificar o empenho do veículo");
      return false;
    }

    const totalValue = (pecas + servicos) / 100;
    if (session?.nivel_aprovacao > 0 && totalValue > session?.nivel_aprovacao) {
      toast.error(
        `O valor total (${totalValue.toLocaleString("pt-BR", {
          style: "currency",
          currency: "BRL",
        })}) ultrapassa o seu limite de ${session?.nivel_aprovacao.toLocaleString(
          "pt-BR",
          {
            style: "currency",
            currency: "BRL",
          }
        )} para aprovação`
      );
      return false;
    }
    const empenho = os.veiculo.faturamentoVeiculo.empenho;
    const limiteServicos = empenho.saldo_servicos;
    const limitePecas = empenho.saldo_pecas;
    if (pecas > limitePecas) {
      toast.error(`O valor das peças ultrapassa o saldo disponível para peças`);
      return false;
    }

    if (servicos > limiteServicos) {
      toast.error(
        `O valor dos serviços (${servicos.toLocaleString("pt-BR", {
          style: "currency",
          currency: "BRL",
        })}) ultrapassa o saldo disponível para serviços (${limiteServicos.toLocaleString(
          "pt-BR",
          {
            style: "currency",
            currency: "BRL",
          }
        )})`
      );
      return false;
    }

    return true;
  };

  const verificarOrcamentosComValorZero = () => {
    const orcamentosComValorZero = selectedOrcamentos.filter(
      (orc) => !orc.valorTotal || orc.valorTotal === 0
    );

    if (orcamentosComValorZero.length > 0) {
      toast.error("Não é possível enviar orçamentos com valor total zero");
      return false;
    }

    return true;
  };

  useEffect(() => {
    if (!os || !os.id) {
      setOrcamentos([]);
      setLoading(false);
      return;
    }
    if (
      ordemServicoData?.id === os.id &&
      veiculoData?.id === os.veiculo?.id &&
      JSON.stringify(orcamentos) === JSON.stringify(os.orcamentos || [])
    ) {
      setLoading(false);
      return;
    }
    try {
      setOrdemServicoData(os);
      setVeiculoData(os.veiculo);
      setOrcamentos(
        Array.isArray(os.orcamentos)
          ? session?.roles.includes("ORCAMENTISTA_OFICINA")
            ? os.orcamentos.filter(
                (orc) => orc.credenciadoId === session.credenciadoId
              )
            : os.orcamentos
          : []
      );
    } catch (error) {
      toast.error("Erro ao carregar os dados");
    } finally {
      setLoading(false);
    }
  }, [os?.id]);

  const verificarGastoVeiculo = async (orcamento: Orcamento) => {
    const currentContrato = contratos?.find((contrato) => {
      return contrato.id === session?.contratoId;
    });
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
    const veiculoId = os?.veiculo?.id;
    if (!veiculoId) {
      toast.error("ID do veículo não encontrado");
      return "N/A";
    }
    const res = await getOrcamentoByVehicleId(veiculoId);
    const allOs = res.data.ordens as OS[];
    const finalizadas = allOs.flatMap((order) =>
      (order.orcamentos || []).filter(
        (orc) =>
          orc.status === "faturada" && new Date(orc.createdAt) >= oneYearAgo
      )
    );
    if (!finalizadas || finalizadas.length === 0) return "N/A";
    const currentOrcamentoTotal = orcamento.valorTotal / 100;
    const total =
      finalizadas.reduce((acc, orc) => acc + (orc.valorTotal || 0), 0) / 100 +
      currentOrcamentoTotal;
    const totalWithCurrent = total + currentOrcamentoTotal;
    const venalVeiculo = parseInt(os?.veiculo?.valor_venal || "0");
    if (!currentContrato?.limite_gastos_percent) {
      return "N/A";
    }
    const spendingLimit =
      venalVeiculo * (Number(currentContrato.limite_gastos_percent) / 100);
    if (totalWithCurrent > spendingLimit) {
      return "Excedido";
    }
    return "Dentro do limite";
  };

  const validarEnvioParaAprovacao = () => {
    // Verifica se a quantidade mínima de orçamentos foi selecionada
    const minimumRequired = parseInt(os?.minimun_orcament || "3", 10);

    if (selectedOrcamentos.length < minimumRequired) {
      toast.error(
        `É necessário selecionar no mínimo ${minimumRequired} orçamentos para enviar para análise`
      );
      return false;
    }

    // Verifica se algum dos orçamentos selecionados tem valor zero
    if (!verificarOrcamentosComValorZero()) {
      return false;
    }

    return true;
  };

  const onConfirmEnviarAprovacao = async () => {
    if (!selectedOrcamento) {
      toast.error("Nenhum orçamento foi selecionado");
      return;
    }

    // Validar requisitos antes de prosseguir
    if (!validarEnvioParaAprovacao()) {
      return;
    }

    // Check empenho limits first
    const isWithinLimits = checkEmpenhoLimits(
      selectedOrcamento.processedPecas?.reduce(
        (acc, pecas) => acc + (pecas.valorNegociado || 0),
        0
      ) || 0,
      selectedOrcamento.processedServicos?.reduce(
        (acc, servicos) => acc + (servicos.valor || 0),
        0
      ) || 0
    );

    if (!isWithinLimits) {
      return;
    }

    await processEnviarAprovacao();
  };

  const onEnviarParaOrcamentacao = async () => {
    if (!os || !orcamentos.length) {
      toast.error("OS ou orçamento não encontrado");
      return;
    }
    try {
      // Atualiza status da OS para "orcamentaçao"
      await updateOsStatus(os.id, "orcamentaçao", orcamentos[0].id);

      // Atualiza status do orçamento principal para "enviado"
      await updateOrcamentoStatus(orcamentos[0].id, "enviado");

      toast.success("OS enviada para orçamentação e orçamento enviado!");
      window.location.href = `/dashboard/ordens-de-servico/ordens`;
    } catch (error) {
      toast.error("Erro ao enviar para orçamentação");
      console.error(error);
    }
  };

  const processEnviarAprovacao = async () => {
    try {
      if (os && selectedOrcamentos.length > 0) {
        const data_emissao = new Date();
        const principalOrcamento = selectedOrcamentos[0];

        await updateOsStatus(os.id, "analise", principalOrcamento.id);

        const atualizacoes = selectedOrcamentos.map((orcamento) =>
          updateOrcamentoStatus(orcamento.id, "analise", data_emissao)
        );

        await Promise.all(atualizacoes);

        toast.success(
          `${selectedOrcamentos.length} orçamentos enviados para análise com sucesso`
        );
        setShowApproveModal(false);
        setShowExcessiveSpendingModal(false);
        window.location.href = `/dashboard/ordens-de-servico/ordens`;
      }
    } catch (error) {
      console.error("Erro ao enviar para aprovação:", error);
      toast.error("Erro ao enviar para aprovação");
    }
  };

  const onConfirmEnviarAutorizacao = async () => {
    if (!selectedOrcamento) {
      toast.error("Nenhum orçamento foi selecionado");
      return;
    }

    const isWithinLimits = checkEmpenhoLimits(
      selectedOrcamento.processedPecas?.reduce(
        (acc, pecas) => acc + (pecas.valorNegociado || 0),
        0
      ) || 0,
      selectedOrcamento.processedServicos?.reduce(
        (acc, servicos) => acc + (servicos.valor || 0),
        0
      ) || 0
    );

    if (!isWithinLimits) {
      return;
    }

    // Check vehicle spending status
    const status = await verificarGastoVeiculo(selectedOrcamento);
    setSpendingStatus(status);

    if (status === "Excedido") {
      // Show confirmation modal for excessive spending
      setShowExcessiveSpendingModal(true);
      setShowApproveModal(false);
      return;
    }
    // If spending is not excessive, proceed normally
    await processEnviarAutorizacao();
  };
  const processEnviarAutorizacao = async () => {
    if (!selectedOrcamento) {
      toast.error("Nenhum orçamento foi selecionado");
      return;
    }

    try {
      if (os) {
        await updateOrcamentoStatus(selectedOrcamento.id, "autorizada");
        await updateOsStatus(
          os.id,
          "autorizada",
          selectedOrcamento.id,
          undefined,
          undefined
        );
        if (os.veiculo?.faturamentoVeiculo.empenho?.id) {
          await updateEmpenhoSaldosBloqueadosAction(
            os.veiculo?.faturamentoVeiculo.empenho?.id,
            {
              bloqueado_pecas:
                selectedOrcamento.processedPecas?.reduce(
                  (acc, pecas) => acc + pecas.valorNegociado,
                  0
                ) || 0,
              bloqueado_servicos:
                selectedOrcamento.processedServicos?.reduce(
                  (acc, servicos) => acc + (servicos.valor || 0),
                  0
                ) || 0,
            }
          );
          toast.success("Orçamento autorizado com sucesso");
          setShowApproveModal(false);
          window.location.href = `/dashboard/ordens-de-servico/ordens`;
        }
      }
    } catch (error) {
      console.error("Erro ao enviar para aprovação:", error);
      toast.error("Erro ao enviar para aprovação");
    }
  };

  // Add new function to handle complementary budget authorization
  const onAutorizarOrcamentoComplementar = async () => {
    if (!selectedOrcamento) {
      toast.error("Nenhum orçamento foi selecionado");
      return;
    }

    const isWithinLimits = checkEmpenhoLimits(
      selectedOrcamento.processedPecas?.reduce(
        (acc, pecas) => acc + (pecas.valorNegociado || 0),
        0
      ) || 0,
      selectedOrcamento.processedServicos?.reduce(
        (acc, servicos) => acc + (servicos.valor || 0),
        0
      ) || 0
    );

    if (!isWithinLimits) {
      return;
    }

    try {
      if (os) {
        // Update the orcamento status to authorized
        await updateOrcamentoStatus(
          selectedOrcamento.id,
          "complementar_authorized"
        );

        // Update empenho saldos bloqueados
        if (os.veiculo?.faturamentoVeiculo.empenho?.id) {
          await updateEmpenhoSaldosBloqueadosAction(
            os.veiculo?.faturamentoVeiculo.empenho?.id,
            {
              bloqueado_pecas:
                selectedOrcamento.processedPecas?.reduce(
                  (acc, pecas) =>
                    acc + (pecas.valorNegociado * pecas.quantidade || 0),
                  0
                ) || 0,
              bloqueado_servicos:
                selectedOrcamento.processedServicos?.reduce(
                  (acc, servicos) => acc + (servicos.valor || 0),
                  0
                ) || 0,
            }
          );

          toast.success("Orçamento complementar autorizado com sucesso");
          setShowApproveModal(false);
          window.location.reload();
        }
      }
    } catch (error) {
      console.error("Erro ao autorizar orçamento complementar:", error);
      toast.error("Erro ao autorizar orçamento complementar");
    }
  };

  if (loading) return <div>Carregando...</div>;

  // Filter the displayed orçamentos based on current status
  const displayedOrcamentos = orcamentos.filter((orcamento) => {
    if (currentStatus === "analise") {
      return orcamento.status === "analise";
    } else if (currentStatus === "execucao") {
      // When OS is in execution, only show execution and complementary budgets
      return [
        "execucao",
        "complementar_unauthorized",
        "complementar_authorized",
      ].includes(orcamento.status ?? "");
    }
    return ["lançada", "enviado", "analise", "aprovado", "autorizada"].includes(
      orcamento.status ?? ""
    );
  });
  // Verificar se há orçamentos suficientes para aprovação
  const minimumRequired = parseInt(os?.minimun_orcament || "3", 10);
  const hasMinimumOrcamentos =
    currentStatus === "orcamentaçao"
      ? displayedOrcamentos.length >= minimumRequired
      : true;
  return (
    <div className="grid grid-cols-[70%_30%] gap-6 p-6">
      <div>
        <div className="flex flex-col gap-4">
          <ServiceOrderDetails
            expanded={expanded}
            setExpanded={setExpanded}
            os={os ?? undefined}
            setFichaTecnicaOpen={setFichaTecnicaOpen}
          />
          <TechnicalInfo
            fichaTecnicaOpen={fichaTecnicaOpen}
            setFichaTecnicaOpen={setFichaTecnicaOpen}
          />
        </div>

        <h3 className="text-sm font-bold mt-4 mb-4">
          Orçamentos ({displayedOrcamentos.length} orçamentos)
        </h3>
        <div className="flex flex-col gap-4">
          {displayedOrcamentos.map((orcamento) => (
            <CardOrcamento
              key={orcamento.id}
              orcamento={orcamento}
              selected={selectedOrcamentos.some((item) => item.id === orcamento.id)}
              os={os}
              onSelectOrcamento={handleSelectOrcamento}
              onShowModal={setShowDetailsModal}
              onSetActiveTab={setActiveTab}
              principal={orcamento === displayedOrcamentos[0]}
            />
          ))}
        </div>

        <div className="mt-4">
          {/* Renderiza botões diferentes de acordo com o status do orçamento selecionado */}
          {session?.roles.includes("GESTOR_FROTA") &&
          (selectedOrcamento?.status === "enviado" ||
            selectedOrcamento?.status === "orcamentaçao" ||
            selectedOrcamento?.status === "lançada") ? (
            <Button
              onClick={() => {
                if (validarEnvioParaAprovacao()) {
                }
                setShowApproveModal(true);
              }}
              variant="default"
              className="w-full"
              disabled={
                selectedOrcamentos.some((orcamento) => orcamento.valorTotal === 0) ||
                selectedOrcamentos.length < minimumRequired
              }>
              Enviar para Análise
            </Button>
          ) : (session?.roles.includes("GESTOR_FROTA") || session?.roles.includes("APROVADOR")) &&
            selectedOrcamento?.status === "analise" ? (
            <Button
              onClick={() => {
                setShowApproveModal(true);
              }}
              variant="default"
              className="w-full">
              Aprovar Orçamento
            </Button>
          ) : (session?.roles.includes("GESTOR_FROTA") || session?.roles.includes("APROVADOR")) &&
            selectedOrcamento?.status === "complementar_unauthorized" ? (
            <Button
              onClick={() => {
                if (validarEnvioParaAprovacao()) {
                  setShowApproveModal(true);
                }
              }}
              variant="default"
              className="w-full">
              Autorizar Orçamento Complementar
            </Button>
          ) : null}

          {!selectedOrcamento && (
            <p className="text-sm text-gray-500">
              Selecione um orçamento para ver as ações disponíveis.
            </p>
          )}
          {(selectedOrcamento?.status === "enviado" ||
            selectedOrcamento?.status === "orcamentaçao" ||
            selectedOrcamento?.status === "lançada") &&
            selectedOrcamento &&
            !hasMinimumOrcamentos && (
              <p className="my-2 text-red-500">
                São necessários pelo menos {Math.max(3, minimumRequired)} orçamentos para
                prosseguir. Atualmente existem {displayedOrcamentos.length} orçamento(s).
              </p>
            )}
          {(selectedOrcamento?.status === "enviado" ||
            selectedOrcamento?.status === "orcamentaçao" ||
            selectedOrcamento?.status === "lançada") &&
            hasMinimumOrcamentos &&
            selectedOrcamentos.length < minimumRequired && (
              <p className="my-2 text-red-500">
                É necessário selecionar pelo menos {minimumRequired} orçamento(s) para enviar para
                análise. Atualmente há {selectedOrcamentos.length} orçamento(s) selecionado(s).
              </p>
            )}

          {(selectedOrcamento?.status === "enviado" ||
            selectedOrcamento?.status === "orcamentaçao" ||
            selectedOrcamento?.status === "lançada") &&
            hasMinimumOrcamentos &&
            selectedOrcamentos.some((orcamento) => orcamento.valorTotal === 0) && (
              <p className="my-2 text-red-500">
                Não é possível enviar orçamentos para análise com valor total zerado.
              </p>
            )}
        </div>

        <Dialog open={showApproveModal} onOpenChange={setShowApproveModal}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Confirmação</DialogTitle>
              <DialogDescription>
                {selectedOrcamento?.status === "enviado"
                  ? "Deseja enviar o orçamento selecionado para aprovação?"
                  : selectedOrcamento?.status === "complementar_unauthorized"
                  ? "Deseja autorizar este orçamento complementar?"
                  : "Deseja aprovar o orçamento selecionado?"}
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="flex gap-2">
              <Button
                onClick={
                  selectedOrcamento?.status === "enviado" ||
                  selectedOrcamento?.status === "orcamentaçao" ||
                  selectedOrcamento?.status === "lançada"
                    ? onConfirmEnviarAprovacao
                    : selectedOrcamento?.status === "complementar_unauthorized"
                    ? onAutorizarOrcamentoComplementar
                    : onConfirmEnviarAutorizacao
                }>
                Confirmar
              </Button>
              <DialogTrigger asChild>
                <Button variant="outline">Cancelar</Button>
              </DialogTrigger>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* New dialog for excessive spending confirmation */}
        <Dialog open={showExcessiveSpendingModal} onOpenChange={setShowExcessiveSpendingModal}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Atenção: Gasto Excessivo</DialogTitle>
              <DialogDescription>
                O veículo excedeu o limite de gastos permitido pelo contrato. Como gestor, você
                confirma que deseja prosseguir com este orçamento mesmo assim?
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="flex gap-2">
              <Button onClick={processEnviarAutorizacao} variant="destructive">
                Confirmar mesmo assim
              </Button>
              <Button variant="outline" onClick={() => setShowExcessiveSpendingModal(false)}>
                Cancelar
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Dialog open={showDetailsModal} onOpenChange={setShowDetailsModal}>
          <DialogContent className="max-w-[90%] h-[90vh] overflow-y-auto">
            {selectedOrcamento && (
              <Detalhes
                showTitle={true}
                data={os}
                orcamento={selectedOrcamento}
                defaultTab={activeTab}
                onClose={() => setShowDetailsModal(false)}
              />
            )}
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex flex-col gap-4 shadow-md p-4 rounded-md">
        <h3 className="text-sm font-bold mt-4 border-b pb-2">
          Orçamentos Selecionados: {selectedOrcamentos.length}/{minimumRequired} mínimo necessário
        </h3>
        <div className="flex flex-col gap-4">
          <FileUploader
            files={uploadedFiles}
            setFiles={setUploadedFiles}
            onFilesChange={handleFilesChange}
          />
          {session?.roles.includes("ADMIN") &&
            os?.status === "analise" && // ajuste conforme o status inicial da sua OS
            orcamentos.length > 0 && (
              <Button onClick={onEnviarParaOrcamentacao} variant="default" className="w-full mb-4">
                Retornar para Orçamentação
              </Button>
            )}
        </div>
        {os?.orcamentos?.[0]?.observacoes && (
          <div className="border p-4 rounded-lg">
            <h2 className="mb-2 font-bold text-lg">Suas Observações:</h2>
            <p>{os?.orcamentos?.[0]?.observacoes}</p>
          </div>
        )}
        {os?.veiculoId && <VeiculoHistoricoManutencao veiculoId={os.veiculoId} />}
      </div>
    </div>
  );
}
