import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogTrigger } from "@/components/ui/dialog";
import { MessageSquare } from "lucide-react";
import Link from "next/link";

interface IniciarChatDialogProps {
  showButton?: boolean;
  onStartChat?: () => void;
  osId: string | null | undefined;
}

export function IniciarChatDialog({
  showButton = true,
  onStartChat,
  osId,
}: IniciarChatDialogProps) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        {showButton && (
          <Button
            asChild
            variant="ghost"
            size="sm"
            className="flex items-center"
          >
            <Link href={`/dashboard/ordens-de-servico/${osId}/chat`}>
              <MessageSquare className="w-3 h-3 mr-1" />
              <span className="text-sm">Chat Online</span>
            </Link>
          </Button>
        )}
      </DialogTrigger>
    </Dialog>
  );
}
