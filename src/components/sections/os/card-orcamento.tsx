"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Home,
  CheckCircle,
  Wrench,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON>gle,
  <PERSON><PERSON>,
  Cog,
  Search,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Detalhes } from "./detalhes";
import { toast } from "sonner";
import { useCredenciado } from "@/context/credenciado-context";
import { Session } from "@/lib/auth/types";
import {
  replicarOrcamentoAction,
  deleteOrcamentoAction,
  updateOrcamentoAction,
  updateOsStatus,
  updateOrcamentoStatus,
} from "@/serverActions/orcamentoAction";
import { getServerSession } from "@/lib/auth/server-session";
import { useEffect, useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { getCredenciadosByContratoId } from "@/serverActions/credenciadoAction";
import { useSession } from "@/components/session/use-session";
import { IniciarChatDialog } from "./IniciarChatDialog";
import { createChat } from "@/serverActions/chatActions";
import { useNavigationWithId } from "./useNavigationWithId";

export interface LayoutOrcamentoProps {
  orcamento: Orcamento | null | undefined;
  selected?: boolean;
  os?: OS | null | undefined;
  onSelectOrcamento: (orcamento: Orcamento) => void;
  onShowModal: (show: boolean) => void;
  onSetActiveTab?: (tab: string) => void;
  principal?: boolean;
  showDeleteButton?: boolean;
  showReplicarButton?: boolean;
  showPecasButton?: boolean;
}

export function CardOrcamento({
  orcamento,
  selected,
  os,
  onSelectOrcamento,
  onShowModal,
  onSetActiveTab,
  principal,
  showDeleteButton = true,
  showReplicarButton = true,
  showPecasButton = true,
}: LayoutOrcamentoProps) {
  const { session } = useSession();
  const { credenciados = [] } = useCredenciado();
  const [selectedAccreditedIds, setSelectedCredenciados] = useState<string[]>(
    []
  );
  const [adjustmentMessage, setAdjustmentMessage] = useState("");
  const [isRequestLoading, setIsRequestLoading] = useState<boolean>(false);
  const [credenciadosByContract, setCredenciadosByContract] = useState<
    credenciado[]
  >([]);

  // Novos estados para melhorar a seleção de credenciados
  const [credenciadoSearch, setCredenciadoSearch] = useState("");
  const [isLoadingCredenciados, setIsLoadingCredenciados] = useState(false);

  const credenciadosVinculados =
    credenciadosByContract.length > 0 ? credenciadosByContract : [];
  const { osId, goToChat } = useNavigationWithId();
  // Filtrar credenciados com base na busca
  const credenciadosFiltered =
    credenciadosVinculados?.filter((credenciado) => {
      const typeServiceOS = os?.TiposDeOs?.descricao;

      const matchesService = credenciado.servicos.some(
        (service) =>
          service.descricao === "Todos" || service.descricao === typeServiceOS
      );

      if (!matchesService) return false;

      if (!credenciadoSearch) return true;

      const searchLower = credenciadoSearch.toLowerCase();
      const info = credenciado.informacoes?.[0];
      const razaoSocial = info?.razao_social?.toLowerCase() || "";
      const cnpj = info?.cnpj?.toLowerCase() || "";

      return razaoSocial.includes(searchLower) || cnpj.includes(searchLower);
    }) || [];

  // Função para buscar credenciados com loading state
  async function fetchCredenciadosForSelection() {
    setIsLoadingCredenciados(true);

    try {
      if (session?.contratoId) {
        const response = await getCredenciadosByContratoId(session.contratoId);

        if (response.success && response.data) {
          console.log("Credenciados:", response.data.credenciados);
          setCredenciadosByContract(response.data.credenciados);
          if (response.data.credenciados.length > 0 && !selectedAccreditedIds) {
            setSelectedCredenciados(response.data.credenciados[0].id);
          }
        } else {
          console.error("Formato de resposta inesperado:", response);
          setCredenciadosByContract([]);
        }
      }
    } catch (error) {
      console.error("Erro ao buscar credenciados para seleção:", error);
      toast.error("Erro ao carregar orçamentistas");
    } finally {
      setIsLoadingCredenciados(false);
    }
  }

  useEffect(() => {
    async function fetchSession() {
      if (session?.contratoId) {
        await fetchCredenciadosForSelection();
      }
    }

    fetchSession();
  }, []);

  const onViewPecasServicos = () => {
    onSelectOrcamento(orcamento!);
    onShowModal(true);
    if (onSetActiveTab) {
      onSetActiveTab("pecas-servicos");
    }
  };

  const onReplicarOrcamento = async () => {
    if (!selectedAccreditedIds) {
      toast.error("Selecione um credenciado para replicar o orçamento");
      return;
    }
    try {
      await replicarOrcamentoAction(
        orcamento?.id as string,
        selectedAccreditedIds
      );
      toast.success("Orçamento replicado com sucesso");
      window.location.reload();
    } catch (error) {
      console.error("Erro:", error);
      toast.error("Erro ao replicar orçamento");
    }
  };

  const toggleCredenciadoSelection = (id: string) => {
    setSelectedCredenciados((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
    );
  };

  const onExcluirOrcamento = async () => {
    try {
      await deleteOrcamentoAction(orcamento?.id as string);
      toast.success("Orçamento excluído com sucesso");
      window.location.reload();
    } catch (error) {
      console.error("Erro:", error);
      toast.error("Erro ao excluir orçamento");
    }
  };

  const onRequestAdjustments = async () => {
    try {
      setIsRequestLoading(true);
      await updateOsStatus(
        os?.id || "",
        "pendente",
        undefined,
        undefined,
        undefined,
        undefined,
        adjustmentMessage
      );

      await updateOrcamentoStatus(orcamento?.id || "", "pendente");

      toast.success("Ajuste solicitado com sucesso");
      setIsRequestLoading(false);
      window.location.href = "/dashboard/ordens-de-servico/ordens";
    } catch (error) {
      console.error("Erro:", error);
      toast.error("Erro ao solicitar ajuste");
    }
  };

  const onAtualizarOrcamento = async () => {
    try {
      await updateOrcamentoAction(orcamento?.id as string, {
        ...orcamento,
        status: "orcamentacao",
      });
      toast.success("Orçamentação atualizada com sucesso");
      window.location.reload();
    } catch (error) {
      console.error("Erro ao atualizar orçamentação:", error);
      toast.error("Erro ao atualizar orçamentação");
    }
  };
  const handleIniciarChat = async () => {
    if (!os) return;
    createChat(os.id)
      .then((data) => {
        toast.success("Chat Iniciado");
        // setChatId(data.id)
      })
      .catch((error) => {
        toast.error(error.message || "Erro inesperado ao iniciar chat");
      })
      .finally(() => {
        // goToChat();
      });
  };

  const pecas = orcamento?.processedPecas?.reduce(
    (acc: number, item: any) => acc + item.valorNegociado,
    0
  );
  const totalPecas = (pecas ?? 0) / 100;

  const servicos = orcamento?.processedServicos?.reduce(
    (acc: number, item: any) => acc + item.valor,
    0
  );
  const totalServicos = (servicos ?? 0) / 100;

  // Check if this is a complementary budget
  const isComplementary =
    orcamento?.status === "complementar_unauthorized" ||
    orcamento?.status === "complementar_authorized";
  return (
    <div
      className={`relative cursor-pointer ${
        selected ? "border-2 border-blue-500 rounded-lg" : ""
      }`}
      onClick={() => onSelectOrcamento(orcamento!)}
    >
      {principal && (
        <Badge
          className="absolute rigth-2 top-2 transform -translate-x-1"
          variant="default"
        >
          Principal
        </Badge>
      )}
      {orcamento?.status === "finalizada" && (
        <Badge
          className="absolute left-1/2 top-2 transform -translate-x-1/2"
          variant="secondary"
        >
          Finalizado
        </Badge>
      )}
      {/* Badge for complementary budgets */}
      {isComplementary && (
        <Badge
          className="absolute left-1/2 top-2 transform -translate-x-1/2"
          variant="outline"
          style={{
            backgroundColor:
              orcamento.status === "complementar_authorized"
                ? "#dcfce7"
                : "#fee2e2",
            color:
              orcamento.status === "complementar_authorized"
                ? "#166534"
                : "#991b1b",
            borderColor:
              orcamento.status === "complementar_authorized"
                ? "#bbf7d0"
                : "#fecaca",
          }}
        >
          {orcamento.status === "complementar_authorized"
            ? "Complementar Autorizado"
            : "Complementar Pendente"}
        </Badge>
      )}
      <Card className="p-4 border rounded-lg shadow-sm">
        <div className="flex flex-col lg:flex-row justify-between">
          <div className="flex flex-col lg:flex-row gap-4 items-start mb-4">
            <div className="relative w-20 h-20">
              <Home className="absolute w-20 h-20 opacity-50" />
              <CheckCircle className="absolute w-4 h-4 bottom-0 right-0" />
            </div>
            <div className="flex flex-col">
              <h2 className="text-sm font-semibold">
                {orcamento?.credenciado?.informacoes[0]?.razao_social} -{" "}
                {orcamento?.credenciado?.informacoes[0]?.cnpj}
              </h2>
              <p className="text-sm">
                <span>
                  {orcamento?.credenciado?.endereco?.logradouro} -{" "}
                  {orcamento?.credenciado?.endereco?.bairro},{" "}
                  {orcamento?.credenciado?.endereco?.cidade} -{" "}
                  {orcamento?.credenciado?.endereco?.estado}, CEP:{" "}
                  {orcamento?.credenciado?.endereco?.cep}
                </span>
              </p>
              <p className="text-sm my-4">
                <span>
                  Data:{" "}
                  {orcamento?.createdAt
                    ? new Date(orcamento.createdAt).toLocaleDateString("pt-BR")
                    : ""}{" "}
                  | Validade:{" "}
                  {orcamento?.validade
                    ? new Date(orcamento.validade).toLocaleDateString("pt-BR")
                    : ""}{" "}
                  | Prazo de entrega: {orcamento?.prazoEntrega} dias
                </span>
              </p>
            </div>
          </div>
          <div>
            <div className="flex flex-col">
              <span className="text-sm">Valor em peças:</span>
              <span className="text-sm">
                {totalPecas.toLocaleString("pt-BR", {
                  style: "currency",
                  currency: "BRL",
                })}
              </span>
            </div>
            <div className="flex flex-col">
              <span className="text-sm">Valor em serviços:</span>
              <span className="text-sm">
                {totalServicos.toLocaleString("pt-BR", {
                  style: "currency",
                  currency: "BRL",
                })}
              </span>
            </div>
          </div>
        </div>
        <div className="flex justify-between font-semibold border-t pt-2">
          <div className="flex gap-2">
            {/* Ações de Peças, Replicar e Excluir permanecem */}
            <Dialog>
              <DialogTrigger asChild>
                {showPecasButton && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="flex items-center"
                  >
                    <Wrench className="w-3 h-3 mr-1" />
                    <span className="text-sm">Peças de serviços</span>
                  </Button>
                )}
              </DialogTrigger>
              <DialogContent className="max-w-[90%] h-[90vh] overflow-y-auto">
                <DialogTitle></DialogTitle>
                <Detalhes
                  showTitle={true}
                  defaultTab="pecas-servicos"
                  data={os}
                  orcamento={orcamento}
                />
              </DialogContent>
            </Dialog>
            {orcamento?.status === "enviado" &&
              os?.status === "orcamentaçao" && (
                <Dialog>
                  <DialogTrigger asChild>
                    {showReplicarButton && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex items-center"
                      >
                        <Copy className="w-3 h-3 mr-1" />
                        <span className="text-sm">Replicar</span>
                      </Button>
                    )}
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Confirmação de replicação</DialogTitle>
                      <DialogDescription className="flex flex-col gap-2 mb-4">
                        <AlertTriangle className="w-8 h-8 text-yellow-500" />
                        <span>
                          Selecione o credenciado para replicar o orçamento:
                        </span>

                        {/* Campo de busca */}
                        <div className="relative">
                          <Input
                            placeholder="Buscar por nome ou CNPJ"
                            value={credenciadoSearch}
                            onChange={(e) =>
                              setCredenciadoSearch(e.target.value)
                            }
                            className="w-full mb-2"
                            autoFocus
                          />
                          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        </div>

                        {/* Lista de credenciados */}
                        <div className="border rounded-md max-h-60 overflow-y-auto">
                          {isLoadingCredenciados ? (
                            <div className="p-6 flex justify-center items-center">
                              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
                            </div>
                          ) : credenciadosFiltered.length > 0 ? (
                            <div className="p-0 divide-y">
                              {credenciadosFiltered.map((credenciado) => {
                                const isSelected =
                                  selectedAccreditedIds.includes(
                                    credenciado.id
                                  );
                                return (
                                  <div
                                    key={credenciado.id}
                                    className={`p-3 cursor-pointer transition-colors border ${
                                      isSelected
                                        ? "border-green-500"
                                        : "border-transparent"
                                    }`}
                                    onClick={() =>
                                      toggleCredenciadoSelection(credenciado.id)
                                    }
                                  >
                                    <div className="flex items-center gap-2">
                                      <div
                                        className={`h-4 w-4 rounded-full border flex items-center justify-center ${
                                          isSelected
                                            ? "border-green-500 bg-green-500"
                                            : "border-gray-300"
                                        }`}
                                      >
                                        {isSelected && (
                                          <div className="h-2 w-2 rounded-full bg-white" />
                                        )}
                                      </div>
                                      <span>
                                        {credenciado.informacoes?.[0]
                                          ?.razao_social || "Sem nome"}{" "}
                                        -{" "}
                                        {credenciado.informacoes?.[0]?.cnpj ||
                                          "Sem CNPJ"}
                                      </span>
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          ) : (
                            <div className="p-6 text-center text-sm text-muted-foreground">
                              Nenhum orçamentista encontrado
                            </div>
                          )}
                        </div>

                        {/* Rodapé com contagem de credenciados */}
                        <div className="flex justify-end px-2 py-1 text-xs text-muted-foreground">
                          {isLoadingCredenciados
                            ? "Carregando..."
                            : `${credenciadosFiltered.length} orçamentista${
                                credenciadosFiltered.length === 1 ? "" : "s"
                              } disponível${
                                credenciadosFiltered.length === 1 ? "" : "is"
                              }`}
                        </div>
                      </DialogDescription>
                    </DialogHeader>
                    <DialogFooter className="flex gap-2">
                      <Button onClick={onReplicarOrcamento}>
                        Confirmar replicação
                      </Button>
                      <DialogTrigger asChild>
                        <Button variant="outline">Cancelar</Button>
                      </DialogTrigger>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              )}

            <Dialog>
              <DialogTrigger asChild>
                {(orcamento?.status === "orcamentaçao" ||
                  orcamento?.status === "analise" ||
                  orcamento?.status === "lançada" ||
                  orcamento?.status === "pendente" ||
                  orcamento?.status === "enviado") && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="flex items-center"
                  >
                    <Cog className="w-3 h-3 mr-1" />
                    <span className="text-sm">Solicitar ajustes</span>
                  </Button>
                )}
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Solicitar Ajustes</DialogTitle>
                </DialogHeader>
                <Textarea
                  value={adjustmentMessage}
                  onChange={(e) => setAdjustmentMessage(e.target.value)}
                  style={{ resize: "none" }}
                />
                <DialogFooter className="flex gap-2">
                  <Button
                    onClick={onRequestAdjustments}
                    disabled={
                      adjustmentMessage.length === 0 || isRequestLoading
                    }
                  >
                    {isRequestLoading ? "Enviando..." : "Enviar"}
                  </Button>
                  <DialogTrigger asChild>
                    <Button variant="outline">Cancelar</Button>
                  </DialogTrigger>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            <Dialog>
              <DialogTrigger asChild>
                {showDeleteButton && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="flex items-center"
                  >
                    <Trash2 className="w-3 h-3 mr-1" />
                    <span className="text-sm">Excluir</span>
                  </Button>
                )}
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Confirmação de exclusão</DialogTitle>
                  <DialogDescription className="flex items-center gap-2 mb-4">
                    <AlertTriangle className="w-8 h-8 text-yellow-500" />
                    Você tem certeza que deseja excluir o orçamento selecionado?
                    Essa ação é irreversível.
                  </DialogDescription>
                </DialogHeader>
                <DialogFooter className="flex gap-2">
                  <Button onClick={onExcluirOrcamento} variant="destructive">
                    Confirmar exclusão
                  </Button>
                  <DialogTrigger asChild>
                    <Button variant="outline">Cancelar</Button>
                  </DialogTrigger>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            <IniciarChatDialog onStartChat={handleIniciarChat} osId={os?.id} />
          </div>
          <div className="flex gap-2">
            <span className="text-sm">Total:</span>
            <span className="text-sm">
              {((orcamento?.valorTotal ?? 0) / 100).toLocaleString("pt-BR", {
                style: "currency",
                currency: "BRL",
              })}
            </span>
          </div>
        </div>
      </Card>
    </div>
  );
}
