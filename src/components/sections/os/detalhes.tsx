"use client";

import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { Car, X } from "lucide-react";
import { formatData } from "@/utils/format";
import {
  createOrcamentoV1Action,
  invoiceOSAction,
  updateOrcamentoAction,
  updateOrcamentoStatus,
  updateOsStatus,
  updateStatusAndObservationsAction,
} from "@/serverActions/orcamentoAction";
import { useEffect, useState } from "react";
import { Session } from "@/lib/auth/types";
import { getServerSession } from "@/lib/auth/server-session";
import { useContrato } from "@/context/contrato-context";
import { Part, VehicleInfoResponse } from "@/interfaces/suiv.interface";
import {
  getIndividualOverlap<PERSON><PERSON>yNickname,
  getNicknames,
  searchParts,
  searchPartsPrice,
} from "@/serverActions/suivAction";
import { toast } from "sonner";
import { useCredenciado } from "@/context/credenciado-context";
import FileUploader from "../orcamento/edit-orcamento/file-upload";
import { useRouter } from "next/router";
import ConfirmModal from "@/components/modal/confirm-modal-user";
import { ModalCardsOrcamento } from "../orcamento/modal-cards-orcamento";
import { useSearchParams } from "next/navigation";
import { BsCheckCircleFill, BsCircleFill } from "react-icons/bs";
import { formatCNPJ, formatDate } from "@/lib/utils";
import { getCredenciadoById } from "@/serverActions/credenciadoAction";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  getVehicleByPlateWithCache,
  getVehicleTokenWithCache,
} from "@/service/suivCache.service";

interface DetalhesProps {
  showTitle?: boolean;
  defaultTab?: string;
  data: OS | null | undefined;
  orcamento?: Orcamento | null | undefined;
  onClose?: () => void;
  osStatus?: string;
  displayOnlyInvoice?: boolean;
}

const workflowSteps = [
  { key: "lançada", label: "Lançadas" },
  { key: "orcamentaçao", label: "Orçamentação" },
  { key: "analise", label: "Análise e Aprovação" },
  { key: "autorizada", label: "Autorizadas" },
  { key: "execucao", label: "Em Execução" },
  {
    key: "finalizada",
    additionalKeys: ["Aguardando Aprovação"],
    label: "Finalizadas",
  },
  { key: "faturada", label: "Faturadas" },
  { key: "cancelada", label: "Canceladas" },
];

export function Detalhes({
  showTitle = false,
  defaultTab = "ordem-de-servico",
  data,
  orcamento = null,
  onClose,
  osStatus = "",
  displayOnlyInvoice = false,
}: DetalhesProps) {
  const searchParams = useSearchParams();
  const detailsOsDefaultTabParam = searchParams.get("detailsOsDefaultTab");
  const [sessionData, setSessionData] = useState<Session>();
  const [selectedDefaultTab, setSelectedDefaultTab] = useState(
    detailsOsDefaultTabParam || defaultTab
  );
  const [availableTabs, setAvailableTabs] = useState<any[]>([]);
  const [overlaps, setOverlaps] = useState<any[]>([]);
  const [showOverlapsModal, setShowOverlapsModal] = useState(false);
  const [nicknames, setNicknames] = useState<
    { id: number; description: string }[]
  >([]);

  const targetOrcamento =
    orcamento ||
    data?.orcamentos?.find(
      (o) =>
        o.status === "finalizada" ||
        o.status === "execucao" ||
        o.status === "faturada"
    ) ||
    data?.orcamentos?.[0];

  useEffect(() => {
    setSelectedDefaultTab(detailsOsDefaultTabParam || defaultTab);
  }, [defaultTab, detailsOsDefaultTabParam]);

  const currentStatus = osStatus ?? "";
  const currentStepIndex = workflowSteps.findIndex(
    ({ key, additionalKeys = [] }) =>
      key === currentStatus || additionalKeys.includes(currentStatus)
  );

  useEffect(() => {
    if (sessionData && data) {
      const availableTabs = [
        {
          id: "ordem-de-servico",
          label: "Ordem de serviço",
          available: !!data,
        },
        {
          id: "veiculo",
          label: "Veículo",
          available: !!data?.veiculo,
        },
        {
          id: "orcamento",
          label: "Orçamento",
          available:
            Array.isArray(data?.orcamentos) && data.orcamentos.length > 0,
        },
        {
          id: "pecas-e-servicos",
          label: "Peças e serviços",
          available:
            !!orcamento ||
            (Array.isArray(data?.orcamentos) &&
              data.orcamentos.some(
                (orcamento) => orcamento.pecas || orcamento.servico
              )),
        },
        {
          id: "orcamentista",
          label: "Orçamentista",
          available: !!data?.credenciado,
        },
        {
          id: "acervo-digital",
          label: "Acervo digital",
          available: true,
        },
      ];

      const tabsByDisplayOnlyInvoice = [
        {
          id: "nota-fiscal",
          label: "Nota Fiscal",
          available:
            sessionData?.roles.includes("FINANCEIRO") ||
            sessionData?.roles.includes("ADMIN") ||
            sessionData?.roles.includes("ORCAMENTISTA_OFICINA") ||
            sessionData?.contrato.ajuste_gestor,
        },
        {
          id: "pecas-e-servicos",
          label: "Peças e serviços",
          available:
            !!orcamento ||
            (Array.isArray(data?.orcamentos) &&
              data.orcamentos.some(
                (orcamento) => orcamento.pecas || orcamento.servico
              )),
        },
      ];

      // Filtrar abas disponíveis
      const filteredTabs = availableTabs.filter((tab) => tab.available);
      const filteredTabsByDisplayOnlyInvoice = tabsByDisplayOnlyInvoice.filter(
        (tab) => tab.available
      );
      setAvailableTabs(
        displayOnlyInvoice ? filteredTabsByDisplayOnlyInvoice : filteredTabs
      );

      // Definir a aba padrão
      const detailsOsDefaultTabParam = searchParams.get("detailsOsDefaultTab");
      const defaultTab = detailsOsDefaultTabParam || filteredTabs[0]?.id;
      setSelectedDefaultTab(defaultTab);
    }
  }, [sessionData, data, searchParams]);

  const filteredTabs = availableTabs.filter((tab) => tab.available);

  const { contratos } = useContrato();
  const [vehicleData, setVehicleData] = useState<VehicleInfoResponse>();
  const [vehicleToken, setVehicleToken] = useState<any>(null);
  const [partsPrices, setPartsPrices] = useState<
    { description: string; price: number; codigo: string }[]
  >([]);
  const [showAjustesForm, setShowAjustesForm] = useState(false);
  const [ajustesObservacoes, setAjustesObservacoes] = useState("");
  const [isSubmittingAjustes, setIsSubmittingAjustes] = useState(false);
  const [infoCredenciado, setInfoCredenciado] = useState<credenciado>();
  const { credenciados } = useCredenciado();
  const [showAllOrcamentosModal, setShowAllOrcamentosModal] = useState(false);

  const [uploadedInvoicePdfPecas, setUploadedInvoicePdfPecas] = useState<
    File[]
  >([]);
  const [uploadedInvoiceXmlPecas, setUploadedInvoiceXmlPecas] = useState<
    File[]
  >([]);
  const [uploadedInvoicePdfServicos, setUploadedInvoicePdfServicos] = useState<
    File[]
  >([]);
  const [uploadedInvoiceXmlServicos, setUploadedInvoiceXmlServicos] = useState<
    File[]
  >([]);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);

  const firstOrcamento = data?.orcamentos?.[0];

  const pecasProcessadas = firstOrcamento?.processedPecas ?? [];
  const servicosProcessados = firstOrcamento?.processedServicos ?? [];

  const showUploadInvoices =
    sessionData?.roles.includes("ADMIN") ||
    sessionData?.roles.includes("ORCAMENTISTA_OFICINA");

  useEffect(() => {
    const fetchCredenciado = async () => {
      if (sessionData?.credenciadoId) {
        const filterOrcamento = data?.orcamentos?.find(
          (orcamento) => orcamento.credenciadoId === sessionData?.credenciadoId
        );
        const credenciado = await getCredenciadoById(
          filterOrcamento?.credenciadoId as string
        );
        setInfoCredenciado(credenciado.data.credenciado);
      } else if (orcamento) {
        const credenciado = credenciados.find(
          (credenciado) => credenciado.id === orcamento?.credenciadoId
        );
        setInfoCredenciado(credenciado);
      } else {
        const filterOrcamento = data?.orcamentos?.find(
          (orcamento) =>
            orcamento.status === "execucao" ||
            orcamento.status === "autorizada" ||
            orcamento.status === "finalizada" ||
            orcamento.status === "faturada" ||
            orcamento.status === "pendente"
        );
        if (filterOrcamento?.credenciadoId) {
          const credenciado = await getCredenciadoById(
            filterOrcamento?.credenciadoId as string
          );
          setInfoCredenciado(credenciado.data.credenciado);
        }
      }
    };
    fetchCredenciado();
  }, [sessionData, data]);
  useEffect(() => {
    const fetchData = async () => {
      if (data?.veiculo?.placa) {
        const fetchVehicleData = await getVehicleByPlateWithCache(
          data?.veiculo?.placa
        );
        setVehicleData(fetchVehicleData);

        const token = await getVehicleTokenWithCache(
          fetchVehicleData.suivDataCollection[0]?.versionId,
          fetchVehicleData.yearModel
        );
        setVehicleToken(token);

        const orcamentoParts =
          orcamento?.processedPecas ??
          data.orcamentos?.filter((o) => o.status === osStatus)?.[0]
            ?.processedPecas ??
          [];

        const partsPricesArray = (
          await Promise.all(
            orcamentoParts.map(async (peca) => {
              const response = await searchParts(token.token, peca.descricao);
              console.log({ response });
              const setIds = Object.values(response)
                .filter(
                  (item) =>
                    typeof item === "object" &&
                    (item as any).setId !== undefined
                )
                .map((item) => (item as any).setId);
              return setIds;
            })
          )
        ).flat();

        const newNicknames: { id: number; description: string }[] = [];
        for (const peca of partsPricesArray) {
          const nicknameResponse = await getNicknames(token.token, peca);
          console.log("Nickname Response:", nicknameResponse);
          if (Array.isArray(nicknameResponse) && nicknameResponse.length > 0) {
            const matchingObjects = nicknameResponse
              .filter((nick) =>
                orcamentoParts.some(
                  (op) =>
                    op.descricao &&
                    (nick.description
                      .toLowerCase()
                      .includes(op.descricao.toLowerCase()) ||
                      op.descricao
                        .toLowerCase()
                        .includes(nick.description.toLowerCase()))
                )
              )
              .map((nick) => ({ id: nick.id, description: nick.description }));
            newNicknames.push(...matchingObjects);
          }
        }

        const uniqueNicknames = newNicknames.filter(
          (value, index, self) =>
            index ===
            self.findIndex(
              (n) => n.id === value.id && n.description === value.description
            )
        );
        setNicknames((prev) => [...prev, ...uniqueNicknames]);

        if (uniqueNicknames.length > 0 && token?.token) {
          const partsIds = uniqueNicknames.map((n) => n.id);
          const res = await getIndividualOverlapsByNickname(
            token.token,
            partsIds
          );
          console.log("Individual Overlaps:", res);
          setOverlaps(res);
        }
      }
    };
    fetchData();
  }, [data?.veiculo?.placa]);
  useEffect(() => {
    async function fetchSessionData() {
      const sessionData = await getServerSession();
      setSessionData(sessionData);
    }
    fetchSessionData();
  }, []);

  const pecas = targetOrcamento?.processedPecas
    ? targetOrcamento?.processedPecas.reduce(
        (acc: number, item: any) => acc + item.valorNegociado,
        0
      )
    : 0;

  const totalPecas = pecas ?? 0;

  const servicos = targetOrcamento?.processedServicos
    ? targetOrcamento?.processedServicos.reduce((acc: number, item: any) => {
        const valorNegociado = item.valor || 0;
        return acc + Number(valorNegociado);
      }, 0)
    : 0;

  const totalServicos = servicos ?? 0;

  const handleFilesChange = (files: File[]) => {
    setUploadedFiles(files);
  };

  const handleFaturarOS = async () => {
    if (!data?.id) return;

    try {
      const targetOrcamento = data.orcamentos?.find(
        ({ status }) =>
          status === "finalizada" ||
          status === "complementar_authorized" ||
          status === "Aguardando Aprovação" ||
          status === "execucao"
      );

      if (targetOrcamento) {
        await updateOrcamentoStatus(targetOrcamento.id, "faturada");
      }

      const res = await updateOsStatus(
        data?.id || "",
        "faturada",
        targetOrcamento?.id
      );
      if (res.error) {
        console.error("Erro ao faturar OS:", res.error);
        toast.error("Erro ao faturar OS");
        return;
      }
      toast.success("OS faturada com sucesso");
      window.location.reload();
    } catch (error) {
      console.error("Erro ao faturar OS:", error);
    }
  };

  const rawOrcamentoArquivos = data?.orcamentos?.[0]?.arquivos;
  const rawOSArquivos = data?.arquivos;

  const orcamentoArquivos = (() => {
    if (!rawOrcamentoArquivos) return [];
    if (typeof rawOrcamentoArquivos === "string") {
      try {
        return rawOrcamentoArquivos ? JSON.parse(rawOrcamentoArquivos) : [];
      } catch (e) {
        console.error("Invalid JSON in orcamentoArquivos", e);
        return [];
      }
    }
    return Array.isArray(rawOrcamentoArquivos) ? rawOrcamentoArquivos : [];
  })();

  const osArquivos = (() => {
    if (!rawOSArquivos) return [];
    if (typeof rawOSArquivos === "string") {
      try {
        return rawOSArquivos ? JSON.parse(rawOSArquivos) : [];
      } catch (e) {
        console.error("Invalid JSON in osArquivos", e);
        return [];
      }
    }
    return Array.isArray(rawOSArquivos) ? rawOSArquivos : [];
  })();

  const arquivos = [...orcamentoArquivos, ...osArquivos];

  const arquivosNotaFiscal = (() => {
    if (!data?.arquivosNotaFiscal) return [];
    if (typeof data.arquivosNotaFiscal === "string") {
      try {
        return data.arquivosNotaFiscal
          ? JSON.parse(data.arquivosNotaFiscal)
          : [];
      } catch (e) {
        console.error("Invalid JSON in arquivosNotaFiscal", e);
        return [];
      }
    }
    return Array.isArray(data.arquivosNotaFiscal)
      ? data.arquivosNotaFiscal
      : [];
  })();

  const authorizedOrcamento =
    orcamento && orcamento.status === "autorizada"
      ? orcamento
      : data?.orcamentos?.find((o) => o.status === "autorizada");
  console.log(data);
  console.log(orcamento);
  const filteredOrcamentos = (
    orcamento ? [orcamento] : data?.orcamentos || []
  ).filter((o) =>
    osStatus === "finalizada" || osStatus === "Aguardando Aprovação"
      ? o.status === "finalizada" ||
        o.status === "complementar_authorized" ||
        o.status === "execucao" ||
        o.status === "faturada"
      : o.status === osStatus
  );
  const formatFilePath = (filePath: string) => {
    const uploadsPath = filePath?.replace(/^.*?(\/uploads\/.*)$/, "$1");
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL
      ? process.env.NEXT_PUBLIC_API_BASE_URL.replace(/\/api$/, "")
      : "https://api.sistema.grupocarletto.net.br";
    const file = `${baseUrl}${uploadsPath}`;
    return file;
  };
  const handleEnviarAjustes = async () => {
    // Use o orçamento da props ou procure um na OS (prefira o autorizado ou mais recente)
    const targetOrcamento =
      orcamento ||
      data?.orcamentos?.find((o) => o.status === "finalizada") ||
      data?.orcamentos?.[0];

    if (!targetOrcamento?.id || !ajustesObservacoes.trim()) {
      toast.error("Não foi possível identificar o orçamento para ajustes");
      return;
    }

    try {
      setIsSubmittingAjustes(true);

      // Preparar dados para atualização
      const updateData = {
        observacoes: ajustesObservacoes,
        status: "Aguardando Aprovação",
      };

      // Chamar a action para atualizar o orçamento
      const result = await updateStatusAndObservationsAction(
        targetOrcamento.id,
        updateData
      );
      await updateOsStatus(
        data?.id || "",
        "Aguardando Aprovação",
        targetOrcamento.id
      );
      if (result.success) {
        toast.success("Solicitação de ajustes enviada com sucesso");
        setShowAjustesForm(false);
        setAjustesObservacoes("");
        window.location.reload();
      } else {
        toast.error("Erro ao enviar solicitação de ajustes");
      }
    } catch (error) {
      console.error("Erro ao solicitar ajustes:", error);
      toast.error("Erro ao enviar solicitação de ajustes");
    } finally {
      setIsSubmittingAjustes(false);
    }
  };

  const uploadFile = async (file: File) => {
    const formData = new FormData();
    formData.append("file", file);

    const res = await fetch("/api/upload", {
      method: "POST",
      body: formData,
    });

    if (!res.ok) {
      throw new Error("Erro ao fazer upload do arquivo");
    }

    return await res.json();
  };

  const osId = data?.id;

  const valorTotalOrcamento = targetOrcamento?.valorTotal
    ? targetOrcamento?.valorTotal
    : 0;

  const handleSend = async () => {
    try {
      const allFiles = [
        ...uploadedInvoicePdfPecas,
        ...uploadedInvoiceXmlPecas,
        ...uploadedInvoicePdfServicos,
        ...uploadedInvoiceXmlServicos,
      ];

      const arquivoLocations = await Promise.all(
        allFiles.map((file) => uploadFile(file))
      );

      const arquivosNotaFiscal = arquivoLocations;

      const response = await fetch(`/api/invoice/${osId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ arquivosNotaFiscal }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || "Erro ao atualizar arquivos da nota fiscal"
        );
      }

      window.location.reload();
    } catch (error: any) {
      console.error("Erro ao enviar arquivos:", error.message);
    }
  };

  const handleSendFiles = async () => {
    try {
      const arquivoLocations = await Promise.all(
        uploadedFiles.map((file) => uploadFile(file))
      );

      const arquivos = arquivoLocations;

      const response = await fetch(`/api/files-os/${osId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ arquivos }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Erro ao atualizar arquivos");
      }

      window.location.reload();
    } catch (error: any) {
      console.error("Erro ao enviar arquivos:", error.message);
    }
  };
  function formatHorasDecimal(
    horasDecimal: number | string | null | undefined
  ): string {
    if (!horasDecimal) return "01:00";

    // Converter para número se for string
    const horasNum =
      typeof horasDecimal === "string"
        ? parseFloat(horasDecimal)
        : horasDecimal;

    // Extrair horas (parte inteira) e minutos (parte fracionária * 60)
    const horas = Math.floor(horasNum);
    const minutos = Math.round((horasNum - horas) * 60);

    // Formatar com zero à esquerda quando necessário
    const horasStr = String(horas).padStart(2, "0");
    const minutosStr = String(minutos).padStart(2, "0");

    return `${horasStr}:${minutosStr}`;
  }

  useEffect(() => {
    if (detailsOsDefaultTabParam) {
      setSelectedDefaultTab(detailsOsDefaultTabParam);
    } else {
      setSelectedDefaultTab(filteredTabs[0]?.id);
    }
  }, [detailsOsDefaultTabParam, filteredTabs, sessionData]);

  if (!selectedDefaultTab || availableTabs.length === 0) {
    return <div>Carregando...</div>;
  }

  const getHoursSince = (date: Date | string | undefined) => {
    if (!date) return 0;
    const start = new Date(date).getTime();
    const now = new Date().getTime();
    return Math.round((now - start) / (1000 * 60 * 60));
  };

  const getRemainingHours = (hoursUsed: number) => {
    const remaining = 72 - hoursUsed;
    return remaining > 0 ? remaining : 0;
  };

  // Check if still within time limit
  const isWithinTimeLimit = (hoursUsed: number) => hoursUsed <= 72;

  const hoursUsedInCurrentStep = data?.upDateTimedAt
    ? getHoursSince(data.upDateTimedAt)
    : 0;
  const remainingHours = getRemainingHours(hoursUsedInCurrentStep);
  const isCurrentlyWithinLimit = isWithinTimeLimit(hoursUsedInCurrentStep);

  return (
    <div className="w-full">
      {showTitle ? (
        <div className="">
          <h2 className="text-2xl font-bold !mb-4">Detalhes da OS</h2>
        </div>
      ) : (
        ""
      )}
      <div className="overflow-x-auto">
        <div className="flex items-center gap-2 min-w-max px-4">
          {workflowSteps.map((step, index) => {
            const isCompleted = index < currentStepIndex;
            const isCurrent = index === currentStepIndex;

            // Para etapas completadas, assumimos que estavam dentro do prazo
            // Na etapa atual, verificamos com base no upDateTimedAt
            const isStepWithinDeadline =
              isCompleted || (isCurrent && isCurrentlyWithinLimit);

            const icon = isCompleted ? (
              <BsCheckCircleFill className="text-green-500" />
            ) : isCurrent ? (
              <BsCheckCircleFill
                className={
                  isCurrentlyWithinLimit ? "text-yellow-500" : "text-red-500"
                }
              />
            ) : (
              <BsCircleFill className="text-gray-400" />
            );

            const labelClass = isCompleted
              ? "text-green-600"
              : isCurrent
              ? isCurrentlyWithinLimit
                ? "text-yellow-600 font-medium"
                : "text-red-600 font-medium"
              : "text-gray-400";

            return (
              <div key={step.key} className="flex items-center gap-2">
                <div className="flex flex-col items-center">
                  {icon}
                  <span
                    className={`text-xs mt-1 ${labelClass} whitespace-nowrap`}
                  >
                    {step.label}
                  </span>
                  {isCurrent && data?.upDateTimedAt && (
                    <>
                      <span
                        className={`text-xs ${
                          isCurrentlyWithinLimit
                            ? "text-yellow-600"
                            : "text-red-600"
                        }`}
                      >
                        {formatDate(new Date(data?.upDateTimedAt || ""))}
                      </span>
                      <span
                        className={`text-[10px] font-medium ${
                          isCurrentlyWithinLimit
                            ? "text-yellow-600"
                            : "text-red-600"
                        }`}
                      >
                        {isCurrentlyWithinLimit
                          ? `${remainingHours}h restantes`
                          : `Prazo excedido em ${hoursUsedInCurrentStep - 72}h`}
                      </span>
                    </>
                  )}
                </div>
                {index < workflowSteps.length - 1 && (
                  <div
                    className={`w-6 h-px ${
                      isCompleted
                        ? "bg-green-300"
                        : isCurrent && !isCurrentlyWithinLimit
                        ? "bg-red-300"
                        : "bg-gray-300"
                    } mx-1`}
                  ></div>
                )}
              </div>
            );
          })}
        </div>
      </div>
      <Tabs defaultValue={selectedDefaultTab} className="w-full">
        <TabsList className="w-full justify-start">
          {filteredTabs.map((tab) => (
            <TabsTrigger key={tab.id} value={tab.id} className="text-sm">
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value="ordem-de-servico">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                Informações da Ordem de Serviço
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Centro de Custo
                </p>
                <p>
                  {data?.veiculo?.faturamentoVeiculo?.centro_custo?.descricao}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Número OS</p>
                <p>{data?.osNumber}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Data de Lançamento
                </p>
                <p>
                  {data?.createdAt ? formatData(data.createdAt.toString()) : ""}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Condutor</p>
                <p>{data?.condutor?.nome}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Matrícula do Condutor
                </p>
                <p>{data?.condutor?.matricula}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Tipo de Manutenção
                </p>
                <p>{data?.tipo_manutencao}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Contato do Condutor
                </p>
                <p>{data?.condutor?.contato}</p>
              </div>
              <div className="col-span-2">
                <hr />
                <p className="text-sm font-medium text-gray-500">Observações</p>
                <p>{data?.descricao}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Gestor Criador
                </p>
                <p>{data?.gestorCriador ? data.gestorCriador : "N/A"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Gestor Aprovador
                </p>
                <p>{data?.gestorAprovador ? data.gestorAprovador : "N/A"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Veículo Imobilizado
                </p>
                <Badge variant={data?.mobilizado ? "destructive" : "default"}>
                  {data?.mobilizado ? "Sim" : "Não"}
                </Badge>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Localização</p>
                <p>
                  {data?.cidade_loc}-{data?.estado_loc}
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {data?.veiculo ? (
          <TabsContent value="veiculo">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  Informações do Veículo
                </CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Tipo de Veículo
                  </p>
                  <p>{data.veiculo.tiposVeiculos?.descricao}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Placa</p>
                  <p>{data.veiculo.placa}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Marca/Modelo
                  </p>
                  <p>{data.veiculo.modelo?.descricao}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Ano</p>
                  <p>{data.veiculo.ano_fab}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Cor</p>
                  <p>{data.veiculo.cor}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Chassis</p>
                  <p>{data.veiculo.vin}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Odômetro</p>
                  <p>{data.veiculo.odometro_atual} km</p>
                </div>
                {data.veiculo.lembretes && (
                  <div className="col-span-2">
                    <p className="text-sm font-medium text-gray-500">
                      Mais Informações
                    </p>
                    <p>
                      {Array.isArray(data.veiculo.lembretes)
                        ? data.veiculo.lembretes.join(", ")
                        : data.veiculo.lembretes}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        ) : null}

        {orcamento || filteredOrcamentos.length > 0 ? (
          <TabsContent value="orcamento">
            {!sessionData?.roles.includes("ORCAMENTISTA_OFICINA") && (
              <div className="flex justify-end mb-4">
                <Button
                  variant="outline"
                  onClick={() => setShowAllOrcamentosModal(true)}
                  className="flex items-center gap-2"
                >
                  <span>Visualizar todos os orçamentos</span>
                </Button>
              </div>
            )}

            <ModalCardsOrcamento
              isOpen={showAllOrcamentosModal}
              onClose={() => setShowAllOrcamentosModal(false)}
              title="Todos os Orçamentos"
              os={data}
              onSelectOrcamento={(orc) => {
                setShowAllOrcamentosModal(false);
              }}
              onShowModal={(show) => {}}
              selectedOrcamento={orcamento}
            />

            <div className="border rounded-md shadow-sm">
              {orcamento ? (
                <div className="divide-y">
                  <div className="grid grid-cols-2 px-4 py-3">
                    <span className="">Número do orçamento</span>
                    <span className="font-medium">
                      # {orcamento.numeroOrcamento}
                    </span>
                  </div>
                  <div className="grid grid-cols-2 px-4 py-3">
                    <span className="">Data de lançamento</span>
                    <span className="font-medium">
                      {format(
                        new Date(orcamento.dataLancamento),
                        "dd/MM/yyyy HH:mm"
                      )}
                    </span>
                  </div>
                  <div className="grid grid-cols-2 px-4 py-3">
                    <span className="">Observações</span>
                    <span className="font-medium">
                      {orcamento.observacoes || "-"}
                    </span>
                  </div>
                  <div className="grid grid-cols-2 px-4 py-3">
                    <span className="">Validade</span>
                    <span className="font-medium">
                      {format(new Date(orcamento.validade), "dd/MM/yyyy")}
                    </span>
                  </div>
                  <div className="grid grid-cols-2 px-4 py-3">
                    <span className="">Prazo de entrega</span>
                    <span className="font-medium">
                      {orcamento.prazoEntrega} dias
                    </span>
                  </div>
                  <div className="grid grid-cols-2 px-4 py-3">
                    <span className="">Valor peças</span>
                    <span className="font-medium">
                      {(
                        (orcamento.processedPecas?.reduce(
                          (accP, p) => accP + p.valorUnitario * p.quantidade,
                          0
                        ) || 0) / 100
                      ).toLocaleString("pt-BR", {
                        style: "currency",
                        currency: "BRL",
                      })}
                    </span>
                  </div>
                  <div className="grid grid-cols-2 px-4 py-3">
                    <span className="">Valor serviços</span>
                    <span className="font-medium">
                      {(
                        (orcamento.processedServicos?.reduce(
                          (accS, s) => accS + Math.trunc(s.valor),
                          0
                        ) || 0) / 100
                      ).toLocaleString("pt-BR", {
                        style: "currency",
                        currency: "BRL",
                      })}
                    </span>
                  </div>
                  {/* ----------- */}
                  <div className="grid grid-cols-2 px-4 py-3">
                    <span className="font-bold text-yellow-500">
                      Valor total bruto
                    </span>
                    <span className="font-bold text-yellow-500">
                      {(() => {
                        const valorPecas =
                          orcamento.processedPecas?.reduce(
                            (accP, p) => accP + p.valorUnitario * p.quantidade,
                            0
                          ) || 0;
                        const valorServicos =
                          orcamento.processedServicos?.reduce(
                            (accS, s) => accS + Math.trunc(s.valor),
                            0
                          ) || 0;

                        return (
                          (valorPecas + valorServicos) /
                          100
                        ).toLocaleString("pt-BR", {
                          style: "currency",
                          currency: "BRL",
                        });
                      })()}
                    </span>
                  </div>
                  {/* --------- */}
                  {/* ----------- */}
                  <div className="grid grid-cols-2 px-4 py-3">
                    <span className="font-bold text-red-500">
                      Desconto total
                    </span>
                    <span className="font-bold text-red-500">
                      {(
                        (orcamento.processedPecas?.reduce(
                          (accP, p) =>
                            accP +
                            (p.valorUnitario * p.quantidade - p.valorNegociado),
                          0
                        ) || 0) / 100
                      ).toLocaleString("pt-BR", {
                        style: "currency",
                        currency: "BRL",
                      })}
                    </span>
                  </div>
                  {/* --------- */}
                  <div className="grid grid-cols-2 px-4 py-3">
                    <span className="font-bold text-green-500">
                      Valor total
                    </span>
                    <span className="font-bold text-green-500">
                      {(() => {
                        const valorPecas =
                          orcamento.processedPecas?.reduce(
                            (accP, p) => accP + p.valorUnitario * p.quantidade,
                            0
                          ) || 0;
                        const valorServicos =
                          orcamento.processedServicos?.reduce(
                            (accS, s) => accS + Math.trunc(s.valor),
                            0
                          ) || 0;
                        const valorTotal = valorPecas + valorServicos;
                        const desconto =
                          orcamento.processedPecas?.reduce(
                            (accP, p) =>
                              accP +
                              (p.valorUnitario * p.quantidade -
                                p.valorNegociado),
                            0
                          ) || 0;
                        const descontoFinal = valorTotal - desconto;

                        return (descontoFinal / 100).toLocaleString("pt-BR", {
                          style: "currency",
                          currency: "BRL",
                        });
                      })()}
                    </span>
                  </div>
                  <div className="grid grid-cols-2 px-4 py-3">
                    <span className="font-bold ">Valor aprovado</span>
                    <span className="font-bold">
                      {(() => {
                        const valorPecas =
                          orcamento.processedPecas?.reduce(
                            (accP, p) => accP + p.valorUnitario * p.quantidade,
                            0
                          ) || 0;
                        const valorServicos =
                          orcamento.processedServicos?.reduce(
                            (accS, s) => accS + Math.trunc(s.valor),
                            0
                          ) || 0;
                        const valorTotal = valorPecas + valorServicos;
                        const desconto =
                          orcamento.processedPecas?.reduce(
                            (accP, p) =>
                              accP +
                              (p.valorUnitario * p.quantidade -
                                p.valorNegociado),
                            0
                          ) || 0;
                        const descontoFinal = valorTotal - desconto;

                        return (descontoFinal / 100).toLocaleString("pt-BR", {
                          style: "currency",
                          currency: "BRL",
                        });
                      })()}
                    </span>
                  </div>
                </div>
              ) : (
                filteredOrcamentos.length > 0 && (
                  <div className="divide-y">
                    {filteredOrcamentos.map((orc, index) => {
                      console.log(orc);
                      return (
                        <div
                          key={orc.id || index}
                          className={index > 0 ? "mt-8" : ""}
                        >
                          {index > 0 && <div className="border-t my-4"></div>}
                          <div className="divide-y">
                            <div className="grid grid-cols-2 px-4 py-3">
                              <span className="">Número do orçamento</span>
                              <span className="font-medium">
                                # {orc.numeroOrcamento}
                              </span>
                            </div>
                            <div className="grid grid-cols-2 px-4 py-3">
                              <span className="">Data de lançamento</span>
                              <span className="font-medium">
                                {format(
                                  new Date(orc.dataLancamento),
                                  "dd/MM/yyyy HH:mm"
                                )}
                              </span>
                            </div>
                            <div className="grid grid-cols-2 px-4 py-3">
                              <span className="">Observações</span>
                              <span className="font-medium">
                                {orc.observacoes || "-"}
                              </span>
                            </div>
                            <div className="grid grid-cols-2 px-4 py-3">
                              <span className="">Validade</span>
                              <span className="font-medium">
                                {format(new Date(orc.validade), "dd/MM/yyyy")}
                              </span>
                            </div>
                            <div className="grid grid-cols-2 px-4 py-3">
                              <span className="">Prazo de entrega</span>
                              <span className="font-medium">
                                {orc.prazoEntrega} dias
                              </span>
                            </div>
                            <div className="grid grid-cols-2 px-4 py-3">
                              <span className="">Valor bruto peças</span>
                              <span className="font-medium">
                                {(
                                  (orc.processedPecas?.reduce(
                                    (accP, p) =>
                                      accP + p.valorUnitario * p.quantidade,
                                    0
                                  ) || 0) / 100
                                ).toLocaleString("pt-BR", {
                                  style: "currency",
                                  currency: "BRL",
                                })}
                              </span>
                            </div>
                            <div className="grid grid-cols-2 px-4 py-3">
                              <span className="">Valor liquido peças</span>
                              <span className="font-medium">
                                {(
                                  (orc.processedPecas?.reduce(
                                    (accP, p) => accP + p.valorNegociado,
                                    0
                                  ) || 0) / 100
                                ).toLocaleString("pt-BR", {
                                  style: "currency",
                                  currency: "BRL",
                                })}
                              </span>
                            </div>
                            <div className="grid grid-cols-2 px-4 py-3">
                              <span className="">Valor bruto serviços</span>
                              <span className="font-medium">
                                {(
                                  (orc.processedServicos?.reduce(
                                    (accS, s) =>
                                      accS +
                                      Math.trunc(
                                        (s.valorUnitario
                                          ? s.valorUnitario
                                          : s.valor) *
                                          (s.quantidadeHoras
                                            ? s.quantidadeHoras
                                            : 1)
                                      ),
                                    0
                                  ) || 0) / 100
                                ).toLocaleString("pt-BR", {
                                  style: "currency",
                                  currency: "BRL",
                                })}
                              </span>
                            </div>
                            <div className="grid grid-cols-2 px-4 py-3">
                              <span className="">Valor liquido serviços</span>
                              <span className="font-medium">
                                {(
                                  (orc.processedServicos?.reduce(
                                    (accS, s) => accS + Math.trunc(s.valor),
                                    0
                                  ) || 0) / 100
                                ).toLocaleString("pt-BR", {
                                  style: "currency",
                                  currency: "BRL",
                                })}
                              </span>
                            </div>
                            {/* ----------- */}
                            <div className="grid grid-cols-2 px-4 py-3">
                              <span className="font-bold text-yellow-500">
                                Valor total bruto
                              </span>
                              <span className="font-bold text-yellow-500">
                                {(
                                  ((orc.processedPecas?.reduce(
                                    (accP, p) =>
                                      accP + p.valorUnitario * p.quantidade,
                                    0
                                  ) || 0) +
                                    (orc.processedServicos?.reduce(
                                      (accS, s) =>
                                        accS +
                                        Math.trunc(
                                          (s.valorUnitario
                                            ? s.valorUnitario
                                            : s.valor) *
                                            (s.quantidadeHoras
                                              ? s.quantidadeHoras
                                              : 1)
                                        ),
                                      0
                                    ) || 0)) /
                                  100
                                ).toLocaleString("pt-BR", {
                                  style: "currency",
                                  currency: "BRL",
                                })}
                              </span>
                            </div>
                            {/* --------- */}
                            {/* ----------- */}
                            <div className="grid grid-cols-2 px-4 py-3">
                              <span className="font-bold text-red-500">
                                Desconto peças
                              </span>
                              <span className="font-bold text-red-500">
                                {(
                                  ((orc.processedPecas?.reduce(
                                    (accP, p) =>
                                      accP + p.valorUnitario * p.quantidade,
                                    0
                                  ) || 0) -
                                    (orc.processedPecas?.reduce(
                                      (accP, p) => accP + p.valorNegociado,
                                      0
                                    ) || 0)) /
                                  100
                                ).toLocaleString("pt-BR", {
                                  style: "currency",
                                  currency: "BRL",
                                })}
                              </span>
                            </div>
                            <div className="grid grid-cols-2 px-4 py-3">
                              <span className="font-bold text-red-500">
                                Desconto serviços
                              </span>
                              <span className="font-bold text-red-500">
                                {(
                                  (orc.processedServicos?.reduce(
                                    (accS, s) =>
                                      accS +
                                      Math.trunc(
                                        (s.valorUnitario
                                          ? s.valorUnitario
                                          : s.valor) *
                                          (s.quantidadeHoras
                                            ? s.quantidadeHoras
                                            : 1) -
                                          s.valor
                                      ),
                                    0
                                  ) || 0) / 100
                                ).toLocaleString("pt-BR", {
                                  style: "currency",
                                  currency: "BRL",
                                })}
                              </span>
                            </div>
                            <div className="grid grid-cols-2 px-4 py-3">
                              <span className="font-bold text-red-500">
                                Desconto total
                              </span>
                              <span className="font-bold text-red-500">
                                {(
                                  ((orc.processedPecas?.reduce(
                                    (accP, p) =>
                                      accP +
                                      (p.valorUnitario * p.quantidade -
                                        p.valorNegociado),
                                    0
                                  ) || 0) +
                                    (orc.processedServicos?.reduce(
                                      (accS, s) =>
                                        accS +
                                        Math.trunc(
                                          (s.valorUnitario
                                            ? s.valorUnitario
                                            : s.valor) *
                                            (s.quantidadeHoras
                                              ? s.quantidadeHoras
                                              : 1) -
                                            s.valor
                                        ),
                                      0
                                    ) || 0)) /
                                  100
                                ).toLocaleString("pt-BR", {
                                  style: "currency",
                                  currency: "BRL",
                                })}
                              </span>
                            </div>
                            {/* --------- */}
                            <div className="grid grid-cols-2 px-4 py-3">
                              <span className="font-bold text-green-500">
                                Valor total
                              </span>
                              <span className="font-bold text-green-500">
                                {(() => {
                                  const valorPecas =
                                    orc.processedPecas?.reduce(
                                      (accP, p) =>
                                        accP + p.valorUnitario * p.quantidade,
                                      0
                                    ) || 0;
                                  const valorServicos =
                                    orc.processedServicos?.reduce(
                                      (accS, s) => accS + Math.trunc(s.valor),
                                      0
                                    ) || 0;
                                  const valorTotal = valorPecas + valorServicos;
                                  const desconto =
                                    orc.processedPecas?.reduce(
                                      (accP, p) =>
                                        accP +
                                        (p.valorUnitario * p.quantidade -
                                          p.valorNegociado),
                                      0
                                    ) || 0;
                                  const descontoFinal = valorTotal - desconto;

                                  return (descontoFinal / 100).toLocaleString(
                                    "pt-BR",
                                    {
                                      style: "currency",
                                      currency: "BRL",
                                    }
                                  );
                                })()}
                              </span>
                            </div>
                            <div className="grid grid-cols-2 px-4 py-3">
                              <span className="font-bold ">Valor aprovado</span>
                              <span className="font-bold">
                                {(() => {
                                  const valorPecas =
                                    orc.processedPecas?.reduce(
                                      (accP, p) =>
                                        accP + p.valorUnitario * p.quantidade,
                                      0
                                    ) || 0;
                                  const valorServicos =
                                    orc.processedServicos?.reduce(
                                      (accS, s) => accS + Math.trunc(s.valor),
                                      0
                                    ) || 0;
                                  const valorTotal = valorPecas + valorServicos;
                                  const desconto =
                                    orc.processedPecas?.reduce(
                                      (accP, p) =>
                                        accP +
                                        (p.valorUnitario * p.quantidade -
                                          p.valorNegociado),
                                      0
                                    ) || 0;
                                  const descontoFinal = valorTotal - desconto;

                                  return (descontoFinal / 100).toLocaleString(
                                    "pt-BR",
                                    {
                                      style: "currency",
                                      currency: "BRL",
                                    }
                                  );
                                })()}
                              </span>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )
              )}
              {!orcamento && filteredOrcamentos.length === 0 && (
                <div className="p-4 text-center text-gray-500">
                  Nenhum orçamento disponível.
                </div>
              )}
            </div>
          </TabsContent>
        ) : null}

        {orcamento || filteredOrcamentos.length > 0 ? (
          <TabsContent value="pecas-e-servicos">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Peças e Serviços</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-2 text-sm font-medium text-gray-500">
                          Tipo
                        </th>
                        <th className="text-left py-3 px-2 text-sm font-medium text-gray-500">
                          Grupo
                        </th>
                        <th className="text-left py-3 px-2 text-sm font-medium text-gray-500">
                          Descrição
                        </th>
                        <th className="text-left py-3 px-2 text-sm font-medium text-gray-500">
                          Código
                        </th>
                        <th className="text-left py-3 px-2 text-sm font-medium text-gray-500">
                          Marca
                        </th>
                        <th className="text-left py-3 px-2 text-sm font-medium text-gray-500">
                          Garantia
                        </th>
                        <th className="text-left py-3 px-2 text-sm font-medium text-gray-500">
                          Quantidade
                        </th>
                        <th className="text-left py-3 px-2 text-sm font-medium text-gray-500">
                          Valor unitário
                        </th>
                        <th className="text-left py-3 px-2 text-sm font-medium text-gray-500">
                          Valor total bruto
                        </th>

                        <th className="text-left py-3 px-2 text-sm font-medium text-gray-500">
                          Valor desconto
                        </th>
                        <th className="text-left py-3 px-2 text-sm font-medium text-gray-500">
                          Valor negociado
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {/* Renderizar peças */}
                      {(
                        orcamento?.processedPecas ||
                        filteredOrcamentos.flatMap(
                          (o) => o.processedPecas || []
                        )
                      ).map((peca, index) => {
                        return (
                          <tr key={`peca-${index}`} className="border-b">
                            <td className="py-3 px-2">Peças</td>
                            <td className="py-3 px-2">
                              {peca?.tipoPecas ?? "Peça"}
                            </td>
                            <td className="py-3 px-2">{peca?.descricao}</td>
                            <td className="py-3 px-2">
                              {peca?.codigo?.toUpperCase()}
                            </td>
                            <td className="py-3 px-2">{peca?.marca || "-"}</td>
                            <td className="py-3 px-2">
                              {peca?.garantia
                                ? new Date(peca.garantia).toLocaleDateString(
                                    "pt-BR"
                                  )
                                : "-"}
                            </td>
                            <td className="py-3 px-2">{peca?.quantidade}</td>
                            <td className="py-3 px-2">
                              {(
                                (peca?.valorUnitario ?? 0) / 100
                              ).toLocaleString("pt-BR", {
                                style: "currency",
                                currency: "BRL",
                              })}
                            </td>
                            <td className="py-3 px-2">
                              {(
                                ((peca?.valorUnitario ?? 0) * peca.quantidade) /
                                100
                              ).toLocaleString("pt-BR", {
                                style: "currency",
                                currency: "BRL",
                              })}
                            </td>
                            <td className="py-3 px-2">
                              {(
                                ((peca?.valorUnitario ?? 0) * peca.quantidade -
                                  (peca?.valorNegociado ?? 0)) /
                                100
                              ).toLocaleString("pt-BR", {
                                style: "currency",
                                currency: "BRL",
                              })}
                            </td>
                            <td className="py-3 px-2">
                              {(
                                (peca?.valorNegociado ?? 0) / 100
                              ).toLocaleString("pt-BR", {
                                style: "currency",
                                currency: "BRL",
                              })}
                            </td>
                          </tr>
                        );
                      })}

                      {/* Renderizar serviços */}
                      {(
                        orcamento?.processedServicos ||
                        filteredOrcamentos.flatMap(
                          (o) => o.processedServicos || []
                        )
                      ).map((servico, index) => {
                        return (
                          <tr key={`servico-${index}`} className="border-b">
                            <td className="py-3 px-2">Serviços</td>
                            <td className="py-3 px-2">
                              {servico.tipoServico ?? "Mão de Obra"}
                            </td>
                            <td className="py-3 px-2">{servico?.descricao}</td>
                            <td className="py-3 px-2">
                              {Math.floor(Math.random() * 90000) + 10000}
                            </td>
                            <td className="py-3 px-2">-</td>
                            <td className="py-3 px-2">
                              {servico?.garantia
                                ? new Date(servico.garantia).toLocaleDateString(
                                    "pt-BR"
                                  )
                                : "-"}
                            </td>
                            <td className="py-3 px-2">
                              {formatHorasDecimal(servico?.quantidadeHoras)}
                            </td>
                            <td className="py-3 px-2">
                              {servico.valorUnitario
                                ? (
                                    (servico?.valorUnitario ?? 0) / 100
                                  ).toLocaleString("pt-BR", {
                                    style: "currency",
                                    currency: "BRL",
                                  })
                                : "-"}
                            </td>
                            <td className="py-3 px-2">
                              {servico.valorUnitario
                                ? (
                                    (servico?.valorUnitario *
                                      servico.quantidadeHoras) /
                                    100
                                  ).toLocaleString("pt-BR", {
                                    style: "currency",
                                    currency: "BRL",
                                  })
                                : "-"}
                            </td>
                            <td className="py-3 px-2">
                              {servico.valorUnitario
                                ? (
                                    (servico?.valorUnitario *
                                      servico.quantidadeHoras -
                                      servico.valor) /
                                    100
                                  ).toLocaleString("pt-BR", {
                                    style: "currency",
                                    currency: "BRL",
                                  })
                                : "-"}
                            </td>
                            <td className="py-3 px-2">
                              {((servico?.valor ?? 0) / 100).toLocaleString(
                                "pt-BR",
                                {
                                  style: "currency",
                                  currency: "BRL",
                                }
                              )}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                    <tfoot>
                      <tr className="border-t">
                        <td
                          colSpan={8}
                          className="text-right py-3 px-2 font-medium"
                        >
                          Total:
                        </td>
                        <td colSpan={2} className="py-3 px-2 font-medium">
                          {(() => {
                            if (orcamento) {
                              const valorPecas =
                                orcamento.processedPecas?.reduce(
                                  (accP, p) => accP + p.valorNegociado,
                                  0
                                ) || 0;

                              const valorServicos =
                                orcamento.processedServicos?.reduce(
                                  (accS, s) => accS + Math.trunc(s.valor),
                                  0
                                ) || 0;

                              return (
                                (valorPecas + valorServicos) /
                                100
                              ).toLocaleString("pt-BR", {
                                style: "currency",
                                currency: "BRL",
                              });
                            } else if (filteredOrcamentos.length > 0) {
                              // Calcular para todos os orçamentos filtrados
                              let totalPecas = 0;
                              let totalServicos = 0;

                              filteredOrcamentos.forEach((orc) => {
                                totalPecas +=
                                  orc.processedPecas?.reduce(
                                    (accP, p) => accP + p.valorNegociado,
                                    0
                                  ) || 0;

                                totalServicos +=
                                  orc.processedServicos?.reduce(
                                    (accS, s) => accS + Math.trunc(s.valor),
                                    0
                                  ) || 0;
                              });

                              return (
                                (totalPecas + totalServicos) /
                                100
                              ).toLocaleString("pt-BR", {
                                style: "currency",
                                currency: "BRL",
                              });
                            }

                            return "R$ 0,00";
                          })()}
                        </td>
                      </tr>
                      <tr>
                        <td
                          colSpan={8}
                          className="text-right py-3 px-2 font-medium"
                        >
                          Aprovado:
                        </td>
                        <td colSpan={2} className="py-3 px-2 font-medium">
                          {(() => {
                            if (orcamento) {
                              const valorPecas =
                                orcamento.processedPecas?.reduce(
                                  (accP, p) => accP + p.valorNegociado,
                                  0
                                ) || 0;

                              const valorServicos =
                                orcamento.processedServicos?.reduce(
                                  (accS, s) => accS + Math.trunc(s.valor),
                                  0
                                ) || 0;

                              return (
                                (valorPecas + valorServicos) /
                                100
                              ).toLocaleString("pt-BR", {
                                style: "currency",
                                currency: "BRL",
                              });
                            } else if (filteredOrcamentos.length > 0) {
                              // Calcular para todos os orçamentos filtrados
                              let totalPecas = 0;
                              let totalServicos = 0;

                              filteredOrcamentos.forEach((orc) => {
                                totalPecas +=
                                  orc.processedPecas?.reduce(
                                    (accP, p) => accP + p.valorNegociado,
                                    0
                                  ) || 0;

                                totalServicos +=
                                  orc.processedServicos?.reduce(
                                    (accS, s) => accS + Math.trunc(s.valor),
                                    0
                                  ) || 0;
                              });

                              return (
                                (totalPecas + totalServicos) /
                                100
                              ).toLocaleString("pt-BR", {
                                style: "currency",
                                currency: "BRL",
                              });
                            }

                            return "R$ 0,00";
                          })()}
                        </td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </CardContent>
            </Card>
            <Dialog
              open={showOverlapsModal}
              onOpenChange={setShowOverlapsModal}
            >
              <DialogContent className="max-w-3xl w-full p-4">
                <DialogHeader className="flex justify-between items-center mb-4">
                  <DialogTitle className="text-lg font-bold">
                    Tabela Templaria
                  </DialogTitle>
                  <button
                    onClick={() => setShowOverlapsModal(false)}
                    className="text-red-500"
                  >
                    Fechar
                  </button>
                </DialogHeader>
                <table className="min-w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="py-2 px-4 text-left">Funilaria</th>
                      <th className="py-2 px-4 text-left">Pintura</th>
                      <th className="py-2 px-4 text-left">Estofamento</th>
                      <th className="py-2 px-4 text-left">Elétrico</th>
                      <th className="py-2 px-4 text-left">Mecânica</th>
                      <th className="py-2 px-4 text-left">Vidraçaria</th>
                    </tr>
                  </thead>
                  <tbody>
                    {overlaps.map((item, index) => (
                      <tr key={index} className="border-b">
                        <td className="py-2 px-4">{item.bodyshop}</td>
                        <td className="py-2 px-4">{item.painting}</td>
                        <td className="py-2 px-4">{item.tapestry}</td>
                        <td className="py-2 px-4">{item.electrical}</td>
                        <td className="py-2 px-4">{item.mechanic}</td>
                        <td className="py-2 px-4">{item.glazing}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </DialogContent>
            </Dialog>
          </TabsContent>
        ) : null}

        {infoCredenciado ? (
          <TabsContent value="orcamentista">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  Informações do Orçamentista
                </CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Razão Social
                  </p>
                  <p>{infoCredenciado.informacoes[0].razao_social}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">CNPJ</p>
                  <p>{infoCredenciado.informacoes[0].cnpj}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Atividade Principal
                  </p>
                  <p>{infoCredenciado.informacoes[0].atividade_principal}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Funcionamento
                  </p>
                  <p>{infoCredenciado.informacoes[0].horario_funcionamento}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Telefone</p>
                  <p>{infoCredenciado.contato.telefone}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Celular</p>
                  <p>{infoCredenciado.contato.celular}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Email</p>
                  <p>{infoCredenciado.contato.email}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Endereço</p>
                  <p>
                    {[
                      infoCredenciado.endereco.logradouro,
                      infoCredenciado.endereco.bairro,
                      infoCredenciado.endereco.cidade,
                      infoCredenciado.endereco.estado,
                    ]
                      .filter(Boolean)
                      .join(", ")}
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        ) : null}

        {(sessionData?.contrato.ajuste_gestor ||
          sessionData?.roles.includes("FINANCEIRO") ||
          sessionData?.roles.includes("ADMIN") ||
          sessionData?.roles.includes("ORCAMENTISTA_OFICINA")) && (
          <TabsContent value="nota-fiscal">
            {showUploadInvoices && (
              <Card style={{ marginBottom: "20px" }}>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">
                      Anexar Notas Fiscais
                    </CardTitle>
                    <p>Número OS: {data?.osNumber}</p>
                    <p>Placa do veículo: {data?.veiculo?.placa}</p>
                    <p>
                      Valor total das peças:{" "}
                      {((totalPecas ?? 0) / 100).toLocaleString("pt-BR", {
                        style: "currency",
                        currency: "BRL",
                      })}
                    </p>
                    <p>
                      Valor total dos serviços:{" "}
                      {((totalServicos ?? 0) / 100).toLocaleString("pt-BR", {
                        style: "currency",
                        currency: "BRL",
                      })}
                    </p>
                    <p>
                      Valor total:{" "}
                      {((valorTotalOrcamento ?? 0) / 100).toLocaleString(
                        "pt-BR",
                        {
                          style: "currency",
                          currency: "BRL",
                        }
                      )}
                    </p>
                    <br />
                    <a href="" className="font-bold underline">
                      VISUALIZAR MODELO DA NOTA FISCAL
                    </a>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-md p-3 text-sm text-blue-800 mt-2">
                    <p>
                      Destino da Nota Fiscal:{" "}
                      <span className="ml-4 font-bold">
                        {sessionData?.contrato?.send_nf_to}
                      </span>
                    </p>
                    <p>
                      CNPJ:{" "}
                      <span className="ml-4 font-bold">
                        {formatCNPJ(
                          sessionData?.contrato?.cnpj_financial ?? ""
                        )}
                      </span>
                    </p>
                    <p>
                      CEP:{" "}
                      <span className="ml-4 font-bold">
                        {sessionData?.contrato?.cep_financial}
                      </span>
                    </p>
                    <p>
                      Endereço:{" "}
                      <span className="ml-4 font-bold">
                        {sessionData?.contrato?.logradouro_financial}
                      </span>
                    </p>
                    <p>
                      Bairro:{" "}
                      <span className="ml-4 font-bold">
                        {sessionData?.contrato?.bairro_financial} -{" "}
                        {sessionData?.contrato?.estado_financial}
                      </span>
                    </p>
                    <p>
                      Número do Contrato:{" "}
                      <span className="ml-4 font-bold">
                        {sessionData?.contrato?.numero}
                      </span>
                    </p>
                    <br />
                    <p className="font-medium mb-1">
                      📋 Informações obrigatórias na Nota Fiscal:
                    </p>
                    <ul className="list-disc pl-5">
                      {sessionData?.contrato.nf_parameter?.placa_veiculo && (
                        <li>Placa do veículo</li>
                      )}
                      {sessionData?.contrato?.nf_parameter?.modelo_veiculo && (
                        <li>Modelo do veículo</li>
                      )}
                      {sessionData.contrato.nf_parameter?.numero_os && (
                        <li>Número da Ordem de Serviço</li>
                      )}
                      {sessionData.contrato.nf_parameter?.numero_contrato && (
                        <li>Número do contrato</li>
                      )}
                    </ul>
                    <p className="mt-1 text-xs italic">
                      A nota fiscal deve conter todas as informações acima para
                      ser aceita pelo sistema.
                    </p>
                    <p className="mt-1 text-xs italic text-red-500">
                      CASO NÃO SEJA OPTANTE PELO SIMPLES DEVERÁ CONTER OS
                      DESCONTOS DE ISS E IRPJ.
                    </p>
                  </div>
                </CardHeader>
                {pecasProcessadas.length > 0 && (
                  <div className="flex flex-row">
                    <div className="w-full">
                      <FileUploader
                        onFilesChange={setUploadedInvoicePdfPecas}
                        title="Nota Fiscal de Peças (PDF) *Obrigatorio"
                        files={uploadedInvoicePdfPecas}
                        setFiles={setUploadedInvoicePdfPecas}
                        accept="application/pdf"
                      />
                    </div>
                    <div className="w-full">
                      <FileUploader
                        onFilesChange={setUploadedInvoiceXmlPecas}
                        title="Nota Fiscal de Peças (XML) *Obrigatorio"
                        files={uploadedInvoiceXmlPecas}
                        setFiles={setUploadedInvoiceXmlPecas}
                        accept=".xml"
                      />
                    </div>
                  </div>
                )}
                {servicosProcessados.length > 0 && (
                  <div className="flex flex-row">
                    <div className="w-full">
                      <FileUploader
                        onFilesChange={setUploadedInvoicePdfServicos}
                        title="Nota Fiscal de Serviço (PDF) *"
                        files={uploadedInvoicePdfServicos}
                        setFiles={setUploadedInvoicePdfServicos}
                        accept="application/pdf"
                      />
                    </div>
                    <div className="w-full">
                      <FileUploader
                        onFilesChange={setUploadedInvoiceXmlServicos}
                        title="Nota Fiscal de Serviço (XML) *"
                        files={uploadedInvoiceXmlServicos}
                        setFiles={setUploadedInvoiceXmlServicos}
                        accept=".xml"
                      />
                    </div>
                  </div>
                )}
                <div className="full-w flex flex-row justify-center mt-5 mb-5">
                  <Button
                    onClick={() => setShowConfirmationModal(true)}
                    disabled={
                      // Se houver peças processadas, precisa pelo menos 1 PDF ou 1 XML de peças
                      (pecasProcessadas.length > 0 &&
                        uploadedInvoicePdfPecas.length === 0 &&
                        uploadedInvoiceXmlPecas.length === 0) ||
                      (servicosProcessados.length > 0 &&
                        uploadedInvoicePdfServicos.length === 0 &&
                        uploadedInvoiceXmlServicos.length === 0)
                    }
                  >
                    Enviar
                  </Button>
                </div>
              </Card>
            )}

            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-lg">Nota Fiscal</CardTitle>
                {(sessionData?.roles.includes("FINANCEIRO") ||
                  sessionData?.roles.includes("ADMIN")) && (
                  <div className="flex gap-2">
                    {showAjustesForm ? (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowAjustesForm(false)}
                      >
                        Cancelar
                      </Button>
                    ) : (
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={() => setShowAjustesForm(true)}
                      >
                        Recusar
                      </Button>
                    )}
                    {(data?.status === "finalizada" ||
                      data?.status === "Aguardando Aprovação" ||
                      data?.status === "execucao") && (
                      <Button
                        variant="default"
                        size="sm"
                        onClick={handleFaturarOS}
                      >
                        Faturar OS
                      </Button>
                    )}
                  </div>
                )}
              </CardHeader>
              <CardContent>
                {showAjustesForm && (
                  <div className="mb-6 p-4 border rounded-lg">
                    <h3 className="text-sm font-bold mb-2">
                      Solicitar ajustes
                    </h3>
                    <textarea
                      className="w-full border rounded-md p-2 min-h-[100px] mb-2"
                      placeholder="Descreva os ajustes necessários..."
                      value={ajustesObservacoes}
                      onChange={(e) => setAjustesObservacoes(e.target.value)}
                    />
                    <Button
                      onClick={handleEnviarAjustes}
                      disabled={
                        isSubmittingAjustes || !ajustesObservacoes.trim()
                      }
                    >
                      {isSubmittingAjustes ? "Enviando..." : "Enviar ajustes"}
                    </Button>
                  </div>
                )}

                {data?.orcamentos &&
                data.orcamentos.length > 0 &&
                arquivosNotaFiscal?.length > 0 ? (
                  <div className="grid grid-cols-1 gap-4">
                    {arquivosNotaFiscal?.map((arquivo: any, index: number) => (
                      <div
                        key={index}
                        className="border p-4 rounded-lg flex items-center gap-4"
                      >
                        {arquivo.file.mimetype?.startsWith("image/") && (
                          <img
                            src={`${formatFilePath(arquivo.file.path)}`}
                            alt={arquivo.file.originalname}
                            className="w-16 h-16 object-cover rounded"
                          />
                        )}
                        <div>
                          <p className="font-medium">
                            {arquivo.file.originalname}
                          </p>
                          <p className="text-xs text-gray-500">
                            {(arquivo.file.size / 1024).toFixed(2)} KB
                          </p>
                          <a
                            href={`${formatFilePath(arquivo.file.path)}`}
                            target="_blank"
                            download={arquivo.file.originalname}
                            className="text-blue-500 underline text-sm"
                          >
                            Download
                          </a>
                        </div>
                      </div>
                    ))}
                    {(sessionData?.roles.includes("FINANCEIRO") ||
                      sessionData?.roles.includes("ADMIN")) && (
                      <>
                        <p className="text-red-500">
                          Credenciado Optante Simples Nacional:{" "}
                          <span className="ml-4 font-bold">
                            {data?.credenciado?.optante ? "Sim" : "Não"}
                          </span>
                        </p>
                        <p className="text-red-500">
                          Observaçoes do Credenciado:{" "}
                        </p>
                        <p className="text-red-500">
                          {data?.credenciado?.informacoes[0]
                            .observacoes_gerais || "Sem observação"}
                        </p>
                        <p className="text-red-500">
                          Prazos:{" "}
                          <span>
                            {data?.credenciado?.Prazo?.descricao ||
                              "Sem Prazos"}{" "}
                          </span>
                        </p>
                      </>
                    )}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">
                    Nenhum arquivo disponível.
                  </p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        )}
        <TabsContent value="acervo-digital">
          <Card style={{ marginBottom: "20px" }}>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-lg">Anexar Arquivos</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center">
                <div className="w-full mb-5">
                  <FileUploader
                    onFilesChange={handleFilesChange}
                    files={uploadedFiles}
                    setFiles={setUploadedFiles}
                  />
                </div>
                <Button
                  disabled={uploadedFiles.length === 0}
                  onClick={handleSendFiles}
                >
                  Enviar
                </Button>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-lg">Acervo Digital</CardTitle>
            </CardHeader>
            <CardContent>
              {data?.orcamentos &&
              data.orcamentos.length > 0 &&
              arquivos?.length > 0 ? (
                <div className="grid grid-cols-1 gap-4">
                  {arquivos?.map((arquivo: any, index: number) => (
                    <div
                      key={index}
                      className="border p-4 rounded-lg flex items-center gap-4"
                    >
                      {arquivo.file.mimetype?.startsWith("image/") && (
                        <img
                          src={`${formatFilePath(arquivo.file.path)}`}
                          alt={arquivo.file.originalname}
                          className="w-16 h-16 object-cover rounded"
                        />
                      )}
                      <div>
                        <p className="font-medium">
                          {arquivo.file.originalname}
                        </p>
                        <p className="text-xs text-gray-500">
                          {(arquivo.file.size / 1024).toFixed(2)} KB
                        </p>
                        <a
                          href={`${formatFilePath(arquivo.file.path)}`}
                          target="_blank"
                          download={arquivo.file.originalname}
                          className="text-blue-500 underline text-sm"
                        >
                          Download
                        </a>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500">
                  Nenhum arquivo disponível.
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      <ConfirmModal
        title="Anexar notas fiscais"
        message={`Você confirma que a nota fiscal irá para ${
          data?.send_nf_to === "centro_custo"
            ? data?.veiculo?.faturamentoVeiculo?.centro_custo?.descricao
            : "carletto"
        }?`}
        onConfirm={handleSend}
        open={showConfirmationModal}
        onCancel={() => setShowConfirmationModal(false)}
      />
    </div>
  );
}
