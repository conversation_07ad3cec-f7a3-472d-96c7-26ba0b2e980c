import React, { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";

export function ChecklistDetailsDialog({
  isOpen,
  onClose,
  checklist,
}: {
  isOpen: boolean;
  onClose: () => void;
  checklist: any;
}) {
  if (!checklist) return null;

  const hasEntryAndExit = checklist.entrada && checklist.saida;

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd/MM/yyyy HH:mm");
    } catch (error) {
      return "Data inválida";
    }
  };

  const renderChecklistContent = (checklistData: any) => {
    if (!checklistData) return <div>Nenhum dado disponível</div>;
    return (
      <Tabs defaultValue="info" className="w-full">
        <TabsList className="grid grid-cols-3">
          <TabsTrigger value="info">Informações</TabsTrigger>
          <TabsTrigger value="status">Status do Veículo</TabsTrigger>
          <TabsTrigger value="images">Imagens</TabsTrigger>
        </TabsList>

        <TabsContent value="info" className="space-y-4">
          <ScrollArea className="h-[60vh]">
            <div className="space-y-4 p-1">
              <div className="bg-muted/50 p-4 rounded-md">
                <h3 className="font-medium mb-2">Informações Básicas</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="font-medium">Tipo:</span> {checklistData.tipo_checklist}
                  </div>
                  <div>
                    <span className="font-medium">Data:</span>{" "}
                    {formatDate(checklistData.data_checklist)}
                  </div>
                  <div>
                    <span className="font-medium">Odômetro:</span> {checklistData.odometro_atual}
                  </div>
                  <div>
                    <span className="font-medium">Nível Combustível:</span>{" "}
                    {checklistData.nivel_combustivel}
                  </div>
                  <div>
                    <span className="font-medium">Status da OS:</span>{" "}
                    {checklistData.checklist_entrada?.status || "N/A"}
                  </div>
                </div>
              </div>

              <div className="bg-muted/50 p-4 rounded-md">
                <h3 className="font-medium mb-2">Veículo</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="font-medium">Placa:</span>{" "}
                    {checklistData.veiculo?.placa || "N/A"}
                  </div>
                  <div>
                    <span className="font-medium">Marca/Modelo:</span>{" "}
                    {checklistData.veiculo?.marca?.descricao}{" "}
                    {checklistData.veiculo?.modelo?.descricao}
                  </div>
                  <div>
                    <span className="font-medium">Centro de Custo:</span>{" "}
                    {checklistData.veiculo?.faturamentoVeiculo?.centro_custo?.descricao || "N/A"}
                  </div>
                </div>
              </div>

              <div className="bg-muted/50 p-4 rounded-md">
                <h3 className="font-medium mb-2">Condutor</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="font-medium">Nome:</span>{" "}
                    {checklistData.condutor?.nome || "N/A"}
                  </div>
                  <div>
                    <span className="font-medium">Matrícula:</span>{" "}
                    {checklistData.condutor?.matricula || "N/A"}
                  </div>
                  <div>
                    <span className="font-medium">Contato:</span>{" "}
                    {checklistData.condutor?.contato || "N/A"}
                  </div>
                </div>
              </div>

              {checklistData.observacao && (
                <div className="bg-muted/50 p-4 rounded-md">
                  <h3 className="font-medium mb-2">Observações</h3>
                  <p className="text-sm">{checklistData.observacao}</p>
                </div>
              )}

              <div className="bg-muted/50 p-4 rounded-md">
                <h3 className="font-medium mb-2">Observações Específicas</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="font-medium">Rodas:</span>{" "}
                    {checklistData.observacoes_rodas || "N/A"}
                  </div>
                  <div>
                    <span className="font-medium">Luzes Externas:</span>{" "}
                    {checklistData.observacoes_luzes_externas || "N/A"}
                  </div>
                  <div>
                    <span className="font-medium">Luz do Painel:</span>{" "}
                    {checklistData.observacoes_luz_do_painel || "N/A"}
                  </div>
                  <div>
                    <span className="font-medium">Limpeza:</span>{" "}
                    {checklistData.observacoes_limpeza || "N/A"}
                  </div>
                  <div>
                    <span className="font-medium">Estado Geral:</span>{" "}
                    {checklistData.observacoes_estado_geral || "N/A"}
                  </div>
                </div>
              </div>
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="status">
          <ScrollArea className="h-[60vh]">
            <div className="grid grid-cols-2 gap-2 p-4">
              {checklistData.situacao_veiculo &&
                Object.entries(checklistData.situacao_veiculo)
                  .filter(([key]) => key !== "id" && key !== "checklistId")
                  .map(([key, value]) => (
                    <div key={key} className="flex items-center gap-2">
                      <div
                        className={`w-3 h-3 rounded-full ${
                          value ? "bg-green-500" : "bg-red-500"
                        }`}></div>
                      <span>{key.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}</span>
                    </div>
                  ))}
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="images">
          <ScrollArea className="h-[60vh]">
            <div className="grid grid-cols-2 gap-4 p-1">
              {hasEntryAndExit ? (
                checklistData.image && checklistData.image.flat().length > 0 ? (
                  checklistData.image.flat().map((img: string, i: number) => (
                    <div
                      key={i}
                      className="relative aspect-square overflow-hidden rounded-md border">
                      <a href={img} target="_blank" rel="noopener noreferrer">
                        {/* eslint-disable-next-line @next/next/no-img-element */}
                        <img
                          src={img}
                          alt={`Imagem do checklist ${i + 1}`}
                          className="object-cover w-full h-full hover:scale-105 transition-transform"
                        />
                      </a>
                    </div>
                  ))
                ) : (
                  <div className="col-span-2 h-40 flex items-center justify-center text-muted-foreground">
                    Nenhuma imagem disponível
                  </div>
                )
              ) : checklistData.saida &&
                checklistData.saida.image &&
                checklistData.saida.image.flat().length > 0 ? (
                checklistData.saida.image.flat().map((img: string, i: number) => (
                  <div key={i} className="relative aspect-square overflow-hidden rounded-md border">
                    <a href={img} target="_blank" rel="noopener noreferrer">
                      {/* eslint-disable-next-line @next/next/no-img-element */}
                      <img
                        src={img}
                        alt={`Imagem do checklist ${i + 1}`}
                        className="object-cover w-full h-full hover:scale-105 transition-transform"
                      />
                    </a>
                  </div>
                ))
              ) : checklistData.entrada &&
                checklistData.entrada.image &&
                checklistData.entrada.image.flat().length > 0 ? (
                checklistData.entrada.image.flat().map((img: string, i: number) => (
                  <div key={i} className="relative aspect-square overflow-hidden rounded-md border">
                    <a href={img} target="_blank" rel="noopener noreferrer">
                      {/* eslint-disable-next-line @next/next/no-img-element */}
                      <img
                        src={img}
                        alt={`Imagem do checklist ${i + 1}`}
                        className="object-cover w-full h-full hover:scale-105 transition-transform"
                      />
                    </a>
                  </div>
                ))
              ) : (
                <div className="col-span-2 h-40 flex items-center justify-center text-muted-foreground">
                  Nenhuma imagem disponível
                </div>
              )}
            </div>
          </ScrollArea>
        </TabsContent>
      </Tabs>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Detalhes do Checklist</span>
            {!hasEntryAndExit && (
              <Badge variant={checklist.tipo_checklist === "Entrada" ? "default" : "secondary"}>
                {checklist.tipo_checklist || "Checklist"}
              </Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            OS: {checklist.checklist_entrada?.osNumber || checklist.osNumber || "N/A"}
          </DialogDescription>
        </DialogHeader>

        {hasEntryAndExit ? (
          <Tabs defaultValue="entrada" className="w-full">
            <TabsList className="grid grid-cols-2">
              <TabsTrigger value="entrada">
                <Badge className="mr-2" variant="default">
                  Entrada
                </Badge>
                {checklist.entrada?.data_checklist && formatDate(checklist.entrada.data_checklist)}
              </TabsTrigger>
              <TabsTrigger value="saida">
                <Badge className="mr-2" variant="secondary">
                  Saída
                </Badge>
                {checklist.saida?.data_checklist && formatDate(checklist.saida.data_checklist)}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="entrada">{renderChecklistContent(checklist.entrada)}</TabsContent>

            <TabsContent value="saida">{renderChecklistContent(checklist.saida)}</TabsContent>
          </Tabs>
        ) : (
          renderChecklistContent(checklist)
        )}
      </DialogContent>
    </Dialog>
  );
}
