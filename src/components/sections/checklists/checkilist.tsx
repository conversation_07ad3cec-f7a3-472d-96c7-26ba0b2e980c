"use client";

import { useState, useMemo, useEffect } from "react";
import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { Checkbox } from "@/components/ui/checkbox";
import { useChecklist } from "@/context/checklist-context";
import { ColumnDef } from "@tanstack/react-table";
import { format, isAfter, isBefore, startOfDay, endOfDay } from "date-fns";
import { useRouter } from "next/navigation";
import { FilterIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { ChecklistDetailsDialog } from "./checklist-details";
import {
  ChecklistFilters,
  type ChecklistFilters as ChecklistFiltersType,
} from "./checklist-filters";
import { Session } from "@/lib/auth/types";
import { getServerSession } from "@/lib/auth/server-session";

// Função para agrupar checklists pela OS
const mergeChecklistsForSameOS = (checklists: any[]): any[] => {
  // Criar um mapa para agrupar os checklists por osId
  const checklistsByOS = new Map();

  checklists.forEach((checklist) => {
    const osId = checklist.osId;

    if (!checklistsByOS.has(osId)) {
      checklistsByOS.set(osId, {
        id: osId,
        osNumber:
          checklist.checklist_entrada?.osNumber ||
          checklist.checklist_saida?.osNumber,
        veiculo: checklist.veiculo,
        condutor: checklist.condutor,
        centro_de_custo:
          checklist.veiculo?.faturamentoVeiculo?.centro_custo?.descricao,
        odometro_atual: checklist.odometro_atual,
        status:
          checklist.checklist_entrada?.status ||
          checklist.checklist_saida?.status,
        entrada: null,
        saida: null,
        situacao_veiculo: checklist.situacao_veiculo,
        aparencia_veiculo: checklist.aparencia_veiculo,
        checklist_entrada: checklist.checklist_entrada,
      });
    }

    const mergedItem = checklistsByOS.get(osId);

    // Atribuir o checklist ao campo apropriado
    if (checklist.tipo_checklist === "Entrada") {
      mergedItem.entrada = checklist;
    } else if (checklist.tipo_checklist === "Saída") {
      mergedItem.saida = checklist;
    }
  });

  // Converter o mapa de volta para um array
  return Array.from(checklistsByOS.values());
};

export const checklistColumns: ColumnDef<any>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "centro_de_custo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Centro de Custo" />
    ),
    enableHiding: true,
  },
  {
    id: "veiculo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Veículo" />
    ),
    cell: ({ row }) => {
      const veiculo = row.original.veiculo;
      return (
        <span>
          {veiculo?.placa} | {veiculo?.marca?.descricao}{" "}
          {veiculo?.modelo?.descricao}
        </span>
      );
    },
    enableHiding: true,
  },
  {
    accessorKey: "odometro_atual",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Odômetro" />
    ),
    enableHiding: true,
  },
  {
    id: "osNumber",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="OS" />
    ),
    cell: ({ row }) => {
      const osNumber =
        row.original.checklist_entrada?.osNumber || row.original.osNumber;
      return <span>{osNumber}</span>;
    },
    enableHiding: true,
  },
  {
    id: "data_entrada",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Entrada" />
    ),
    cell: ({ row }) => {
      const data = row.original.entrada?.data_checklist;
      return data ? (
        <span className="whitespace-nowrap">
          {format(new Date(data), "dd/MM/yyyy HH:mm")}
        </span>
      ) : (
        <span>-</span>
      );
    },
    enableHiding: true,
  },
  {
    id: "data_saida",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Saida" />
    ),
    cell: ({ row }) => {
      const data = row.original.saida?.data_checklist;
      return data ? (
        <span className="whitespace-nowrap">
          {format(new Date(data), "dd/MM/yyyy HH:mm")}
        </span>
      ) : (
        <span>-</span>
      );
    },
    enableHiding: true,
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status da OS" />
    ),
    enableHiding: true,
  },
  {
    id: "total_itens",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Total de Itens" />
    ),
    cell: ({ row }) => {
      const entrada = row.original.entrada;
      const saida = row.original.saida;

      // Usar o checklist de entrada como padrão, ou o de saída se não houver entrada
      const checklist = entrada || saida;

      const situacaoKeys = Object.keys(
        checklist?.situacao_veiculo || {}
      ).filter((key) => key !== "id" && key !== "checklistId");
      const aparenciaKeys = Object.keys(
        checklist?.aparencia_veiculo || {}
      ).filter((key) => key !== "id" && key !== "checklistId");

      return situacaoKeys.length + aparenciaKeys.length;
    },
    enableHiding: true,
  },
];

export function Checklist() {
  const router = useRouter();
  const { checklists, setChecklists } = useChecklist();
  const [selectedChecklist, setSelectedChecklist] = useState<any>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [filters, setFilters] = useState<ChecklistFiltersType>({});
  const [showFilters, setShowFilters] = useState(false);
  const [session, setSessionData] = useState<Session | null>();

  useEffect(() => {
    async function fetchSessionData() {
      const sessionData = await getServerSession();
      setSessionData(sessionData);
    }
    fetchSessionData();
  }, []);

  const filteredChecklistsByRole = session?.roles.includes(
    "ORCAMENTISTA_OFICINA"
  )
    ? checklists.filter(
        ({ credenciadoId }) => credenciadoId === session.credenciadoId
      )
    : checklists;

  // Processar os dados para combinar checklists da mesma OS
  const mergedChecklists = useMemo(
    () => mergeChecklistsForSameOS(filteredChecklistsByRole || []),
    [filteredChecklistsByRole]
  );

  // Extrair opções de centro de custo para o filtro
  const centroCustoOptions = useMemo(() => {
    const options = new Set<string>();
    mergedChecklists.forEach((item) => {
      const centroCusto = item.centro_de_custo;
      if (centroCusto) options.add(centroCusto);
    });
    return Array.from(options);
  }, [mergedChecklists]);

  // Extrair opções de status para o filtro
  const statusOptions = useMemo(() => {
    const options = new Set<string>();
    mergedChecklists.forEach((item) => {
      const status = item.status;
      if (status) options.add(status);
    });
    return Array.from(options);
  }, [mergedChecklists]);

  // Filtrar os dados
  const filteredChecklists = useMemo(() => {
    return mergedChecklists.filter((item) => {
      // Filtrar por número da OS
      if (
        filters.osNumber &&
        !String(item.osNumber || "")
          .toLowerCase()
          .includes(filters.osNumber.toLowerCase())
      ) {
        return false;
      }

      // Filtrar por placa
      if (
        filters.placa &&
        item.veiculo?.placa &&
        !item.veiculo.placa.toLowerCase().includes(filters.placa.toLowerCase())
      ) {
        return false;
      }

      // Filtrar por centro de custo
      if (filters.centroCusto && item.centro_de_custo !== filters.centroCusto) {
        return false;
      }

      // Filtrar por status
      if (filters.status && item.status !== filters.status) {
        return false;
      }

      // Filtrar por data de entrada
      if (filters.startEntryDate || filters.endEntryDate) {
        if (!item.entrada?.data_checklist) return false;

        const entryDate = new Date(item.entrada.data_checklist);

        if (
          filters.startEntryDate &&
          isBefore(entryDate, startOfDay(filters.startEntryDate))
        ) {
          return false;
        }

        if (
          filters.endEntryDate &&
          isAfter(entryDate, endOfDay(filters.endEntryDate))
        ) {
          return false;
        }
      }

      // Filtrar por data de saída
      if (filters.startExitDate || filters.endExitDate) {
        if (!item.saida?.data_checklist) return false;

        const exitDate = new Date(item.saida.data_checklist);

        if (
          filters.startExitDate &&
          isBefore(exitDate, startOfDay(filters.startExitDate))
        ) {
          return false;
        }

        if (
          filters.endExitDate &&
          isAfter(exitDate, endOfDay(filters.endExitDate))
        ) {
          return false;
        }
      }

      return true;
    });
  }, [mergedChecklists, filters]);

  const handleRowClick = (row: any) => {
    // Verificar se é um objeto já com entrada e saída ou um checklist individual
    const checklistData = row.original || row;

    // Se tiver entrada e saída, criar um objeto combinado para exibição
    if (checklistData.entrada || checklistData.saida) {
      setSelectedChecklist({
        ...checklistData,
        // Garantir que o modal possa mostrar ambos os checklists
        tipo_checklist:
          checklistData.entrada && checklistData.saida
            ? "Entrada e Saída"
            : checklistData.entrada
            ? "Entrada"
            : "Saída",
      });
    } else {
      // Caso estejamos lidando com um checklist individual
      setSelectedChecklist(checklistData);
    }

    setIsDialogOpen(true);
  };

  // Limpar todos os filtros
  const handleClearFilters = () => {
    setFilters({});
  };

  // Contar quantos filtros estão ativos
  const activeFiltersCount = Object.values(filters).filter(Boolean).length;

  return (
    <>
      <div className="flex justify-end mb-4 p-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowFilters(!showFilters)}
          className={cn("flex items-center gap-2", showFilters && "bg-muted")}
        >
          <FilterIcon className="h-4 w-4" />
          Filtros
          {activeFiltersCount > 0 && (
            <Badge
              variant="secondary"
              className="ml-1 h-5 w-5 rounded-full p-0 flex items-center justify-center"
            >
              {activeFiltersCount}
            </Badge>
          )}
        </Button>
      </div>

      {showFilters && (
        <ChecklistFilters
          filters={filters}
          setFilters={setFilters}
          centroCustoOptions={centroCustoOptions}
          onClearFilters={handleClearFilters}
        />
      )}

      <DataTable
        data={filteredChecklists}
        onClick={() => router.push("/dashboard/checklists/novo-checklist")}
        exportTo={true}
        columns={checklistColumns}
        className="w-full"
        showToolbar={true}
        showSearch={true}
        handleRowClick={handleRowClick}
      />

      <ChecklistDetailsDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        checklist={selectedChecklist}
      />
    </>
  );
}
