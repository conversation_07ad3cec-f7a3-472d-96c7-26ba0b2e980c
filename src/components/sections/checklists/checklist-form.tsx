"use client";
import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import Cookies from "js-cookie"; // Importando a biblioteca js-cookie

import { defaultValues } from "@/components/forms/defaultValues";
import { GetChecklistConfig } from "@/components/forms/inputConfig";
import { PageForm } from "@/components/forms/page-form";
import { checklistSchema } from "@/components/forms/schemas";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { useChecklist } from "@/context/checklist-context";
import { useCondutor } from "@/context/condutor-context";
import { useCredenciado } from "@/context/credenciado-context";
import { useVeiculos } from "@/context/veiculos-context";
import { toast } from "sonner";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  Di<PERSON><PERSON>ontent,
  <PERSON><PERSON>D<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON><PERSON>rigger,
} from "@/components/ui/dialog";
import { useEffect, useState, useMemo } from "react";
import { Session } from "@/lib/auth/types";
import { getServerSession } from "@/lib/auth/server-session";
import { createChecklistAction } from "@/serverActions/checklistAction";
import { ConductorAuthModal } from "@/components/modal/condutor-auth-modal";
import { validateOrCreateCondutorPassword } from "@/serverActions/condutorAction";
import { useOS } from "@/context/os-context";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import ConfirmModal from "@/components/modal/confirm-modal-user";
import { useRouter } from "next/navigation";
import {
  updateOrcamentoStatus,
  updateOsStatus,
} from "@/serverActions/orcamentoAction";
import {
  getCredenciadoById,
  getCredenciadosByContratoId,
} from "@/serverActions/credenciadoAction";

export function ChecklistForm({
  veiculo,
  osId,
  checklistType = "",
}: {
  veiculo?: veiculo;
  osId?: string;
  checklistType?: string;
}) {
  const { setChecklists } = useChecklist();
  const { veiculos } = useVeiculos();
  const { condutores } = useCondutor();
  const { credenciados } = useCredenciado();
  const { ordensDeServico } = useOS();
  const [session, setSessionData] = useState<Session | null>();
  const [token, setToken] = useState<string | null>(null);
  const [uploadedUrl, setUploadedUrl] = useState<string | null>(null);
  const configVeiculos = veiculo ? [veiculo] : veiculos;
  const [condutorHasPassword, setCondutorHasPassword] =
    useState<boolean>(false);
  const [condutor, setCondutor] = useState<condutor>();
  const [showConductorFormModal, setShowConductorFormModal] = useState(false);
  const [formValues, setFormValues] = useState<z.infer<
    typeof checklistSchema
  > | null>(null);
  const [formResolve, setFormResolve] = useState<() => void>(() => {});
  const [formReject, setFormReject] = useState<(reason?: any) => void>(
    () => {}
  );
  const [useSimplifiedChecklist, setUseSimplifiedChecklist] = useState(false);
  const [selectedOsId, setSelectedOsId] = useState<string | undefined>(osId);
  const [confirmationModal, setConfirmationModal] = useState(false);
  const [selectedChecklistType, setSelectedChecklistType] = useState(
    checklistType || "Entrada"
  );
  const [credenciadosByContract, setCredenciadosByContract] = useState<
    credenciado[]
  >([]);
  const [selectedValues, setSelectedValues] = useState<z.infer<
    typeof checklistSchema
  > | null>(null);

  const router = useRouter();

  const hasChecklistEntrada = veiculo?.checklists?.some(
    (check) => check.tipo_checklist === "Entrada"
  );
  const hasChecklistSaida = veiculo?.checklists?.some(
    (check) => check.tipo_checklist === "Saída"
  );

  // Carrega o token do cookie ao montar o componente
  useEffect(() => {
    async function fetchSessionData() {
      const sessionData = await getServerSession();
      setSessionData(sessionData);
    }
    fetchSessionData();

    // Pegar token do localStorage quando o componente montar
    const storedToken = localStorage.getItem("token");
    if (storedToken) {
      setToken(storedToken);
    }
  }, []);

  // Função para fazer upload de arquivos permanece no client, por tratar de FormData
  const uploadFile = async (file: File): Promise<string | null> => {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Erro ao fazer upload do arquivo");
      }

      const data = await response.json();
      return data.file.path;
    } catch (error) {
      console.error("Erro no upload:", error);
      toast.error("Falha ao enviar arquivo");
      return null;
    }
  };

  // Adicione esta função no componente
  const handleAuthSuccess = async (matricula: string, senha: string) => {
    if (!formValues) {
      toast.error("Erro ao processar formulário");
      return;
    }

    try {
      const data = await createChecklistAction(formValues);
      if (!data.success) {
        toast("Erro inesperado", {
          description: "Checklist não foi retornado pelo servidor.",
        });
        formReject("Checklist não foi retornado pelo servidor");
        return;
      }
      setChecklists(data.data);
      toast("Checklist criado com sucesso!");
      formResolve();

      const selectedOs = veiculo?.os.find(
        ({ status }) => status === "execucao"
      );
      const confirmOrcamento = selectedOs?.orcamentos?.find(
        (orc: any) => orc.status === "execucao" || orc.status === "autorizada"
      );

      if (!hasChecklistSaida && selectedValues?.tipo !== "Saída") {
        toast.error("É obrigatório o checklist de Saída");
        window.location.href = "/dashboard/checklists/consultar-checklist";
        return;
      }

      if (
        !session?.contrato?.checklist_simplificado_pecas &&
        !hasChecklistEntrada
      ) {
        toast.error("É obrigatório o checklist de Entrada");
        window.location.href = "/dashboard/checklists/consultar-checklist";
        return;
      }

      if (
        session?.contrato?.checklist_simplificado_pecas &&
        (hasChecklistSaida || formValues.tipo === "Saída")
      ) {
        setConfirmationModal(true);
        return;
      }
    } catch (err) {
      console.error("Erro no submit:", err);
      toast("Falha na comunicação com o servidor.");
      formReject(err);
    }
  };

  async function onSubmit(values: z.infer<typeof checklistSchema>) {
    const selectedOs = credenciadoOrc.find((os) => os.id === values.osId);
    const selectedBudgetAuthorizateOrExecution = selectedOs?.orcamentos?.find(
      ({ status }) => status === "autorizada" || status === "execucao"
    );

    const osHasServices =
      selectedBudgetAuthorizateOrExecution?.processedServicos
        ? selectedBudgetAuthorizateOrExecution?.processedServicos.length > 0
        : false;

    if (osHasServices) {
      if (!uploadedUrl) {
        toast.error(
          "É obrigatório o envio de uma foto com o odômetro do veículo"
        );
        return;
      }
    }

    if (!values.veiculoId) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Veículo",
      });
      return;
    }

    if (!values.credenciadoId) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Credenciado",
      });
      return;
    }

    if (!values.condutorId) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Condutor",
      });
      return;
    }

    if (!values.odometro_atual) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Odômetro Atual",
      });
      return;
    }

    if (uploadedUrl) {
      values.arquivos = [uploadedUrl];
    }

    // Se estiver usando o formulário externo de seleção de OS, garantir que o valor é passado
    if (selectedOsId && !values.osId) {
      values.osId = selectedOsId;
    }

    setSelectedValues(values);

    // Create a promise that will resolve when authentication is successful

    return new Promise<void>((resolve, reject) => {
      // Set state that will open the modal

      if (osHasServices) {
        setShowConductorFormModal(true);
        setCondutor(condutores.find((c) => c.id === values.condutorId));
        setCondutorHasPassword(
          condutores.find((c) => c.id === values.condutorId)?.password !== null
        );
        setFormValues(values);
        setFormResolve(() => resolve);
        setFormReject(() => reject);
      } else if (
        values.tipo === "Saída" &&
        !session?.contrato?.checklist_simplificado_pecas
      ) {
        setShowConductorFormModal(true);
        setCondutor(condutores.find((c) => c.id === values.condutorId));
        setCondutorHasPassword(
          condutores.find((c) => c.id === values.condutorId)?.password !== null
        );
        setFormValues(values);
        setFormResolve(() => resolve);
        setFormReject(() => reject);
      } else {
        (async () => {
          try {
            const data = await createChecklistAction(values);
            if (!data.success) {
              toast("Erro inesperado", {
                description: "Checklist não foi retornado pelo servidor.",
              });
              reject("Checklist não foi retornado pelo servidor");
              return;
            }
            setChecklists(data.data);
            toast("Checklist criado com sucesso!");
            resolve();

            if (values.tipo === "Entrada") {
              window.location.href = `/dashboard/orcamento/consultar-orcamento?osId=${values.osId}`;
              return;
            }
            if (!hasChecklistSaida && values.tipo !== "Saída") {
              toast.error("É obrigatório o checklist de Saída");
              window.location.href =
                "/dashboard/checklists/consultar-checklist";
              return;
            }
            if (
              !session?.contrato?.checklist_simplificado_pecas &&
              !hasChecklistEntrada
            ) {
              toast.error("É obrigatório o checklist de Entrada");
              window.location.href =
                "/dashboard/checklists/consultar-checklist";
              return;
            }
            const selectedOs =
              veiculo?.os.find(({ status }) => status === "execucao") ?? null;
              const confirmOrcamento = selectedOs?.orcamentos?.find(
                (orc: any) => orc.status === "execucao" || orc.status === "autorizada"
              );

            if (
              session?.contrato?.checklist_simplificado_pecas &&
              (hasChecklistSaida || values.tipo === "Saída")
            ) {
              setConfirmationModal(true);
              return;
            }

            await updateOrcamentoStatus(
              confirmOrcamento?.id ?? "",
              values.tipo === "Saída" ? "finalizada" : "execucao"
            );
            await updateOsStatus(
              selectedOs?.id ?? "",
              values.tipo === "Saída" ? "finalizada" : "execucao"
            );
          } catch (err) {
            console.error("Erro no submit:", err);
            toast("Falha na comunicação com o servidor.");
            reject(err);
          }
        })();
      }

      // Store these values to be used after authentication
      setFormValues(values);
      setFormResolve(() => resolve);
      setFormReject(() => reject);
    });
  }

  useEffect(() => {
    async function fetchCredenciadosByContract() {
      if (session?.contratoId) {
        try {
          const response = await getCredenciadosByContratoId(
            session.contratoId
          );
          if (
            response.success &&
            response.data &&
            Array.isArray(response.data)
          ) {
            setCredenciadosByContract(response.data);
          }
        } catch (error) {
          console.error("Erro ao buscar credenciados pelo contrato:", error);
        }
      }
    }

    fetchCredenciadosByContract();
  }, [session?.contratoId]);

  // Then modify your existing code:
  const credenciadosVinculados =
    credenciadosByContract.length > 0
      ? credenciadosByContract.map((credenciado) => ({
          ...credenciado,
          nome:
            credenciado.informacoes?.[0]?.razao_social ??
            "Credenciado sem nome",
        }))
      : credenciados.map((credenciado) => ({
          ...credenciado,
          nome:
            credenciado.informacoes?.[0]?.razao_social ??
            "Credenciado sem nome",
        }));

  const [credenciadoData, setCredenciadoData] = useState<credenciado[]>([]);

  useEffect(() => {
    async function fetchCredenciadoData() {
      if (session?.credenciadoId) {
        // Busca o credenciado específico da API
        try {
          const response = await getCredenciadoById(session.credenciadoId);
          if (response.success && response.data) {
            setCredenciadoData([response.data.credenciado]);
          }
        } catch (error) {
          console.error("Erro ao buscar credenciado:", error);
        }
      } else {
        // Usa os credenciados vinculados ao contrato
        setCredenciadoData(credenciadosVinculados);
      }
    }

    fetchCredenciadoData();
  }, [session?.credenciadoId]);
  const credenciadoOrc = session?.credenciadoId
    ? ordensDeServico.filter((os) => {
        return !!os.orcamentos?.find(
          (orcamento) =>
            orcamento.credenciadoId === session.credenciadoId &&
            (orcamento.status === "autorizada" ||
              orcamento.status === "execucao")
        );
      })
    : [];

  let osData: OS[] | undefined;
  if (osId) {
    osData = credenciadoOrc.filter((os) => os.id === osId);
  } else {
    osData = credenciadoOrc;
  }

  // Função para verificar se deve usar o checklist simplificado
  const checkIfSimplified = (currentOsId: string) => {
    const os = osData?.find((os) => os.id === currentOsId);
    // Primeiro verifica se a flag está habilitada no contrato
    const isSimplifiedEnabled =
      session?.contrato?.checklist_simplificado_pecas === true &&
      os?.orcamentos?.some((orcamento) => orcamento.servico?.length === 0);

    if (!isSimplifiedEnabled) {
      console.log("Flag não habilitada no contrato");
      setUseSimplifiedChecklist(false);
      return;
    }

    // Se não tiver osId, não usa o simplificado
    if (!currentOsId) {
      console.log("Sem osId selecionado, usando formulário completo");
      setUseSimplifiedChecklist(false);
      return;
    }

    // Busca a OS pelo ID
    const selectedOs = credenciadoOrc.find((os) => os.id === currentOsId);

    if (!selectedOs) {
      console.log("OS não encontrada:", currentOsId);
      setUseSimplifiedChecklist(false);
      return;
    }

    // Verifica se tem orçamentos
    if (!selectedOs.orcamentos || selectedOs.orcamentos.length === 0) {
      console.log("OS sem orçamentos");
      setUseSimplifiedChecklist(false);
      return;
    }

    // Verifica cada orçamento - CORREÇÃO AQUI
    const onlyPartsNoService = selectedOs.orcamentos.every((orcamento) => {
      console.log("Verificando orçamento:", orcamento.id);

      // CORRIGIDO: verificar pecas (plural) e peca (singular)
      const hasParts = Boolean(
        (orcamento.pecas && orcamento.pecas.length > 0) ||
          (orcamento.pecas && orcamento.pecas.length > 0)
      );

      // CORRIGIDO: verificar servico (singular) e servicos (plural)
      const hasNoServices = Boolean(
        (!orcamento.servico || orcamento.servico.length === 0) &&
          (!orcamento.servico || orcamento.servico.length === 0)
      );

      console.log(
        `Orçamento ${orcamento.id}: temPeças=${hasParts}, nãoTemServiços=${hasNoServices}`
      );

      return hasParts && hasNoServices;
    });

    console.log("Resultado final: usar simplificado?", onlyPartsNoService);
    setUseSimplifiedChecklist(onlyPartsNoService);
  };

  // Quando a OS é selecionada no select independente
  const handleOsSelect = (newOsId: string) => {
    console.log("OS selecionada no select:", newOsId);
    setSelectedOsId(newOsId);
    checkIfSimplified(newOsId);
  };

  useEffect(() => {
    // Inicializar com o osId fornecido como prop
    if (osId) {
      setSelectedOsId(osId);
      checkIfSimplified(osId);
    }

    if (checklistType) {
      setSelectedChecklistType(checklistType);
    }
  }, [osId, checklistType]);

  // Monitorar mudanças na sessão e na lista de OSs
  useEffect(() => {
    if (selectedOsId) {
      checkIfSimplified(selectedOsId);
    }
  }, [session, credenciadoOrc]);

  // Before the return statement, create a default value for the form
  const formDefaultValues = useMemo(() => {
    return {
      ...defaultValues.checklistSchema,
      nivel_do_combustivel: "3/4",
      osId: selectedOsId,
      tipo: selectedChecklistType,
    };
  }, [selectedOsId, selectedChecklistType]);

  // Criar configuração do formulário com base no modo simplificado
  const formConfig = useMemo(() => {
    // Validar se os dados necessários estão disponíveis
    if (!configVeiculos || !Array.isArray(configVeiculos) || configVeiculos.length === 0) {
      console.warn('configVeiculos não está disponível ou está vazio');
      return {};
    }

    if (!condutores || !Array.isArray(condutores)) {
      console.warn('condutores não está disponível');
      return {};
    }

    if (!credenciadoData || !Array.isArray(credenciadoData)) {
      console.warn('credenciadoData não está disponível');
      return {};
    }

    if (!credenciadoOrc || !Array.isArray(credenciadoOrc)) {
      console.warn('credenciadoOrc não está disponível');
      return {};
    }

    const config = GetChecklistConfig(
      [configVeiculos, condutores, credenciadoData, credenciadoOrc],
      ["modelo.descricao", "nome", "nome", "osNumber"]
    );

    const os = osData?.find((os) => os.id === selectedOsId);
    // Primeiro verifica se a flag está habilitada no contrato
    const selectedOrcamentoByCredenciado = os?.orcamentos?.find((orcamento) => {
      return (
        orcamento.credenciadoId === session?.credenciadoId &&
        (orcamento.status === "autorizada" || orcamento.status === "execucao")
      );
    });

    const isSimplifiedEnabled =
      session?.contrato?.checklist_simplificado_pecas &&
      selectedOrcamentoByCredenciado?.processedServicos?.length === 0;

    // Se estiver usando o formulário simplificado, remove situacao_do_veiculo
    if (isSimplifiedEnabled) {
      // Extrair todos os campos exceto situacao_do_veiculo
      const { situacao_do_veiculo, ...simplifiedConfig } = config;

      // Ocultar osId se estamos usando o select externo
      return {
        ...simplifiedConfig,
        nivel_do_combustivel: {
          ...simplifiedConfig.nivel_do_combustivel,
          className: "hidden",
          fieldClassName: "hidden",
          hidden: true,
        },
        osId: {
          ...simplifiedConfig.osId,
          hidden: true,
          className: "hidden",
          fieldClassName: "hidden",
        },
      };
    }

    // Esconder apenas o campo osId no formulário completo
    return {
      ...config,
      osId: {
        ...config.osId,
        hidden: true,
        className: "hidden",
        fieldClassName: "hidden",
      },
    };
  }, [
    configVeiculos,
    condutores,
    credenciadoData,
    credenciadoOrc,
    useSimplifiedChecklist,
    selectedOsId,
    session?.credenciadoId,
    session?.contrato?.checklist_simplificado_pecas,
  ]);

  return (
    <>
      <div className="p-2 sm:p-4">
        {/* Select independente para escolher a OS */}

        <PageForm
          onSubmit={onSubmit}
          schema={checklistSchema}
          defaultValues={formDefaultValues as any}
        >
          <>
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" className="w-full sm:w-auto mb-4">
                  Abrir Instruções
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="text-lg sm:text-xl">
                    Instruções Operacionais
                  </DialogTitle>
                  <DialogDescription className="text-sm sm:text-base">
                    <div className="space-y-4">
                      <p>
                        1- Para veículos entregues fora do expediente normal de
                        funcionamento ou sem a presença do proprietário, para o
                        estado geral do veículo e acessórios contidos na
                        chegada, serão consideradas as informações descritas
                        neste documento.
                      </p>
                      <p>
                        2- O cliente, através deste documento, autoriza se
                        necessário, a saída do veículo para a execução de testes
                        fora das instalações da oficina, ficando ciente que em
                        caso de sinistro, a responsabilidade da
                        concessionária/fornecedor fica limitada a reparação dos
                        danos causados ao mesmo.
                      </p>
                      <p>
                        3- Os veículos só entram em serviço, após aprovação do
                        Orçamento (valores de mão de obra e peças) pelo
                        responsável.
                      </p>
                      <p>
                        4- Retirada de veículo por terceiro somente com
                        autorização formal do responsável. Declaro ter deixado o
                        veículo acima descrito aos cuidados desta oficina,
                        conforme condições gerais observadas, para execução de
                        orçamento ou reparos que se fizerem necessários.
                      </p>
                      <p>
                        5- <strong>Atenção</strong> → Após a conclusão dos
                        serviços, no ato de entrega do veículo é obrigatório o
                        preenchimento do checklist de saída dentro do sistema
                        Gestão de Frotas.
                      </p>
                      <p>
                        <strong>
                          Importante, o não preenchimento do checklist poderá
                          implicar no bloqueio do pagamento dos valores
                          aprovados na ordem de serviço.
                        </strong>
                      </p>
                    </div>
                  </DialogDescription>
                </DialogHeader>
              </DialogContent>
            </Dialog>
            <div className="px-4">
              <Label htmlFor="os-select" className="text-sm font-medium">
                Selecione a Ordem de Serviço
              </Label>
              <Select value={selectedOsId} onValueChange={handleOsSelect}>
                <SelectTrigger id="os-select" className="w-full">
                  <SelectValue placeholder="Selecione uma OS" />
                </SelectTrigger>
                <SelectContent>
                  {credenciadoOrc.map((os) => (
                    <SelectItem key={os.id} value={os.id}>
                      {os.osNumber || `OS #${os.id}`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500 mt-1">
                Selecione uma ordem de serviço para carregar os dados
              </p>
            </div>
            {/* Formulário único que adapta conforme o modo simplificado */}
            <GenericFormsInput
              fieldConfig={formConfig}
              className="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-6 gap-4 p-2 sm:p-4"
            />
          </>
          <input
            type="file"
            multiple
            onChange={async (e) => {
              const files = e.target.files;

              if (files) {
                const file = files[0];
                const url: string | null = await uploadFile(file);

                if (url) {
                  setUploadedUrl(url);
                } else {
                  toast.error("Não foi possível fazer upload do arquivo.");
                }
              }
            }}
            className="mt-4"
          />
          <p>Inserir imagem do odômetro do veículo (obrigatório)</p>
        </PageForm>
      </div>

      <ConductorAuthModal
        isOpen={showConductorFormModal}
        onClose={() => {
          setShowConductorFormModal(false);
          formReject("Autenticação cancelada");
        }}
        onSuccess={handleAuthSuccess}
        hasPassword={condutorHasPassword}
        osCondutor={condutor}
      />
      <ConfirmModal
        title="Checklist realizado com sucesso"
        message="Deseja ir para a página de anexar Nota Fiscal?"
        onConfirm={async () => {
          const selectedOs =
            veiculo?.os.find(({ status }) => status === "execucao") ?? null;
          const confirmOrcamento = selectedOs?.orcamentos?.find(
            (orc: any) =>
              orc.status ===
              (selectedValues?.tipo === "Saída" ? "finalizada" : "execucao")
          );

          await updateOrcamentoStatus(
            confirmOrcamento?.id ?? "",
            selectedValues?.tipo === "Saída" ? "finalizada" : "execucao"
          );
          await updateOsStatus(
            selectedOs?.id ?? "",
            selectedValues?.tipo === "Saída" ? "finalizada" : "execucao"
          );
          return window.location.replace(`/dashboard/financeiros/nota-fiscal`);
        }}
        onCancel={() => setConfirmationModal(false)}
        open={confirmationModal}
      />
    </>
  );
}
