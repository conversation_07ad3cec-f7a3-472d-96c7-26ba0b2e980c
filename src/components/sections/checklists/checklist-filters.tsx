import React from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

export type ChecklistFilters = {
  osNumber?: string;
  startEntryDate?: Date;
  endEntryDate?: Date;
  startExitDate?: Date;
  endExitDate?: Date;
  placa?: string;
  centroCusto?: string;
  status?: string;
};

interface ChecklistFiltersProps {
  filters: ChecklistFilters;
  setFilters: React.Dispatch<React.SetStateAction<ChecklistFilters>>;
  centroCustoOptions: string[];
  onClearFilters: () => void;
}

export function ChecklistFilters({
  filters,
  setFilters,
  centroCustoOptions,
  onClearFilters,
}: ChecklistFiltersProps) {
  // Conta quantos filtros estão ativos
  const activeFiltersCount = Object.values(filters).filter(Boolean).length;

  return (
    <div className="mb-4 space-y-2 p-4">
      <div className="flex justify-between items-center">
        <h3 className="text-sm font-medium">Filtros</h3>
        {activeFiltersCount > 0 && (
          <Button variant="ghost" size="sm" onClick={onClearFilters} className="h-8 px-2 lg:px-3">
            Limpar {activeFiltersCount} {activeFiltersCount === 1 ? 'filtro' : 'filtros'}
          </Button>
        )}
      </div>

      <div className="flex flex-wrap gap-2">
        {/* Filtro de OS */}
        <div className="flex items-center gap-1.5">
          <Input
            placeholder="Número da OS"
            value={filters.osNumber || ""}
            onChange={(e) => setFilters({ ...filters, osNumber: e.target.value })}
            className="h-8 w-[120px]"
          />
          {filters.osNumber && (
            <Badge variant="outline" className="px-2 font-normal">
              OS: {filters.osNumber}
            </Badge>
          )}
        </div>

        {/* Filtro de Data de Entrada */}
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className={cn(
                "h-8 border-dashed",
                filters.startEntryDate && "text-primary"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              Data de Entrada
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <div className="p-3 space-y-3">
              <h4 className="font-medium text-sm">Período de Entrada</h4>
              <div className="grid gap-2">
                <div className="grid gap-1">
                  <div className="flex items-center gap-2">
                    <Calendar
                      mode="single"
                      selected={filters.startEntryDate}
                      onSelect={(date) => setFilters({ ...filters, startEntryDate: date })}
                      disabled={(date) => filters.endEntryDate ? date > filters.endEntryDate : false}
                      initialFocus
                    />
                  </div>
                  <div className="flex items-center gap-2 pt-2">
                    <Calendar
                      mode="single"
                      selected={filters.endEntryDate}
                      onSelect={(date) => setFilters({ ...filters, endEntryDate: date })}
                      disabled={(date) => filters.startEntryDate ? date < filters.startEntryDate : false}
                      initialFocus
                    />
                  </div>
                </div>
              </div>
            </div>
          </PopoverContent>
        </Popover>
        {(filters.startEntryDate || filters.endEntryDate) && (
          <Badge variant="outline" className="px-2 font-normal">
            Entrada: {filters.startEntryDate ? format(filters.startEntryDate, "dd/MM/yyyy") : "-"} 
            {" a "} 
            {filters.endEntryDate ? format(filters.endEntryDate, "dd/MM/yyyy") : "-"}
          </Badge>
        )}

        {/* Filtro de Data de Saída */}
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className={cn(
                "h-8 border-dashed",
                filters.startExitDate && "text-primary"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              Data de Saída
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <div className="p-3 space-y-3">
              <h4 className="font-medium text-sm">Período de Saída</h4>
              <div className="grid gap-2">
                <div className="grid gap-1">
                  <div className="flex items-center gap-2">
                    <Calendar
                      mode="single"
                      selected={filters.startExitDate}
                      onSelect={(date) => setFilters({ ...filters, startExitDate: date })}
                      disabled={(date) => filters.endExitDate ? date > filters.endExitDate : false}
                      initialFocus
                    />
                  </div>
                  <div className="flex items-center gap-2 pt-2">
                    <Calendar
                      mode="single"
                      selected={filters.endExitDate}
                      onSelect={(date) => setFilters({ ...filters, endExitDate: date })}
                      disabled={(date) => filters.startExitDate ? date < filters.startExitDate : false}
                      initialFocus
                    />
                  </div>
                </div>
              </div>
            </div>
          </PopoverContent>
        </Popover>
        {(filters.startExitDate || filters.endExitDate) && (
          <Badge variant="outline" className="px-2 font-normal">
            Saída: {filters.startExitDate ? format(filters.startExitDate, "dd/MM/yyyy") : "-"} 
            {" a "} 
            {filters.endExitDate ? format(filters.endExitDate, "dd/MM/yyyy") : "-"}
          </Badge>
        )}

        {/* Filtro de Placa */}
        <div className="flex items-center gap-1.5">
          <Input
            placeholder="Placa"
            value={filters.placa || ""}
            onChange={(e) => setFilters({ ...filters, placa: e.target.value })}
            className="h-8 w-[120px]"
          />
          {filters.placa && (
            <Badge variant="outline" className="px-2 font-normal">
              Placa: {filters.placa}
            </Badge>
          )}
        </div>

        {/* Filtro de Centro de Custo */}
        <Select
          value={filters.centroCusto || ""}
          onValueChange={(value) => setFilters({ ...filters, centroCusto: value || undefined })}
        >
          <SelectTrigger className="h-8 w-[180px]">
            <SelectValue placeholder="Centro de Custo" />
          </SelectTrigger>
          <SelectContent>
            {centroCustoOptions.map((centro) => (
              <SelectItem key={centro} value={centro}>
                {centro}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {filters.centroCusto && (
          <Badge variant="outline" className="px-2 font-normal">
            Centro: {filters.centroCusto}
          </Badge>
        )}

        {/* Filtro de Status */}
        <Select
          value={filters.status || ""}
          onValueChange={(value) => setFilters({ ...filters, status: value || undefined })}
        >
          <SelectTrigger className="h-8 w-[180px]">
            <SelectValue placeholder="Status da OS" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="execucao">Em Execução</SelectItem>
            <SelectItem value="autorizada">Autorizada</SelectItem>
            <SelectItem value="finalizada">Finalizada</SelectItem>
            <SelectItem value="cancelada">Cancelada</SelectItem>
          </SelectContent>
        </Select>
        {filters.status && (
          <Badge variant="outline" className="px-2 font-normal">
            Status: {filters.status}
          </Badge>
        )}
      </div>
    </div>
  );
}