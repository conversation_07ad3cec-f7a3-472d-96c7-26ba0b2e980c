"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';

interface ComparacaoProps {
  os: any;
  credenciadoId: string;
}

export function ComparacaoOrcamentos({ os, credenciadoId }: ComparacaoProps) {
  if (!os || !credenciadoId) return null;
  
  // Encontrar meu orçamento
  const meuOrcamento = os.orcamentos?.find(
    (orc: any) => orc.credenciadoId === credenciadoId
  );
  
  // Encontrar orçamento vencedor
  const orcamentoVencedor = os.orcamentos?.find(
    (orc: any) => 
      orc.credenciadoId !== credenciadoId && 
      (orc.status === "autorizada" || 
       orc.status === "execucao" || 
       orc.status === "finalizada" || 
       orc.status === "faturada")
  );
  
  if (!meuOrcamento || !orcamentoVencedor) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">
          Não foi possível encontrar os dados para comparação.
        </p>
      </div>
    );
  }
  
  const valorTotalMeu = meuOrcamento.valor_total || 0;
  const valorTotalVencedor = orcamentoVencedor.valor_total || 0;
  
  const diferenca = valorTotalMeu - valorTotalVencedor;
  const percentual = valorTotalVencedor > 0 ? (diferenca / valorTotalVencedor) * 100 : 0;
  
  // Calcular valores de peças e serviços
  const valorPecasMeu = meuOrcamento.processedPecas?.reduce(
    (acc: number, peca: any) => acc + (peca.valorNegociado || 0),
    0
  ) || 0;
  
  const valorServicosMeu = meuOrcamento.processedServicos?.reduce(
    (acc: number, servico: any) => acc + (servico.valor || 0),
    0
  ) || 0;
  
  const valorPecasVencedor = orcamentoVencedor.processedPecas?.reduce(
    (acc: number, peca: any) => acc + (peca.valorNegociado || 0),
    0
  ) || 0;
  
  const valorServicosVencedor = orcamentoVencedor.processedServicos?.reduce(
    (acc: number, servico: any) => acc + (servico.valor || 0),
    0
  ) || 0;
  
  // Comparar itens
  const itensComparados: any[] = [];
  
  // Comparar peças
  const todasPecas = new Set();
  
  meuOrcamento.processedPecas?.forEach((peca: any) => {
    todasPecas.add(peca.codigo || peca.descricao);
  });
  
  orcamentoVencedor.processedPecas?.forEach((peca: any) => {
    todasPecas.add(peca.codigo || peca.descricao);
  });
  
  todasPecas.forEach((codigo: unknown) => {
    const pecaMinha = meuOrcamento.processedPecas?.find(
      (p: any) => (p.codigo || p.descricao) === codigo
    );
    
    const pecaVencedor = orcamentoVencedor.processedPecas?.find(
      (p: any) => (p.codigo || p.descricao) === codigo
    );
    
    if (pecaMinha || pecaVencedor) {
      const valorMeu = pecaMinha?.valorNegociado || 0;
      const valorVencedor = pecaVencedor?.valorNegociado || 0;
      
      if (valorMeu > 0 || valorVencedor > 0) {
        const diferenca = valorMeu - valorVencedor;
        const percentualItem = valorVencedor > 0 ? (diferenca / valorVencedor) * 100 : 0;
        
        itensComparados.push({
          tipo: "Peça",
          descricao: pecaMinha?.descricao || pecaVencedor?.descricao,
          valorMeu,
          valorVencedor,
          diferenca,
          percentual: percentualItem
        });
      }
    }
  });
  
  // Comparar serviços
  const todosServicos = new Set();
  
  meuOrcamento.processedServicos?.forEach((servico: any) => {
    todosServicos.add(servico.descricao);
  });
  
  orcamentoVencedor.processedServicos?.forEach((servico: any) => {
    todosServicos.add(servico.descricao);
  });
  
  todosServicos.forEach((descricao: unknown) => {
    const servicoMeu = meuOrcamento.processedServicos?.find(
      (s: any) => s.descricao === descricao
    );
    
    const servicoVencedor = orcamentoVencedor.processedServicos?.find(
      (s: any) => s.descricao === descricao
    );
    
    if (servicoMeu || servicoVencedor) {
      const valorMeu = servicoMeu?.valor || 0;
      const valorVencedor = servicoVencedor?.valor || 0;
      
      if (valorMeu > 0 || valorVencedor > 0) {
        const diferenca = valorMeu - valorVencedor;
        const percentualItem = valorVencedor > 0 ? (diferenca / valorVencedor) * 100 : 0;
        
        itensComparados.push({
          tipo: "Serviço",
          descricao: descricao,
          valorMeu,
          valorVencedor,
          diferenca,
          percentual: percentualItem
        });
      }
    }
  });
  
  // Identificar pontos de melhoria
  const pontosMelhoria = [];
  
  // Se o orçamento total é mais caro
  if (diferenca > 0 && percentual > 5) {
    pontosMelhoria.push({
      categoria: "Preço Total",
      prioridade: percentual > 15 ? "alta" : "média",
      descricao: "Seu orçamento está acima do valor de mercado",
      sugestao: "Revise sua política de preços para ser mais competitivo"
    });
  }
  
  // Identificar itens específicos com diferença significativa
  itensComparados
    .filter(item => item.percentual > 10 && item.diferenca > 0)
    .sort((a, b) => b.percentual - a.percentual)
    .slice(0, 3)
    .forEach(item => {
      pontosMelhoria.push({
        categoria: item.tipo,
        prioridade: item.percentual > 20 ? "alta" : "média",
        descricao: `Preço elevado em: ${item.descricao}`,
        sugestao: `Considere ajustar o valor deste item para aumentar competitividade`
      });
    });
  
  // Se há mais serviços que o concorrente
  if (valorServicosMeu > valorServicosVencedor * 1.2) {
    pontosMelhoria.push({
      categoria: "Serviços",
      prioridade: "média",
      descricao: "Valor de serviços significativamente maior",
      sugestao: "Revise seus custos de mão de obra ou tempo estimado"
    });
  }
  
  // Se há mais peças que o concorrente
  if (valorPecasMeu > valorPecasVencedor * 1.2) {
    pontosMelhoria.push({
      categoria: "Peças",
      prioridade: "média",
      descricao: "Valor de peças significativamente maior",
      sugestao: "Considere negociar melhores preços com seus fornecedores"
    });
  }
  
  return (
    <div className="space-y-6 mt-4">
      <h3 className="text-lg font-semibold">Comparação de Orçamentos</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-blue-50 p-4 rounded-md">
          <h4 className="font-medium mb-2">Seu Orçamento</h4>
          <p className="text-xl font-bold">
            {valorTotalMeu.toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}
          </p>
          <div className="text-sm mt-2">
            <p>Peças: {valorPecasMeu.toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}</p>
            <p>Serviços: {valorServicosMeu.toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}</p>
          </div>
        </div>
        
        <div className="bg-green-50 p-4 rounded-md">
          <h4 className="font-medium mb-2">Orçamento Vencedor</h4>
          <p className="text-xl font-bold">
            {valorTotalVencedor.toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}
          </p>
          <div className="text-sm mt-2">
            <p>Peças: {valorPecasVencedor.toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}</p>
            <p>Serviços: {valorServicosVencedor.toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}</p>
          </div>
        </div>
        
        <div className="bg-yellow-50 p-4 rounded-md">
          <h4 className="font-medium mb-2">Diferença</h4>
          <p className="text-xl font-bold">
            {diferenca.toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}
          </p>
          <div className="text-sm mt-2">
            <p>Percentual: {percentual.toFixed(2)}%</p>
          </div>
        </div>
      </div>
    </div>
  );
}
