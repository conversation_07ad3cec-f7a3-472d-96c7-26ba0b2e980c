"use client";
import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AlertCircle, ChevronRight, SmartphoneNfc } from "lucide-react";
import { toast } from "sonner";
import ScanningAnimation from "./scanning-animate";
import VehicleDetails from "./vehicle-details";
import { getVehicleByRfId } from "@/serverActions/vehicleAction";
import { Session } from "@/lib/auth/types";
import { getServerSession } from "@/lib/auth/server-session";
import {
  getOrcamentoByVehicleId,
  updateOrcamentoStatus,
  updateOsStatus,
} from "@/serverActions/orcamentoAction";
import { validateOrCreateCondutorPassword } from "@/serverActions/condutorAction";
import { ConductorAuthModal } from "@/components/modal/condutor-auth-modal";

const RFIDReader: React.FC = () => {
  const [tagData, setTagData] = useState<string>("");
  const [vehicleData, setVehicleData] = useState<veiculo>();
  const [osData, setOsData] = useState<OS[]>();
  const [error, setError] = useState<string>("");
  const [scanning, setScanning] = useState<boolean>(false);
  const [nfcSupported, setNfcSupported] = useState<boolean | null>(null);
  const [sessionData, setSessionData] = useState<Session>();
  const [showApprovedModal, setShowApprovedModal] = useState<boolean>(false);
  const [showConductorFormModal, setShowConductorFormModal] = useState<boolean>(false);
  const [passwordAttempts, setPasswordAttempts] = useState<number>(0);
  const [authorizedOs, setAuthorizedOs] = useState<OS>();
  const [condutorHasPassword, setCondutorHasPassword] = useState<boolean>(false);
  const [condutor, setCondutor] = useState<condutor>();

  useEffect(() => {
    async function fetchSessionData() {
      const session = await getServerSession();
      setSessionData(session);
    }
    fetchSessionData();
  }, []);

  useEffect(() => {
    const checkNFCSupport = () => {
      const isSupported = "NDEFReader" in window;
      setNfcSupported(isSupported);
      if (!isSupported) {
        setError("NFC não suportado neste dispositivo.");
      }
    };

    checkNFCSupport();
  }, []);

  const handleRFIDScan = async () => {
    try {
      setError("");
      setTagData("");
      setVehicleData(undefined);
      setScanning(true);

      if ("NDEFReader" in window) {
        const ndef = new (window as any).NDEFReader();
        toast("Leitura iniciada - aproxime a tag NFC do dispositivo.", {
          duration: 3000,
        });

        await ndef.scan();

        ndef.onreading = (event: any) => {
          setScanning(false);
          const { message } = event;
          const record = message.records[0];
          if (record) {
            const textDecoder = new TextDecoder(record.encoding || "utf-8");
            const text = textDecoder.decode(record.data);
            setTagData(text);
            toast.success(`Tag lida com sucesso: ID ${text.substring(0, 10)}...`, {
              duration: 3000,
            });
            fetchVehicleData(text);
          }
        };

        ndef.onerror = (event: any) => {
          setScanning(false);
          setError("Erro durante a leitura da tag NFC.");
          toast.error("Erro na leitura - não foi possível ler a tag NFC.", {
            duration: 3000,
          });
        };
      } else {
        setScanning(false);
        setError("NFC não suportado neste dispositivo.");
        toast.error("Dispositivo incompatível - seu dispositivo não suporta leitura NFC.", {
          duration: 3000,
        });
      }
    } catch (e) {
      console.error(e);
      setScanning(false);
      setError("Erro ao iniciar leitura NFC. Verifique se a permissão NFC está ativada.");
      toast.error(
        "Falha na inicialização - verifique se a permissão NFC está ativada nas configurações.",
        { duration: 5000 }
      );
    }
  };

  const fetchVehicleData = async (tag: string) => {
    try {
      const response = await getVehicleByRfId(tag);
      if (response.success) {
        setVehicleData(response.data.veiculo);
        const osResponse = await getOrcamentoByVehicleId(response.data.veiculo.id);
        console.log("OS Response:", osResponse);
        if (osResponse.success) {
          const os = osResponse.data.ordens as OS[];
          setOsData(os);
          let currentSession = sessionData;
          if (!currentSession) {
            currentSession = await getServerSession();
            setSessionData(currentSession);
          }
          if (
            currentSession &&
            os?.some(
              (os) =>
                os.status === "autorizada" && os.credenciadoId === currentSession.credenciadoId
            )
          ) {
            setCondutor(os.find((o) => o.status === "autorizada")?.condutor);
            setCondutorHasPassword(
              os.find((o) => o.status === "autorizada")?.condutor?.password !== null ? true : false
            );
            setAuthorizedOs(os.find((o) => o.status === "autorizada"));
            setShowApprovedModal(true);
          }
        } else {
          setError("Erro ao buscar dados da OS.");
          toast.error("Erro ao buscar dados da OS.", { duration: 3000 });
        }
      } else {
        setError("Erro ao buscar dados do veículo.");
        toast.error(
          "Veículo não encontrado - não foi possível encontrar informações para esta tag.",
          { duration: 3000 }
        );
      }
    } catch (e) {
      console.error(e);
      setError("Erro de conexão ao buscar dados do veículo.");
      toast.error("Erro de conexão - não foi possível conectar ao servidor para buscar dados.", {
        duration: 3000,
      });
    }
  };

  const simulateReading = async () => {
    const simulatedTag = "1234124";
    setTagData(simulatedTag);
    await fetchVehicleData(simulatedTag);
    toast.success(`Tag lida com sucesso: ID ${simulatedTag}...`, { duration: 3000 });
  };

  const handleStop = () => {
    setScanning(false);
    toast("Leitura interrompida - a leitura de NFC foi cancelada.", { duration: 3000 });
  };
  const handleConductorSuccess = async (matricula: string, senha: string) => {
    try {
      if (!authorizedOs || !authorizedOs.id) {
        console.error("OS autorizada não encontrada.");
        throw new Error("OS autorizada não encontrada.");
      }
      const validateCondutor = await validateOrCreateCondutorPassword(matricula, senha);
      if (!validateCondutor.success) {
        throw new Error("Credenciais inválidas.");
      }
      const approvedOrcamento = authorizedOs.orcamentos?.find((orc) => orc.status === "autorizada");
      if (!approvedOrcamento) {
        throw new Error("Orçamento autorizado não encontrado.");
      }
      await updateOrcamentoStatus(approvedOrcamento.id, "execucao", undefined, new Date());
      await updateOsStatus(authorizedOs.id, "execucao", approvedOrcamento.id);
      setPasswordAttempts(0);
      setShowConductorFormModal(false);
      window.location.href = `/dashboard/checklists/novo-checklist/veiculo/${authorizedOs?.veiculoId}`;
    } catch (error) {
      const newAttempts = passwordAttempts + 1;
      setPasswordAttempts(newAttempts);
      setShowConductorFormModal(true);

      if (newAttempts >= 3) {
        toast.error(
          "Número máximo de tentativas excedido. Por favor, tente novamente mais tarde ou entre em contato com seu gestor.",
          { duration: 5000 }
        );
        setShowConductorFormModal(true);

        setTimeout(() => {
          setPasswordAttempts(0);
        }, 2000);
      } else {
        toast.error(`Erro ao validar as credenciais do condutor. Tentativa ${newAttempts} de 3.`, {
          duration: 3000,
        });
      }
    }
  };

  return (
    <div className="container mx-auto max-w-md">
      <Card className="w-full shadow-lg">
        <CardHeader className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-t-lg">
          <CardTitle className="flex items-center text-2xl">
            <SmartphoneNfc className="mr-2" size={28} />
            Leitor de Tag NFC
          </CardTitle>
          <CardDescription>Aproxime a tag NFC do seu dispositivo móvel</CardDescription>
        </CardHeader>

        <CardContent className="pt-6">
          {nfcSupported === false && (
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6 flex items-start">
              <AlertCircle className="text-amber-500 mt-0.5 mr-2 flex-shrink-0" size={20} />
              <div>
                <h3 className="font-medium text-amber-800">Dispositivo incompatível</h3>
                <p className="text-amber-700 text-sm">
                  Seu dispositivo não suporta leitura NFC. Por favor, use um dispositivo compatível.
                </p>
              </div>
            </div>
          )}

          {error && !scanning && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 flex items-start">
              <AlertCircle className="text-red-500 mt-0.5 mr-2 flex-shrink-0" size={20} />
              <div>
                <h3 className="font-medium text-red-800">Erro</h3>
                <p className="text-red-700 text-sm">{error}</p>
              </div>
            </div>
          )}

          {scanning ? (
            <div className="text-center md:py-6">
              <ScanningAnimation />
              <p className="mt-4 font-medium">Aproxime a tag NFC do dispositivo...</p>
            </div>
          ) : tagData && vehicleData ? (
            <VehicleDetails vehicleData={vehicleData} tagData={tagData} />
          ) : (
            <div className="text-center py-6">
              <div className="p-6 rounded-full inline-flex items-center justify-center mb-4">
                <SmartphoneNfc size={60} />
              </div>
              <p className="text-slate-500">
                Toque no botão abaixo para iniciar a leitura de tags NFC
              </p>
            </div>
          )}
        </CardContent>

        <CardFooter className="flex justify-center pb-6 gap-2">
          {scanning ? (
            <Button variant="destructive" onClick={handleStop} className="w-full">
              Cancelar Leitura
            </Button>
          ) : (
            <>
              <Button
                onClick={handleRFIDScan}
                className="w-full bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700"
                disabled={nfcSupported === false}>
                {vehicleData ? "Ler Nova Tag" : "Iniciar Leitura"}
                <ChevronRight size={18} className="ml-1" />
              </Button>
              <Button
                onClick={simulateReading}
                className="w-full bg-green-500 hover:bg-green-600 text-white">
                Simular Leitura
              </Button>
            </>
          )}
        </CardFooter>
      </Card>

      <Dialog open={showApprovedModal} onOpenChange={setShowApprovedModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Orçamento Aprovado</DialogTitle>
            <DialogDescription>
              Existe um Orçamento aprovado para esse veículo apontando para você. Deseja iniciar
              agora?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowApprovedModal(false)}>
              Não
            </Button>
            <Button
              onClick={() => {
                setShowApprovedModal(false);
                setShowConductorFormModal(true);
              }}>
              Sim
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <ConductorAuthModal
        isOpen={showConductorFormModal}
        onClose={() => setShowConductorFormModal(false)}
        onSuccess={handleConductorSuccess}
        hasPassword={condutorHasPassword}
        osCondutor={condutor}
      />
    </div>
  );
};

export default RFIDReader;
