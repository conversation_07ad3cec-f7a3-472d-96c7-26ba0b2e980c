"use client";
import React from "react";

const ScanningAnimation: React.FC = () => {
  return (
    <div className="flex justify-center">
      <div className="relative">
        <div className="w-20 h-36 border-2 border-indigo-400 rounded-xl flex items-center justify-center">
          <div className="w-16 h-32 bg-indigo-50 rounded-lg flex items-center justify-center">
            <div className="relative w-10 h-10">
              <div className="absolute inset-0 border-2 border-indigo-400 rounded-full animate-ping opacity-75"></div>
              <div
                className="absolute inset-2 border-2 border-indigo-500 rounded-full animate-ping opacity-75"
                style={{ animationDelay: "0.5s" }}></div>
              <div
                className="absolute inset-4 border-2 border-indigo-600 rounded-full animate-ping opacity-75"
                style={{ animationDelay: "1s" }}></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-4 h-4 bg-indigo-600 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>

        <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2">
          <div className="w-32 h-12 flex items-center justify-center">
            <div className="w-full h-1 flex items-center justify-center">
              <div className="w-6 h-6 bg-purple-600 rounded-full opacity-75 animate-ping"></div>
            </div>
          </div>
        </div>
        <div className="absolute -bottom-10 left-1/2 transform -translate-x-1/2">
          <div className="w-12 h-6 bg-indigo-200 border border-indigo-300 rounded-md flex items-center justify-center">
            <div className="w-8 h-2 bg-indigo-400 rounded-sm"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScanningAnimation;
