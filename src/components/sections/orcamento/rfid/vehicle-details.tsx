'use client';
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { But<PERSON> } from "@/components/ui/button";
import { Tag } from "lucide-react";

interface VehicleDetailsProps {
  vehicleData: veiculo;
  tagData: string;
  onFichaTecnicaOpen?: () => void;
}

const VehicleDetails: React.FC<VehicleDetailsProps> = ({ vehicleData, tagData, onFichaTecnicaOpen }) => {
  return (
    <div className="animate-fade-in">
      <h3 className="text-xl font-semibold text-center mb-4">
        {vehicleData.marca?.descricao} {vehicleData.modelo?.descricao}
      </h3>

      <Badge className="mb-4 mx-auto block w-fit bg-indigo-100 text-indigo-800 hover:bg-indigo-200">
        <Tag className="h-3.5 w-3.5 mr-1" />
        ID: {tagData.substring(0, 12)}...
      </Badge>

      <ScrollArea className="h-[60vh]">
        <Card className="mb-4">
          <CardContent className="p-2 md:p-4 w-full">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-semibold text-sm text-gray-700">Tipo de veículo</h4>
                <p className="text-sm">{vehicleData.tiposVeiculos?.descricao || "-"}</p>

                <h4 className="font-semibold text-sm text-gray-700">Placa</h4>
                <p className="text-sm">{vehicleData.placa || "-"}</p>

                <h4 className="font-semibold text-sm text-gray-700">Marca / Modelo</h4>
                <p className="text-sm">
                  {vehicleData.marca?.descricao || "-"} - {vehicleData.modelo?.descricao || "-"}
                </p>

                <h4 className="font-semibold text-sm text-gray-700">Ano</h4>
                <p className="text-sm">
                  {vehicleData.ano_fab || "-"} / {vehicleData.ano_modelo || "-"}
                </p>
              </div>

              <div className="space-y-2">
                <h4 className="font-semibold text-sm text-gray-700">Cor</h4>
                <p className="text-sm">{vehicleData.cor || "-"}</p>

                <h4 className="font-semibold text-sm text-gray-700">Chassis (VIN)</h4>
                <p className="text-sm">{vehicleData.vin || "-"}</p>

                <h4 className="font-semibold text-sm text-gray-700">Odômetro</h4>
                <p className="text-sm">{vehicleData.odometro_atual || "-"}</p>

                <h4 className="font-semibold text-sm text-gray-700">Mais Informações</h4>
                {onFichaTecnicaOpen ? (
                  <Button
                    onClick={onFichaTecnicaOpen}
                    variant="outline"
                    size="sm"
                    className="text-blue-600 border-blue-600 hover:bg-blue-50 hover:text-blue-700"
                  >
                    Ficha técnica
                  </Button>
                ) : (
                  <p>-</p>
                )}
              </div>
            </div>

            <Separator className="my-4" />

            {vehicleData.status && (
              <div className="flex items-center">
                <div className="text-slate-500 mr-2 text-sm font-medium">Status:</div>
                <Badge
                  className={`${
                    vehicleData.status.toLowerCase() === "ativo"
                      ? "bg-green-100 text-green-800 hover:bg-green-200"
                      : "bg-red-100 text-red-800 hover:bg-red-200"
                  }`}
                >
                  {vehicleData.status}
                </Badge>
              </div>
            )}
          </CardContent>
        </Card>
      </ScrollArea>
    </div>
  );
};

export default VehicleDetails;