"use client";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Combobox } from "@/components/inputs/combo-box";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CarFront, CarFrontIcon, CarTaxiFront, Upload, X, AlertCircle, ImageIcon, Search } from "lucide-react";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useState, useRef, ChangeEvent } from "react";
import { Textarea } from "@/components/ui/textarea";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

const tabs = ["Ordem de serviço", "Veiculo"];
const ordemServico = [
  "Contrato :",
  "Centro de custo:",
  "Número da OS:",
  "Data de lançamento:",
  "Tipo de serviço:",
  "Tipo de manutenção:",
  "Observações:",
  "Veículo imobilizado:",
  "Localização:",
  "Credenciado orçamentista:",
];
const veiculo = [
  "Tipo de veículo:",
  "Placa:",
  "Marca/Modelo:",
  "Versão:",
  "Ano:",
  "Cor:",
  "Chassis:",
  "Odômetro:",
  "Mais informações:",
];

const tabAdicionarPecaServico = ["Peças:", "Serviços:", "Garantia:"];
const resumo = ["Total em peças:", "Total em serviços:", "Subtotal:", "Descontos:", "Total"]
const grupos = [
    { id: 'pecas', nome: 'Peças' },
    { id: 'servicos', nome: 'Serviços' },
    { id: 'acessorios', nome: 'Acessórios' },
  ];
  
interface OrdemServicoData {
  contrato: string;
  centro_de_custo: string;
  numero_os: string;
  data_lancamento: string;
  tipo_servico: string;
  tipo_manutencao: string;
  observacoes: string;
  veiculo_imobilizado: boolean;
  localizacao: string;
  credenciado_orcamentista: string;
}

interface VeiculoData {
  tipo_veiculo: string;
  placa: string;
  marca_modelo: string;
  versao: string;
  ano: string;
  cor: string;
  chassis: string;
  odometro: string;
  mais_informacoes: string;
}

interface ResumoData {
  total_pecas: number;
  total_servicos: number;
  subtotal: number;
  descontos: number;
  total: number;
}

// Interface para os arquivos
interface FileItem {
  file: File;
  preview?: string;
  id: string;
}

// Adicionar após as interfaces existentes:
interface FichaTecnicaItem {
  id: string;
  descricao: string;
  valor: string;
}

interface FichaTecnicaData {
  veiculo: {
    placa: string;
    modelo: string;
    marca: string;
    ano: string;
  };
  itens: FichaTecnicaItem[];
}

interface DiagnosticoData {
  observacoes: string;
  previsaoEntrega: number;
  validade: string;
}

export function EdOrcamento() {
  const [expanded, setExpanded] = useState(false);
  const [ordemServicoData, setOrdemServicoData] = useState<OrdemServicoData>({
    contrato: "Contrato ABC",
    centro_de_custo: "Centro XYZ",
    numero_os: "OS-123456",
    data_lancamento: "20/03/2024",
    tipo_servico: "Manutenção",
    tipo_manutencao: "Corretiva",
    observacoes: "Observações gerais",
    veiculo_imobilizado: true,
    localizacao: "São Paulo",
    credenciado_orcamentista: "Oficina ABC"
  });

  const [veiculoData, setVeiculoData] = useState<VeiculoData>({
    tipo_veiculo: "Carro",
    placa: "ABC-1234",
    marca_modelo: "Toyota Corolla",
    versao: "XEI",
    ano: "2023",
    cor: "Prata",
    chassis: "123456789",
    odometro: "15000",
    mais_informacoes: "Informações adicionais"
  });

  const [resumoData, setResumoData] = useState<ResumoData>({
    total_pecas: 1500.00,
    total_servicos: 500.00,
    subtotal: 2000.00,
    descontos: 200.00,
    total: 1800.00
  });

  const [files, setFiles] = useState<FileItem[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Adicionar novo estado para controlar o Dialog da ficha técnica
  const [fichaTecnicaOpen, setFichaTecnicaOpen] = useState(false);

  // Adicionar estados para a ficha técnica
  const [fichaTecnicaData, setFichaTecnicaData] = useState<FichaTecnicaData>({
    veiculo: {
      placa: "BBB-0221",
      modelo: "HLX 2.0 20v Gasolina Mec.",
      marca: "Fiat",
      ano: "1998 ⇒ 2002",
    },
    itens: [
      { id: "1", descricao: "Capacidade do tanque", valor: "60 litros" },
      { id: "2", descricao: "Potência", valor: "142 cv" },
      { id: "3", descricao: "Torque", valor: "18,5 kgfm" },
      { id: "4", descricao: "Cilindrada", valor: "1998 cm³" },
      { id: "5", descricao: "Válvulas por cilindro", valor: "5" },
      { id: "6", descricao: "Configuração do motor", valor: "4 cilindros em linha" },
      { id: "7", descricao: "Transmissão", valor: "Manual de 5 marchas" },
      { id: "8", descricao: "Tração", valor: "Dianteira" },
    ]
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredItems, setFilteredItems] = useState<FichaTecnicaItem[]>(fichaTecnicaData.itens);

  const [diagnosticoData, setDiagnosticoData] = useState<DiagnosticoData>({
    observacoes: "",
    previsaoEntrega: 2,
    validade: new Date().toISOString().split('T')[0]
  });

  const formSchema = z.object({
    teste: z.string().min(1, {
      message: "Username must be at least 2 characters.",
    }),
    
  })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      teste: "",
      
    },
  })

  function onSubmit(values: z.infer<typeof formSchema>) {
    
    console.log(values)
  }

  // Função para processar os arquivos selecionados
  const processFiles = (selectedFiles: FileList | null) => {
    if (!selectedFiles || selectedFiles.length === 0) return;
    
    const fileArray = Array.from(selectedFiles);
    
    // Verificar tamanho dos arquivos (max 5MB por arquivo)
    const oversizedFiles = fileArray.filter(file => file.size > 5 * 1024 * 1024);
    if (oversizedFiles.length > 0) {
      toast.error(`${oversizedFiles.length} arquivo(s) excede(m) o tamanho máximo de 5MB`);
      return;
    }
    
    const newFiles = fileArray.map(file => ({
      file,
      id: crypto.randomUUID(),
      preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined
    }));
    
    setFiles(prev => [...prev, ...newFiles]);
    
    toast.success(`${fileArray.length} arquivo(s) adicionado(s)`);
  };

  // Manipulador para o input de arquivo
  const handleFileSelect = (e: ChangeEvent<HTMLInputElement>) => {
    processFiles(e.target.files);
    if (e.target.value) e.target.value = '';
  };

  // Manipulador para o botão de anexar
  const openFileSelector = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Manipuladores para drag and drop
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    e.dataTransfer.dropEffect = 'copy';
    setIsDragging(true);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    processFiles(e.dataTransfer.files);
  };

  // Remover arquivo
  const removeFile = (id: string) => {
    setFiles(prev => {
      const fileToRemove = prev.find(file => file.id === id);
      if (fileToRemove?.preview) {
        URL.revokeObjectURL(fileToRemove.preview);
      }
      return prev.filter(file => file.id !== id);
    });
    toast.success("Arquivo removido");
  };

  // Função para formatar o tamanho do arquivo
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Função para filtrar itens
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    const filtered = fichaTecnicaData.itens.filter(item =>
      item.descricao.toLowerCase().includes(value.toLowerCase()) ||
      item.valor.toLowerCase().includes(value.toLowerCase())
    );
    setFilteredItems(filtered);
  };

  const handleDiagnosticoChange = (field: keyof DiagnosticoData, value: string | number) => {
    setDiagnosticoData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Mostrar toast de confirmação
    toast.success(`${field.charAt(0).toUpperCase() + field.slice(1)} atualizado com sucesso`);
  };

  console.log(veiculoData)
  return (
    <>
      <div className="p-6 grid grid-cols-[70%_30%] gap-4">
        <div className="flex flex-col gap-4 w-full">
          <div
            className="mt-4 cursor-pointer font-medium"
            onClick={() => setExpanded((prev) => !prev)}>
            {expanded ? (
              <div className="flex items-center justify-between">
                <p>Detalhes da ordem de serviço</p>
                <p>▲ </p>
              </div>
            ) : (
              <div className="flex items-center justify-between">
                <p>Detalhes da ordem de serviço</p>
                <p>▼ </p>
              </div>
            )}
          </div>

          {expanded && (
            <div className="mt-2 p-4 w-full border rounded shadow-sm">
              <Tabs defaultValue={tabs[0]} className="">
                <TabsList>
                  {tabs.map((tab, index) => (
                    <TabsTrigger key={tab} value={tab}>
                      {tabs[index]}
                    </TabsTrigger>
                  ))}
                </TabsList>
                <TabsContent value={tabs[0]}>
                  <div className="space-y-2">
                    <div className="flex  w-full border-b border-gray-300 pb-2">
                      <span>Contrato:</span>
                      <span className="ml-20">{ordemServicoData.contrato}</span>
                    </div>
                    <div className="flex w-full border-b border-gray-300 pb-2">
                      <span>Centro de custo:</span>
                      <span className="ml-20">{ordemServicoData.centro_de_custo}</span>
                    </div>
                    <div className="flex w-full border-b border-gray-300 pb-2">
                      <span>Número da OS:</span>
                      <span className="ml-20">{ordemServicoData.numero_os}</span>
                    </div>
                    <div className="flex w-full border-b border-gray-300 pb-2">
                      <span>Data de lançamento:</span>
                      <span className="ml-20">{ordemServicoData.data_lancamento}</span>
                    </div>
                    <div className="flex w-full border-b border-gray-300 pb-2">
                      <span>Tipo de serviço:</span>
                      <span className="ml-20">{ordemServicoData.tipo_servico}</span>
                    </div>
                    <div className="flex w-full border-b border-gray-300 pb-2">
                      <span>Tipo de manutenção:</span>
                      <span className="ml-20">{ordemServicoData.tipo_manutencao}</span>
                    </div>
                    <div className="flex w-full border-b border-gray-300 pb-2">
                      <span>Observações:</span>
                      <span className="ml-20">{ordemServicoData.observacoes}</span>
                    </div>
                    <div className="flex w-full border-b border-gray-300 pb-2">
                      <span>Veículo imobilizado:</span>
                      <span className="ml-20">
                        {ordemServicoData.veiculo_imobilizado ? "Sim" : "Não"}
                      </span>
                    </div>
                    <div className="flex w-full border-b border-gray-300 pb-2">
                      <span>Localização:</span>
                      <span className="ml-20">{ordemServicoData.localizacao}</span>
                    </div>
                    <div className="flex w-full border-gray-300 pb-2">
                      <span>Credenciado orçamentista:</span>
                      <span className="ml-20">{ordemServicoData.credenciado_orcamentista}</span>
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value={tabs[1]}>
                  <div className="space-y-2">
                    <div className="flex w-full border-b border-gray-300 pb-2">
                      <span>Tipo de veículo:</span>
                      <span className="ml-20">{veiculoData.tipo_veiculo}</span>
                    </div>
                    <div className="flex w-full border-b border-gray-300 pb-2">
                      <span>Placa:</span>
                      <span className="ml-20">{veiculoData.placa}</span>
                    </div>
                    <div className="flex w-full border-b border-gray-300 pb-2">
                      <span>Marca/Modelo:</span>
                      <span className="ml-20">{veiculoData.marca_modelo}</span>
                    </div>
                    <div className="flex w-full border-b border-gray-300 pb-2">
                      <span>Versão:</span>
                      <span className="ml-20">{veiculoData.versao}</span>
                    </div>
                    <div className="flex w-full border-b border-gray-300 pb-2">
                      <span>Ano:</span>
                      <span className="ml-20">{veiculoData.ano}</span>
                    </div>
                    <div className="flex w-full border-b border-gray-300 pb-2">
                      <span>Cor:</span>
                      <span className="ml-20">{veiculoData.cor}</span>
                    </div>
                    <div className="flex w-full border-b border-gray-300 pb-2">
                      <span>Chassis:</span>
                      <span className="ml-20">{veiculoData.chassis}</span>
                    </div>
                    <div className="flex w-full border-b border-gray-300 pb-2">
                      <span>Odômetro:</span>
                      <span className="ml-20">{veiculoData.odometro}</span>
                    </div>
                    <div className="flex w-full border-gray-300 pb-2">
                      <span>Mais informações:</span>
                      <span className="ml-20">{veiculoData.mais_informacoes}</span>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          )}
          <div className="flex justify-end mt-4">
            <Dialog>
              <DialogTrigger className="bg-green-500 text-white p-2 text-sm rounded-md">
                + Adicionar peça ou Serviço
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Adicionar peças e serviços</DialogTitle>
                  <DialogDescription>
                    <div className="flex flex-col gap-4">
                      <Tabs defaultValue={tabAdicionarPecaServico[0]} className="w-[400px]">
                        <div className="flex justify-between gap-2">
                          <TabsList className="flex justify-between gap-2">
                            {tabAdicionarPecaServico.map((tab, index) => (
                              <TabsTrigger
                                key={tab}
                                value={tab}
                                className="capitalize text-xs px-4 py-2 border-b-2 border-transparent data-[state=active]:border-gray-800 data-[state=active]:text-gray-800 rounded-none bg-transparent">
                                {tabAdicionarPecaServico[index]}
                              </TabsTrigger>
                            ))}
                          </TabsList>
                          <button
                            className="flex items-center gap-2 hover:text-blue-600 transition-colors"
                            onClick={() => setFichaTecnicaOpen(true)}>
                            <CarFront size={24} className="text-gray-600 cursor-pointer" />
                            ficha
                          </button>
                        </div>

                        <TabsContent value={tabAdicionarPecaServico[0]}>
                          <FormPecaServico
                            tipo="peca"
                            onSubmit={(data) => {
                              console.log("Dados da peça:", data);
                              // Aqui você pode adicionar a lógica para salvar a peça
                            }}
                          />
                        </TabsContent>
                        <TabsContent value={tabAdicionarPecaServico[1]}>
                          <FormPecaServico
                            tipo="servico"
                            onSubmit={(data) => {
                              console.log("Dados do serviço:", data);
                              // Aqui você pode adicionar a lógica para salvar o serviço
                            }}
                          />
                        </TabsContent>
                        <TabsContent value={tabAdicionarPecaServico[2]}>
                          <Form {...form}>
                            <form>
                              <div className="mt-4 space-y-4">
                                <Combobox
                                  datas={[]}
                                  placeholder="item "
                                  referenceId="garantia"
                                  title="Adicionar itens em garatinha"
                                  chave="descricao"
                                  name="garantia"
                                />
                                <div className="flex justify-end">
                                  <Button
                                    type="submit"
                                    className="bg-green-500 text-white hover:bg-green-600">
                                    Adicionar
                                  </Button>
                                </div>
                              </div>
                            </form>
                          </Form>
                        </TabsContent>
                      </Tabs>
                    </div>
                  </DialogDescription>
                </DialogHeader>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <div className="flex flex-col gap-4">
          <div className="shadow-sm rounded-md p-4">
            <div className="flex flex-col gap-4 ">
              <h3 className="border-b border-gray-300 font-semibold pb-2">Resumo do orçamento</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="ml-20">Total em peças:</span>
                  <span className="ml-20">R$ {resumoData.total_pecas.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="ml-20">Total em serviços:</span>
                  <span className="ml-20">R$ {resumoData.total_servicos.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="ml-20">Subtotal:</span>
                  <span className="ml-20">R$ {resumoData.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="ml-20">Descontos:</span>
                  <span className="ml-20">R$ {resumoData.descontos.toFixed(2)}</span>
                </div>
                <div className="flex justify-between font-bold">
                  <span className="ml-20">Total:</span>
                  <span className="ml-20">R$ {resumoData.total.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-4 shadow-sm rounded-md p-4">
            <h3 className="border-b border-gray-300 pb-2 font-semibold">Observações/Diagnóstico</h3>
            <div className="flex flex-col gap-4">
              <Textarea
                placeholder="Digite alguma observação"
                value={diagnosticoData.observacoes}
                onChange={(e) => handleDiagnosticoChange("observacoes", e.target.value)}
              />
            </div>
            <div className="flex gap-4">
              <div>
                <h3 className="font-semibold">Previsão de entrega</h3>
                <p className="text-sm text-gray-500">
                  Digite quantos dias úteis será necessário para execução dos serviços após
                  aprovação.
                </p>
              </div>
              <Input
                type="number"
                placeholder="2"
                min={1}
                value={diagnosticoData.previsaoEntrega}
                onChange={(e) =>
                  handleDiagnosticoChange("previsaoEntrega", parseInt(e.target.value) || 1)
                }
              />
            </div>
            <div className="flex gap-4">
              <div>
                <h3 className="font-semibold">Validade</h3>
                <p className="text-sm text-gray-500">
                  Selecione a data de validade deste orçamento
                </p>
              </div>
              <Input
                type="date"
                value={diagnosticoData.validade}
                min={new Date().toISOString().split("T")[0]}
                onChange={(e) => handleDiagnosticoChange("validade", e.target.value)}
              />
            </div>
          </div>
          <div className="flex flex-col gap-4 shadow-sm rounded-md p-4">
            <div>
              <h3 className="font-semibold">Arquivos e Imagens</h3>
              <p className="text-sm text-gray-500">
                Adicione arquivos e imagens relacionados ao orçamento
              </p>
            </div>

            {/* Informações sobre upload */}
            <div className="flex items-start space-x-2 text-xs text-gray-500 bg-gray-50 p-2 rounded">
              <AlertCircle className="h-4 w-4 text-blue-500 flex-shrink-0 mt-0.5" />
              <div>
                <p>Tamanho máximo por arquivo: 5MB</p>
                <p>Formatos aceitos: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG</p>
              </div>
            </div>

            {/* Input de arquivo oculto */}
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              multiple
              accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
              onChange={handleFileSelect}
            />

            {/* Área de drag and drop */}
            <div
              className={`flex items-center justify-center bg-gray-50 p-8 rounded-md border-2 border-dashed transition-colors cursor-pointer
                    ${isDragging ? "border-blue-400 bg-blue-50" : "border-gray-200"}`}
              onDragEnter={handleDragEnter}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={openFileSelector}>
              <div className="text-center">
                <Upload
                  className={`h-8 w-8 mx-auto mb-2 ${
                    isDragging ? "text-blue-500" : "text-gray-400"
                  }`}
                />
                <p className="text-sm text-gray-500 mb-2">
                  {isDragging
                    ? "Solte os arquivos aqui"
                    : "Arraste arquivos ou clique para selecionar"}
                </p>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    openFileSelector();
                  }}>
                  <ImageIcon className="h-4 w-4 mr-2" />
                  Selecionar arquivos
                </Button>
              </div>
            </div>

            {/* Lista de arquivos */}
            {files.length > 0 && (
              <div className="mt-4">
                <h4 className="text-sm font-medium mb-2">Arquivos anexados ({files.length})</h4>
                <div className="grid grid-cols-2 gap-4">
                  {files.map((file) => (
                    <div key={file.id} className="relative group border rounded-lg p-3 bg-gray-50">
                      <div className="flex items-start gap-3">
                        {file.preview ? (
                          <div className="h-16 w-16 rounded-md overflow-hidden flex-shrink-0">
                            <img
                              src={file.preview}
                              alt={file.file.name}
                              className="h-full w-full object-cover"
                            />
                          </div>
                        ) : (
                          <div className="h-16 w-16 rounded-md bg-gray-200 flex items-center justify-center flex-shrink-0">
                            <ImageIcon className="h-8 w-8 text-gray-400" />
                          </div>
                        )}
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">{file.file.name}</p>
                          <p className="text-xs text-gray-500">{formatFileSize(file.file.size)}</p>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 text-gray-500 hover:text-red-500"
                          onClick={(e) => {
                            e.stopPropagation();
                            removeFile(file.id);
                          }}>
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <Dialog open={fichaTecnicaOpen} onOpenChange={setFichaTecnicaOpen}>
        <DialogContent className="max-w-[800px] p-0">
          <DialogHeader className="p-6 pb-2">
            <DialogTitle>Ficha técnica do veículo</DialogTitle>
            <DialogDescription>
              Informações referentes ao manual técnico do veículo.
            </DialogDescription>
          </DialogHeader>

          <div className="p-6 pt-2">
            <div className="flex flex-col space-y-4">
              {/* Campo de busca */}
              <div className="flex items-center space-x-2 border rounded p-2">
                <Input
                  placeholder="Busca rápida"
                  className="border-0 focus-visible:ring-0"
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                />
                <Button variant="ghost" size="icon" onClick={() => handleSearch("")}>
                  {searchTerm ? <X className="h-4 w-4" /> : <Search className="h-4 w-4" />}
                </Button>
              </div>

              {/* Informações do veículo */}
              <div className="bg-gray-50 p-4 rounded-md">
                <p className="text-sm text-gray-600">
                  {`${fichaTecnicaData.veiculo.placa} | ${fichaTecnicaData.veiculo.marca} | ${fichaTecnicaData.veiculo.modelo} (${fichaTecnicaData.veiculo.ano}) `}
                </p>
              </div>

              {/* Tabela de descrição e valor */}
              <div className="border rounded-md">
                <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 font-medium text-sm">
                  <div>Descrição</div>
                  <div>Valor</div>
                </div>
                <div className="divide-y">
                  {filteredItems.length > 0 ? (
                    filteredItems.map((item) => (
                      <div key={item.id} className="grid grid-cols-2 gap-4 p-4 hover:bg-gray-50">
                        <div className="text-sm text-gray-600">{item.descricao}</div>
                        <div className="text-sm text-gray-900 font-medium">{item.valor}</div>
                      </div>
                    ))
                  ) : (
                    <div className="p-4 text-sm text-gray-500 text-center">
                      {searchTerm ? "Nenhum resultado encontrado" : "Nenhum item disponível"}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-end gap-2 p-6 pt-0">
            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm("");
                setFilteredItems(fichaTecnicaData.itens);
                setFichaTecnicaOpen(false);
              }}>
              Fechar
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}

const formSchema = z.object({
  grupo: z.string().min(1, { message: "Grupo é obrigatório" }),
  codigo: z.string().min(2, { message: "Código deve ter no mínimo 2 caracteres" }),
  descricao: z.string().min(2, { message: "Descrição deve ter no mínimo 2 caracteres" }),
  marca: z.string().min(2, { message: "Marca deve ter no mínimo 2 caracteres" }),
  prazoGarantia: z.string().min(2, { message: "Prazo de garantia é obrigatório" }),
  valorUnitario: z.number().min(0, { message: "Valor unitário deve ser maior que 0" }),
  valorNegociado: z.number().min(0, { message: "Valor negociado deve ser maior que 0" }),
  quantidade: z.number().min(1, { message: "Quantidade deve ser maior que 0" }),
  valorHora: z.string().optional(),
  horas: z.string().optional(),
});

type FormPecaServicoProps = {
  tipo: "peca" | "servico";
  onSubmit: (data: z.infer<typeof formSchema>) => void;
};

export function FormPecaServico({ tipo, onSubmit }: FormPecaServicoProps) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      grupo: "",
      codigo: "",
      descricao: "",
      marca: "",
      prazoGarantia: "",
      valorUnitario: 0,
      valorNegociado: 0,
      quantidade: 1,
      valorHora: "",
      horas: "",
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="grupo"
          render={({ field }) => (
            <>
              <div>
                <FormItem>
                  <FormLabel>Grupo</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o grupo" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {grupos.map((grupo) => (
                        <SelectItem key={grupo.id} value={grupo.id}>
                          {grupo.nome}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              </div>

              <div className="flex gap-4">
                <FormItem>
                  <FormLabel>Código</FormLabel>
                  <FormControl>
                    <Input placeholder="CB700-000B" {...form.register("codigo")} />
                  </FormControl>
                  <FormMessage />
                </FormItem>

                <FormItem>
                  <FormLabel>Descrição</FormLabel>
                  <FormControl>
                    <Input placeholder="Filtro de óleo" {...form.register("descricao")} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </div>

              <div className="flex gap-4">
                <FormItem>
                  <FormLabel>Marca</FormLabel>
                  <FormControl>
                    <Input placeholder="Bosch" {...form.register("marca")} />
                  </FormControl>
                  <FormMessage />
                </FormItem>

                <FormItem>
                  <FormLabel>Prazo de garantia</FormLabel>
                  <FormControl>
                    <Input placeholder="90 dias" {...form.register("prazoGarantia")} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </div>

              {tipo === "peca" ? (
                <div className="flex gap-4">
                  <FormItem>
                    <FormLabel>Valor Unitário</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="R$ 1500" 
                        {...form.register("valorUnitario", { valueAsNumber: true })} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>

                  <FormItem>
                    <FormLabel>Valor Negociado</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="R$ 1500" 
                        {...form.register("valorNegociado", { valueAsNumber: true })} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>

                  <FormItem>
                    <FormLabel>Quantidade</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="1,00" 
                        {...form.register("quantidade", { valueAsNumber: true })} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </div>
              ) : (
                <div className="flex gap-4">
                  <FormItem>
                    <FormLabel>Valor hora</FormLabel>
                    <FormControl>
                      <Input placeholder="R$ 1500" {...form.register("valorHora")} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>

                  <FormItem>
                    <FormLabel>Valor Negociado</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="R$ 1500" 
                        {...form.register("valorNegociado", { valueAsNumber: true })} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>

                  <FormItem>
                    <FormLabel>Horas</FormLabel>
                    <FormControl>
                      <Input placeholder="1,00" {...form.register("horas")} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </div>
              )}

              <div className="flex justify-center bg-green-100 p-2 rounded-md">
                R$ {form.watch("valorNegociado") * (tipo === "peca" ? form.watch("quantidade") : 1)}
              </div>
            </>
          )}
        />
        <div className="flex justify-end">
          <Button type="submit">Adicionar</Button>
        </div>
      </form>
    </Form>
  );
} 