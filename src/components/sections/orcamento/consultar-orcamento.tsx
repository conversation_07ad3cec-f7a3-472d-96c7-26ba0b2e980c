"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@radix-ui/react-tabs";
import { Input } from "@/components/ui/input";
import { Filter, Search, Edit } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";
import { DataTable } from "@/components/tables/data-table";
import {
  osEnviadaColumn,
  osExecucaoColumn,
  osFaturadasColumn,
  osFinalizadasColumn,
  osLancadosColumn,
  osPerdidosColumn,
} from "@/components/tables/columns";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { format } from "date-fns";
import ModalOS from "@/components/modal/modal.os";
import { useSession } from "@/components/session/use-session";
import { Badge } from "@/components/ui/badge";
import { useOS } from "@/context/os-context";
import DetailsDialogue from "./details-dialogue";
import { Detalhes } from "../os/detalhes";
import { ColumnDef } from "@tanstack/react-table";

const tabsPT = ["Disponíveis", "Enviados", "Em execução", "Concluídos", "Faturados", "Perdidos"];
const tab = ["lançada", "orcamentaçao", "execucao", "finalizada", "faturada", "perdido"];
export function OrConsltar({ os }: { os: OS[] }) {
  const searchParams = useSearchParams();
  const { session } = useSession();
  const osIdSearchParam = searchParams.get("osId");
  const [searchQuery, setSearchQuery] = useState("");
  const [showModal, setShowModal] = useState({ id: "", status: "" });
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedRowData, setSelectedRowData] = useState<OS | null>(null);

  const [visibleColumns, setVisibleColumns] = useState<ColumnDef<OS>[]>([]);
  const isAdmin = session?.roles.includes("ADMIN");
  const [selectedOsId, setSelectedOsId] = useState(osIdSearchParam || "");

  useEffect(() => {
    setSelectedOsId(osIdSearchParam || "");
  }, [osIdSearchParam]);

  useEffect(() => {
    if (!!osIdSearchParam) {
      const foundOs = os.find((os: OS) => os.id === selectedOsId);
      if (foundOs) {
        handleRowClick(foundOs);
      }
    }
  }, [osIdSearchParam, os, selectedOsId]);

  useEffect(() => {
    const filteredColumns = isAdmin
      ? osLancadosColumn
      : osLancadosColumn.filter(
          (column) => !("accessorKey" in column && column.accessorKey === "credenciado")
        );

    setVisibleColumns(filteredColumns);
  }, [session, isAdmin]);

  const countItemsByStatus = (status: string) => {
    if (status === "orcamentaçao") {
      const targetStatuses = ["orcamentaçao", "analise", "autorizada"];
      return Array.isArray(os)
        ? os.filter(
            (item) =>
              targetStatuses.includes(item.status.toLowerCase()) ||
              (item.orcamentos &&
                item.orcamentos.some(
                  (orcamento) =>
                    orcamento.status && // Add this check
                    targetStatuses.includes(orcamento.status.toLowerCase()) &&
                    orcamento.credenciadoId === session?.credenciadoId
                ))
          ).length
        : 0;
    } else if (status === tab[2]) {
      return Array.isArray(os)
        ? os.filter(
            (o) =>
              (o.status.toLowerCase() === tab[2] &&
                o?.status?.toLowerCase() !== "finalizada" &&
                o?.status !== "Aguardando Aprovação") ||
              (o.orcamentos &&
                o.orcamentos.some(
                  (orcamento) =>
                    orcamento.status?.toLowerCase() === tab[2] &&
                    orcamento.credenciadoId === session?.credenciadoId &&
                    o?.status?.toLowerCase() !== "finalizada" &&
                    o?.status !== "Aguardando Aprovação"
                ))
          ).length
        : 0;
    } else if (status === tab[3]) {
      return Array.isArray(os) // Add this check
        ? os.filter(
            // Remove the unnecessary ? after os
            (item) =>
              item.status.toLowerCase() === status ||
              item.status === "Aguardando Aprovação" ||
              (item.orcamentos &&
                item.orcamentos.some(
                  (orcamento) =>
                    orcamento.status && // Add this check
                    orcamento.status.toLowerCase() === status &&
                    orcamento.credenciadoId === session?.credenciadoId
                ))
          ).length
        : 0; // Add return value when os is not an array
    } else if (status === "perdido") {
      // Lógica específica para orçamentos perdidos - apenas com status "Perdido"
      return Array.isArray(os)
        ? os.filter((item) => {
            // Verificar se a OS tem status "Perdido"
            return item.status?.toLowerCase() === "perdido";
          }).length
        : 0;
    } else {
      return Array.isArray(os)
        ? os.filter(
            (item) =>
              item.status.toLowerCase() === status ||
              (item.orcamentos &&
                item.orcamentos.some(
                  (orcamento) =>
                    orcamento.status && // Add this check
                    orcamento.status.toLowerCase() === status &&
                    orcamento.credenciadoId === session?.credenciadoId
                ))
          ).length
        : 0;
    }
  };

  const [currentTab, setCurrentTab] = useState(tab[0]);

  const handleRowClick = (data: OS) => {
    if (currentTab === tab[0] || currentTab === tab[2] || data.status === "pendente") {
      window.location.href = `/dashboard/orcamento/editar/${data.id}/${data.status}`;
      return;
    }
    setSelectedRowData(data);
    setIsDialogOpen(true);
  };
  return (
    <div className="p-6">
      <Tabs
        defaultValue={tab[0]}
        className="w-full"
        onValueChange={(value) => setCurrentTab(value)}>
        <div className="border-b mb-4 flex justify-between">
          <TabsList className="flex flex-wrap sm:flex-nowrap overflow-x-auto space-x-1 bg-transparent h-auto pb-1">
            {tab.map((tab, index) => (
              <TabsTrigger
                key={tab}
                value={tab}
                className="capitalize text-xs px-4 py-2 border-b-2 border-transparent 
                           data-[state=active]:border-gray-800 rounded-none bg-transparent flex items-center gap-1">
                {tabsPT[index]}
                <Badge variant="secondary" className="h-5 w-5 p-0 flex items-center justify-center">
                  {countItemsByStatus(tab)}
                </Badge>
              </TabsTrigger>
            ))}
          </TabsList>
        </div>
        <div className="mb-4 flex w-full p-4 rounded-md gap-2">
          <div className="relative">
            <Input
              placeholder="Buscar rápido"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9 h-9 max-w-[240px] rounded-sm"
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          </div>
          <Button variant="outline" className="h-9 gap-1 text-xs rounded-sm">
            <Filter size={14} />
            Filtros
          </Button>
        </div>
        <TabsContent value={tab[0]}>
          <DataTable
            data={
              Array.isArray(os)
                ? os.filter((o) => {
                  console.log("Filtering OS with status:", o);
                    const osStatus = o.status.toLowerCase();
                    const allowedOsStatuses = ["lançada", "orcamentaçao", "analise", "pendente"];
                    if (!allowedOsStatuses.includes(osStatus)) return false;
                    console.log(
                      "Filtering OS with status:",o)
                    return (
                      o.status.toLowerCase() === tab[0] ||
                      (o.orcamentos &&
                        o.orcamentos.some(
                          (orcamento) =>
                            (orcamento.status?.toLowerCase() === "lançada" ||
                              orcamento.status?.toLowerCase() === "pendente") &&
                            orcamento.credenciadoId === session?.credenciadoId
                        ))
                    );
                  })
                : []
            }
            columns={visibleColumns}
            exportTo={true}
            handleRowClick={handleRowClick}
          />
        </TabsContent>
        <TabsContent value={tab[1]}>
          <DataTable
            data={
              Array.isArray(os)
                ? os.filter((o) => {
                    const allowedOsStatuses = ["orcamentaçao", "analise", "autorizada"];
                    if (!allowedOsStatuses.includes(o.status.toLowerCase())) return false;
                    return (
                      o.orcamentos &&
                      o.orcamentos.some(
                        (orc) =>
                          orc.status &&
                          ["enviado", "autorizada"].includes(orc.status.toLowerCase()) &&
                          orc.credenciadoId === session?.credenciadoId
                      )
                    );
                  })
                : []
            }
            columns={osEnviadaColumn}
            exportTo={true}
            handleRowClick={handleRowClick}
          />
        </TabsContent>
        <TabsContent value={tab[2]}>
          <DataTable
            data={
              Array.isArray(os)
                ? os.filter((o) => {
                    const isOrcamentista = session?.roles.includes("ORCAMENTISTA_OFICINA");
                    if (isOrcamentista) {
                      return (
                        o.orcamentos &&
                        o.orcamentos.some(
                          (orcamento) =>
                            orcamento.status?.toLowerCase() === tab[2] &&
                            orcamento.credenciadoId === session?.credenciadoId &&
                            o.status?.toLowerCase() !== "finalizada" &&
                            o.status?.toLowerCase() !== "faturada" &&
                            o.status !== "Aguardando Aprovação"
                        )
                      );
                    }
                    return (
                      o.status.toLowerCase() === tab[2] &&
                      o.status?.toLowerCase() !== "finalizada" &&
                      o.status?.toLowerCase() !== "faturada" &&
                      o.status !== "Aguardando Aprovação"
                    );
                  })
                : []
            }
            columns={osExecucaoColumn}
            handleRowClick={handleRowClick}
            exportTo={true}
          />
        </TabsContent>
        <TabsContent value={tab[3]}>
          <DataTable
            data={
              Array.isArray(os)
                ? os.filter(
                    (o) =>
                      o.status.toLowerCase() === tab[3] ||
                      o.status === "Aguardando Aprovação" ||
                      (o.orcamentos &&
                        o.orcamentos.some(
                          (orcamento) =>
                            orcamento.status?.toLowerCase() === tab[3] &&
                            orcamento.credenciadoId === session?.credenciadoId
                        ))
                  )
                : []
            }
            columns={osFinalizadasColumn}
            handleRowClick={handleRowClick}
            exportTo={true}
          />
        </TabsContent>
        <TabsContent value={tab[4]}>
          <DataTable
            data={
              Array.isArray(os)
                ? os.filter(
                    (o) =>
                      o.status.toLowerCase() === tab[4] &&
                      (o.orcamentos &&
                        o.orcamentos.some(
                          (orcamento) =>
                            orcamento.status?.toLowerCase() === tab[4] &&
                            orcamento.credenciadoId === session?.credenciadoId
                        ))
                  )
                : []
            }
            columns={osFaturadasColumn}
            exportTo={true}
            handleRowClick={handleRowClick}
          />
        </TabsContent>
        <TabsContent value={tab[5]}>
          <DataTable
            data={
              Array.isArray(os)
                ? os.filter((o) => {
                    // Mostrar apenas OS com status "Perdido"
                    return o.status?.toLowerCase() === "perdido";
                  })
                : []
            }
            columns={osPerdidosColumn}
            exportTo={true}
            handleRowClick={handleRowClick}
          />
        </TabsContent>
      </Tabs>

      {/* Diálogo de detalhes da OS */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent style={{ maxWidth: "1200px", overflow: "auto", height: "85vh" }}>
          <DialogHeader>
            <DialogTitle>Detalhes da Ordem de Serviço</DialogTitle>
            <DialogDescription>
              Aqui estão os detalhes da ordem de serviço selecionada.
            </DialogDescription>
          </DialogHeader>
          <Detalhes
            data={selectedRowData}
            osStatus={selectedRowData?.status}
            orcamento={selectedRowData?.orcamentos?.find(
              (orcamento) => orcamento.credenciadoId === session?.credenciadoId
            )}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
