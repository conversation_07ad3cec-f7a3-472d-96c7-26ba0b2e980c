"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/tables/data-table';
import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { TrendingDown, AlertCircle, Eye, BarChart3, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { useSession } from '@/components/session/use-session';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface OrcamentoPerdido {
  id: string;
  ordemServico: {
    osNumber: string;
    veiculo: {
      placa: string;
      marca: { descricao: string };
      modelo: { descricao: string };
    };
    descricao: string;
  };
  valor_total: number;
  orcamentoVencedor: {
    valor_total: number;
    credenciado: {
      informacoes: Array<{ razao_social: string }>;
      endereco: { cidade: string };
    };
  };
  diferencaPercentual: string;
  dataDecisao: string;
  motivoPerda: string;
}

interface EstatisticasPerdidos {
  totalPerdidos: number;
  taxaPerda: number;
  diferencaMedia: number;
  valorTotalPerdido: number;
  principaisMotivos: Array<{ motivo: string; quantidade: number }>;
}

export function OrcamentosPerdidos() {
  const { session } = useSession();
  const [orcamentosPerdidos, setOrcamentosPerdidos] = useState<OrcamentoPerdido[]>([]);
  const [estatisticas, setEstatisticas] = useState<EstatisticasPerdidos | null>(null);
  const [loading, setLoading] = useState(false);
  const [showDetalhes, setShowDetalhes] = useState(false);
  const [orcamentoSelecionado, setOrcamentoSelecionado] = useState<OrcamentoPerdido | null>(null);
  const [comparacaoData, setComparacaoData] = useState<any>(null);
  const [loadingComparacao, setLoadingComparacao] = useState(false);

  // Verificar se o usuário é credenciado
  const isCredenciado = session?.roles.includes("ORCAMENTISTA_OFICINA");

  // Buscar orçamentos perdidos
  const fetchOrcamentosPerdidos = async () => {
    if (!isCredenciado) return;
    
    setLoading(true);
    try {
      const response = await fetch('/api/orcamentos/perdidos');
      if (response.ok) {
        const data = await response.json();
        setOrcamentosPerdidos(data.data?.orcamentos || []);
      } else {
        toast.error('Erro ao carregar orçamentos perdidos');
      }
    } catch (error) {
      console.error('Erro ao buscar orçamentos perdidos:', error);
      toast.error('Erro ao carregar orçamentos perdidos');
    } finally {
      setLoading(false);
    }
  };

  // Buscar estatísticas
  const fetchEstatisticas = async () => {
    if (!isCredenciado) return;
    
    try {
      const response = await fetch('/api/orcamentos/perdidos/estatisticas', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ periodo: '30d' }),
      });
      
      if (response.ok) {
        const data = await response.json();
        setEstatisticas(data.data);
      }
    } catch (error) {
      console.error('Erro ao buscar estatísticas:', error);
    }
  };

  // Buscar comparação detalhada
  const fetchComparacao = async (orcamentoId: string) => {
    setLoadingComparacao(true);
    try {
      const response = await fetch(`/api/orcamentos/comparacao/${orcamentoId}`);
      if (response.ok) {
        const data = await response.json();
        setComparacaoData(data.data);
      } else {
        toast.error('Erro ao carregar comparação');
      }
    } catch (error) {
      console.error('Erro ao buscar comparação:', error);
      toast.error('Erro ao carregar comparação');
    } finally {
      setLoadingComparacao(false);
    }
  };

  useEffect(() => {
    if (isCredenciado) {
      fetchOrcamentosPerdidos();
      fetchEstatisticas();
    }
  }, [isCredenciado]);

  // Definição das colunas
  const columns: ColumnDef<OrcamentoPerdido>[] = [
    {
      accessorKey: "ordemServico.osNumber",
      header: "Nº OS",
    },
    {
      accessorKey: "ordemServico.veiculo.placa",
      header: "Placa",
    },
    {
      accessorKey: "ordemServico.veiculo.marca.descricao",
      header: "Marca",
    },
    {
      accessorKey: "valor_total",
      header: "Meu Orçamento",
      cell: ({ row }) => {
        const valor = row.original.valor_total || 0;
        return valor.toLocaleString("pt-BR", {
          style: "currency",
          currency: "BRL",
        });
      },
    },
    {
      accessorKey: "orcamentoVencedor.valor_total",
      header: "Valor Vencedor",
      cell: ({ row }) => {
        const valor = row.original.orcamentoVencedor?.valor_total || 0;
        return valor.toLocaleString("pt-BR", {
          style: "currency",
          currency: "BRL",
        });
      },
    },
    {
      accessorKey: "diferencaPercentual",
      header: "Diferença %",
      cell: ({ row }) => {
        const diferenca = row.original.diferencaPercentual;
        if (!diferenca) return "N/A";
        
        const isPositive = parseFloat(diferenca) > 0;
        return (
          <Badge variant={isPositive ? "destructive" : "secondary"}>
            {isPositive ? "+" : ""}{diferenca}%
          </Badge>
        );
      },
    },
    {
      accessorKey: "dataDecisao",
      header: "Data Decisão",
      cell: ({ row }) => {
        const data = row.original.dataDecisao;
        if (!data) return "N/A";
        return format(new Date(data), "dd/MM/yyyy");
      },
    },
    {
      accessorKey: "motivoPerda",
      header: "Motivo",
      cell: ({ row }) => {
        const motivo = row.original.motivoPerda || "Não informado";
        return (
          <Badge variant="outline" className="max-w-[150px] truncate">
            {motivo}
          </Badge>
        );
      },
    },
    {
      id: "acoes",
      header: "Ações",
      cell: ({ row }) => (
        <Button
          size="sm"
          variant="outline"
          onClick={(e) => {
            e.stopPropagation(); // Evitar que o clique propague para a linha
            setOrcamentoSelecionado(row.original);
            fetchComparacao(row.original.id);
            setShowDetalhes(true);
          }}
        >
          <Eye className="h-4 w-4 mr-1" />
          Comparar
        </Button>
      ),
    },
  ];

  const handleRowClick = (orcamento: OrcamentoPerdido) => {
    setOrcamentoSelecionado(orcamento);
    fetchComparacao(orcamento.id);
    setShowDetalhes(true);
  };

  if (!isCredenciado) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">
          Esta funcionalidade está disponível apenas para credenciados.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Estatísticas */}
      {estatisticas && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <TrendingDown className="h-5 w-5 text-red-600" />
                <span className="text-sm font-medium text-red-800">Total Perdidos</span>
              </div>
              <p className="text-2xl font-bold text-red-900">{estatisticas.totalPerdidos}</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-orange-600" />
                <span className="text-sm font-medium text-orange-800">Taxa de Perda</span>
              </div>
              <p className="text-2xl font-bold text-orange-900">{estatisticas.taxaPerda}%</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-600" />
                <span className="text-sm font-medium text-blue-800">Diferença Média</span>
              </div>
              <p className="text-2xl font-bold text-blue-900">{estatisticas.diferencaMedia}%</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-gray-800">Valor Total Perdido</span>
              </div>
              <p className="text-2xl font-bold text-gray-900">
                {estatisticas.valorTotalPerdido?.toLocaleString("pt-BR", {
                  style: "currency",
                  currency: "BRL",
                })}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Tabela de Orçamentos Perdidos */}
      <Card>
        <CardHeader>
          <CardTitle>Orçamentos Perdidos</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            data={orcamentosPerdidos}
            columns={columns}
            exportTo={true}
            handleRowClick={handleRowClick}
            isLoading={loading}
          />
        </CardContent>
      </Card>

      {/* Modal de Detalhes e Comparação */}
      <Dialog open={showDetalhes} onOpenChange={setShowDetalhes}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              Comparação Detalhada - OS {orcamentoSelecionado?.ordemServico.osNumber}
            </DialogTitle>
          </DialogHeader>
          
          {loadingComparacao ? (
            <div className="text-center py-8">
              <p>Carregando dados de comparação...</p>
            </div>
          ) : comparacaoData ? (
            <div className="space-y-6">
              {/* Resumo da Comparação */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h3 className="font-medium text-red-800 mb-2">Seu Orçamento</h3>
                  <p className="text-2xl font-bold text-red-900">
                    {comparacaoData.orcamentoCredenciado?.valor_total?.toLocaleString("pt-BR", {
                      style: "currency",
                      currency: "BRL",
                    })}
                  </p>
                </div>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h3 className="font-medium text-green-800 mb-2">Orçamento Vencedor</h3>
                  <p className="text-2xl font-bold text-green-900">
                    {comparacaoData.orcamentoVencedor?.valor_total?.toLocaleString("pt-BR", {
                      style: "currency",
                      currency: "BRL",
                    })}
                  </p>
                  <p className="text-sm text-green-700 mt-1">
                    {comparacaoData.orcamentoVencedor?.credenciado?.informacoes[0]?.razao_social} - {comparacaoData.orcamentoVencedor?.credenciado?.endereco?.cidade}
                  </p>
                </div>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="font-medium text-blue-800 mb-2">Diferença</h3>
                  <p className="text-2xl font-bold text-blue-900">
                    {comparacaoData.analiseComparativa?.diferencaPercentual}%
                  </p>
                  <p className="text-sm text-blue-700 mt-1">
                    {(parseFloat(comparacaoData.analiseComparativa?.diferencaValor || 0)).toLocaleString("pt-BR", {
                      style: "currency",
                      currency: "BRL",
                    })}
                  </p>
                </div>
              </div>

              {/* Pontos de Melhoria */}
              {comparacaoData.analiseComparativa?.pontosMelhoria?.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Pontos de Melhoria</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {comparacaoData.analiseComparativa.pontosMelhoria.map((ponto: any, index: number) => (
                        <div key={index} className="border-l-4 border-orange-400 pl-4">
                          <div className="flex items-center gap-2 mb-1">
                            <Badge variant="outline">{ponto.categoria}</Badge>
                            <Badge variant={ponto.prioridade === "alta" ? "destructive" : "secondary"}>
                              {ponto.prioridade}
                            </Badge>
                          </div>
                          <p className="font-medium">{ponto.descricao}</p>
                          <p className="text-sm text-gray-600">{ponto.sugestao}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Comparação Detalhada de Itens */}
              {comparacaoData.itensComparados?.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Comparação de Itens</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-muted">
                            <th className="p-2 text-left">Item</th>
                            <th className="p-2 text-right">Seu Valor</th>
                            <th className="p-2 text-right">Valor Vencedor</th>
                            <th className="p-2 text-right">Diferença</th>
                          </tr>
                        </thead>
                        <tbody>
                          {comparacaoData.itensComparados.map((item: any, index: number) => (
                            <tr key={index} className="border-b">
                              <td className="p-2">{item.descricao}</td>
                              <td className="p-2 text-right">
                                {item.valorSeu.toLocaleString("pt-BR", {
                                  style: "currency",
                                  currency: "BRL",
                                })}
                              </td>
                              <td className="p-2 text-right">
                                {item.valorVencedor.toLocaleString("pt-BR", {
                                  style: "currency",
                                  currency: "BRL",
                                })}
                              </td>
                              <td className={`p-2 text-right ${item.diferenca > 0 ? 'text-red-500' : 'text-green-500'}`}>
                                {item.diferenca > 0 ? '+' : ''}
                                {item.diferenca.toLocaleString("pt-BR", {
                                  style: "currency",
                                  currency: "BRL",
                                })} ({item.diferencaPercentual}%)
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">Erro ao carregar dados de comparação</p>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
