import { Button } from "@/components/ui/button";
import { Edit, Trash2, Plus, Minus, Check } from "lucide-react";
import { useState, useEffect } from "react";

export interface ItemCardProps {
  id: string;
  tipo: "pecas" | "servico";
  descricao: string;
  codigo: string;
  marca: string;
  garantia: string;
  preco: number;
  precoUnitario: number;
  desconto?: number;
  quantidade: number;
  isNew?: boolean;
  onEdit: (updatedItem: Partial<ItemCardProps>) => void;
  onDelete: () => void;
  onQuantityChange: (quantity: number) => void;
  complementarStatus?: string;
  isComplementaryBudget?: boolean;
  onApprove?: () => void;
}

export function ItemCard({
  id,
  tipo,
  descricao,
  codigo,
  marca,
  garantia,
  preco,
  precoUnitario,
  quantidade,
  desconto = 0,
  isNew,
  onEdit,
  onDelete,
  onQuantityChange,
  complementarStatus,
  isComplementaryBudget,
  onApprove,
}: ItemCardProps) {
  const [quantityState, setQuantityState] = useState(quantidade);

  // Sincronizar o estado interno quando a prop quantidade mudar
  useEffect(() => {
    setQuantityState(quantidade);
  }, [quantidade]);

  const handleDecrease = () => {
    const newQuantity = Math.max(1, quantityState - 1);
    setQuantityState(newQuantity);
    onQuantityChange(newQuantity);
  };

  const handleIncrease = () => {
    const newQuantity = quantityState + 1;
    setQuantityState(newQuantity);
    onQuantityChange(newQuantity);
  };

  const handleEdit = () => {
    const updatedItem = {
      tipo,
      descricao,
      codigo,
      marca,
      garantia,
      preco,
      desconto,
      quantidade: quantityState,
    };
    onEdit(updatedItem);
  };
  const correctDesconto = precoUnitario * quantidade - preco;
  const formattedPrice = new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format((precoUnitario * quantidade) / 100);

  const formattedDesconto = new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(correctDesconto / 100);

  const total = new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(preco / 100);

  return (
    <div className="border rounded-lg p-4 mb-4">
      <div className="flex justify-between items-start mb-2">
        <div>
          <div className="flex items-center">
            <h3 className="text-lg font-medium">{descricao}</h3>
            <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
              {tipo === "pecas" ? "Peças" : "Serviços"}
            </span>
          </div>
          <p className="text-sm text-gray-500">
            Código: {codigo.toLocaleUpperCase()} | Marca: {marca} | Garantia: {garantia}
          </p>
        </div>
          <div className="text-right">
            <p className="font-medium">Preço:</p>
            <p className="text-lg font-bold">{formattedPrice}</p>
            <p className="text-sm text-gray-500">
              Desconto:
              <span className="font-bold text-white"> {formattedDesconto}</span>
            </p>
          </div>
      </div>

      <div className="flex justify-between items-center mt-4">
        {tipo !== "servico" && (
          <div className="flex items-center border rounded-md">
            <Button variant="ghost" size="sm" onClick={handleDecrease} className="h-9 px-2">
              <Minus className="h-4 w-4" />
            </Button>
            <div className="px-3 py-1">{quantityState}</div>
            <Button variant="ghost" size="sm" onClick={handleIncrease} className="h-9 px-2">
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        )}

        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={handleEdit} className="flex items-center">
            <Edit className="h-4 w-4 mr-1" /> Editar
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={onDelete}
            className="flex items-center text-red-600 border-red-200 hover:bg-red-50">
            <Trash2 className="h-4 w-4 mr-1" /> Excluir
          </Button>

          {isComplementaryBudget && isNew && (
            <Button
              variant="outline"
              size="sm"
              onClick={onApprove}
              className="flex items-center text-green-600 border-green-200 hover:border-green-50">
              <Check className="h-4 w-4 mr-1" /> Enviar para aprovação
            </Button>
          )}

          {complementarStatus && isNew && (
            <span
              className={`text-sm ${
                complementarStatus === "complementar_authorized" ? "text-green-500" : "text-red-500"
              }`}>
              {complementarStatus === "complementar_authorized"
                ? "Orçamento Complementar Autorizado"
                : "Orçamento Complementar Não Autorizado"}
            </span>
          )}
        </div>

        <div className="text-right">
          <span className="text-sm text-gray-500">Total: </span>
          <span className="font-bold">{total}</span>
        </div>
      </div>
    </div>
  );
}
