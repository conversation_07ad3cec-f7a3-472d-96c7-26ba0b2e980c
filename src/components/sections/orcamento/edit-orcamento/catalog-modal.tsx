import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Search,
  X,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Check,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  getAftermarketParts,
  getNicknames,
  getParts,
  getVehicleSets,
} from "@/serverActions/suivAction";
import { Part, VehicleInfoResponse } from "@/interfaces/suiv.interface";
import { getVehicleByPlateWithCache, getVehicleTokenWithCache } from "@/service/suivCache.service";

interface CatalogModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectPeca: (peca: any) => void;
  veiculoPlate?: string;
}
const formatSelectedPart = (part: Part) => {
  return {
    codigo: part.partNumber,
    descricao: part.description,
    marca: "Genuina",
    prazo_garantia: "",
    valor_unitario: part.price,
    valor_negociado: part.price,
    quantidade: "1",
    grupo: "",
  };
};

export function CatalogModal({ isOpen, onClose, onSelectPeca, veiculoPlate }: CatalogModalProps) {
  const [selectedGrupo, setSelectedGrupo] = useState<string | null>(null);
  const [selectedSubgrupo, setSelectedSubgrupo] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [isSearchModalOpen, setIsSearchModalOpen] = useState(false);
  const [vehicleData, setVehicleData] = useState<VehicleInfoResponse>();
  const [grupos, setGrupos] = useState<any[]>([]);
  const [subgrupos, setSubgrupos] = useState<any[]>([]);
  const [vehicleToken, setVehicleToken] = useState<any>(null);
  const [filteredItems, setFilteredItems] = useState<Part[]>([]);
  const [parts, setParts] = useState<Part[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      if (veiculoPlate) {
        const fetchVehicleData = await getVehicleByPlateWithCache(veiculoPlate);
        setVehicleData(fetchVehicleData);

        const token = await getVehicleTokenWithCache(
          fetchVehicleData.suivDataCollection[0]?.versionId,
          fetchVehicleData.yearModel
        );
        setVehicleToken(token);

        const vehicleSets = await getVehicleSets(token.token);
        setGrupos(vehicleSets);
      }
    };
    fetchData();
  }, [veiculoPlate]);

  const handleGrupoChange = async (value: string) => {
    setSelectedGrupo(value);
    setSelectedSubgrupo(null);

    if (vehicleToken && value) {
      const groupId = parseInt(value, 10);
      const vehicleNicknames = await getNicknames(vehicleToken.token, groupId);
      setSubgrupos(vehicleNicknames);
    }

    filterItems();
  };

  const itemsPerPage = 3;

  const filterItems = () => {
    let filtered = parts;

    if (searchTerm.trim()) {
      filtered = filtered.filter(
        (item) =>
          item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.partNumber.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredItems(filtered);
    setCurrentPage(1);
  };

  const handleSubgrupoChange = async (value: string) => {
    setSelectedSubgrupo(value);
    if (vehicleToken && value) {
      const nicknameId = parseInt(value, 10);
      const parts = await getParts(vehicleToken.token, nicknameId);
      setParts(parts);
      setFilteredItems(parts);
      console.log(parts);
    }
  };

  const handleSearch = () => {
    filterItems();
    setIsSearchModalOpen(false);
  };

  const handlePartSelection = (part: Part) => {
    const formattedPart = formatSelectedPart(part);
    onSelectPeca(formattedPart);
    onClose();
  };

  const totalPages = Math.ceil(filteredItems.length / itemsPerPage);
  const currentItems = filteredItems.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>Catálogo de peças</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Grupo</label>
              <Select value={selectedGrupo || ""} onValueChange={handleGrupoChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um grupo" />
                </SelectTrigger>
                <SelectContent>
                  {grupos.map((grupo) => (
                    <SelectItem key={grupo.id} value={grupo.id}>
                      {grupo.description}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium">Subgrupo</label>
              <Select
                value={selectedSubgrupo || ""}
                onValueChange={handleSubgrupoChange}
                disabled={!selectedGrupo}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um subgrupo" />
                </SelectTrigger>
                <SelectContent>
                  {subgrupos.map((subgrupo) => (
                    <SelectItem key={subgrupo.id} value={subgrupo.id}>
                      {subgrupo.description}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <div className="relative">
              <Input
                placeholder="Pesquisar peças..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                className="pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-full"
                onClick={handleSearch}>
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {selectedSubgrupo && (
            <div>
              <h3 className="text-lg font-medium mb-4">Peças encontradas</h3>
              <div className="space-y-4">
                {currentItems.length > 0 ? (
                  currentItems.map((item) => (
                    <div key={item.id} className="border-b pb-4 flex items-start gap-2">
                      <div className="flex-1">
                        <div className="font-medium">{item.description}</div>
                        <div className="text-sm">Marca: Genuina</div>
                        <div className="text-sm">Partnumber: {item.partNumber}</div>
                        {/* <div className="text-sm">Preço: R$ {item.price.toLocaleString("pt-BR", { style: "currency", currency: "BRL" })}</div> */}
                      </div>
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={() => handlePartSelection(item)}
                        className="mt-1">
                        <Check className="h-4 w-4 mr-1" />
                        Selecionar
                      </Button>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">Nenhuma peça encontrada</div>
                )}
              </div>

              {filteredItems.length > 0 && (
                <div className="flex justify-center gap-1 mt-4">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setCurrentPage(1)}
                    disabled={currentPage === 1}>
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}>
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  <span className="flex items-center justify-center w-8">{currentPage}</span>

                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}>
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setCurrentPage(totalPages)}
                    disabled={currentPage === totalPages}>
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
