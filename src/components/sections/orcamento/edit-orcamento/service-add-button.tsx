import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface ServiceAddButtonProps {
  veiculo: any;
  onAddPeca?: () => void;
  onAddServico?: () => void;
}

const ServiceAddButton = ({
  veiculo,
  onAddPeca,
  onAddServico,
}: ServiceAddButtonProps) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button className="bg-teal-500 hover:bg-teal-600">
          <Plus className="mr-2 h-4 w-4" />
          Adicionar
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuItem onClick={() => onAddPeca && onAddPeca()} className="cursor-pointer">
          Adicionar <PERSON>ças
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onAddServico && onAddServico()} className="cursor-pointer">
          Adicionar Serviço
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ServiceAddButton;
