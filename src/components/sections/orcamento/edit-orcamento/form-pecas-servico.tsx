"use client";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useEffect, useState, useCallback } from "react";

// Extend the Window interface to include servicesDescriptions
declare global {
  interface Window {
    servicesDescriptions?: string[];
  }
}
import { formatDateToBrazilian } from "@/utils/format";
import { CurrencyInput } from "@/components/inputs/currency-input";
import { Search, BarChart3 } from "lucide-react";
import { CatalogModal } from "./catalog-modal";
import { DateInput } from "@/components/inputs/date-input";
import { ComparacaoPrecos } from "./comparacao-precos";
import { Session } from "@/lib/auth/types";
import { getServerSession } from "@/lib/auth/server-session";
import {
  getDescontosContratoAction,
  DescontoContrato,
} from "@/serverActions/contratoAction";
import { useContrato } from "@/context/contrato-context";
import { toast } from "sonner";

// Remova o discriminatedUnion e use um schema único com refinamentos
const pecaServicoSchema = z
  .object({
    tipo: z.enum(["pecas", "servico"]), // Agora obrigatório para controle do tipo

    // Campos comuns
    grupo: z.string().min(1, "Grupo é obrigatório"),
    descricao: z.string().min(1, "Descrição é obrigatória"),
    prazo_garantia: z.string().min(1, "Prazo de garantia é obrigatória"),

    // Campos de peças
    codigo: z.string().optional(),
    marca: z.string().optional(),
    valor_unitario: z.number().optional(),
    quantidade: z.string().min(1, "Quantidade é obrigatória"),
    tipoPecas: z.string().optional(),
    valor_negociado: z.number().optional(),

    // Campos de serviço
    valor: z.number().optional(),
    tipoServico: z.string().optional(),
    quantidadeHoras: z.string().optional(),

    // Outros campos opcionais
    horas: z.string().optional(),
    minutos: z.string().optional(),
    valor_hora: z.number().optional(),
    valorAutorizado: z.number().optional(),
  })
  .superRefine((data, ctx) => {
    if (data.tipo === "servico") {
      if (data.valor === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Valor é obrigatório",
          path: ["valor"],
        });
      }
      if (!data.tipoServico || data.tipoServico.trim() === "") {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Tipo de serviço é obrigatório",
          path: ["tipoServico"],
        });
      }
      if (!data.quantidadeHoras || data.quantidadeHoras.trim() === "") {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Quantidade de horas é obrigatória",
          path: ["quantidadeHoras"],
        });
      }
    }

    if (data.tipo === "pecas") {
      if (!data.marca || data.marca.trim() === "") {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Marca é obrigatória",
          path: ["marca"],
        });
      }
      if (data.valor_unitario === undefined || data.valor_unitario <= 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Valor unitário é obrigatório e deve ser maior que 0",
          path: ["valor_unitario"],
        });
      }
    }
  });

// Tipo derivado do schema
export type PecaServicoFormData = z.infer<typeof pecaServicoSchema>;

interface FormPecaServicoProps {
  tipo: "pecas" | "servico";
  onSubmit: (data: PecaServicoFormData) => void;
  initialValue?: Partial<PecaServicoFormData>;
  veiculo?: veiculo;
  isEditing?: boolean;
}

export function FormPecaServico({
  tipo,
  onSubmit,
  initialValue,
  veiculo,
  isEditing = false,
}: FormPecaServicoProps) {
  const { contratos } = useContrato();
  const [isCatalogModalOpen, setIsCatalogModalOpen] = useState(false);
  const [selectedPart, setSelectedPart] = useState<any>(null);
  const [sessionData, setSessionData] = useState<Session>();
  const [descontos, setDescontos] = useState<ItemContrato[]>([]);
  const [valorAutorizado, setValorAutorizado] = useState<number>(0);
  const [valorBruto, setValorBruto] = useState<number>(0);
  const [descontoAplicado, setDescontoAplicado] = useState<number | null>(null);
  const [currentContrato, setCurrentContrato] = useState<contrato | null>(null);
  const [precoMaximoDefinido, setPrecoMaximoDefinido] = useState<number | null>(
    null
  );
  const [excedePrecoMaximo, setExcedePrecoMaximo] = useState(false);

  useEffect(() => {
    if (descontos.length > 0) {
      // Armazenar descrições de serviços para uso pelo schema de validação
      const servicosDescriptions = descontos
        .filter((d) => d.tipoItem === "Serviços")
        .map((d) => d.descricao);

      // Armazenar em uma variável global para uso pelo refinamento
      window.servicesDescriptions = servicosDescriptions;
    }
  }, [descontos]);

  useEffect(() => {
    async function fetchSessionData() {
      const sessionData = await getServerSession();
      setSessionData(sessionData);

      if (sessionData?.contratoId) {
        try {
          const contrato = contratos.find(
            (c) => c.id === sessionData.contratoId
          );
          if (contrato) {
            setCurrentContrato(contrato);
          }

          const result = await getDescontosContratoAction(
            sessionData.contratoId
          );
          if (result.success && result.data && result.data.descontos) {
            setDescontos(result.data.descontos);
          }
        } catch (error) {
          console.error("Erro ao buscar descontos:", error);
        }
      }
    }
    fetchSessionData();
  }, [contratos]);

  const form = useForm<PecaServicoFormData>({
    resolver: zodResolver(pecaServicoSchema),
    defaultValues: initialValue || {
      tipo,
      codigo: "",
      descricao: "",
      marca: "",
      prazo_garantia: "",
      quantidade: "1",
      valor_unitario: 0,
      valor: tipo === "servico" ? 0 : undefined,
      tipoServico: tipo === "servico" ? "" : undefined,
      tipoPecas: tipo === "pecas" ? "" : undefined,
      quantidadeHoras: tipo === "servico" ? "1" : undefined,
      horas: tipo === "servico" ? "1" : undefined,
      minutos: tipo === "servico" ? "0" : undefined,
      valor_negociado: tipo === "pecas" ? 0 : undefined,
      valor_hora: 0,
      grupo: "",
    },
  });

  useEffect(() => {
    // Define um valor padrão para grupo assim que os descontos forem carregados
    if ((descontos.length > 0 && !form.getValues("grupo")) || "") {
      // Filtra os descontos pelo tipo apropriado
      const descontosFiltrados = descontos.filter(
        (d) => d.tipoItem === (tipo === "pecas" ? "Peças" : "Serviços")
      );

      if (descontosFiltrados.length > 0) {
        // Usa o primeiro desconto disponível como valor padrão
        form.setValue("grupo", descontosFiltrados[0].descricao);

        // Recalcula o valor autorizado se necessário
        if (veiculo?.tiposVeiculos?.descricao) {
          if (tipo === "pecas") {
            const valorUnitario = form.getValues("valor_unitario") || 0;
            const quantidade = parseFloat(form.getValues("quantidade") || "1");
            calcularValorAutorizado(
              valorUnitario,
              "Peças",
              veiculo.tiposVeiculos.descricao,
              quantidade,
              descontosFiltrados[0].descricao
            );
          } else {
            const valor = form.getValues("valor") || 0;
            const quantidadeHoras = parseFloat(
              form.getValues("quantidadeHoras") || "1"
            );
            calcularValorAutorizado(
              valor,
              "Serviços",
              veiculo.tiposVeiculos.descricao,
              quantidadeHoras,
              descontosFiltrados[0].descricao
            );
          }
        }
      }
    }
  }, [descontos, tipo, form, veiculo?.tiposVeiculos?.descricao]);

  useEffect(() => {
    if (initialValue) {
      const formattedValues = { ...initialValue };

      // Formatar data de garantia se necessário
      if (
        formattedValues.prazo_garantia &&
        typeof formattedValues.prazo_garantia === "string"
      ) {
        try {
          if (
            formattedValues.prazo_garantia.includes("T") ||
            formattedValues.prazo_garantia.includes("Z") ||
            formattedValues.prazo_garantia.includes("/")
          ) {
            let date;

            if (formattedValues.prazo_garantia.includes("/")) {
              const parts = formattedValues.prazo_garantia.split("/");
              if (parts.length === 3) {
                const day = parts[0].split("T")[0];
                const month = parts[1];
                const year = parts[2];

                formattedValues.prazo_garantia = `${day.padStart(
                  2,
                  "0"
                )}/${month.padStart(2, "0")}/${year}`;
              }
            } else {
              date = new Date(formattedValues.prazo_garantia);
              if (!isNaN(date.getTime())) {
                formattedValues.prazo_garantia = `${String(
                  date.getDate()
                ).padStart(2, "0")}/${String(date.getMonth() + 1).padStart(
                  2,
                  "0"
                )}/${date.getFullYear()}`;
              }
            }
          }
        } catch (e) {
          console.error("Erro ao formatar data:", e);
        }
      }

      // Converter quantidadeHoras para horas e minutos
      if (tipo === "servico" && formattedValues.quantidadeHoras) {
        const horasDecimal = parseFloat(formattedValues.quantidadeHoras);
        const horasInteiras = Math.floor(horasDecimal);
        const minutos = Math.round((horasDecimal - horasInteiras) * 60);

        formattedValues.horas = horasInteiras.toString();
        formattedValues.minutos = minutos.toString();
      }

      form.reset(formattedValues);
      form.trigger();
    }
  }, [initialValue, form, tipo]);

  useEffect(() => {
    if (selectedPart) {
      form.setValue("codigo", selectedPart.codigo || "");
      form.setValue("descricao", selectedPart.descricao || "");
      form.setValue("marca", selectedPart.marca || "");
      form.setValue("quantidade", "1");

      if (selectedPart.prazo_garantia) {
        const formattedDate = formatDateToBrazilian(
          selectedPart.prazo_garantia
        );
        form.setValue("prazo_garantia", formattedDate);
      }
    }
  }, [selectedPart, form]);

  const encontrarDesconto = useCallback(
    (
      descricaoItem: string,
      tipoItem: string,
      tipoVeiculo: string
    ): { desconto: number | null; precoMaximo: number | null } => {
      const descontoAdmin = currentContrato?.taxa_admin ?? null;
      const descontoEncontrado = descontos.find(
        (d) =>
          d.tipoItem.toLowerCase() === tipoItem.toLowerCase() &&
          d.tipoVeiculo.toLowerCase() === tipoVeiculo.toLowerCase() &&
          d.descricao.toLowerCase().includes(descricaoItem.toLowerCase())
      );
      if (descontoEncontrado) {
        return {
          desconto: descontoEncontrado.desconto,
          precoMaximo: descontoEncontrado.precoMaximo ?? null,
        };
      }

      return { desconto: descontoAdmin, precoMaximo: null };
    },
    [descontos, currentContrato]
  );

  const verificaSeExcedePrecoMaximo = useCallback(
    (total: number, precoMaximoAtual: number | null = null) => {
      const precoMaximoEfetivo =
        precoMaximoAtual !== null ? precoMaximoAtual : precoMaximoDefinido;

      console.log("Total:", total);
      if (precoMaximoEfetivo !== null) {
        console.log("Preço máximo definido (efetivo):", precoMaximoEfetivo);
        setExcedePrecoMaximo(total > precoMaximoEfetivo * 100);
      } else {
        setExcedePrecoMaximo(false);
      }
    },
    [precoMaximoDefinido]
  );

  const calcularValorAutorizado = useCallback(
    (
      valor: number,
      tipoItem: string,
      tipoVeiculo: string,
      quantidade: number = 1,
      descricaoItem: string
    ) => {
      const { desconto: descontoPercentual, precoMaximo } = encontrarDesconto(
        descricaoItem,
        tipoItem,
        tipoVeiculo
      );
      console.log("Preço máximo encontrado:", precoMaximo);
      setDescontoAplicado(descontoPercentual);
      setPrecoMaximoDefinido(precoMaximo);

      let valorComDesconto = valor;
      if (descontoPercentual !== null) {
        valorComDesconto = Math.trunc(
          valor - (valor * descontoPercentual) / 100
        );
      }
      // Multiplica pelo valor da quantidade/horas
      const valorAutorizadoTotal = Math.trunc(valorComDesconto * quantidade);
      setValorAutorizado(valorAutorizadoTotal);

      setValorBruto(Math.trunc(valor * quantidade));

      // Passa o precoMaximo atualizado diretamente para a verificação
      verificaSeExcedePrecoMaximo(valor, precoMaximo);
    },
    [encontrarDesconto, verificaSeExcedePrecoMaximo]
  );

  useEffect(() => {
    const subscription = form.watch((_, { name }) => {
      const tipoVeiculo = veiculo?.tiposVeiculos?.descricao || "";

      if (tipo === "pecas") {
        const valorUnitario = form.getValues("valor_unitario") || 0;
        const quantidade = parseFloat(form.getValues("quantidade") || "1");
        calcularValorAutorizado(
          valorUnitario,
          "Peças",
          tipoVeiculo,
          quantidade,
          form.getValues("grupo") || ""
        );
      } else if (tipo === "servico") {
        const valor = form.getValues("valor") || 0;
        const quantidadeHoras = parseFloat(
          form.getValues("quantidadeHoras") || "1"
        );
        calcularValorAutorizado(
          valor,
          "Serviços",
          tipoVeiculo,
          quantidadeHoras,
          form.getValues("grupo") || ""
        );
      }
    });

    return () => subscription.unsubscribe();
  }, [form, tipo, veiculo, calcularValorAutorizado]);

  const handleSubmit = (data: PecaServicoFormData) => {
    console.log("Dados do formulário:", data);
    const { total } = calculateTotal();

    if (precoMaximoDefinido !== null && total > precoMaximoDefinido * 100) {
      toast.error(
        `Valor total excede o preço máximo permitido de ${new Intl.NumberFormat(
          "pt-BR",
          {
            style: "currency",
            currency: "BRL",
          }
        ).format(precoMaximoDefinido)} por unidade`
      );
      return;
    }

    // Criar uma cópia dos dados para adicionar propriedades
    const newData = { ...data };

    // Adicionar valorAutorizado para todos os tipos, independente do grupo
    (newData as any).valorAutorizado = valorAutorizado;

    if (tipo === "servico") {
      const horas = parseInt(form.getValues("horas") || "0");
      const minutos = parseInt(form.getValues("minutos") || "0");
      const horasDecimal = horas + minutos / 60;
      data.quantidadeHoras = horasDecimal.toString();
    } else if (tipo === "pecas") {
      // Para peças, manter compatibilidade com valor_negociado
      (newData as any).valor_negociado = valorAutorizado;
    }

    onSubmit(newData);
    form.reset();
    setSelectedPart(null);
  };

  const calculateTotal = () => {
    // O valor autorizado já está calculado considerando a quantidade
    let total = 0;
    let desconto = 0;

    if (tipo === "pecas") {
      const valorUnitario = form.watch("valor_unitario") || 0;
      const quantidade = parseFloat(form.watch("quantidade") || "1");
      desconto = (valorUnitario * quantidade - valorAutorizado) / 100;
      total = valorUnitario;
    } else if (tipo === "servico") {
      const valor = form.watch("valor") || 0;
      const quantidadeHoras = parseFloat(form.watch("quantidadeHoras") || "1");
      desconto = (valor * quantidadeHoras - valorAutorizado) / 100;
      total = valor;
    }

    return {
      total,
      desconto,
    };
  };
  console.log("excedePrecoMaximo", excedePrecoMaximo);
  console.log("form errors", form.formState.errors);
  return (
    <Form {...form}>
      <div className="flex items-center gap-2 ">
        {tipo === "pecas" && (
          <>
            <Button
              variant="outline"
              size="sm"
              className="text-sm gap-1 h-8 rounded"
              onClick={() => setIsCatalogModalOpen(true)}>
              <Search className="h-3.5 w-3.5" />
              Catálogo
            </Button>
            <CatalogModal
              veiculoPlate={veiculo?.placa}
              isOpen={isCatalogModalOpen}
              onClose={() => setIsCatalogModalOpen(false)}
              onSelectPeca={(pecas) => {
                setSelectedPart(pecas);
                setIsCatalogModalOpen(false);
              }}
            />
          </>
        )}
      </div>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit(handleSubmit)(e);
          console.log("Tentou submeter!", form.getValues());
        }}
        className="space-y-4 mt-4">
        <FormField
          control={form.control}
          name="grupo"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Grupo *</FormLabel>
              {tipo === "pecas" ? (
                <Select onValueChange={field.onChange} value={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione um grupo" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.values(
                      descontos
                        .filter((desconto) => desconto.tipoItem === "Peças")
                        .reduce((acc, desconto) => {
                          if (!acc[desconto.descricao]) {
                            acc[desconto.descricao] = desconto;
                          }
                          return acc;
                        }, {} as Record<string, ItemContrato>)
                    ).length > 0 ? (
                      Object.values(
                        descontos
                          .filter((desconto) => desconto.tipoItem === "Peças")
                          .reduce((acc, desconto) => {
                            if (!acc[desconto.descricao]) {
                              acc[desconto.descricao] = desconto;
                            }
                            return acc;
                          }, {} as Record<string, ItemContrato>)
                      ).map((desconto: ItemContrato) => (
                        <SelectItem key={desconto.id} value={desconto.descricao}>
                          {desconto.descricao}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="Peças">Peças</SelectItem>
                    )}
                  </SelectContent>
                </Select>
              ) : (
                <Select onValueChange={field.onChange} value={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Mão de Obra" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.values(
                      descontos
                        .filter((desconto) => desconto.tipoItem === "Serviços")
                        .reduce((acc, desconto) => {
                          if (!acc[desconto.descricao]) {
                            acc[desconto.descricao] = desconto;
                          }
                          return acc;
                        }, {} as Record<string, ItemContrato>)
                    ).length > 0 ? (
                      Object.values(
                        descontos
                          .filter((desconto) => desconto.tipoItem === "Serviços")
                          .reduce((acc, desconto) => {
                            if (!acc[desconto.descricao]) {
                              acc[desconto.descricao] = desconto;
                            }
                            return acc;
                          }, {} as Record<string, ItemContrato>)
                      ).map((desconto: ItemContrato) => (
                        <SelectItem key={desconto.id} value={desconto.descricao}>
                          {desconto.descricao}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="Mão de Obra">Mão de Obra</SelectItem>
                    )}
                  </SelectContent>
                </Select>
              )}
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          {tipo === "pecas" && (
            <FormField
              control={form.control}
              name="codigo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Código</FormLabel>
                  <FormControl>
                    <Input placeholder="CB700-000B" {...field} className="uppercase" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          <FormField
            control={form.control}
            name="descricao"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Descrição *</FormLabel>
                <FormControl>
                  <Input placeholder="Digite a descrição" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {tipo === "servico" && (
            <FormField
              control={form.control}
              name="tipoServico"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tipo de Serviço *</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o tipo de serviço" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="mecanica">Mecânica</SelectItem>
                      <SelectItem value="eletrica">Elétrica</SelectItem>
                      <SelectItem value="funilaria">Funilaria</SelectItem>
                      <SelectItem value="pintura">Pintura</SelectItem>
                      <SelectItem value="outro">Outro</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>

        <div className="grid grid-cols-2 gap-4">
          {tipo === "pecas" ? (
            <FormField
              control={form.control}
              name="marca"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Marca *</FormLabel>
                  <FormControl>
                    <Input placeholder="Bosch" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : (
            <FormField
              control={form.control}
              name="valor"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Valor *</FormLabel>
                  <FormControl>
                    <CurrencyInput
                      value={field.value}
                      onChange={(value) => {
                        field.onChange(value);
                        if (veiculo?.tiposVeiculos?.descricao) {
                          const quantidadeHoras = parseFloat(form.watch("quantidadeHoras") || "1");
                          const descricao = form.watch("grupo") || "" || "";
                          calcularValorAutorizado(
                            value || 0,
                            "Serviços",
                            veiculo.tiposVeiculos.descricao,
                            quantidadeHoras,
                            descricao
                          );
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {tipo === "servico" ? (
            <div className="space-y-2">
              <FormLabel>Quantidade de Horas *</FormLabel>
              <div className="flex items-center gap-2">
                <div className="flex-1">
                  <FormField
                    control={form.control}
                    name="horas"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            step="1"
                            placeholder="Horas"
                            {...field}
                            onChange={(e) => {
                              field.onChange(e.target.value);
                              // Calcula o total de horas decimais
                              const horas = parseInt(e.target.value || "0");
                              const minutos = parseInt(form.watch("minutos") || "0");
                              const horasDecimal = horas + minutos / 60;

                              // Atualiza o campo quantidadeHoras que será usado para submissão
                              form.setValue("quantidadeHoras", horasDecimal.toString());

                              // if (veiculo?.tiposVeiculos?.descricao) {
                              //   const valor = form.watch("valor") || 0;
                              //   calcularValorAutorizado(
                              //     valor,
                              //     "Serviços",
                              //     veiculo.tiposVeiculos.descricao,
                              //     horasDecimal,
                              //     form.watch("descricao")
                              //   );
                              // }
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <span className="text-sm font-medium">:</span>
                <div className="flex-1">
                  <FormField
                    control={form.control}
                    name="minutos"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            max="59"
                            step="1"
                            placeholder="Minutos"
                            {...field}
                            onChange={(e) => {
                              // Limita os minutos entre 0-59
                              let value = parseInt(e.target.value || "0");
                              if (value < 0) value = 0;
                              if (value > 59) value = 59;

                              field.onChange(value.toString());

                              // Calcula o total de horas decimais
                              const horas = parseInt(form.watch("horas") || "0");
                              const horasDecimal = horas + value / 60;

                              // Atualiza o campo quantidadeHoras que será usado para submissão
                              form.setValue("quantidadeHoras", horasDecimal.toString());

                              // if (veiculo?.tiposVeiculos?.descricao) {
                              //   const valor = form.watch("valor") || 0;
                              //   calcularValorAutorizado(
                              //     valor,
                              //     "Serviços",
                              //     veiculo.tiposVeiculos.descricao,
                              //     horasDecimal,
                              //     form.watch("grupo") || ""
                              //   );
                              // }
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <FormField
                control={form.control}
                name="quantidadeHoras"
                render={({ field }) => <input type="hidden" {...field} />}
              />

              <FormMessage />
              <p className="text-xs text-muted-foreground mt-1">
                Digite as horas e minutos separadamente
              </p>
            </div>
          ) : (
            <FormField
              control={form.control}
              name="prazo_garantia"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Prazo de garantia *</FormLabel>
                  <FormControl>
                    <DateInput
                      value={field.value}
                      onChange={(value) => {
                        if (value && value.includes("-")) {
                          const [year, month, day] = value.split("-");
                          if (year && month && day) {
                            field.onChange(`${day}/${month}/${year}`);
                            return;
                          }
                        }
                        field.onChange(value);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>

        {tipo === "servico" && (
          <div className="grid grid-cols-1">
            <FormField
              control={form.control}
              name="prazo_garantia"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Prazo de garantia</FormLabel>
                  <FormControl>
                    <DateInput
                      value={field.value}
                      onChange={(value) => {
                        if (value && value.includes("-")) {
                          const [year, month, day] = value.split("-");
                          if (year && month && day) {
                            field.onChange(`${day}/${month}/${year}`);
                            return;
                          }
                        }
                        field.onChange(value);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        {tipo === "pecas" && (
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="valor_unitario"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Valor unitário *</FormLabel>
                  <FormControl>
                    <CurrencyInput
                      value={field.value}
                      onChange={(value) => {
                        field.onChange(value);
                        if (veiculo?.tiposVeiculos?.descricao) {
                          const quantidade = parseFloat(form.watch("quantidade") || "1");
                          calcularValorAutorizado(
                            value || 0,
                            "Peças",
                            veiculo.tiposVeiculos.descricao,
                            quantidade,
                            form.watch("grupo") || ""
                          );
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="quantidade"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Quantidade *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="1,00"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        if (veiculo?.tiposVeiculos?.descricao) {
                          const valorUnitario = form.watch("valor_unitario") || 0;
                          const quantidade = parseFloat(e.target.value || "1");
                          calcularValorAutorizado(
                            valorUnitario,
                            "Peças",
                            veiculo.tiposVeiculos.descricao,
                            quantidade,
                            form.watch("grupo") || ""
                          );
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        {tipo === "pecas" && (
          <ComparacaoPrecos
            codigo={form.watch("codigo")}
            descricao={form.watch("descricao")}
            precoAtual={form.watch("valor_unitario")}
            onPrecoSugerido={(preco) => {
              form.setValue("valor_unitario", preco);
              if (veiculo?.tiposVeiculos?.descricao) {
                const quantidade = parseFloat(form.watch("quantidade") || "1");
                calcularValorAutorizado(
                  preco,
                  "Peças",
                  veiculo.tiposVeiculos.descricao,
                  quantidade,
                  form.watch("grupo") || ""
                );
              }
            }}
          />
        )}

        <div className="grid grid-cols-1">
          <FormItem>
            <FormLabel>Valor Total Bruto</FormLabel>
            <FormControl>
              <div className="flex items-center">
                <CurrencyInput
                  value={valorBruto}
                  onChange={() => {}}
                  disabled
                  className="bg-gray-100 text-black"
                />
              </div>
            </FormControl>
          </FormItem>
        </div>

        <div className="grid grid-cols-1">
          <FormItem>
            <FormLabel>Valor Autorizado (Total)</FormLabel>
            <FormControl>
              <div className="flex items-center">
                <CurrencyInput
                  value={valorAutorizado}
                  onChange={() => {}}
                  disabled
                  className="bg-gray-100 text-black"
                />
                {descontoAplicado !== null && (
                  <span className="ml-2 text-sm text-green-600">
                    (Desconto: {descontoAplicado}%)
                  </span>
                )}
              </div>
            </FormControl>
            <p className="text-sm text-muted-foreground">
              {descontoAplicado !== null
                ? `Valor total com desconto contratual aplicado.`
                : `Não há desconto contratual para este tipo de item/veículo.`}
            </p>
          </FormItem>
        </div>

        {precoMaximoDefinido !== null && (
          <div
            className={`text-sm p-2 rounded ${
              excedePrecoMaximo ? "bg-red-100 text-red-800" : "bg-blue-50 text-gray-600"
            }`}>
            Preço máximo permitido por unidade:{" "}
            {new Intl.NumberFormat("pt-BR", {
              style: "currency",
              currency: "BRL",
            }).format(precoMaximoDefinido)}
            {excedePrecoMaximo && <span className="font-bold ml-2">(Limite excedido!)</span>}
          </div>
        )}

        <div className="flex justify-end pt-4">
          <Button
            type="submit"
            className={`${
              excedePrecoMaximo ? "bg-gray-400" : "bg-green-500 hover:bg-green-600"
            } text-white`}
            disabled={excedePrecoMaximo || !form.watch("grupo")}>
            {isEditing ? "Salvar" : "Adicionar"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
