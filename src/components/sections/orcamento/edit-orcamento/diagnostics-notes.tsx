import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { DateInput } from "@/components/inputs/date-input";

interface DiagnosticData {
  observacoes: string;
  previsaoEntrega: number;
  validade: string;
}

interface DiagnosticNotesProps {
  diagnosticoData: DiagnosticData;
  handleDiagnosticoChange: (
    field: keyof DiagnosticData,
    value: string | number
  ) => void;
}

const DiagnosticNotes = ({
  diagnosticoData,
  handleDiagnosticoChange,
}: DiagnosticNotesProps) => {
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">Informações Gerais</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="observacoes">Observações</Label>
          <Textarea
            id="observacoes"
            placeholder="Digite observações sobre o diagnóstico..."
            value={diagnosticoData.observacoes}
            onChange={(e) =>
              handleDiagnosticoChange("observacoes", e.target.value)
            }
            className="min-h-[100px]"
          />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="previsaoEntrega">
              Previsão de Entrega (dias) *
            </Label>
            <Input
              id="previsaoEntrega"
              type="number"
              min={1}
              value={diagnosticoData.previsaoEntrega}
              onChange={(e) =>
                handleDiagnosticoChange(
                  "previsaoEntrega",
                  parseInt(e.target.value)
                )
              }
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="validade">Validade do Orçamento *</Label>
            <DateInput
              id="validade"
              value={diagnosticoData.validade}
              onChange={(value) => {
                if (value && value.includes("-")) {
                  const [year, month, day] = value.split("-");
                  if (year && month && day) {
                    handleDiagnosticoChange(
                      "validade",
                      `${day}/${month}/${year}`
                    );
                    return;
                  }
                }
                handleDiagnosticoChange("validade", value);
              }}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DiagnosticNotes;
