import { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { X, Search } from "lucide-react";
import { FichaTecnicaItem } from "@/interfaces/edit-orcamento/edit-orcamento.interface";

interface TechnicalInfoProps {
  fichaTecnicaOpen: boolean;
  setFichaTecnicaOpen: (open: boolean) => void;
}

export default function TechnicalInfo({
  fichaTecnicaOpen,
  setFichaTecnicaOpen,
}: TechnicalInfoProps) {
  // Adicionar estados para a ficha técnica
  const [fichaTecnicaData, setFichaTecnicaData] = useState({
    veiculo: {
      placa: "BBB-0221",
      modelo: "HLX 2.0 20v Gasolina Mec.",
      marca: "Fiat",
      ano: "1998 ⇒ 2002",
    },
    itens: [
      { id: "1", descricao: "Capacidade do tanque", valor: "60 litros" },
      { id: "2", descricao: "Potência", valor: "142 cv" },
      { id: "3", descricao: "Torque", valor: "18,5 kgfm" },
      { id: "4", descricao: "Cilindrada", valor: "1998 cm³" },
      { id: "5", descricao: "Válvulas por cilindro", valor: "5" },
      { id: "6", descricao: "Configuração do motor", valor: "4 cilindros em linha" },
      { id: "7", descricao: "Transmissão", valor: "Manual de 5 marchas" },
      { id: "8", descricao: "Tração", valor: "Dianteira" },
    ],
  });

  const [searchTerm, setSearchTerm] = useState("");
  const [filteredItems, setFilteredItems] = useState<FichaTecnicaItem[]>(fichaTecnicaData.itens);

  // Função para filtrar itens
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    const filtered = fichaTecnicaData.itens.filter(
      (item) =>
        item.descricao.toLowerCase().includes(value.toLowerCase()) ||
        item.valor.toLowerCase().includes(value.toLowerCase())
    );
    setFilteredItems(filtered);
  };

  // Resetar a pesquisa quando o modal abrir
  useEffect(() => {
    if (fichaTecnicaOpen) {
      setSearchTerm("");
      setFilteredItems(fichaTecnicaData.itens);
    }
  }, [fichaTecnicaOpen, fichaTecnicaData.itens]);

  return (
    <Dialog open={fichaTecnicaOpen} onOpenChange={setFichaTecnicaOpen}>
      <DialogContent className="max-w-[800px] p-0">
        <DialogHeader className="p-6 pb-2">
          <DialogTitle>Ficha técnica do veículo</DialogTitle>
          <DialogDescription>
            Informações referentes ao manual técnico do veículo.
          </DialogDescription>
        </DialogHeader>

        <div className="p-6 pt-2">
          <div className="flex flex-col space-y-4">
            {/* Campo de busca */}
            <div className="flex items-center space-x-2 border rounded p-2">
              <Input
                placeholder="Busca rápida"
                className="border-0 "
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
              />
              <Button variant="ghost" size="icon" onClick={() => handleSearch("")}>
                {searchTerm ? <X className="h-4 w-4" /> : <Search className="h-4 w-4" />}
              </Button>
            </div>

            {/* Informações do veículo */}
            <div className="bg-gray-50 p-4 rounded-md">
              <p className="text-sm text-gray-600">
                {`${fichaTecnicaData.veiculo.placa} | ${fichaTecnicaData.veiculo.marca} | ${fichaTecnicaData.veiculo.modelo} (${fichaTecnicaData.veiculo.ano}) `}
              </p>
            </div>

            {/* Tabela de descrição e valor */}
            <div className="border rounded-md">
              <div className="grid grid-cols-2 gap-4 p-4 font-medium text-sm">
                <div>Descrição</div>
                <div>Valor</div>
              </div>
              <div className="divide-y">
                {filteredItems.length > 0 ? (
                  filteredItems.map((item) => (
                    <div key={item.id} className="grid grid-cols-2 gap-4 p-4 hover:">
                      <div className="text-sm 0">{item.descricao}</div>
                      <div className="text-sm font-medium">{item.valor}</div>
                    </div>
                  ))
                ) : (
                  <div className="p-4 text-sm  text-center">
                    {searchTerm ? "Nenhum resultado encontrado" : "Nenhum item disponível"}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-end gap-2 p-6 pt-0">
          <Button
            variant="outline"
            onClick={() => {
              setSearchTerm("");
              setFilteredItems(fichaTecnicaData.itens);
              setFichaTecnicaOpen(false);
            }}>
            Fechar
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
