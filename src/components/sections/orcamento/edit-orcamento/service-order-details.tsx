import { <PERSON><PERSON>ron<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, CircleAlert } from "lucide-react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { BsCircleFill, BsCheckCircleFill } from "react-icons/bs";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface ServiceOrderDetailsProps {
  expanded: boolean;
  setExpanded: (expanded: boolean) => void;
  os: OS | undefined;
  setFichaTecnicaOpen: (open: boolean) => void;
  isOs?: boolean;
}

const tabs = ["Ordem de serviço", "Veículo"];

const workflowSteps = [
  { key: "lançada", label: "Lançadas" },
  { key: "orcamentaçao", label: "Orçamentação" },
  { key: "analise", label: "Análise e Aprovação" },
  { key: "autorizada", label: "Autorizadas" },
  { key: "execucao", label: "Em Execução" },
  { key: "finalizada", label: "Aguardando Nota Fiscal" },
  { key: "faturada", label: "Faturadas" },
  { key: "cancelada", label: "Canceladas" },
];

export default function ServiceOrderDetails({
  expanded,
  setExpanded,
  os,
  setFichaTecnicaOpen,
  isOs = true,
}: ServiceOrderDetailsProps) {
  const toggleExpanded = () => setExpanded(!expanded);
  const currentStatus = os?.status ?? "";
  const currentStepIndex = workflowSteps.findIndex((step) => step.key === currentStatus);
  
  // Helper function to format date
  const formatDate = (date: Date | string | undefined) => {
    if (!date) return "";
    return new Date(date).toLocaleDateString("pt-BR");
  };
  
  // Helper function to calculate hours difference between dates
  const getHoursSince = (date: Date | string | undefined) => {
    if (!date) return 0;
    const start = new Date(date).getTime();
    const now = new Date().getTime();
    return Math.round((now - start) / (1000 * 60 * 60));
  };
  
  // Calculate remaining time from 72 hours
  const getRemainingHours = (hoursUsed: number) => {
    const remaining = 72 - hoursUsed;
    return remaining > 0 ? remaining : 0;
  };

  // Check if still within time limit
  const isWithinTimeLimit = (hoursUsed: number) => hoursUsed <= 72;
  
  // Calculate hours used in current step
  const hoursUsedInCurrentStep = os?.upDateTimedAt ? getHoursSince(os.upDateTimedAt) : 0;
  const remainingHours = getRemainingHours(hoursUsedInCurrentStep);
  const isCurrentlyWithinLimit = isWithinTimeLimit(hoursUsedInCurrentStep);
  return (
    <>
      {/* Workflow sempre visível */}
      {isOs && (
        <div className="overflow-x-auto pb-4">
          <div className="flex items-center gap-2 min-w-max px-4">
            {workflowSteps.map((step, index) => {
              const isCompleted = index < currentStepIndex;
              const isCurrent = index === currentStepIndex;

              // Para etapas completadas, assumimos que estavam dentro do prazo
              // Na etapa atual, verificamos com base no upDateTimedAt
              const isStepWithinDeadline = isCompleted || (isCurrent && isCurrentlyWithinLimit);

              const icon = isCompleted ? (
                <BsCheckCircleFill className="text-green-500" />
              ) : isCurrent ? (
                <BsCheckCircleFill
                  className={isCurrentlyWithinLimit ? "text-yellow-500" : "text-red-500"}
                />
              ) : (
                <BsCircleFill className="text-gray-400" />
              );

              const labelClass = isCompleted
                ? "text-green-600"
                : isCurrent
                ? isCurrentlyWithinLimit
                  ? "text-yellow-600 font-medium"
                  : "text-red-600 font-medium"
                : "text-gray-400";

              return (
                <div key={step.key} className="flex items-center gap-2">
                  <div className="flex flex-col items-center">
                    {icon}
                    <span className={`text-xs mt-1 ${labelClass} whitespace-nowrap`}>
                      {step.label}
                    </span>
                    {isCurrent && os?.upDateTimedAt && (
                      <>
                        <span
                          className={`text-xs ${
                            isCurrentlyWithinLimit ? "text-yellow-600" : "text-red-600"
                          }`}>
                          {formatDate(os.upDateTimedAt)}
                        </span>
                        <span
                          className={`text-[10px] font-medium ${
                            isCurrentlyWithinLimit ? "text-yellow-600" : "text-red-600"
                          }`}>
                          {isCurrentlyWithinLimit
                            ? `${remainingHours}h restantes`
                            : `Prazo excedido em ${hoursUsedInCurrentStep - 72}h`}
                        </span>
                      </>
                    )}
                  </div>
                  {index < workflowSteps.length - 1 && (
                    <div
                      className={`w-6 h-px ${
                        isCompleted
                          ? "bg-green-300"
                          : isCurrent && !isCurrentlyWithinLimit
                          ? "bg-red-300"
                          : "bg-gray-300"
                      } mx-1`}></div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Card expansível */}
      <Card className={`shadow ${expanded ? "border-b-0 border-x-0 border-t-2" : "border "}`}>
        <div className="flex items-center gap-2 p-4 cursor-pointer" onClick={toggleExpanded}>
          <CircleAlert className="h-5 w-5 text-blue-600" />
          <span className="text-sm font-medium">Detalhes da Ordem de Serviço</span>
          <div className="ml-auto">
            {expanded ? (
              <ChevronUp className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            )}
          </div>
        </div>

        {expanded && (
          <div className="rounded-lg border mb-4">
            <Tabs defaultValue={tabs[0]} className="w-full">
              <TabsList className="w-full grid grid-cols-2 rounded-none h-auto border-b">
                {tabs.map((tab) => (
                  <TabsTrigger
                    key={tab}
                    value={tab}
                    className="py-3 data-[state=active]:border-b-2 data-[state=active]:border-blue-500 data-[state=active]:bg-transparent data-[state=active]:shadow-none rounded-none">
                    {tab}
                  </TabsTrigger>
                ))}
              </TabsList>

              <TabsContent value={tabs[0]} className="p-0 m-0">
                <div className="p-4">
                  <table className="w-full table-auto">
                    <tbody>
                      <tr className="border-b">
                        <td className="py-3">Centro de custo:</td>
                        <td className="py-3">
                          {os?.veiculo?.faturamentoVeiculo?.centro_custo?.descricao}
                        </td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3">Número da OS:</td>
                        <td className="py-3">{os?.osNumber}</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3">Data de lançamento:</td>
                        <td className="py-3">
                          {os?.createdAt ? new Date(os.createdAt).toLocaleDateString("pt-BR") : ""}
                        </td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3">Tipo de serviço:</td>
                        <td className="py-3">{os?.TiposDeOs?.descricao}</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3">Tipo de manutenção:</td>
                        <td className="py-3">{os?.tipo_manutencao}</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3">Observações:</td>
                        <td className="py-3">{os?.descricao}</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3">Veículo imobilizado:</td>
                        <td className="py-3">{os?.imobilizado ? "Sim" : "Não"}</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3">Localização:</td>
                        <td className="py-3">
                          {`${os?.cidade_loc} - ${os?.estado_loc}` || "Sem informações"}
                        </td>
                      </tr>
                      <tr>
                        <td className="py-3">Credenciado orçamentista:</td>
                        <td className="py-3">
                          {os?.credenciado?.informacoes?.[0]?.razao_social ?? "N/A"}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </TabsContent>

              <TabsContent value={tabs[1]} className="p-0 m-0">
                <div className="p-4">
                  <table className="w-full table-auto">
                    <tbody>
                      <tr className="border-b">
                        <td className="py-3">Tipo de veículo:</td>
                        <td className="py-3">{os?.veiculo?.tiposVeiculos?.descricao}</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3">Placa:</td>
                        <td className="py-3">{os?.veiculo?.placa}</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3">Marca/Modelo:</td>
                        <td className="py-3">
                          {os?.veiculo?.marca?.descricao} - {os?.veiculo?.modelo?.descricao}
                        </td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3">Ano:</td>
                        <td className="py-3">
                          {os?.veiculo?.ano_fab}/{os?.veiculo?.ano_modelo}
                        </td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3">Cor:</td>
                        <td className="py-3">{os?.veiculo?.cor || "-"}</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3">Chassis:</td>
                        <td className="py-3">{os?.veiculo?.vin}</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3">Valor Venal:</td>
                        <td className="py-3">
                          {os?.veiculo?.valor_venal
                            ? Number(os.veiculo.valor_venal).toLocaleString("pt-BR", {
                                style: "currency",
                                currency: "BRL",
                              })
                            : "R$ 0,00"}
                        </td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3">Odômetro:</td>
                        <td className="py-3">
                          {os?.veiculo?.odometro_atual
                            ? Number(os.veiculo.odometro_atual).toLocaleString("pt-BR")
                            : "-"}{" "}
                          km
                        </td>
                      </tr>
                      <tr>
                        <td className="py-3">Mais informações:</td>
                        <td className="py-3">
                          <Button
                            onClick={() => setFichaTecnicaOpen(true)}
                            variant="outline"
                            size="sm"
                            className="text-blue-600 border-blue-600 hover:bg-blue-50 hover:text-blue-700">
                            Ficha técnica
                          </Button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}
      </Card>
    </>
  );
}