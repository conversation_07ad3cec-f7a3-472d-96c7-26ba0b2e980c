import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

interface BudgetSummaryProps {
  resumoData: {
    total_pecas: number;
    total_servicos: number;
    subtotal: number;
    descontos: number;
    total: number;
  };
}

const BudgetSummary = ({ resumoData }: BudgetSummaryProps) => {
  const formatCurrency = (value: number) =>
    new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value/100);

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">Resumo do Orçamento</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex justify-between text-sm">
          <span className="text-muted-foreground">Peças:</span>
          <span>{formatCurrency(resumoData.total_pecas)}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-muted-foreground">Serviços:</span>
          <span>{formatCurrency(resumoData.total_servicos)}</span>
        </div>
        <Separator />
        <div className="flex justify-between text-sm">
          <span className="text-muted-foreground">Subtotal:</span>
          <span>{formatCurrency(resumoData.subtotal)}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-muted-foreground">Descontos:</span>
          <span>{formatCurrency(resumoData.descontos)}</span>
        </div>
        <Separator />
        <div className="flex justify-between font-medium">
          <span>Total:</span>
          <span className="text-lg">{formatCurrency(resumoData.total)}</span>
        </div>
      </CardContent>
    </Card>
  );
};

export default BudgetSummary;
