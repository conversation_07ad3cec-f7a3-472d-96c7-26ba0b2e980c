import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Plus, X } from "lucide-react";

interface AddAnotherItemModalProps {
  open: boolean;
  onClose: () => void;
  onAddAnother: () => void;
  onFinish: () => void;
}

export function AddAnotherItemModal({
  open,
  onClose,
  onAddAnother,
  onFinish,
}: AddAnotherItemModalProps) {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Adicionar outro item?</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col items-center mt-2">
          <div className="flex items-center mb-4">
            <div className="bg-gray-600 rounded-full p-2 mr-3">
              <Plus className="h-5 w-5 text-white" />
            </div>
            <p className="text-gray-600">Deseja cadastrar mais um item ou encerrar o processo?</p>
          </div>
          <div className="flex space-x-3 mt-4">
            <Button variant="outline" onClick={onFinish} className="flex items-center">
              <X className="mr-2 h-4 w-4" />
              Encerrar
            </Button>
            <Button
              onClick={onAddAnother}
              className="bg-black text-white hover:bg-gray-800 flex items-center">
              <Plus className="mr-2 h-4 w-4" />
              Adicionar item
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
