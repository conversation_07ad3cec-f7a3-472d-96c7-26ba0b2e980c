import { Button } from "@/components/ui/button";
import { ItemCard, ItemCardProps } from "./item-card";
import { Grid, List, Plus } from "lucide-react";
import { useState } from "react";

interface Item
  extends Omit<ItemCardProps, "onEdit" | "onDelete" | "onQuantityChange" | "onApprove"> {
  id: string;
  precoUnitario: number;
  isNew?: boolean;
}

interface ItemsListProps {
  items: Item[];
  onAddItem: () => void;
  onEditItem: (id: string | undefined) => void;
  onDeleteItem: (id: string | undefined) => void;
  onQuantityChange: (id: string | undefined, quantity: number) => void;
  complementarStatus?: string;
  isComplementaryBudget?: boolean;
  onApproveItem?: (id: string | undefined) => void;
}

export function ItemsList({
  items,
  onAddItem,
  onEditItem,
  onDeleteItem,
  onQuantityChange,
  complementarStatus,
  isComplementaryBudget,
  onApproveItem,
}: ItemsListProps) {
  console.log("ItemsList", items);
  return (
    <div className="mt-6 rounded-lg">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-medium">
          Peças e serviços ({items.length} {items.length === 1 ? "item" : "itens"})
        </h2>
      </div>

      {items.length === 0 ? (
        <div className="text-center py-10 text-gray-500 ">
          Nenhum item adicionado. Clique em "Adicionar peça ou serviço" para começar.
        </div>
      ) : (
        <div>
          {items.map((item) => (
            <ItemCard
              key={item.id}
              {...item}
              onEdit={() => onEditItem(item.id)}
              onDelete={() => onDeleteItem(item.id)}
              onQuantityChange={(quantity) => onQuantityChange(item.id, quantity)}
              complementarStatus={complementarStatus}
              isComplementaryBudget={isComplementaryBudget}
              onApprove={() => onApproveItem && onApproveItem(item.id)}
            />
          ))}
        </div>
      )}
    </div>
  );
}
