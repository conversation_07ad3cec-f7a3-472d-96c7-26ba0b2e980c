import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Upload } from "lucide-react";
import { useId, useState } from "react";

export interface FileUploaderProps {
  onFilesChange?: (files: File[]) => void;
  title?: string;
  files: File[];
  setFiles: (files: File[]) => void;
  accept?: string;
}

const FileUploader = ({
  onFilesChange,
  title = "Anexos",
  files,
  setFiles,
  accept = "*/*",
}: FileUploaderProps) => {
  const inputId = useId();
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      const updatedFiles = [...files, ...newFiles];
      setFiles(updatedFiles);
      if (onFilesChange) {
        onFilesChange(updatedFiles);
      }
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
          <Upload className="h-8 w-8 text-gray-400 mb-2" />
          <p className="text-sm text-gray-500 mb-2">
            Arraste arquivos ou clique para anexar
          </p>
          <input
            id={inputId}
            type="file"
            className="hidden"
            multiple
            onChange={handleFileChange}
            accept={accept}
          />
          <label htmlFor={inputId}>
            <Button
              variant="outline"
              size="sm"
              className="cursor-pointer"
              asChild
            >
              <span>Selecionar arquivos</span>
            </Button>
          </label>
        </div>
        {files?.length > 0 && (
          <div className="mt-4">
            <p className="text-sm font-medium mb-2">
              Arquivos anexados ({files.length})
            </p>
            <ul className="space-y-1">
              {files.map((file, index) => (
                <li key={index} className="text-sm text-gray-600">
                  {file.name}
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default FileUploader;
