"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Loader2, TrendingUp, TrendingDown, Calendar, MapPin, Building } from "lucide-react";
import { toast } from "sonner";

interface ComparacaoData {
  ultimosSessentaDias?: {
    data: string;
    preco: number;
    fornecedor: string;
    osNumero: string;
  };
  estado?: {
    data: string;
    preco: number;
    fornecedor: string;
    osNumero: string;
  };
  localidade?: {
    data: string;
    preco: number;
    fornecedor: string;
    osNumero: string;
  };
  fornecedor?: {
    data: string;
    preco: number;
    fornecedor: string;
    osNumero: string;
  };
}

interface ComparacaoPrecosProps {
  codigo?: string;
  descricao?: string;
  precoAtual?: number;
  onPrecoSugerido?: (preco: number) => void;
}

export function ComparacaoPrecos({
  codigo,
  descricao,
  precoAtual = 0,
  onPrecoSugerido,
}: ComparacaoPrecosProps) {
  const [loading, setLoading] = useState(false);
  const [comparacaoData, setComparacaoData] = useState<ComparacaoData | null>(null);
  const [expanded, setExpanded] = useState(false);

  const fetchComparacao = async () => {
    if (!codigo && !descricao) {
      toast.error("Código ou descrição da peça é necessário para comparação");
      return;
    }

    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (codigo) params.append("codigo", codigo);
      if (descricao) params.append("descricao", descricao);

      const response = await fetch(`/api/orcamentos/comparacao-pecas?${params}`);
      if (response.ok) {
        const data = await response.json();
        setComparacaoData(data);
        setExpanded(true);
      } else {
        toast.error("Erro ao buscar comparação de preços");
      }
    } catch (error) {
      console.error("Erro ao buscar comparação:", error);
      toast.error("Erro ao buscar comparação de preços");
    } finally {
      setLoading(false);
    }
  };

  const calcularDiferenca = (precoComparacao: number) => {
    if (precoAtual === 0) return 0;
    return ((precoAtual - precoComparacao) / precoComparacao) * 100;
  };

  const formatarData = (dataString: string) => {
    try {
      return new Date(dataString).toLocaleDateString("pt-BR");
    } catch {
      return dataString;
    }
  };

  const renderComparacaoItem = (
    titulo: string,
    icon: React.ReactNode,
    data: ComparacaoData[keyof ComparacaoData],
    color: string
  ) => {
    if (!data) return null;

    const diferenca = calcularDiferenca(data.preco);
    const isPrecoMaior = diferenca > 0;

    return (
      <div className={`border-l-4 border-${color}-400 pl-4 py-3`}>
        <div className="flex items-center gap-2 mb-2">
          {icon}
          <h4 className="font-medium text-sm">{titulo}</h4>
        </div>
        <div className="space-y-1">
          <div className="flex justify-between items-center">
            <span className="text-lg font-bold">
              {data.preco.toLocaleString("pt-BR", {
                style: "currency",
                currency: "BRL",
              })}
            </span>
            {precoAtual > 0 && (
              <Badge variant={isPrecoMaior ? "destructive" : "secondary"}>
                {isPrecoMaior ? "+" : ""}{diferenca.toFixed(1)}%
              </Badge>
            )}
          </div>
          <p className="text-xs text-gray-600">{data.fornecedor}</p>
          <p className="text-xs text-gray-500">
            {formatarData(data.data)} • {data.osNumero}
          </p>
          {onPrecoSugerido && (
            <Button
              size="sm"
              variant="outline"
              className="mt-2 h-6 text-xs"
              onClick={() => onPrecoSugerido(data.preco)}
            >
              Usar este preço
            </Button>
          )}
        </div>
      </div>
    );
  };

  if (!codigo && !descricao) {
    return null;
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={fetchComparacao}
          disabled={loading}
          className="text-xs h-8"
        >
          {loading ? (
            <Loader2 className="h-3 w-3 animate-spin mr-1" />
          ) : (
            <BarChart3 className="h-3 w-3 mr-1" />
          )}
          Comparar Preços
        </Button>
        {precoAtual > 0 && (
          <span className="text-sm text-gray-600">
            Preço atual: {precoAtual.toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}
          </span>
        )}
      </div>

      {expanded && comparacaoData && (
        <Card className="mt-3">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Comparação de Preços</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {renderComparacaoItem(
              "Últimos 60 dias",
              <Calendar className="h-4 w-4 text-blue-600" />,
              comparacaoData.ultimosSessentaDias,
              "blue"
            )}
            {renderComparacaoItem(
              "Mesmo estado",
              <MapPin className="h-4 w-4 text-green-600" />,
              comparacaoData.estado,
              "green"
            )}
            {renderComparacaoItem(
              "Mesma localidade",
              <MapPin className="h-4 w-4 text-orange-600" />,
              comparacaoData.localidade,
              "orange"
            )}
            {renderComparacaoItem(
              "Mesmo fornecedor",
              <Building className="h-4 w-4 text-purple-600" />,
              comparacaoData.fornecedor,
              "purple"
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
