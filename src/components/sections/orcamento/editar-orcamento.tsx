import { useState, useEffect } from "react";
import { toast } from "sonner";
import TechnicalInfo from "./edit-orcamento/technical-info";
import ServiceOrderDetails from "./edit-orcamento/service-order-details";
import BudgetSummary from "./edit-orcamento/budget-summary";
import ServiceAddButton from "./edit-orcamento/service-add-button";
import FileUploader from "./edit-orcamento/file-upload";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  FormPecaServico,
  PecaServicoFormData,
} from "./edit-orcamento/form-pecas-servico";
import { ItemsList } from "./edit-orcamento/item-list";
import DiagnosticNotes from "./edit-orcamento/diagnostics-notes";
import { AddAnotherItemModal } from "./edit-orcamento/add-another-item";
import {
  createOrcamentoAction,
  updateOrcamentoAction,
  updateOrcamentoStatus,
  updateOsStatus,
} from "@/serverActions/orcamentoAction";
import { validateOrCreateCondutorPassword } from "@/serverActions/condutorAction";
import { ConductorAuthModal } from "@/components/modal/condutor-auth-modal";
import { useSession } from "@/components/session/use-session";
import { Button } from "@/components/ui/button";

interface ItemData {
  id: string;
  tipo: "pecas" | "servico";
  descricao: string;
  codigo: string;
  marca: string;
  garantia: string;
  preco: number;
  precoUnitario: number;
  quantidade: number;
  quantidadeHoras?: number;
  tipoServico?: string;
  tipoPecas?: string;
  desconto: number;
  isNew?: boolean;
}

const ConfirmationModal = ({ isOpen, onClose, onConfirm, title }: any) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        onClick={(e) => e.stopPropagation()}
        onMouseDown={(e) => e.stopPropagation()}
      >
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              onClose();
            }}
          >
            Cancelar
          </Button>
          <Button onClick={onConfirm}>Confirmar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export function EdOrcamento({
  os,
  status,
}: {
  os: OS | undefined;
  status?: string;
}) {
  const { session } = useSession();
  const [expanded, setExpanded] = useState(false);
  const [fichaTecnicaOpen, setFichaTecnicaOpen] = useState(false);
  const [formOpen, setFormOpen] = useState(false);
  const [addAnotherOpen, setAddAnotherOpen] = useState(false);
  const [formType, setFormType] = useState<"pecas" | "servico">("pecas");
  const [items, setItems] = useState<ItemData[]>([]);
  const [editingItemId, setEditingItemId] = useState<string | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [uploadedInvoiceFiles, setUploadedInvoiceFiles] = useState<File[]>([]);
  const [showConductorFormModal, setShowConductorFormModal] = useState(false);
  const [isCreatingComplementaryBudget, setIsCreatingComplementaryBudget] =
    useState(false);
  const [complementarStatus, setComplementarStatus] = useState<
    string | undefined
  >(undefined);
  const [condutorHasPassword, setCondutorHasPassword] =
    useState<boolean>(false);
  const [condutor, setCondutor] = useState<condutor>();
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);

  const [resumoData, setResumoData] = useState({
    total_pecas: 0,
    total_servicos: 0,
    subtotal: 0,
    descontos: 0,
    total: 0,
  });

  const [diagnosticoData, setDiagnosticoData] = useState({
    observacoes: "",
    previsaoEntrega: 2,
    validade: new Date().toISOString().split("T")[0],
  });

  // Carregar dados do localStorage quando componente montar
  useEffect(() => {
    if (os?.id) {
      const savedItemsStr = localStorage.getItem(`orcamento-items-${os.id}`);
      let parsedItems: ItemData[] = [];
      if (savedItemsStr) {
        try {
          parsedItems = JSON.parse(savedItemsStr);
        } catch (e) {
          console.error("Erro ao carregar itens do localStorage:", e);
        }
      }

      if (parsedItems.length > 0) {
        setItems(parsedItems);
        calculateTotals(parsedItems);
      } else if (os.orcamentos) {
        const osExecucao = os.status === "execucao" ? os : undefined;
        setCondutorHasPassword(
          osExecucao?.condutor?.password !== null ? true : false
        );
        setCondutor(osExecucao?.condutor);
        const relevantOrcamentos = os.orcamentos.filter(
          (orc: any) =>
            orc.status === "execucao" ||
            orc.status === "complementar_unauthorized" ||
            orc.status === "complementar_authorized" ||
            orc.status === "pendente" ||
            (orc.status === "lançada" &&
              orc.credenciadoId === session?.credenciadoId)
        );
        if (relevantOrcamentos.length > 0) {
          let allPecasItems: ItemData[] = [];
          let allServicosItems: ItemData[] = [];
          relevantOrcamentos.forEach((orcamento: Orcamento) => {
            const isComplementary =
              orcamento.status === "complementar_unauthorized" ||
              orcamento.status === "complementar_authorized";
            const pecasItems: ItemData[] = (orcamento.processedPecas || []).map(
              (p: OrcamentoPecas) => ({
                id: p.id,
                tipo: "pecas",
                descricao: p.descricao,
                codigo: p.codigo,
                marca: p.marca,
                garantia:
                  typeof p.garantia === "object" && p.garantia instanceof Date
                    ? p.garantia.toISOString()
                    : String(p.garantia || ""),
                preco: p.valorNegociado,
                precoUnitario: p.valorUnitario,
                quantidade: p.quantidade,
                desconto: p.valorDesconto,
                isNew:
                  isComplementary &&
                  orcamento.status === "complementar_unauthorized",
              })
            );
            const servicosItems: ItemData[] = (
              orcamento.processedServicos || []
            ).map((s: OrcamentoServico) => ({
              id: s.id,
              tipo: "servico",
              descricao: s.descricao,
              codigo: "",
              marca: "",
              garantia: "",
              preco: s.valor, // This is already the final price
              precoUnitario: s.valor,
              quantidade: s.quantidadeHoras, // Always set to 1 since backend doesn't store quantity
              desconto: s.valorDesconto,
              isNew:
                isComplementary &&
                orcamento.status === "complementar_unauthorized",
            }));
            allPecasItems = [...allPecasItems, ...pecasItems];
            allServicosItems = [...allServicosItems, ...servicosItems];
          });
          const combinedItems = [...allPecasItems, ...allServicosItems];
          setItems(combinedItems);
          calculateTotals(combinedItems);
          if (
            relevantOrcamentos.some(
              (orc: any) =>
                orc.status === "complementar_unauthorized" ||
                orc.status === "complementar_authorized"
            )
          ) {
            setComplementarStatus(
              relevantOrcamentos.some(
                (orc: any) => orc.status === "complementar_authorized"
              )
                ? "complementar_authorized"
                : "complementar_unauthorized"
            );
          }
        }
      }
    }
  }, [os?.id]);
  // Salvar no localStorage quando items mudar
  useEffect(() => {
    if (os?.id) {
      if (items.length > 0) {
        localStorage.setItem(`orcamento-items-${os.id}`, JSON.stringify(items));
      } else {
        localStorage.removeItem(`orcamento-items-${os.id}`);
      }
    }
  }, [items, os?.id]);

  const handleDiagnosticoChange = (
    field: keyof typeof diagnosticoData,
    value: string | number
  ) => {
    setDiagnosticoData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleFormSubmit = (data: PecaServicoFormData) => {
    console.log("DADOSSS:", data);

    // Variáveis para armazenar dados processados
    let preco = 0;
    let precoUnitario = 0;
    let quantidade = 0;
    let codigo = "";
    let tipoServico = "";
    let tipoPecas = "";
    let garantia = "";
    let marca = "";

    // Garantir que valorAutorizado esteja presente (com type assertion para segurança)
    const valorAutorizado =
      (data as any).valorAutorizado !== undefined
        ? (data as any).valorAutorizado
        : 0;

    // Usamos o formType (que é mais confiável que data.grupo) para determinar o tipo de item
    if (formType === "servico") {
      // Para serviços
      preco = valorAutorizado;
      precoUnitario = parseFloat((data as any).valor?.toString() || "0");
      quantidade = parseFloat((data as any).quantidadeHoras || "1");
      tipoServico = (data as any).grupo || "Mão de Obra";
      garantia = (data as any).prazo_garantia || "";
      codigo = "";
      marca = "";
    } else {
      // Para peças
      preco = valorAutorizado;
      tipoPecas = (data as any).grupo || "Peças";
      precoUnitario = parseFloat(
        (data as any).valor_unitario?.toString() || "0"
      );
      quantidade = parseFloat((data as any).quantidade || "1");
      codigo = (data as any).codigo || "";
      garantia = (data as any).prazo_garantia || "";
      marca = (data as any).marca || "";
    }

    // Cálculo correto do desconto baseado no valor autorizado
    const desconto = precoUnitario * quantidade - preco;

    if (editingItemId) {
      const updatedItems = items.map((item) =>
        item.id === editingItemId
          ? {
              ...item,
              descricao: data.descricao,
              codigo: codigo,
              marca: marca,
              garantia: garantia,
              preco: preco,
              precoUnitario,
              quantidade,
              desconto,
              tipoServico: formType === "servico" ? tipoServico : undefined,
              tipoPecas: formType === "pecas" ? tipoPecas : undefined,
            }
          : item
      );
      setItems(updatedItems);
      calculateTotals(updatedItems);
      setEditingItemId(null);
      toast.success("Item atualizado com sucesso!");
    } else {
      // Gerar ID temporário único baseado em timestamp
      const tempId = `temp-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 9)}`;

      const newItem: ItemData = {
        id: tempId,
        tipo: formType,
        descricao: data.descricao,
        codigo: codigo,
        marca: marca,
        garantia: garantia,
        preco: preco,
        precoUnitario,
        quantidade,
        desconto,
        tipoServico: formType === "servico" ? tipoServico : undefined,
        tipoPecas: formType === "pecas" ? tipoPecas : undefined,
        isNew: isCreatingComplementaryBudget,
      };
      console.log("Novo item:", newItem);

      const updatedItems = [...items, newItem];
      setItems(updatedItems);
      calculateTotals(updatedItems);
      toast.success("Item adicionado com sucesso!");
    }

    setFormOpen(false);
    setAddAnotherOpen(true);
  };

  const calculateTotals = (currentItems: ItemData[]) => {
    const totalPecas = currentItems
      .filter((item) => item.tipo === "pecas")
      .reduce((sum, item) => sum + item.preco, 0);

    const totalServicos = currentItems
      .filter((item) => item.tipo === "servico")
      .reduce((sum, item) => sum + item.preco, 0);

    const subtotal = totalPecas + totalServicos;

    setResumoData({
      total_pecas: totalPecas,
      total_servicos: totalServicos,
      subtotal,
      descontos: 0,
      total: subtotal,
    });
  };

  const handleOpenForm = (type: "pecas" | "servico") => {
    setFormType(type);
    setFormOpen(true);
  };

  const handleEditItem = (id: string | undefined) => {
    if (!id) {
      toast.error("ID inválido para edição.");
      return;
    }
    const item = items.find((item) => item.id === id);
    if (item) {
      setFormType(item.tipo);
      setEditingItemId(id);
      setFormOpen(true);
    }
  };

  const handleDeleteItem = (id: string | undefined) => {
    if (!id) {
      toast.error("ID inválido para exclusão.");
      return;
    }
    const updatedItems = items.filter((item) => item.id !== id);
    setItems(updatedItems);
    calculateTotals(updatedItems);
    toast.success("Item removido com sucesso!");
  };

  const handleQuantityChange = (id: string | undefined, quantity: number) => {
    if (!id) {
      toast.error("ID inválido para alteração de quantidade.");
      return;
    }
    const updatedItems = items.map((item) =>
      item.id === id ? { ...item, quantidade: quantity } : item
    );
    setItems(updatedItems);
    calculateTotals(updatedItems);
  };

  const handleFilesChange = (files: File[]) => {
    setUploadedFiles(files);
  };

  const handleInvoiceFilesChange = (files: File[]) => {
    setUploadedInvoiceFiles(files);
  };

  const uploadFile = async (file: File) => {
    const formData = new FormData();
    formData.append("file", file);

    const res = await fetch("/api/upload", {
      method: "POST",
      body: formData,
    });

    if (!res.ok) {
      throw new Error("Erro ao fazer upload do arquivo");
    }

    return await res.json();
  };

  const handleSubmitOrcamento = async (orcamentoId?: string) => {
    let arquivoLocations: any[] = [];
    if (uploadedFiles.length > 0) {
      arquivoLocations = await Promise.all(
        uploadedFiles.map((file) => uploadFile(file))
      );
    }

    let arquivoInvoiceLocations: any[] = [];

    if (uploadedInvoiceFiles.length > 0) {
      arquivoInvoiceLocations = await Promise.all(
        uploadedInvoiceFiles.map((file) => uploadFile(file))
      );
    }

    const servicosArray = items
      .filter((item) => item.tipo === "servico")
      .map((servico) => ({
        descricao: servico.descricao,
        tipoServico: servico.tipoServico || "mecanica",
        valor: Number(servico.preco),
        valorUnitario: Number(servico.precoUnitario) || null,
        codigo: servico.codigo || "",
        garantia: servico.garantia ? new Date(servico.garantia) : null,
        quantidadeHoras: Number(servico.quantidade) || null,
      }));

    const pecasArray = items
      .filter((item) => item.tipo === "pecas")
      .map((peca) => ({
        descricao: peca.descricao,
        codigo: peca.codigo,
        marca: peca.marca,
        garantia: peca.garantia,
        tipoPecas: peca.tipoPecas,
        quantidade: peca.quantidade,
        valorUnitario: peca.precoUnitario,
        valorDesconto: peca.desconto,
        valorNegociado: peca.preco,
      }));

    console.log("session", session);
    const orcamentoData = {
      osId: os?.id,
      credenciadoId: session?.credenciadoId,
      dataLancamento: new Date().toISOString(),
      validade: diagnosticoData.validade,
      prazoEntrega: diagnosticoData.previsaoEntrega,
      valorTotal: resumoData.total,
      observacoes: diagnosticoData.observacoes,
      arquivos: arquivoLocations,
      arquivosNotaFiscal: arquivoInvoiceLocations,
      servico: servicosArray,
      pecas: pecasArray,
      status: isCreatingComplementaryBudget
        ? "complementar_unauthorized"
        : status === "pendente"
        ? "enviado"
        : undefined,
    };

    if (!orcamentoData.prazoEntrega) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Previsão de Entrega",
      });
      return;
    }

    if (!orcamentoData.validade) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Validade do orçamento",
      });
      return;
    }

    if (!orcamentoData.valorTotal) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Adicione pelo menos um serviço ou uma peça",
      });
      return;
    }

    if (!orcamentoData.osId) {
      toast("OS Não identificada", {
        description: "Não foi possível identificar a OS",
      });
      return;
    }

    try {
      const response = orcamentoId
        ? await updateOrcamentoAction(orcamentoId, orcamentoData)
        : await createOrcamentoAction(orcamentoData);
      if (response.success) {
        if (os?.id) {
          localStorage.removeItem(`orcamento-items-${os.id}`);
        }
        if (status === "pendente" && os?.id) {
          await updateOsStatus(os.id, "orcamentaçao");
        }
        console.log(response);
        toast.success("Orçamento enviado para orçamentação!");
        window.location.href = `/dashboard/orcamento/consultar-orcamento`;
      } else {
        toast.error(response.error || "Erro ao criar orçamento");
      }
    } catch (error: any) {
      toast.error("Erro ao criar orçamento: " + error.message);
    }
  };

  const handleFinalizarServico = async () => {
    setShowConfirmationModal(true);
  };

  const handleConfirmation = () => {
    if (session?.contrato?.checklist_simplificado_pecas) {
      handleFinalizacaoSuccess();
    } else {
      setShowConductorFormModal(true);
    }
  };

  const handleFinalizacaoSuccess = async () => {
    let arquivoLocations: any[] = [];
    if (uploadedFiles.length > 0) {
      arquivoLocations = await Promise.all(
        uploadedFiles.map((file) => uploadFile(file))
      );
    }

    try {
      // Encontre o orçamento em execução
      const orcamentoExec = os?.orcamentos?.find(
        (orc: any) => orc.status === "execucao"
      );
      if (!orcamentoExec) {
        throw new Error("Orçamento em execução não encontrado");
      }

      await updateOrcamentoStatus(orcamentoExec.id, "finalizada");
      await updateOsStatus(
        os?.id || "",
        "finalizada",
        orcamentoExec.id,
        arquivoLocations
      );

      toast.success("Serviço finalizado com sucesso!");
      window.location.href = `/dashboard/checklists/novo-checklist/veiculo/${os?.veiculoId}?osId=${os?.id}`;
    } catch (error) {
      toast.error("Erro ao finalizar serviço: " + (error as Error).message);
    }
  };

  const handleApproveItem = async (itemId: string | undefined) => {
    console.log("Item ID para aprovação:", itemId);
    if (!itemId) {
      toast.error("ID inválido para aprovação.");
      return;
    }

    // Get the selected item
    const item = items.find((i) => i.id === itemId);
    if (!item) {
      toast.error("Item não encontrado.");
      return;
    }

    // Create arrays with only the selected item
    const servicosArray =
      item.tipo === "servico"
        ? [
            {
              descricao: item.descricao,
              tipoServico: "mao_de_obra",
              valor: item.preco * item.quantidade,
              quantidade: item.quantidade,
              codigo: item.codigo,
            },
          ]
        : [];

    const pecasArray =
      item.tipo === "pecas"
        ? [
            {
              descricao: item.descricao,
              codigo: item.codigo,
              marca: item.marca,
              garantia: item.garantia,
              quantidade: item.quantidade,
              valorUnitario: item.precoUnitario,
              valorDesconto: item.desconto || 0,
              valorNegociado: item.preco,
            },
          ]
        : [];

    const orcamentoData = {
      osId: os?.id,
      credenciadoId: os?.credenciadoId,
      dataLancamento: new Date().toISOString(),
      validade: diagnosticoData.validade,
      prazoEntrega: diagnosticoData.previsaoEntrega,
      valorTotal: item.preco * item.quantidade,
      observacoes: diagnosticoData.observacoes,
      arquivos: [],
      arquivosNotaFiscal: [],
      servico: servicosArray,
      pecas: pecasArray,
      status: "complementar_unauthorized",
    };

    try {
      const response = await createOrcamentoAction(orcamentoData);
      if (response.success) {
        setComplementarStatus("complementar_unauthorized");
        // Remove the approved item from the list
        const updatedItems = items.filter((i) => i.id !== itemId);
        setItems(updatedItems);
        calculateTotals(updatedItems);

        toast.success("Item enviado para aprovação complementar!");

        // If all items are approved, redirect
        if (updatedItems.length === 0 && os?.id) {
          localStorage.removeItem(`orcamento-items-${os.id}`);
        }
        window.location.reload();
      } else {
        toast.error(response.error || "Erro ao enviar item para aprovação");
      }
    } catch (error: any) {
      toast.error("Erro ao enviar item para aprovação: " + error.message);
    }
  };

  const hasChecklistEntrada = !!os?.checklistEntradaId;
  const hasChecklistSaida = !!os?.checklistSaidaId;

  const isFinalizarDisabled = false;

  return (
    <>
      <div className="p-6 grid  grid-cols-1 lg:grid-cols-[70%_30%] gap-4">
        <div className="flex flex-col gap-4 w-full">
          <ServiceOrderDetails
            expanded={expanded}
            setExpanded={setExpanded}
            os={os}
            isOs={false}
            setFichaTecnicaOpen={setFichaTecnicaOpen}
          />
          {status === "execucao" && !isCreatingComplementaryBudget && (
            <div className="flex justify-end mt-4">
              <button
                onClick={() => setIsCreatingComplementaryBudget(true)}
                className="bg-blue-500 text-white py-2 px-4 rounded"
              >
                Criar orçamento complementar
              </button>
            </div>
          )}
          {(status !== "execucao" || isCreatingComplementaryBudget) && (
            <div className="flex justify-end mt-4">
              <ServiceAddButton
                veiculo={os?.veiculo}
                onAddPeca={() => handleOpenForm("pecas")}
                onAddServico={() => handleOpenForm("servico")}
              />
            </div>
          )}

          {items.length > 0 && (
            <ItemsList
              items={items}
              onAddItem={() => handleOpenForm("pecas")}
              onEditItem={handleEditItem}
              onDeleteItem={handleDeleteItem}
              onQuantityChange={handleQuantityChange}
              complementarStatus={complementarStatus}
              isComplementaryBudget={isCreatingComplementaryBudget}
              onApproveItem={handleApproveItem}
            />
          )}
          {status === "pendente" && (
            <div className="flex flex-col justify-start mt-4 border p-4 rounded-lg">
              <h3 className="text-xl font-bold">Ajustes Solicitados: </h3>
              <p className="text-lg">{os?.observacao}</p>
            </div>
          )}
          {status === "execucao" && (
            <>
              {!session?.contrato?.checklist_simplificado_pecas &&
                !hasChecklistEntrada && (
                  <button
                    onClick={() =>
                      (window.location.href = `/dashboard/checklists/novo-checklist/veiculo/${os?.veiculoId}?osId=${os?.id}&checklistType=Entrada`)
                    }
                    className="bg-blue-500 text-white py-2 px-4 rounded ml-4 w-1/3"
                  >
                    Realizar Checklist entrada
                  </button>
                )}
              {!hasChecklistSaida && (
                <button
                  onClick={() =>
                    (window.location.href = `/dashboard/checklists/novo-checklist/veiculo/${os?.veiculoId}?osId=${os?.id}&checklistType=Saída`)
                  }
                  className="bg-blue-500 text-white py-2 px-4 rounded ml-4 w-1/3"
                >
                  Realizar Checklist saída
                </button>
              )}

              {!session?.contrato?.checklist_simplificado_pecas &&
                !hasChecklistEntrada && (
                  <p className="text-red-500">
                    É necessário preencher o checklist de Entrada
                  </p>
                )}
              {!hasChecklistSaida && (
                <p className="text-red-500">
                  É necessário preencher o checklist de Saída
                </p>
              )}

              {os?.orcamentos?.[0]?.observacoes && (
                <div className="border p-4 rounded-lg">
                  <h2 className="mb-2 font-bold text-lg">
                    Observações do Gestor:
                  </h2>
                  <p>{os?.orcamentos?.[0]?.observacoes}</p>
                </div>
              )}
            </>
          )}
        </div>

        <div className="flex flex-col gap-4">
          <BudgetSummary resumoData={resumoData} />
          <DiagnosticNotes
            diagnosticoData={diagnosticoData}
            handleDiagnosticoChange={handleDiagnosticoChange}
          />
          <FileUploader
            onFilesChange={handleFilesChange}
            files={uploadedFiles}
            setFiles={setUploadedFiles}
          />
        </div>
      </div>

      <TechnicalInfo
        fichaTecnicaOpen={fichaTecnicaOpen}
        setFichaTecnicaOpen={setFichaTecnicaOpen}
      />
      <Dialog open={formOpen} onOpenChange={setFormOpen}>
        <DialogContent
          style={{ maxWidth: "1200px", overflow: "auto", minHeight: "85vh" }}
          className="sm:max-w-xl w-[90%] rounded-lg"
        >
          <DialogHeader>
            <DialogTitle>
              {editingItemId ? "Editar" : "Adicionar"}{" "}
              {formType === "pecas" ? "Peça" : "Serviço"}
            </DialogTitle>
          </DialogHeader>
          <FormPecaServico
            tipo={formType}
            onSubmit={handleFormSubmit}
            veiculo={os?.veiculo}
            initialValue={
              editingItemId
                ? (() => {
                    const item = items.find((i) => i.id === editingItemId);
                    if (!item) return undefined;
                    if (item.tipo === "pecas") {
                      return {
                        tipo: "pecas",
                        descricao: item.descricao,
                        quantidade: item.quantidade.toString(),
                        valor_unitario: item.precoUnitario,
                        valor_negociado: item.preco,
                        grupo: item.tipoPecas,
                        codigo: item.codigo,
                        marca: item.marca,
                        prazo_garantia: item.garantia,
                      };
                    }
                    // Para serviço
                    return {
                      tipo: "servico",
                      descricao: item.descricao,
                      quantidade: item.quantidade?.toString() || "1",
                      valor: item.precoUnitario,
                      valor_negociado: item.preco,
                      grupo: item.tipoServico,
                      codigo: item.codigo,
                      prazo_garantia: item.garantia,
                      quantidadeHoras: item.quantidade?.toString() || "1",
                    };
                  })()
                : undefined
            }
            isEditing={!!editingItemId}
          />
        </DialogContent>
      </Dialog>

      <AddAnotherItemModal
        open={addAnotherOpen}
        onClose={() => setAddAnotherOpen(false)}
        onAddAnother={() => {
          setAddAnotherOpen(false);
          setFormOpen(true);
        }}
        onFinish={() => {
          setAddAnotherOpen(false);
        }}
      />

      <div className="mt-4 flex justify-end">
        {status === "execucao" ? (
          <>
            {isCreatingComplementaryBudget ? null : (
              <div>
                {!session?.contrato.checklist_simplificado_pecas &&
                  (!hasChecklistEntrada || !hasChecklistSaida) && (
                    <p className="text-red-500 text-sm mt-2">
                      É necessário ter Checklist de Entrada e Saída.
                    </p>
                  )}
              </div>
            )}
          </>
        ) : (
          <button
            onClick={() => {
              if (status === "pendente") {
                console.log("Orçamento pendente");
                const relevantOrcamento = os?.orcamentos?.find(
                  (orc: any) => orc.status === "pendente"
                );

                handleSubmitOrcamento(relevantOrcamento?.id);
              } else {
                handleSubmitOrcamento();
              }
            }}
            className="bg-green-500 text-white py-2 px-4 rounded"
          >
            Enviar Para Aprovação
          </button>
        )}
      </div>

      <ConductorAuthModal
        isOpen={showConductorFormModal}
        onClose={() => setShowConductorFormModal(false)}
        onSuccess={handleFinalizacaoSuccess}
        hasPassword={condutorHasPassword}
        osCondutor={condutor}
        title="Credenciais do Condutor"
        description="Por favor, insira a matrícula e senha do condutor para finalizar o serviço."
        buttonText="Finalizar Serviço"
      />
      <ConfirmationModal
        isOpen={showConfirmationModal}
        title={`Você confirma que a nota fiscal irá para ${
          os?.send_nf_to === "centro_custo"
            ? os?.veiculo?.faturamentoVeiculo?.centro_custo?.descricao
            : "carletto"
        }?`}
        onClose={() => setShowConfirmationModal(false)}
        onConfirm={handleConfirmation}
      />
    </>
  );
}

export default EdOrcamento;
