import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { CardOrcamento } from "../os/card-orcamento";
import { useEffect, useState } from "react";
import { useSession } from "@/components/session/use-session";
import { toast } from "sonner";

interface ModalCardsOrcamentoProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  os?: OS | null | undefined;
  onSelectOrcamento: (orcamento: Orcamento) => void;
  onShowModal: (show: boolean) => void;
  selectedOrcamento?: Orcamento | null;
}

export function ModalCardsOrcamento({
  isOpen,
  onClose,
  title,
  os,
  onSelectOrcamento,
  onShowModal,
  selectedOrcamento,
}: ModalCardsOrcamentoProps) {
  const { session } = useSession();
  const [loading, setLoading] = useState(true);
  const [ordemServicoData, setOrdemServicoData] = useState<any>(null);
  const [veiculoData, setVeiculoData] = useState<veiculo | undefined>(undefined);
  const [orcamentos, setOrcamentos] = useState<Orcamento[]>([]);

    const [ordensDeServico, setOrdensDeServico] = useState<OS>();

    useEffect(() => {
      const fetchOrdensDeServico = async () => {
        try {
          const response = await fetch(`/api/os/${os?.id}`);
          if (!response.ok) throw new Error("Erro ao buscar ordens de serviço.");
          const data = await response.json();
          setOrdensDeServico(data.data);
        } catch (err) {
          toast.error("Erro ao obter OS. Tente Novamente!");
        } finally {
          setLoading(false);
        }
      };

      fetchOrdensDeServico();
    }, []);
  useEffect(() => {
    if (!ordensDeServico || !ordensDeServico.id) {
      setOrcamentos([]);
      setLoading(false);
      return;
    }
    if (
      ordemServicoData?.id === ordensDeServico.id &&
      veiculoData?.id === ordensDeServico.veiculo?.id &&
      JSON.stringify(orcamentos) === JSON.stringify(ordensDeServico.orcamentos || [])
    ) {
      setLoading(false);
      return;
    }
    try {
      setOrdemServicoData(ordensDeServico);
      setVeiculoData(ordensDeServico.veiculo);
      setOrcamentos(
        Array.isArray(ordensDeServico.orcamentos)
          ? session?.roles.includes("ORCAMENTISTA_OFICINA")
            ? ordensDeServico.orcamentos.filter(
                (orc) => orc.credenciadoId === session.credenciadoId
              )
            : ordensDeServico.orcamentos
          : []
      );
    } catch (error) {
      toast.error("Erro ao carregar os dados");
    } finally {
      setLoading(false);
    }
  }, [ordensDeServico?.id]);
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle>{title}</DialogTitle>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </DialogHeader>

        <div className="py-4 space-y-4">
          {orcamentos.length === 0 ? (
            <div className="text-center py-10 text-gray-500">Nenhum orçamento encontrado.</div>
          ) : (
            orcamentos.map((orcamento) => (
              <CardOrcamento
                key={orcamento.id}
                orcamento={orcamento}
                os={os}
                onSelectOrcamento={onSelectOrcamento}
                onShowModal={onShowModal}
                selected={selectedOrcamento?.id === orcamento.id}
                principal={orcamento.status !== "lançada" && orcamento.status !== "orcamentaçao"}
                showDeleteButton={false}
                showPecasButton={false}
                showReplicarButton={false}
              />
            ))
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
