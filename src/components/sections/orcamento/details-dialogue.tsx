import ModalOS from "@/components/modal/modal.os";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { format } from "date-fns";
import React from "react";

interface DetailsDialogueProps {
  isDialogOpen: boolean;
  setIsDialogOpen: (open: boolean) => void;
  selectedRowData: OS | null;
  handleChangeOS: (rowData: OS) => void;
  showModal: { id: string; status: string };
  tabsPT: string[];
}

const DetailsDialogue: React.FC<DetailsDialogueProps> = ({
  isDialogOpen,
  setIsDialogOpen,
  selectedRowData,
  handleChangeOS,
  showModal,
  tabsPT,
}) => {
  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogContent className="overflow-y-auto ">
        <DialogHeader>
          <DialogTitle>Detalhes da Ordem de Serviço</DialogTitle>
          <DialogDescription>
            Aqui estão os detalhes da ordem de serviço selecionada.
          </DialogDescription>
        </DialogHeader>
        <Tabs defaultValue="os">
          <div className="flex justify-between gap-4 w-full mb-4">
            <TabsList>
              <TabsTrigger value="os">Ordem de serviço</TabsTrigger>
              <TabsTrigger value="veiculo">Veículo</TabsTrigger>
            </TabsList>
          
          </div>
          <TabsContent value="os">
            {selectedRowData && (
              <div className="space-y-2">
                <div className="space-y-1">
                  <h4 className="text-sm font-medium leading-none">Número da OS</h4>
                  <p className="text-sm text-muted-foreground">
                    {`#${selectedRowData.osNumber}/${new Date(
                      selectedRowData.createdAt
                    ).getFullYear()}`}
                  </p>
                </div>
                <Separator className="my-4" />

                <div className="space-y-1">
                  <h4 className="text-sm font-medium leading-none">Centro de custo</h4>
                  <p className="text-sm text-muted-foreground">
                    {selectedRowData.veiculo?.lotacao_veiculos?.centro_custo?.descricao ||
                      "Sem informações"}
                  </p>
                </div>
                <Separator className="my-4" />

                <div className="space-y-1">
                  <h4 className="text-sm font-medium leading-none">Data de lançamento</h4>
                  <p className="text-sm text-muted-foreground">
                    {format(selectedRowData.createdAt, "dd/MM/yyyy") || "Sem informações"}
                  </p>
                </div>
                <Separator className="my-4" />

                <div className="space-y-1">
                  <h4 className="text-sm font-medium leading-none">Tipo de serviço</h4>
                  <p className="text-sm text-muted-foreground">
                    {selectedRowData.TiposDeOs?.descricao || "Sem informações"}
                  </p>
                </div>
                <Separator className="my-4" />

                <div className="space-y-1">
                  <h4 className="text-sm font-medium leading-none">Tipo de manutenção</h4>
                  <p className="text-sm text-muted-foreground">
                    {selectedRowData.tipo_manutencao || "Sem informações"}
                  </p>
                </div>
                <Separator className="my-4" />

                <div className="space-y-1">
                  <h4 className="text-sm font-medium leading-none">Observações</h4>
                  <p className="text-sm text-muted-foreground">
                    {selectedRowData.descricao || "Sem informações"}
                  </p>
                </div>
                <Separator className="my-4" />

                <div className="space-y-1">
                  <h4 className="text-sm font-medium leading-none">Veículo imobilizado</h4>
                  <p className="text-sm text-muted-foreground">
                    {selectedRowData.mobilizado ? "Sim" : "Não"}
                  </p>
                </div>
                <Separator className="my-4" />

                <div className="space-y-1">
                  <h4 className="text-sm font-medium leading-none">Localização</h4>
                  <p className="text-sm text-muted-foreground">
                    {`${selectedRowData.cidade_loc} - ${selectedRowData.estado_loc}` ||
                      "Sem informações"}
                  </p>
                </div>
                <Separator className="my-4" />

                <div className="space-y-1">
                  <h4 className="text-sm font-medium leading-none">Credenciado orçamentista</h4>
                  <p className="text-sm text-muted-foreground">
                    {selectedRowData.credenciado?.informacoes[0]?.razao_social || "Sem informações"}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {selectedRowData.credenciado?.informacoes[0]?.cnpj || "Sem informações"}
                  </p>
                </div>
              </div>
            )}
          </TabsContent>
          <TabsContent value="veiculo">
            {selectedRowData?.veiculo && (
              <div className="space-y-2">
                <div className="space-y-1">
                  <h4 className="text-sm font-medium leading-none">Placa</h4>
                  <p className="text-sm text-muted-foreground">
                    {selectedRowData.veiculo.placa || "Sem informações"}
                  </p>
                </div>
                <Separator className="my-4" />

                <div className="space-y-1">
                  <h4 className="text-sm font-medium leading-none">Marca</h4>
                  <p className="text-sm text-muted-foreground">
                    {selectedRowData.veiculo.marca?.descricao || "Sem informações"}
                  </p>
                </div>
                <Separator className="my-4" />

                <div className="space-y-1">
                  <h4 className="text-sm font-medium leading-none">Modelo</h4>
                  <p className="text-sm text-muted-foreground">
                    {selectedRowData.veiculo.modelo?.descricao || "Sem informações"}
                  </p>
                </div>
                <Separator className="my-4" />

                <div className="space-y-1">
                  <h4 className="text-sm font-medium leading-none">Ano</h4>
                  <p className="text-sm text-muted-foreground">
                    {`${selectedRowData.veiculo.ano_fab} / ${selectedRowData.veiculo.ano_modelo}` ||
                      "Sem informações"}
                  </p>
                </div>
                <Separator className="my-4" />

                <div className="space-y-1">
                  <h4 className="text-sm font-medium leading-none">Chassi</h4>
                  <p className="text-sm text-muted-foreground">
                    {selectedRowData.veiculo.vin || "Sem informações"}
                  </p>
                </div>
                <Separator className="my-4" />

                <div className="space-y-1">
                  <h4 className="text-sm font-medium leading-none">Cor</h4>
                  <p className="text-sm text-muted-foreground">
                    {selectedRowData.veiculo.cor || "Sem informações"}
                  </p>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
      {showModal.id && <ModalOS tabs={tabsPT} modal={showModal} />}
    </Dialog>
  );
};

export default DetailsDialogue;
