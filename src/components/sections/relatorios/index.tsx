import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { RelatoriosCustomizados } from "./relatorios-customizados";

export function Relatorios() {
  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Relatórios</h1>
      
      <Tabs defaultValue="customizados">
        <TabsList>
          <TabsTrigger value="customizados">Relatórios Customizados</TabsTrigger>
          {/* Adicione mais abas conforme necessário */}
        </TabsList>
        
        <TabsContent value="customizados">
          <RelatoriosCustomizados />
        </TabsContent>
      </Tabs>
    </div>
  );
} 