'use client';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from 'sonner';
import { Download, Filter, Plus, Trash, BarChart2, Pie<PERSON><PERSON> } from 'lucide-react';
import * as XLSX from 'xlsx';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, Pie, Cell } from 'recharts';
import { DateInput } from '@/components/inputs/date-input';

interface ReportField {
  name: string;
  label: string;
  type: 'string' | 'number' | 'date' | 'boolean';
  format?: string;
}

interface ReportFilter {
  startDate?: string;
  endDate?: string;
  vehicleIds?: string[];
  driverIds?: string[];
  establishmentIds?: string[];
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
  groupBy?: string[];
}

export function RelatoriosCustomizados() {
  const [availableFields, setAvailableFields] = useState<ReportField[]>([]);
  const [selectedFields, setSelectedFields] = useState<string[]>([]);
  const [filters, setFilters] = useState<ReportFilter>({});
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [chartType, setChartType] = useState<'bar' | 'pie' | null>(null);
  const [chartData, setChartData] = useState<any[]>([]);

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

  useEffect(() => {
    loadAvailableFields();
  }, []);

  const loadAvailableFields = async () => {
    try {
      const response = await fetch('/api/reports/fields');
      if (!response.ok) throw new Error('Erro ao carregar campos');
      const fields = await response.json();
      setAvailableFields(fields);
    } catch (error) {
      toast.error('Erro ao carregar campos disponíveis');
    }
  };

  const handleFieldToggle = (fieldName: string) => {
    setSelectedFields(prev => 
      prev.includes(fieldName) 
        ? prev.filter(f => f !== fieldName)
        : [...prev, fieldName]
    );
  };

  const handleGenerateReport = async () => {
    if (selectedFields.length === 0) {
      toast.error('Selecione pelo menos um campo para o relatório');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/reports/custom', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fields: availableFields.filter(f => selectedFields.includes(f.name)),
          filters
        })
      });

      if (!response.ok) throw new Error('Erro ao gerar relatório');
      const data = await response.json();
      setResults(data);
      toast.success('Relatório gerado com sucesso');
    } catch (error) {
      toast.error('Erro ao gerar relatório');
    } finally {
      setLoading(false);
    }
  };

  const handleExportExcel = () => {
    if (results.length === 0) {
      toast.error('Não há dados para exportar');
      return;
    }

    try {
      // Criar uma nova planilha
      const ws = XLSX.utils.json_to_sheet(results);
      
      // Criar um novo workbook
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Relatório');
      
      // Gerar o arquivo
      XLSX.writeFile(wb, 'relatorio.xlsx');
      
      toast.success('Relatório exportado com sucesso');
    } catch (error) {
      console.error('Erro ao exportar relatório:', error);
      toast.error('Erro ao exportar relatório');
    }
  };

  const handleGenerateChart = () => {
    if (results.length === 0) {
      toast.error('Não há dados para gerar gráfico');
      return;
    }

    if (selectedFields.length < 2) {
      toast.error('Selecione pelo menos dois campos para gerar o gráfico');
      return;
    }

    // Agrupar dados para o gráfico
    const groupedData = results.reduce((acc: any, curr: any) => {
      const key = curr[selectedFields[0]];
      if (!acc[key]) {
        acc[key] = 0;
      }
      acc[key] += Number(curr[selectedFields[1]]) || 0;
      return acc;
    }, {});

    const chartData = Object.entries(groupedData).map(([name, value]) => ({
      name,
      value
    }));

    setChartData(chartData);
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Relatório Customizado</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Seleção de Campos */}
            <div>
              <Label>Campos do Relatório</Label>
              <div className="grid grid-cols-3 gap-2 mt-2">
                {availableFields.map(field => (
                  <div key={field.name} className="flex items-center space-x-2">
                    <Checkbox
                      id={field.name}
                      checked={selectedFields.includes(field.name)}
                      onCheckedChange={() => handleFieldToggle(field.name)}
                    />
                    <Label htmlFor={field.name}>{field.label}</Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Filtros */}
            <div className="space-y-4">
              <Label>Filtros</Label>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Data Inicial</Label>
                  <DateInput
                    value={filters.startDate || null}
                    onChange={(dateString) => setFilters(prev => ({ ...prev, startDate: dateString || undefined }))}
                  />
                </div>
                <div>
                  <Label>Data Final</Label>
                  <DateInput
                    value={filters.endDate || null}
                    onChange={(dateString) => setFilters(prev => ({ ...prev, endDate: dateString || undefined }))}
                  />
                </div>
              </div>
            </div>

            {/* Ordenação */}
            <div>
              <Label>Ordenar por</Label>
              <Select
                value={filters.orderBy}
                onValueChange={(value) => setFilters(prev => ({ ...prev, orderBy: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um campo" />
                </SelectTrigger>
                <SelectContent>
                  {selectedFields.map(fieldName => {
                    const field = availableFields.find(f => f.name === fieldName);
                    return field ? (
                      <SelectItem key={field.name} value={field.name}>
                        {field.label}
                      </SelectItem>
                    ) : null;
                  })}
                </SelectContent>
              </Select>
            </div>

            {/* Botões de Ação */}
            <div className="flex space-x-2">
              <Button onClick={handleGenerateReport} disabled={loading}>
                Gerar Relatório
              </Button>
              <Button variant="outline" onClick={handleExportExcel}>
                <Download className="w-4 h-4 mr-2" />
                Exportar Excel
              </Button>
            </div>

            {/* Visualização do Gráfico */}
            {results.length > 0 && (
              <div className="space-y-4">
                <Label>Visualização do Gráfico</Label>
                <div className="flex space-x-2">
                  <Button
                    variant={chartType === 'bar' ? 'default' : 'outline'}
                    onClick={() => setChartType('bar')}
                  >
                    <BarChart2 className="w-4 h-4 mr-2" />
                    Gráfico de Barras
                  </Button>
                  <Button
                    variant={chartType === 'pie' ? 'default' : 'outline'}
                    onClick={() => setChartType('pie')}
                  >
                    <PieChart className="w-4 h-4 mr-2" />
                    Gráfico de Pizza
                  </Button>
                </div>

                {chartType && (
                  <div className="h-[400px] w-full">
                    {chartType === 'bar' ? (
                      <BarChart width={800} height={400} data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="value" fill="#8884d8" />
                      </BarChart>
                    ) : (
                      <PieChart width={800} height={400}>
                        <Pie
                          data={chartData}
                          cx={400}
                          cy={200}
                          labelLine={false}
                          label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                          outerRadius={150}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {chartData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip />
                        <Legend />
                      </PieChart>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Resultados */}
      {results.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Resultados</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  {selectedFields.map(fieldName => {
                    const field = availableFields.find(f => f.name === fieldName);
                    return field ? (
                      <TableHead key={field.name}>{field.label}</TableHead>
                    ) : null;
                  })}
                </TableRow>
              </TableHeader>
              <TableBody>
                {results.map((result, index) => (
                  <TableRow key={index}>
                    {selectedFields.map(fieldName => (
                      <TableCell key={fieldName}>{result[fieldName]}</TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 