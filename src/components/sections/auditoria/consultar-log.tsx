"use client";

import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { useEffect } from "react";
import { useAuditoria } from "@/context/auditoria";

interface Log {
  id: string;
  usuario_nome: string;
  acao_do_usuario: string;
  funcao_do_usuario: string;
  ip_requisicao: string;
  createdAd: Date;
}

const logColumns: ColumnDef<Log>[] = [
  {
    accessorKey: "usuario_nome",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Nome" />
    ),
  },
  {
    accessorKey: "usuario_matricula",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Matrícula" />
    ),
  },
  {
    accessorKey: "acao_do_usuario",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Ação" />
    ),
  },
  {
    accessorKey: "funcao_do_usuario",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Função" />
    ),
  },
  {
    accessorKey: "ip_requisicao",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="IP" />
    ),
  },
  {
    accessorKey: "createdAd",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Data do Evento" />
    ),
    cell: ({ row }) => {
      const data = new Date(row.getValue("createdAd"));
      return format(data, "dd/MM/yyyy HH:mm:ss");
    },
  },
];

export function LogTable() {
  const { logs, isLoading, fetchLogs } = useAuditoria();

  console.log("LOGS", logs);

  useEffect(() => {
    fetchLogs(1, 10);
  }, [fetchLogs]);

  if (isLoading) {
    return <div>Carregando...</div>;
  }
  return <DataTable data={logs} columns={logColumns} exportTo={true} />;
}
