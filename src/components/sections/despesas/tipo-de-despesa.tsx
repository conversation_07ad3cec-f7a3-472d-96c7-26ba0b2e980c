"use client";
import { TiposDeDeDespesaConfig } from "@/components/forms/inputConfig";
import { tiposDeDespesaSchema } from "@/components/forms/schemas";
import { SheetForm } from "@/components/forms/sheet-form";
import { FieldConfig, GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogClose,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useTipoDeDespesa } from "@/context/tipos-de-despesa-context";
import { ColumnDef } from "@tanstack/react-table";
import { Delete, Edit, Search, Plus, RefreshCcw, FilterX } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { z, ZodType } from "zod";

// Componente de célula de ações
function ActionCell({ row }: { row: any }) {
  const { setTiposDeDespesa } = useTipoDeDespesa();
  const [open, setOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<tipo_de_despesa | null>(null);

  const editItem = async () => {
    setSelectedItem(row.original);
    setOpen(true);
  };

  const updateItem = async (
    values: z.infer<typeof tiposDeDespesaSchema>
  ) => {
    try {
      const res = await fetch(`/api/tipos-de-despesa/${row.original.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values),
      });

      if (!res.ok) {
        toast("Erro ao atualizar o tipo de despesa");
        return;
      }

      const data = await res.json();
      setTiposDeDespesa((prev) =>
        prev.map((item) =>
          item.id === data.tipo_de_despesa.id ? data.tipo_de_despesa : item
        )
      );

      toast("Tipo de despesa atualizado com sucesso!");
      setOpen(false);
    } catch (error) {
      toast("Erro ao atualizar o tipo de despesa");
    }
  };

  const deleteItem = async () => {
    const res = await fetch(`/api/tipos-de-despesa/${row.original.id}`, {
      method: "DELETE",
    });
    if (!res.ok) {
      toast("Ops, algo deu errado", {
        description: "Houve um erro ao deletar o tipo de despesa",
      });
      return;
    }
    setTiposDeDespesa((prev) =>
      prev.filter((item) => item.id !== row.original.id)
    );
    toast("Tipo de despesa deletado com sucesso!");
  };

  return (
    <div className="flex justify-end">
      <Button variant="ghost" size="icon" onClick={editItem} className="h-8 w-8 p-0 text-blue-500">
        <Edit className="h-4 w-4" />
      </Button>
      <Button 
        variant="ghost" 
        size="icon"
        className="h-8 w-8 p-0"
        onClick={() => {
          toast("Tem certeza?", {
            description: "Essa ação não pode ser desfeita",
            action: {
              label: "Tenho certeza!",
              onClick: deleteItem,
            },
          });
        }}
      >
        <Delete className="h-4 w-4" />
      </Button>
      {selectedItem && open && (
        <SheetForm
          title="Editar Tipo de Despesa"
          schema={tiposDeDespesaSchema}
          onSubmit={updateItem}
          triggerLabel="Editar Tipo de Despesa"
          defaultValues={selectedItem}
        >
          <GenericFormsInput
            fieldConfig={TiposDeDeDespesaConfig as Record<string, FieldConfig<ZodType<any, any, any>>>}
            variants="single"
          />
        </SheetForm>
      )}
    </div>
  );
}

export const tipoDeDespesaColumn: ColumnDef<tipo_de_despesa>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "descricao",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Descrição do tipo" />
    ),
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => <ActionCell row={row} />,
  },
];

export function TipoDeDespesaTable() {
  const { tiposDeDespesa, setTiposDeDespesa, loading } = useTipoDeDespesa();
  const [searchTerm, setSearchTerm] = useState("");
  const [showNewForm, setShowNewForm] = useState(false);
  const [novoTipo, setNovoTipo] = useState("");
  const [refreshing, setRefreshing] = useState(false);

  async function refreshTiposDespesa() {
    setRefreshing(true);
    try {
      const response = await fetch("/api/tipos-de-despesa");
      if (!response.ok) {
        throw new Error("Erro ao buscar tipos de despesa");
      }
      const data = await response.json();
      setTiposDeDespesa(data.data);
      toast.success("Lista atualizada com sucesso");
    } catch (error) {
      toast.error("Erro ao atualizar a lista");
    } finally {
      setRefreshing(false);
    }
  }

  const clearFilters = () => {
    setSearchTerm("");
    toast.success("Filtros limpos com sucesso");
  };

  async function onNewTipoDeDespesa() {
    if (!novoTipo.trim()) {
      toast.error("Por favor, preencha a descrição do tipo de despesa");
      return;
    }
    
    try {
      const response = await fetch("/api/tipos-de-despesa", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ descricao: novoTipo }),
      });

      if (!response.ok) {
        throw new Error("Erro ao criar tipo de despesa");
      }

      const data = await response.json();
      setTiposDeDespesa((prev) => [...prev, data.tipo_de_despesa]);
      toast.success("Tipo de despesa cadastrado com sucesso!");
      setShowNewForm(false);
      setNovoTipo("");
    } catch (error) {
      toast.error("Ops, algo deu errado", {
        description: "Houve um erro ao criar o tipo de despesa",
      });
    }
  }

  const filteredData = tiposDeDespesa.filter(item => 
    item.descricao.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex gap-2 items-center">
          <div className="relative w-64">
            <Input
              placeholder="Busca rápida"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-10"
            />
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={clearFilters}
            disabled={!searchTerm}
            className="h-10 w-10 border"
          >
            <FilterX className="h-4 w-4" />
          </Button>
         
          <Button
            variant="ghost"
            size="icon"
            onClick={refreshTiposDespesa}
            disabled={refreshing}
            className="h-10 w-10 border"
          >
            <RefreshCcw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          </Button>
          
        </div>
        
        <div className="flex items-center gap-2">
          
          <Button 
            onClick={() => setShowNewForm(true)}
            className="bg-gray-900 text-white hover:bg-gray-800 h-10"
          >
            <Plus className="mr-2 h-4 w-4" />
            Novo tipo
          </Button>
        </div>
      </div>
      
      <DataTable
        data={filteredData}
        columns={tipoDeDespesaColumn}
        exportTo={true}
      />
      
      <Dialog open={showNewForm} onOpenChange={setShowNewForm}>
        <DialogContent className="sm:max-w-md">
          <DialogTitle>Cadastre um tipo de despesa</DialogTitle>
          <div className="py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="descricao-tipo">Descrição do tipo</Label>
                <Input
                  id="descricao-tipo"
                  placeholder="Troca de óleo"
                  value={novoTipo}
                  onChange={(e) => setNovoTipo(e.target.value)}
                  className="border border-gray-300"
                />
              </div>
            </div>
          </div>
          <DialogFooter className="flex justify-end gap-2">
            <DialogClose asChild>
              <Button variant="outline" type="button" className="h-10">
                Fechar
              </Button>
            </DialogClose>
            <Button 
              onClick={onNewTipoDeDespesa} 
              className="bg-green-600 text-white hover:bg-green-700 h-10"
            >
              Cadastrar tipo
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
