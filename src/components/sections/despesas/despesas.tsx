"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { GetDespesaConfig } from "@/components/forms/inputConfig";
import { despesaSchema } from "@/components/forms/schemas";
import { SheetForm } from "@/components/forms/sheet-form";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useCondutor } from "@/context/condutor-context";
import { useDespesa } from "@/context/despesa-context";
import { useTipoDeDespesa } from "@/context/tipos-de-despesa-context";
import { useVeiculos } from "@/context/veiculos-context";
import { ColumnDef } from "@tanstack/react-table";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { z } from "zod";
import { Edit, Delete, MoreHorizontal } from "lucide-react";

export const despesaColumns: ColumnDef<despesa>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "veiculos[0].placa",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Veículo" />,
    accessorFn: (row) => (row.veiculos && row.veiculos[0].placa) || "Não informado",
  },
  {
    accessorKey: "odometro",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Odômetro" />,
    accessorFn: (row) => `${row.odometro} Km` || "Não informado",
  },
  {
    accessorKey: "tipo_despesa.descricao",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Tipo de Despesa" />,
  },
  {
    accessorKey: "local",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Local" />,
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const { setDespesas } = useDespesa();
      const router = useRouter();

      const deleteItem = async () => {
        const res = await fetch(`/api/despesa/${row.original.id}`, {
          method: "DELETE",
        });
        if (!res.ok) {
          toast("Ops, algo deu errado", {
            description: "Houve um erro ao deletar a despesa",
          });
          return;
        }
        setDespesas((prev) => prev.filter((item) => item.id !== row.original.id));
        toast("Despesa deletada com sucesso!");
      };

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Abrir o menu</span>
              <MoreHorizontal />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Ações</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => router.push(`/dashboard/despesas/editar-despesa/${row.original.id}`)}>
              Editar
              <Edit className="ml-2 h-4 w-4" />
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                toast("Tem certeza?", {
                  description: "Essa ação não pode ser desfeita",
                  action: {
                    label: "Tenho certeza!",
                    onClick: deleteItem,
                  },
                });
              }}>
              Deletar
              <Delete className="ml-2 h-4 w-4" />
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

export function DespesaTable() {
  const router = useRouter();
  const { despesas, setDespesas } = useDespesa();
  const { veiculos } = useVeiculos();
  const { condutores } = useCondutor();
  const { tiposDeDespesa } = useTipoDeDespesa();

  async function onNewDespesa(values: z.infer<typeof despesaSchema>) {
    const response = await fetch("/api/despesa", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });

    if (!response.ok) {
      toast("Ops, algo deu errado", {
        description: "Houve um erro ao criar a despesa",
      });
      return;
    }

    const data = await response.json();
    setDespesas((prev) => [...prev, data.data]);
  }

  return (
    <DataTable
      data={despesas}
      onClick={() => router.push("/dashboard/despesas/nova-despesa")}
      exportTo={true}
      columns={despesaColumns}
    />
  );
}
