"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { GetDespesaConfig } from "@/components/forms/inputConfig";
import { PageForm } from "@/components/forms/page-form";
import { despesaSchema } from "@/components/forms/schemas";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { Button } from "@/components/ui/button";
import { useCondutor } from "@/context/condutor-context";
import { useDespesa } from "@/context/despesa-context";
import { useTipoDeDespesa } from "@/context/tipos-de-despesa-context";
import { useVeiculos } from "@/context/veiculos-context";
import { toast } from "sonner";
import { z } from "zod";
import { useEffect, useState } from "react";

interface DespesasFormProps {
  id?: string; // Prop opcional para o ID da despesa
}

export function DespesasForm({ id }: DespesasFormProps) {
  const { veiculos } = useVeiculos();
  const { condutores } = useCondutor();
  const { tiposDeDespesa } = useTipoDeDespesa();
  const { setDespesas } = useDespesa();
  const [initialValues, setInitialValues] = useState<z.infer<typeof despesaSchema> | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Busca os dados da despesa se um ID for fornecido
  useEffect(() => {
    if (id) {
      const fetchDespesa = async () => {
        setIsLoading(true);
        const res = await fetch(`/api/despesa/${id}`);
        if (!res.ok) {
          toast("Erro ao carregar a despesa");
          setIsLoading(false);
          return;
        }
        const data = await res.json();
        setInitialValues(data.data.despesa);
        setIsLoading(false);
      };

      fetchDespesa();
    }
  }, [id]);

  const undoDespesa = async (id: string) => {
    const res = await fetch(`/api/despesas/${id}`, {
      method: "DELETE",
    });
    if (!res.ok) {
      toast("Erro ao reverter", {
        description: "Falha ao cancelar o registro da despesa",
      });
      return;
    }

    setDespesas((prev) => prev.filter((despesa) => despesa.id !== id));
    toast("Despesa removida", {
      description: "O registro foi cancelado com sucesso",
    });
  };

  // Função para fazer upload de arquivos
  const uploadFile = async (file: File) => {
    const formData = new FormData();
    formData.append("file", file);

    const res = await fetch("/api/upload", {
      method: "POST",
      body: formData,
    });

    if (!res.ok) {
      throw new Error("Erro ao fazer upload do arquivo");
    }

    return await res.json();
  };

  async function onSubmit(values: z.infer<typeof despesaSchema>) {
    try {
      // Faz o upload dos arquivos, se houver
      let arquivosUrls: string[] = [];
      if (values.arquivos && values.arquivos.length > 0) {
        const uploadPromises = values.arquivos.map((file: any) => uploadFile(file));
        const uploadResponses = await Promise.all(uploadPromises);
        arquivosUrls = uploadResponses.map((response) => response.file.path);
      }

      if (id) {
        // Se houver um ID, faz um PUT para atualizar a despesa
        const res = await fetch(`/api/despesa/${id}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ...values,
            arquivos: arquivosUrls, // Substitui os arquivos pelas URLs dos arquivos
          }),
        });

        if (!res.ok) {
          toast("Erro na atualização", {
            description: "Falha ao atualizar a despesa",
          });
          return;
        }

        const data = await res.json();
        setDespesas((prev) => prev.map((despesa) => (despesa.id === id ? data.data : despesa)));

        toast("Despesa atualizada!", {
          description: "Atualização realizada com sucesso",
        });
        window.location.href = "/dashboard/despesas/consultar-despesa";
      } else {
        // Se não houver um ID, faz um POST para criar uma nova despesa
        const res = await fetch("/api/despesa", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ...values,
            arquivos: arquivosUrls, // Substitui os arquivos pelas URLs dos arquivos
          }),
        });

        if (!res.ok) {
          toast("Erro no registro", {
            description: "Falha ao registrar a despesa",
          });
          return;
        }

        const data = await res.json();
        setDespesas((prev) => [...prev, data.data]);

        toast("Despesa registrada!", {
          description: "Registro realizado com sucesso",
          action: (
            <Button variant="outline" onClick={async () => await undoDespesa(data.data.id)}>
              Desfazer
            </Button>
          ),
        });
        window.location.href = "/dashboard/despesas/consultar-despesas";
      }
    } catch (error) {
      toast("Erro no upload", {
        description: "Falha ao enviar os arquivos",
      });
    }
  }

  if (isLoading) {
    return <div className="p-8">Carregando...</div>;
  }

  return (
    <div className="p-4">
      <PageForm
        onSubmit={onSubmit}
        schema={despesaSchema}
        defaultValues={initialValues || defaultValues.despesaSchema}>
        <GenericFormsInput
          fieldConfig={GetDespesaConfig(
            [veiculos, tiposDeDespesa, condutores],
            ["modelo.descricao", "descricao", "nome"]
          )}
        />
      </PageForm>
    </div>
  );
}
