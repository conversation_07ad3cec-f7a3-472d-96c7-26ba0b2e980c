"use client";

import { Combobox } from "@/components/inputs/combo-box";
import { MultipleSelection } from "@/components/inputs/multi-selector-input";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

const acessoSchema = z.object({
  contratoId: z.string().min(1, "Contrato é obrigatório"),
  centro_de_custoId: z.string().min(1, "Centro de custo é obrigatório"),
  grupos_acesso: z.array(z.object({ value: z.string(), label: z.string() })).min(1, "Selecione ao menos um grupo"),
});

export default function AccessModalForm({ 
  initialValues, 
  onSubmit, 
  onCancel, 
  contratos, 
  centrosDeCusto, 
  accessGroups 
}: {
  initialValues?: z.infer<typeof acessoSchema>;
  onSubmit: (values: z.infer<typeof acessoSchema>) => void;
  onCancel: () => void;
  contratos: any[];
  centrosDeCusto: any[];
  accessGroups: { value: string; label: string }[];
}) {
  const form = useForm<z.infer<typeof acessoSchema>>({
    resolver: zodResolver(acessoSchema),
    defaultValues: initialValues || {
      contratoId: "",
      centro_de_custoId: "",
      grupos_acesso: [],
    },
  });

  const handleSubmit = (values: z.infer<typeof acessoSchema>) => {
    onSubmit(values);
    form.reset();
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <Combobox
          datas={contratos}
          title="Contrato"
          placeholder="Escolha o contrato"
          referenceId="contratoId"
          chave="nome_contrato"
          name="contratoId"
        />

        <Combobox
          datas={centrosDeCusto}
          title="Centro de custo"
          placeholder="Selecione o centro de custo"
          referenceId="centro_de_custoId"
          chave="descricao"
          name="centro_de_custoId"
        />

        <MultipleSelection
          name="grupos_acesso"
          options={accessGroups}
          placeHolder="Escolha os grupos de acesso"
        />

        <div className="flex justify-end gap-2">
          <Button variant="outline" type="button" onClick={onCancel}>
            Fechar
          </Button>
          <Button type="submit">
            {initialValues ? 'Atualizar' : 'Cadastrar'}
          </Button>
        </div>
      </form>
    </Form>
  );
}