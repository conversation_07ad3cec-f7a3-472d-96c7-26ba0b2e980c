"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Edit,
  Delete,
  MoreHorizontal,
  UserX,
  RotateCcw,
  Check,
} from "lucide-react";
import { SheetForm } from "@/components/forms/sheet-form";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { GetUserConfig } from "@/components/forms/inputConfig";
import { usuarioSchema } from "@/components/forms/schemas";
import { useUsuario } from "@/context/usuario-context";
import { z } from "zod";
import { useContrato } from "@/context/contrato-context";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { useRouter } from "next/navigation";
import { useSession } from "@/components/session/use-session";
import {
  inactivateUserAction,
  inactivateUserCreateOsAction,
} from "@/serverActions/userAction";
import ConfirmModal from "@/components/modal/confirm-modal-user";

interface UsuarioActionsProps {
  usuario: usuario;
}

export function UsuarioActions({ usuario }: UsuarioActionsProps) {
  const { setUsuarios } = useUsuario();
  const { contratos } = useContrato();
  const { centrosDeCusto } = useCentroDeCusto();
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [modalConfig, setModalConfig] = useState<{
    title: string;
    message: string;
    onConfirm: () => void;
  } | null>(null);
  const router = useRouter();
  const { session, setSession } = useSession();

  const editItem = () => {
    window.location.href = `/dashboard/usuarios/editar-usuario/${usuario.id}`;
    // setOpen(true);
  };

  const updateItem = async (values: z.infer<typeof usuarioSchema>) => {
    try {
      setIsLoading(true);
      const rolesToSend = values.roles.map((role) => role.value);

      const response = await fetch(`/api/usuario/${usuario.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...values,
          roles: rolesToSend,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setUsuarios((prev) =>
          prev.map((u) => (u.id === usuario.id ? data.data.user : u))
        );

        setOpen(false);
        window.location.href = "/dashboard/usuarios/usuarios-sistema";
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || "Erro ao atualizar usuário");
      }
    } catch (error: any) {
      // Trate o erro conforme necessário
      console.error(
        error.message ||
          "Erro ao atualizar usuário. Por favor, tente novamente."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const deleteItem = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/usuario/${usuario.id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        setUsuarios((prev) => prev.filter((u) => u.id !== usuario.id));
        window.location.href = "/dashboard/usuarios/usuarios-sistema";
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || "Erro ao excluir usuário");
      }
    } catch (error: any) {
      console.error(
        error.message || "Erro ao excluir usuário. Por favor, tente novamente."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const inactivateUser = async () => {
    try {
      setIsLoading(true);
      await inactivateUserAction(usuario.id);
      window.location.reload();
    } catch (error: any) {
      console.error(error.message || "Erro ao inativar usuário");
    } finally {
      setIsLoading(false);
    }
  };

  const inactivateUserCreateOs = async () => {
    try {
      setIsLoading(true);
      await inactivateUserCreateOsAction(usuario.id);
      window.location.reload();
    } catch (error: any) {
      console.error(error.message || "Erro ao inativar usuário");
    } finally {
      setIsLoading(false);
    }
  };

  const openConfirmModal = (
    title: string,
    message: string,
    onConfirm: () => void
  ) => {
    setModalConfig({ title, message, onConfirm });
    setConfirmModalOpen(true);
  };

  const resetPassword = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/auth/reset-password-v2`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
        body: JSON.stringify({
          userId: usuario.id,
          password: "123456",
        }),
      });

      if (response.ok) {
        window.location.href = "/dashboard/usuarios/usuarios-sistema";
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || "Erro ao resetar senha");
      }
    } catch (error: any) {
      console.error(
        error.message || "Erro ao resetar senha. Por favor, tente novamente."
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0" disabled={isLoading}>
            <span className="sr-only">Abrir o menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Ações</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={editItem} disabled={isLoading}>
            <Edit className="mr-2 h-4 w-4" />
            Editar
          </DropdownMenuItem>
          {usuario.roles.includes("ORCAMENTISTA_OFICINA") && (
            <DropdownMenuItem
              onClick={() =>
                openConfirmModal(
                  "Confirmar Resete de Senha",
                  "Deseja resetar a senha deste usuário? Essa ação não pode ser desfeita",
                  async () => {
                    await resetPassword();
                    setConfirmModalOpen(false);
                  }
                )
              }
              disabled={isLoading}
            >
              <RotateCcw className="mr-2 h-4 w-4" />
              Resetar senha
            </DropdownMenuItem>
          )}
          <DropdownMenuItem
            onClick={() =>
              openConfirmModal(
                "Confirmar Inativação",
                "Deseja inativar este usuário? Essa ação não pode ser desfeita",
                async () => {
                  await inactivateUser();
                  setConfirmModalOpen(false);
                }
              )
            }
            disabled={isLoading}
            className="text-destructive"
          >
            <UserX className="mr-2 h-4 w-4" />
            Inativar
          </DropdownMenuItem>
          {session?.roles.includes("GESTOR_GERAL") && (
            <DropdownMenuItem
              onClick={() =>
                openConfirmModal(
                  usuario.create_os
                    ? "Desabilitar Criação de OS"
                    : "Habilitar Criação de OS",
                  usuario.create_os
                    ? "Deseja bloquear que este usuário crie OS? Essa ação não pode ser desfeita"
                    : "Deseja permitir que este usuário crie OS? Essa ação não pode ser desfeita",
                  async () => {
                    await inactivateUserCreateOs();
                    setConfirmModalOpen(false);
                  }
                )
              }
              className={usuario.create_os ? "text-destructive" : ""}
              disabled={isLoading}
            >
              {usuario.create_os ? (
                <UserX className="mr-2 h-4 w-4" />
              ) : (
                <Check className="mr-2 h-4 w-4" />
              )}
              {usuario.create_os
                ? "Desabilitar criação de OS"
                : "Habilitar criação de OS"}
            </DropdownMenuItem>
          )}

          <DropdownMenuItem
            onClick={() =>
              openConfirmModal(
                "Confirmar Exclusão",
                "Deseja excluir este usuário? Essa ação não pode ser desfeita",
                async () => {
                  await deleteItem();
                  setConfirmModalOpen(false);
                }
              )
            }
            disabled={isLoading}
            className="text-destructive"
          >
            <Delete className="mr-2 h-4 w-4" />
            Deletar
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      {open && (
        <SheetForm
          title="Editar Usuário"
          schema={usuarioSchema}
          onSubmit={updateItem}
          triggerLabel="Editar Usuário"
          defaultValues={{
            ...usuario,
            nivel_aprovacao:
              typeof usuario.nivel_aprovacao === "number"
                ? usuario.nivel_aprovacao
                : 0,
            // Transform roles array to the expected format
            roles: usuario.roles?.map((role) => ({ value: role, label: role })),
            contratos: usuario.contratos?.map((contrato) => ({
              value:
                typeof contrato === "object" ? contrato.id || "" : contrato,
              label:
                typeof contrato === "object"
                  ? contrato.nome_contrato || contrato.id
                  : contrato,
            })),
            // Transform unidade_filha_id to the expected format
            unidade_filha_id: usuario.unidade_filha_id
              ? Array.isArray(usuario.unidade_filha_id)
                ? usuario.unidade_filha_id.map((id) => {
                    const unidadeInfo = centrosDeCusto
                      .flatMap((c) => c.centro_custos_filhos || [])
                      .find((u) => u.id === id);
                    return {
                      value: id,
                      label: unidadeInfo?.descricao || "Unidade não encontrada",
                    };
                  })
                : [{ value: usuario.unidade_filha_id, label: "Unidade" }]
              : [],
            // Transform unidades_filhas array if present
            unidades_filha_ids: usuario.unidades_filhas
              ? Array.isArray(usuario.unidades_filhas)
                ? usuario.unidades_filhas.map((u) =>
                    typeof u === "string" ? u : u.id
                  )
                : []
              : [],
          }}
        >
          <GenericFormsInput
            fieldConfig={
              GetUserConfig(
                [contratos, centrosDeCusto],
                ["nome_contrato", "descricao"]
              ) as any
            }
            variants="single"
          />
        </SheetForm>
      )}
      {modalConfig && (
        <ConfirmModal
          open={confirmModalOpen}
          title={modalConfig.title}
          message={modalConfig.message}
          onConfirm={modalConfig.onConfirm}
          onCancel={() => setConfirmModalOpen(false)}
        />
      )}
    </>
  );
}
