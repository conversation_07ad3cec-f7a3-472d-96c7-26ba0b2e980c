"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { GetUserConfig } from "@/components/forms/inputConfig";
import { PageForm } from "@/components/forms/page-form";
import { usuarioSchema } from "@/components/forms/schemas";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { useSession } from "@/components/session/use-session";
import { Button } from "@/components/ui/button";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { useContrato } from "@/context/contrato-context";
import { useUsuario } from "@/context/usuario-context";
import { toast } from "sonner";
import { z } from "zod";

export function UsuarioForm() {
  const { setUsuarios } = useUsuario();
  const { contratos } = useContrato();
  const { centrosDeCusto } = useCentroDeCusto();
  const { session, setSession } = useSession();
 

  const undoUsuario = async (id: string) => {
    const res = await fetch(`/api/usuarios/${id}`, {
      method: "DELETE",
    });

    if (!res.ok) {
      toast("Erro ao reverter", {
        description: "Falha ao remover o usuário",
      });
      return;
    }

    setUsuarios((prev) => prev.filter((usuario) => usuario.id !== id));
    toast("Usuário removido", {
      description: "Registro cancelado com sucesso",
    });
    const session = await res.json();
      setSession(session);
      // try {
      //   await fetch("/api/auditoria", {
      //     method: "POST",
      //     headers: {
      //       "Content-Type": "application/json",
      //     },
      //     body: JSON.stringify({
      //       usuario_nome: session.user.nome,
      //       funcao_do_usuario: session.user.roles.join(", "),
      //       createdAd: new Date().toISOString(),
      //       acao_do_usuario: "deletar o usuário",
      //     }),
      //   });
      // } catch (error) {
      //   console.error("Erro ao registrar atividade de deletar o usuário:", error);
      // } 

      window.location.href = "/dashboard/usuarios/usuarios-contrato";
  };

  async function onSubmit(values: z.infer<typeof usuarioSchema>) {
    const res = await fetch("/api/usuarios", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });

    if (!res.ok) {
      toast("Erro no cadastro", {
        description: "Falha ao registrar o usuário",
      });
      return;
    }

    const data = await res.json();
    setUsuarios((prev) => [...prev, data.data]);

    toast("Usuário cadastrado!", {
      description: "Cadastro realizado com sucesso",
      action: (
        <Button variant="outline" onClick={async () => await undoUsuario(data.data.id)}>
          Desfazer
        </Button>
      ),
    });
    const session = await res.json();
      setSession(session);
      // try {
      //   await fetch("/api/auditoria", {
      //     method: "POST",
      //     headers: {
      //       "Content-Type": "application/json",
      //     },
      //     body: JSON.stringify({
      //       usuario_nome: session.user.nome,
      //       funcao_do_usuario: session.user.roles.join(", "),
      //       createdAd: new Date().toISOString(),
      //       acao_do_usuario: "criar o usuário",
      //     }),
      //   });
      // } catch (error) {
      //   console.error("Erro ao registrar atividade de criar o usuário:", error);
      // }
  }

 const centroCustoFilho = centrosDeCusto.flatMap(
   (centro) =>
     centro.centro_custos_filhos?.map((filho) => ({
       id: filho.id,
       descricao: filho.descricao,
       centro_custo_ascdID: centro.id, 
     })) || []
 );
 
  return (
    <div className="p-4 ">
      <PageForm
        onSubmit={onSubmit}
        schema={usuarioSchema}
        defaultValues={defaultValues.usuarioSchema}>
        <GenericFormsInput<typeof usuarioSchema>
          fieldConfig={GetUserConfig([contratos, centrosDeCusto, centroCustoFilho,], ["nome_contrato", "descricao", "descriçao"])} 
        />
      </PageForm>
    </div>
  );
}
