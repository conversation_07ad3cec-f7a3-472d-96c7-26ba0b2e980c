"use client";

import { Card } from "@/components/ui/card";
import { toast } from "sonner";
import { GetUserConfig } from "@/components/forms/inputConfig";
import { useUsuario } from "@/context/usuario-context";
import { useSession } from "@/components/session/use-session";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { usuarioSchema } from "@/components/forms/schemas";
import { Button } from "@/components/ui/button";
import { z } from "zod";
import { useEffect, useState, useMemo, useRef } from "react";
import { useContrato } from "@/context/contrato-context";

type EditUsuarioType = {
  usuario: any;
  centrosDeCusto: any[];
};

export function EditUsuarioSimple({
  usuario,
  centrosDeCusto,
}: EditUsuarioType) {
  const { setUsuarios } = useUsuario();
  const { session } = useSession();
  const { contratos } = useContrato();
  const [selectedCentroDeCusto, setSelectedCentroDeCusto] = useState<string>("");
  const [selectedContratoId, setSelectedContratoId] = useState<string>("");
  const [loadedCentrosCusto, setLoadedCentrosCusto] = useState<any[]>(centrosDeCusto || []);
  
  // Ref para controlar a inicialização do formulário
  const formInitialized = useRef(false);

  const form = useForm<z.infer<typeof usuarioSchema>>({
    resolver: zodResolver(usuarioSchema),
    defaultValues: {},
  });

  // Process centrosCusto once and memoize the result
  const centroCustoFilho = useMemo(() => {
    return loadedCentrosCusto.flatMap((centro) => {
      if (!centro.centro_custos_filhos) return [];
      return centro.centro_custos_filhos.map((filho: any) => ({
        id: filho.id,
        descricao: filho.descricao || filho.razao_social || "Sem descrição",
        centro_custo_ascdID: centro.id,
      }));
    });
  }, [loadedCentrosCusto]);

  const filteredUnidades = useMemo(() => {
    if (!selectedCentroDeCusto) return [];
    return centroCustoFilho.filter(
      (unidade) => unidade.centro_custo_ascdID === selectedCentroDeCusto
    );
  }, [selectedCentroDeCusto, centroCustoFilho]);

  // Carregar centros de custo quando um contrato é selecionado
  useEffect(() => {
    if (!selectedContratoId) return;

    const fetchCentrosDeCusto = async () => {
      try {
        const response = await fetch(
          `/api/centro-de-custo/contrato/${selectedContratoId}`
        );
        const data = await response.json();
        setLoadedCentrosCusto((prev) => {
          // Evitar duplicatas verificando se já existe
            const existingIds = new Set(prev.map(c => c.id));
            const newCentros = data.data.centrosCusto.filter(
              (c: centro_de_custo) => !existingIds.has(c.id) && c.centro_custo_pai === null
            );
          return [...prev, ...newCentros];
        });
      } catch (error) {
        console.error("Erro ao buscar centros de custo:", error);
      }
    };

    fetchCentrosDeCusto();
  }, [selectedContratoId]);

  // Modifique o userConfig para garantir que os contratos não sejam substituídos
  const userConfig = useMemo(() => {
    if (!contratos || contratos.length === 0) {
      return {};
    }

    const baseConfig = GetUserConfig(
      [contratos, loadedCentrosCusto, centroCustoFilho, setSelectedContratoId, setSelectedCentroDeCusto],
      ["nome_contrato", "descricao", "descricao"]
    );

    return {
      ...baseConfig,
      contratos: {
        ...baseConfig.contratos,
        onChange: (value: any) => {
          if (!value || value.length === 0) return;
          setSelectedContratoId(value[value.length - 1].value);
        }
      },
      unidade_filha_id: {
        ...baseConfig.unidade_filha_id,
        options: filteredUnidades.map((unidade) => ({
          label: unidade.descricao || "Sem descrição",
          value: unidade.id,
        })),
      },
    };
  }, [contratos, loadedCentrosCusto, centroCustoFilho, filteredUnidades]);

  // Initialize form with user data - usando useRef para inicializar apenas uma vez
  useEffect(() => {
    // Só inicialize o formulário na primeira renderização
    if (!formInitialized.current && usuario) {
      // Transform roles for form format
      const transformedRoles = Array.isArray(usuario?.roles)
        ? usuario.roles.map((role: string) => ({ value: role, label: role }))
        : [];

      const transformedContracts = Array.isArray(usuario?.contratos)
        ? usuario.contratos.map((contrato: any) => ({
            value: contrato.id,
            label: contrato.nome_contrato,
          }))
        : [];

      // Transform unidades_filhas with correct descriptions
      const unidadeFilhaId = Array.isArray(usuario?.unidades_filhas)
        ? usuario.unidades_filhas.map((unidade: any) => {
            const unidadeId =
              typeof unidade === "string" ? unidade : unidade.id;
            const unidadeInfo = centroCustoFilho.find(
              (item) => item.id === unidadeId
            );
            return {
              value: unidadeId,
              label: unidadeInfo?.descricao || "Selecione uma unidade",
            };
          })
        : [];

      // Set the selected centro de custo state separately
      if (usuario.centro_de_custoId) {
        setSelectedCentroDeCusto(usuario.centro_de_custoId);
      }

      // Se o usuário tiver contratos, selecione o primeiro para carregar os centros de custo
      if (transformedContracts.length > 0) {
        setSelectedContratoId(transformedContracts[0].value);
      }

      form.reset({
        ...usuario,
        nivel_aprovacao: usuario.valor_nivel_aprovacao,
        roles: transformedRoles,
        unidade_filha_id: unidadeFilhaId,
        contratos: transformedContracts,
      });

      // Marque como inicializado para evitar reset futuro
      formInitialized.current = true;
    }
  }, [usuario, form]); // Removemos centroCustoFilho das dependências

  // Watch for centro_de_custoId changes
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "centro_de_custoId") {
        setSelectedCentroDeCusto(value.centro_de_custoId || "");

        // Clear unidade_filha_id when centro_de_custo changes
        form.setValue("unidade_filha_id", [] as any);
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  // Watch for contratos changes - sem resetar o formulário
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "contratos" && Array.isArray(value.contratos)) {
        if (value.contratos.length > 0) {
          const lastContract = value.contratos[value.contratos.length - 1];
          if (lastContract && lastContract.value) {
            setSelectedContratoId(lastContract.value);
          }
        }
      }
    });
  
    return () => subscription.unsubscribe();
  }, [form]);

  const handleUpdateUsuario = async (values: z.infer<typeof usuarioSchema>) => {
    console.log("Valores do formulário:", values);
    try {
      const rolesToSend = values.roles.map((role) => role.value);
      const contractIds = values.contratos?.map((contrato) => contrato.value);

      // Extract IDs from unidades filhas
      let unidadeFilhaIds = Array.isArray(values.unidade_filha_id)
        ? values.unidade_filha_id.map((unidade) => unidade.value)
        : values.unidade_filha_id;

      // Send to API with the correct field name
      const res = await fetch(`/api/usuario/${usuario.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...values,
          contratoIds: contractIds,
          roles: rolesToSend,
          valor_nivel_aprovacao: values.nivel_aprovacao,
          unidades_filha_ids: unidadeFilhaIds, // This is what the API expects
        }),
      });

      if (!res.ok) {
        toast("Erro na atualização", {
          description: "Falha ao atualizar o usuário",
        });
        return;
      }

      const data = await res.json();
      setUsuarios((prev) =>
        prev.map((u) => (u.id === usuario.id ? data.user : u))
      );

      toast("Usuário atualizado!", {
        description: "Alterações salvas com sucesso",
      });

      try {
        await fetch("/api/auditoria", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            userId: session?.user.id,
            acao_do_usuario: "atualizar o usuário",
          }),
        });
        window.location.href = "/dashboard/usuarios/usuarios-sistema";
      } catch (error) {
        console.error(
          "Erro ao registrar atividade de atualizar o usuário:",
          error
        );
      }
      window.location.href = "/dashboard/usuarios/usuarios-sistema";
    } catch (error) {
      console.error("Erro ao atualizar usuário:", error);
      toast("Erro inesperado", {
        description: "Não foi possível processar a solicitação",
      });
    }
  };

  return (
    <div className="p-4">
      <Card className="p-6">
        <h2 className="text-xl font-bold mb-4">Editar Usuário</h2>
        <FormProvider {...form}>
          <form
            onSubmit={form.handleSubmit(handleUpdateUsuario)}
            className="space-y-4"
          >
            <GenericFormsInput fieldConfig={userConfig} variants="default" />
            <div className="flex justify-end">
              <Button type="submit">Atualizar usuário</Button>
            </div>
          </form>
        </FormProvider>
      </Card>
    </div>
  );
}