"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Edit, Plus, Trash2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { useState } from "react";

type AccessData = {
  id: string;
  contrato: { id: string; nome: string };
  centro_de_custo: { id: string; descricao: string };
  grupos_acesso: string[];
};

type AccessTableProps = {
  data: AccessData[];
  onEdit: (item: AccessData) => void;
  onDelete: (id: string) => void;
  onAdd: () => void;
};

export function AcessoTable({ data, onEdit, onDelete, onAdd }: AccessTableProps) {
  const [search, setSearch] = useState("");
  
  const filteredData = data.filter(
    (item) =>
      item.contrato.nome.toLowerCase().includes(search.toLowerCase()) ||
      item.centro_de_custo.descricao.toLowerCase().includes(search.toLowerCase()) ||
      item.grupos_acesso.some((grupo) => grupo.toLowerCase().includes(search.toLowerCase()))
  );

  return (
    <div className="space-y-4">
      <div className="flex justify-between">
        <h3 className="text-lg font-medium">Controle de acesso aos centros de custo</h3>
        <div className="flex gap-2">
          <div className="flex items-center gap-2">
            <Input
              placeholder="Busca rápida"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="w-[250px]"
            />
          </div>
          <Button onClick={onAdd} size="sm" className="flex items-center gap-1">
            <Plus size={16} />
            Adicionar
          </Button>
        </div>
      </div>
      
      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-1/3">Contrato</TableHead>
              <TableHead className="w-1/3">Centro de custo</TableHead>
              <TableHead className="w-1/4">Grupos de acesso</TableHead>
              <TableHead className="w-[100px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredData.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-4 text-muted-foreground">
                  Nenhum acesso configurado
                </TableCell>
              </TableRow>
            ) : (
              filteredData.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>{item.contrato.nome}</TableCell>
                  <TableCell>{item.centro_de_custo.descricao}</TableCell>
                  <TableCell>{item.grupos_acesso.join(", ")}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onEdit(item)}
                      >
                        <Edit size={18} className="text-primary" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onDelete(item.id)}
                      >
                        <Trash2 size={18} className="text-destructive" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}