"use client";
import { usuarioSchema } from "@/components/forms/schemas";
import { SheetForm } from "@/components/forms/sheet-form";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { useContrato } from "@/context/contrato-context";
import { useUsuario } from "@/context/usuario-context";
import { ColumnDef } from "@tanstack/react-table";
import { toast } from "sonner";
import { z } from "zod";
import { UsuarioActions } from "./usuario-action";
import { GetUserConfig } from "@/components/forms/inputConfig";
import { useEffect, useMemo, useState } from "react";
import { getServerSession } from "@/lib/auth/server-session";
import { Session } from "@/lib/auth/types";
import {
  defaultValues,
  usuarioDefaultValues,
} from "@/components/forms/defaultValues";
import { useSession } from "@/components/session/use-session";
import { useRouter } from "next/navigation";

const roleOptions = [
  { label: "Acesso geral ao sistema", value: "ADMIN" },
  { label: "Operacional", value: "OPERACIONAL" },
  { label: "Orçamentista Oficina", value: "ORCAMENTISTA_OFICINA" },
  { label: "Gestor da Frota", value: "GESTOR_FROTA" },
  { label: "Aprovação de Os", value: "APROVADOR" },
  { label: "Abertura de Os", value: "ABERTURA_OS" },
  { label: "Credenciamento", value: "CREDENCIAMENTO" },
  { label: "Financeiro", value: "FINANCEIRO" },
];

const roleMapping = roleOptions.reduce((acc, role) => {
  acc[role.value] = role.label;
  return acc;
}, {} as Record<string, string>);

export const usuarioColumn: ColumnDef<usuario>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "nome",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Nome" />
    ),
  },
  {
    accessorKey: "email",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Email" />
    ),
  },
  {
    accessorKey: "senha",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Senha" />
    ),
    cell: ({ row }) => {
      return row.original.roles.includes("ORCAMENTISTA_OFICINA")
        ? row.original.senha
        : "******";
    },
  },
  {
    accessorKey: "roles",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Função" />
    ),
    cell: ({ row }) => {
      const roles = row.original.roles;
      return roles.map((role) => roleMapping[role]).join(", ");
    },
  },
  {
    accessorKey: "nivel_aprovacao",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Alçada" />
    ),
    cell: ({ row }) => {
      const centValue = parseInt(row.original.valor_nivel_aprovacao || "0", 10);
      const formattedValue = new Intl.NumberFormat("pt-BR", {
        style: "currency",
        currency: "BRL",
      }).format(centValue / 100);
      return formattedValue;
    },
  },
  {
    accessorKey: "ativo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Ativo" />
    ),
    cell: ({ row }) => (row.original.ativo ? "Sim" : "Não"),
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => <UsuarioActions usuario={row.original} />,
  },
];

export function UsuarioTable() {
  const router = useRouter();
  const { usuarios, setUsuarios } = useUsuario();
  const { contratos } = useContrato();
  const [sessionData, setSessionData] = useState<Session>();
  const [selectedContratoId, setSelectedContratoId] = useState("");
  const [selectedCentroCustoId, setSelectedCentroCustoId] = useState("");
  const [centrosCusto, setCentrosCusto] = useState<any[]>([]);
  const [centrosCustoFilho, setCentrosCustoFilho] = useState<any[]>([]);

  async function onNewUsuario(values: z.infer<typeof usuarioSchema>) {
    const rolesToSend = values.roles.map((role) => role.value);
    const contratoIds = values.contratos?.map((contrato) => contrato.value);
    const response = await fetch("/api/usuario", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        ...values,
        roles: rolesToSend,
        contratoIds,
      }),
    });
    if (!response.ok) {
      toast("Ops, algo deu errado. Verifique os dados e tente novamente.");
      return;
    }

    const data = await response.json();
    setUsuarios((prev) => [...prev, data.data.user]);

    toast("Usuário criado com sucesso!", {
      description: "O usuário foi cadastrado com sucesso no sistema",
    });

    window.location.reload();
    // router.replace("/dashboard/usuarios/usuarios-sistema");
  }

  useEffect(() => {
    const fetchSessionAndContrato = async () => {
      const sessionData = await getServerSession();
      setSessionData(sessionData);
    };

    fetchSessionAndContrato();
  }, []);

  const [filteredUsuarios, setFilteredUsuarios] = useState<usuario[]>(usuarios);

  useEffect(() => {
    if (
      typeof window !== "undefined" &&
      window.location.pathname === "/dashboard/usuarios/usuarios-contrato"
    ) {
      if (sessionData && sessionData.contrato) {
        setFilteredUsuarios(
          usuarios.filter((usuario) =>
            usuario?.contratos?.some(
              (contrato) => contrato.id === sessionData.contratoId
            )
          )
        );
      }
    } else {
      setFilteredUsuarios(usuarios);
    }
  }, [sessionData]);

  useEffect(() => {
    if (!selectedContratoId) return;

    const fetchCentrosDeCusto = async () => {
      try {
        const response = await fetch(
          `/api/centro-de-custo/contrato/${selectedContratoId}`
        );
        const data = await response.json();
        const centros: centro_de_custo[] = data.data.centrosCusto;
        const filteredCentros = centros.filter(
          (centro) => centro.centro_custo_pai === null
        );
        setCentrosCusto((prev) => [...prev, ...filteredCentros]);
      } catch (error) {
        console.error("Erro ao buscar centros de custo:", error);
      }
    };

    fetchCentrosDeCusto();
  }, [selectedContratoId]);

  useEffect(() => {
    const getCentrosCustoFilho = () => {
      const centroCustoFilho = centrosCusto
        .filter(({ id }) => id === selectedCentroCustoId)
        .flatMap((centro) => {
          if (!centro.centro_custos_filhos) return [];
          return centro.centro_custos_filhos.map((filho: any) => ({
            id: filho.id,
            descricao: filho.descricao || filho.razao_social || "Sem descrição",
            centro_custo_ascdID: centro.id,
          }));
        });

      setCentrosCustoFilho(centroCustoFilho);
    };

    getCentrosCustoFilho();
  }, [centrosCusto, selectedCentroCustoId]);

  return (
    <DataTable
      data={filteredUsuarios}
      columns={usuarioColumn}
      exportTo={true}
      newItem={{
        defaultValues: usuarioDefaultValues,
        fieldConfig: GetUserConfig(
          [
            contratos,
            centrosCusto,
            centrosCustoFilho,
            setSelectedContratoId,
            setSelectedCentroCustoId,
          ],
          ["nome_contrato", "descricao", "descricao"]
        ),
        schema: usuarioSchema,
      }}
      modalClassName="max-w-[70vw] "
      onNewItem={onNewUsuario}
      showToolbar
    />
  );
}
