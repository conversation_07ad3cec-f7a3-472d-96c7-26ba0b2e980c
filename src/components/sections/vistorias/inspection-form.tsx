import { useState } from "react";
import { format } from "date-fns";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface InspectionFormProps {
  onSubmit: (data: any) => void;
  credenciados: any[];
}

export function InspectionForm({ onSubmit, credenciados }: InspectionFormProps) {
  const [tipoAvaliacao, setTipoAvaliacao] = useState("inicial");
  const [date, setDate] = useState(new Date());
  const [avaliador, setAvaliador] = useState("");
  const [odometroAtual, setOdometroAtual] = useState(0);
  const [observacoes, setObservacoes] = useState("");

  const handleSubmit = () => {
    onSubmit({
      tipoAvaliacao,
      date,
      avaliador,
      odometroAtual,
      observacoes,
    });
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="text-xl">Avaliação</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Campos do formulário */}
        <Button className="bg-teal-500 hover:bg-teal-600 text-white" onClick={handleSubmit}>
          Cadastrar vistoria
        </Button>
      </CardContent>
    </Card>
  );
}
