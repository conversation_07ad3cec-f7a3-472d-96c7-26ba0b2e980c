"use client";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useVistorias } from "@/context/vistoria-context";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown, FileText, Filter, Search, Loader2 } from "lucide-react";
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import { format } from 'date-fns';
import { pt } from 'date-fns/locale';

// Interface para tipagem das vistorias
interface VistoriaDisplay {
  id: string;
  placa: string;
  tipo_avaliacao: string;
  data: string;
  avaliador: string;
  observacoes?: string;
}

// Componente principal da tabela
export function Vistoria() {
  const { vistorias, loading, error } = useVistorias();
  const [searchQuery, setSearchQuery] = useState("");
  const [formattedVistorias, setFormattedVistorias] = useState<VistoriaDisplay[]>([]);
  const [isExporting, setIsExporting] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  
  // Formatar dados da API para exibição
  useEffect(() => {
    if (vistorias && vistorias.length > 0) {
      const formatted = vistorias.map(v => {
        // Formatação de data se existir
        let formattedDate = '-';
        if ((v as any).data_avaliacao) {
          try {
            const date = new Date((v as any).data_avaliacao);
            formattedDate = format(date, 'dd/MM/yyyy HH:mm', { locale: pt });
          } catch (e) {
            console.error('Erro ao formatar data', e);
          }
        }
        
        return {
          id: v.id || 'no-id',
          placa: (v as any).veiculo?.placa || '-',
          tipo_avaliacao: (v as any).tipo_avaliacao || '-',
          data: formattedDate,
          avaliador: (v as any).avaliador?.nome || '-',
          observacoes: (v as any).observacoes || '-'
        };
      });
      
      setFormattedVistorias(formatted);
    }
  }, [vistorias]);
  
  // Filtrar vistorias com base na busca
  const filteredVistorias = formattedVistorias.filter(vistoria => {
    if (!searchQuery) return true;
    const searchableText = `${vistoria.placa} ${vistoria.tipo_avaliacao} ${vistoria.avaliador} ${vistoria.observacoes || ''}`.toLowerCase();
    return searchableText.includes(searchQuery.toLowerCase());
  });

  // Calcular paginação
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredVistorias.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredVistorias.length / itemsPerPage);

  // Limpar os filtros de busca
  const handleLimparFiltros = () => {
    setSearchQuery("");
    toast.success("Filtros limpos com sucesso");
  };

  // Exportar para Excel
  const exportToExcel = async () => {
    try {
      setIsExporting(true);
      
      // Preparar dados para exportação
      const dataToExport = filteredVistorias.map(({ id, ...rest }) => rest);
      
      // Criar worksheet
      const ws = XLSX.utils.json_to_sheet(dataToExport);
      
      // Ajustar largura das colunas
      const wscols = [
        { wch: 10 }, // Placa
        { wch: 20 }, // Tipo de avaliação
        { wch: 20 }, // Data
        { wch: 20 }, // Avaliador
        { wch: 40 }, // Observações
      ];
      ws['!cols'] = wscols;
      
      // Criar workbook e adicionar worksheet
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Vistorias");
      
      // Gerar arquivo Excel
      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      
      // Baixar arquivo
      saveAs(data, `vistorias_${format(new Date(), 'dd-MM-yyyy')}.xlsx`);
      
      toast.success("Exportação para Excel concluída!");
    } catch (error) {
      toast.error("Erro ao exportar para Excel", {
        description: "Ocorreu um erro durante a exportação. Tente novamente."
      });
      console.error("Erro ao exportar para Excel:", error);
    } finally {
      setIsExporting(false);
    }
  };

  // Exportar para PDF
  const exportToPDF = async () => {
    try {
      setIsExporting(true);
      
      // Criar documento PDF
      const doc = new jsPDF();
      
      // Adicionar título
      doc.setFontSize(18);
      doc.text("Relatório de Vistorias", 14, 22);
      
      // Adicionar data
      doc.setFontSize(11);
      doc.text(`Gerado em: ${format(new Date(), 'dd/MM/yyyy HH:mm')}`, 14, 30);
      
      // Preparar dados para a tabela
      const tableColumn = ["Placa", "Tipo de avaliação", "Data", "Avaliador", "Observações"];
      const tableRows = filteredVistorias.map(vistoria => [
        vistoria.placa,
        vistoria.tipo_avaliacao,
        vistoria.data,
        vistoria.avaliador,
        vistoria.observacoes || '-'
      ]);
      
      // Adicionar tabela ao PDF
      (doc as any).autoTable({
        head: [tableColumn],
        body: tableRows,
        startY: 40,
        styles: {
          fontSize: 9,
          cellPadding: 3,
          lineColor: [44, 62, 80],
          lineWidth: 0.1,
        },
        headStyles: {
          fillColor: [41, 128, 185],
          textColor: 255,
          fontStyle: 'bold',
        },
        alternateRowStyles: {
          fillColor: [245, 245, 245]
        },
      });
      
      // Salvar o PDF
      doc.save(`vistorias_${format(new Date(), 'dd-MM-yyyy')}.pdf`);
      
      toast.success("Exportação para PDF concluída!");
    } catch (error) {
      toast.error("Erro ao exportar para PDF", {
        description: "Ocorreu um erro durante a exportação. Tente novamente."
      });
      console.error("Erro ao exportar para PDF:", error);
    } finally {
      setIsExporting(false);
    }
  };

  // Navegação de página
  const goToPage = (page: number) => {
    if (page > 0 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  // Renderização condicional para loading
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64 w-full">
        <Loader2 className="h-8 w-8 animate-spin " />
        <span className="ml-2 text-lg ">Carregando vistorias...</span>
      </div>
    );
  }

  // Renderização condicional para erro
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64 w-full">
        <p className="text-red-500 text-lg">Erro ao carregar vistorias</p>
        <p className="text-sm  mt-2">{error}</p>
        <Button 
          className="mt-4" 
          onClick={() => window.location.reload()}
        >
          Tentar novamente
        </Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col p-6 rounded-md w-full">
      <div className="flex justify-between items-center mb-4">
        <div className="flex gap-2">  
        <div className="relative w-80">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input 
            placeholder="Busca rápida" 
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button 
            variant="outline" 
            className="flex items-center gap-1"
            onClick={handleLimparFiltros}
          >
            <Filter className="h-4 w-4" />
            Limpar filtros
          </Button>
        </div>
       <div className="flex gap-2">
         
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                className="bg-black hover:bg-black text-white"
                disabled={isExporting}
              >
                {isExporting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Exportando...
                  </>
                ) : (
                  <>
                    Exportar
                    <ChevronDown className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={exportToExcel} disabled={isExporting}>
                <FileText className="mr-2 h-4 w-4" />
                 Excel
              </DropdownMenuItem>
              <DropdownMenuItem onClick={exportToPDF} disabled={isExporting}>
                <FileText className="mr-2 h-4 w-4" />
                 PDF
              </DropdownMenuItem>
              <DropdownMenuItem onClick={exportToPDF} disabled={isExporting}>
                <FileText className="mr-2 h-4 w-4" />
                 CSV
              </DropdownMenuItem>
              <DropdownMenuItem onClick={exportToPDF} disabled={isExporting}>
                <FileText className="mr-2 h-4 w-4" />
                 imprimir
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      
      <div className="border rounded-md">
        <table className="w-full">
          <thead>
            <tr className="border-b ">
              <th className="px-4 py-3 text-left text-sm font-medium ">
                Placa
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium ">
                Tipo de avaliação
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium ">
                Data
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium ">
                Avaliador
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium ">
                Observações
              </th>
            </tr>
          </thead>
          <tbody>
            {currentItems.length > 0 ? (
              currentItems.map((vistoria, index) => (
                <tr key={vistoria.id || index} className="border-b hover:bg-gray-50">
                  <td className="px-4 py-3 text-sm">{vistoria.placa}</td>
                  <td className="px-4 py-3 text-sm">{vistoria.tipo_avaliacao}</td>
                  <td className="px-4 py-3 text-sm">{vistoria.data}</td>
                  <td className="px-4 py-3 text-sm">{vistoria.avaliador}</td>
                  <td className="px-4 py-3 text-sm">{vistoria.observacoes || '-'}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="px-4 py-3 text-sm text-center ">
                  Nenhuma vistoria encontrada
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      
      <div className="flex justify-center mt-4 text-sm ">
        Exibindo de {indexOfFirstItem + 1} a {Math.min(indexOfLastItem, filteredVistorias.length)} de {filteredVistorias.length} registros
      </div>
      
      <div className="flex justify-center mt-2">
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => goToPage(1)} 
            disabled={currentPage === 1}
          >
            «
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => goToPage(currentPage - 1)} 
            disabled={currentPage === 1}
          >
            ‹
          </Button>
          
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            // Lógica para mostrar as páginas centralizadas na página atual
            let pageToShow;
            if (totalPages <= 5) {
              pageToShow = i + 1;
            } else {
              const startPage = Math.max(1, currentPage - 2);
              const endPage = Math.min(totalPages, currentPage + 2);
              if (endPage - startPage < 4) {
                pageToShow = i + Math.max(1, totalPages - 4);
              } else {
                pageToShow = startPage + i;
              }
              if (pageToShow > totalPages) return null;
            }
            
            return (
              <Button 
                key={pageToShow}
                variant={currentPage === pageToShow ? "default" : "outline"} 
                size="sm" 
                className={currentPage === pageToShow ? "bg-blue-600" : ""}
                onClick={() => goToPage(pageToShow)}
              >
                {pageToShow}
              </Button>
            );
          })}
          
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => goToPage(currentPage + 1)} 
            disabled={currentPage === totalPages}
          >
            ›
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => goToPage(totalPages)} 
            disabled={currentPage === totalPages}
          >
            »
          </Button>
        </div>
      </div>
    </div>
  );
}
