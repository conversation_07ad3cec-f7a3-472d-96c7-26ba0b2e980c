"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { GetItensDaVistoria } from "@/components/forms/inputConfig";
import { itemDaVistoria } from "@/components/forms/schemas";
import { SheetForm } from "@/components/forms/sheet-form";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { useItensDaVistoria } from "@/context/itens-da-vistoria-context";
import { useTipoDeVeiculo } from "@/context/tipo-de-veiculo-context";
import { useVeiculos } from "@/context/veiculos-context";
import { ColumnDef } from "@tanstack/react-table";
import { Edit, FileText, FileUp, Plus, Printer, Search, Trash, X } from "lucide-react";
import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";
import { z } from "zod";

// Definição do tipo para item de vistoria
interface item_da_vistoria {
  id: string;
  descricao: string;
  tipo_veiculo?: {
    id: string;
    descricao: string;
  };
  [key: string]: any;
}

// Componente para a célula de ações
function ActionCell({ item }: { item: item_da_vistoria }) {
  const { setItensDaVistoria } = useItensDaVistoria();
  const { veiculos } = useVeiculos();
  const [open, setOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<item_da_vistoria | null>(null);
  
  const editItem = async () => {
    setSelectedItem(item);
    setOpen(true);
  };

  const deleteItem = async () => {
    try {
      const res = await fetch(`/api/itens-da-vistoria/${item.id}`, {
        method: "DELETE",
      });
      if (!res.ok) {
        toast("Ops, algo deu errado", {
          description: "Houve um erro ao deletar o item",
        });
        return;
      }
      setItensDaVistoria((prev) => prev.filter((i) => i.id !== item.id));
      toast("Item deletado com sucesso!");
    } catch (error) {
      toast("Erro ao deletar o item");
    }
  };

  return (
    <div className="flex items-center gap-2">
      <Button
        variant="ghost"
        size="icon" 
        onClick={(e) => {
          e.stopPropagation();
          editItem();
        }}
        className="h-8 w-8 p-0 text-blue-500 hover:text-blue-700 hover:bg-blue-50">
        <Edit className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="icon" 
        onClick={(e) => {
          e.stopPropagation();
          toast("Tem certeza?", {
            description: "Essa ação não pode ser desfeita",
            action: {
              label: "Tenho certeza!",
              onClick: deleteItem,
            },
          });
        }}
        className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50">
        <Trash className="h-4 w-4" />
      </Button>
      {selectedItem && open && (
        <SheetForm
          title="Editar Item"
          schema={itemDaVistoria}
          onSubmit={(values) => {
            const updateItem = async () => {
              try {
                const res = await fetch(`/api/itens-da-vistoria/${item.id}`, {
                  method: "PUT",
                  headers: { "Content-Type": "application/json" },
                  body: JSON.stringify(values),
                });
    
                if (!res.ok) {
                  toast("Erro ao atualizar o item");
                  return;
                }
    
                const data = await res.json();
                setItensDaVistoria((prev) =>
                  prev.map((i) => (i.id === data.data.id ? data.data : i))
                );
    
                toast("Item atualizado com sucesso!");
                setOpen(false);
              } catch (error) {
                toast("Erro ao atualizar o item");
              }
            };
            updateItem();
          }}
          triggerLabel="Editar Item"
          defaultValues={selectedItem as any}>
          <GenericFormsInput
            fieldConfig={GetItensDaVistoria(veiculos, "modelo.nome")}
            variants="single"
          />
        </SheetForm>
      )}
    </div>
  );
}

// Componente principal da tabela
export function ItemsVistoria() {
  const { itensDaVistoria } = useItensDaVistoria();
  const [searchQuery, setSearchQuery] = useState("");
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedItems, setSelectedItems] = useState<any[]>([]);
    const { setItensDaVistoria } = useItensDaVistoria();
    const { tiposDeVeiculo } = useTipoDeVeiculo();

  const filteredItems = itensDaVistoria.filter(item => 
    item.descricao?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Definimos colunas localmente para evitar problemas de tipagem
  const columns = [
    {
      id: "select",
      header: ({ table }: any) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Selecionar todos"
        />
      ),
      cell: ({ row }: any) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Selecionar linha"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "descricao",
      header: ({ column }: any) => {
        return <DataTableColumnHeader column={column} title="Descrição do item" />;
      },
    },
    {
      accessorKey: "tipo_veiculo.descricao",
      header: ({ column }: any) => {
        return <DataTableColumnHeader column={column} title="Tipo de veículos" />;
      },
      cell: ({ row }: any) => <div>{row.original.tipo_veiculo?.descricao || "Todos"}</div>,
    },
    // Adiciona a coluna de ações
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }: any) => {
        return <ActionCell item={row.original} />;
      },
    }
  ] as ColumnDef<any>[];

  // Função para excluir múltiplos itens
  const handleBulkDelete = async () => {
    toast.success(`Excluindo ${selectedItems.length} itens selecionados...`);
    setIsDeleteDialogOpen(false);
    
    // Implementação real para exclusão em massa
    try {
      // Seria feita uma chamada de API para excluir todos os itens selecionados
      // Aqui estamos simulando com um timeout
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Após a exclusão bem-sucedida, atualizamos o estado
      // setItensDaVistoria(prev => prev.filter(item => !selectedItems.some(selected => selected.id === item.id)));
      
      toast.success(`${selectedItems.length} itens excluídos com sucesso!`);
      setSelectedItems([]);
    } catch (error) {
      toast.error('Erro ao excluir itens');
    }
  };

    async function onNewItemVistoria(values: z.infer<typeof itemDaVistoria>) {
      try {
        const response = await fetch("/api/itens-da-vistoria", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(values),
        });

        if (!response.ok) {
          throw new Error("Falha ao criar o item");
        }

        const data = await response.json();
        setItensDaVistoria((prev) => [...prev, data.data]);

        toast.success("Item criado com sucesso!");
        window.location.reload();
      } catch (error) {
        toast.error("Oops! Alguma coisa deu errado", {
          description: "Não foi possível criar o item",
        });
      }
    }

  // Funções para exportação
  const handleExportExcel = async () => {
    toast.success("Exportando para Excel...");
    setTimeout(() => {
      toast.success("Exportação para Excel concluída!");
    }, 1500);
  };
  
  const handleExportPDF = () => {
    toast.info("Preparando PDF...");
    setTimeout(() => {
      toast.success("PDF exportado com sucesso!");
    }, 1500);
  };
  
  const handlePrint = () => {
    toast.success("Preparando impressão...");
    window.print();
  };

  // Atualiza itens selecionados quando checkboxes são marcados
  const handleSelectionChange = useCallback((tableState: any) => {
    const selectedRows = tableState.getSelectedRowModel().rows;
    setSelectedItems(selectedRows.map((row: any) => row.original));
  }, []);

  return (
    <div className="space-y-4 ">
      <div className="flex gap-2 items-center">
        <div className="relative w-72">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Busca rápida"
            className="pl-8 pr-8 rounded-sm h-9"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2"
              onClick={() => setSearchQuery("")}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            className="flex items-center gap-2 rounded-sm h-9 text-xs"
            onClick={() => setSearchQuery("")}>
            <X className="h-4 w-4" />
            Limpar filtros
          </Button>
        </div>
      </div>

      {/* DataTable */}
      <div className="p-2 rounded  shadow-sm">
        <DataTable
          data={filteredItems}
          columns={columns}
          exportTo={true}
          newItem={{
            name: "Item da vistoria",
            defaultValues: defaultValues.vistoriaSchema,
            fieldConfig: GetItensDaVistoria(tiposDeVeiculo, "descricao"),
            schema: itemDaVistoria,
          }}
          onNewItem={onNewItemVistoria}
          
        />
      </div>

      {/* Modal de confirmação para exclusão em massa */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmação de exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedItems.length > 0
                ? `Você tem certeza que deseja excluir ${selectedItems.length} ${
                    selectedItems.length === 1 ? "item selecionado" : "itens selecionados"
                  }? Esta ação não pode ser desfeita.`
                : "Selecione pelo menos um item para excluir."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="rounded-sm">Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleBulkDelete}
              className="bg-red-600 hover:bg-red-700 rounded-sm"
              disabled={selectedItems.length === 0}>
              Excluir {selectedItems.length > 0 && `(${selectedItems.length})`}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}