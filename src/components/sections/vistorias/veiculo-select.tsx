import { useState } from "react";
import { ChevronDown, Search } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface VehicleSelectProps {
  vehicles: any[];
  selectedVehicle: any;
  onSelectVehicle: (vehicle: any) => void;
}

export function VehicleSelect({ vehicles, selectedVehicle, onSelectVehicle }: VehicleSelectProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const filteredVehicles = vehicles.filter((vehicle) =>
    `${vehicle.placa} | ${vehicle.marca?.descricao} ${vehicle.modelo?.descricao} ${vehicle.ano_modelo}`
      .toLowerCase()
      .includes(searchQuery.toLowerCase())
  );

  return (
    <div>
      <Label htmlFor="veiculo">Veículo</Label>
      <div className="relative mt-1">
        <Popover open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              className="w-full justify-between border rounded-md">
              {selectedVehicle ? (
                <span className="text-sm font-medium">
                  {selectedVehicle.placa} | {selectedVehicle.marca?.descricao}{" "}
                  {selectedVehicle.modelo?.descricao}
                </span>
              ) : (
                <span className="text-sm text-gray-500">
                  Pesquise pela placa, versão, marca ou modelo
                </span>
              )}
              <ChevronDown className="h-4 w-4 text-muted-foreground" />
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className="p-0"
            align="start"
            style={{ width: "var(--radix-popover-trigger-width)" }}>
            <div className="p-2">
              <div className="relative mb-2">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar veículo..."
                  className="pl-8 pr-8 w-full"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  autoFocus
                />
              </div>
              <div className="max-h-60 overflow-y-auto border rounded-md">
                {filteredVehicles.length > 0 ? (
                  filteredVehicles.map((vehicle) => (
                    <div
                      key={vehicle.id}
                      className="px-4 py-2 cursor-pointer hover:bg-slate-100 hover:text-black"
                      onClick={() => {
                        onSelectVehicle(vehicle);
                        setIsDropdownOpen(false);
                      }}>
                      <span className="text-sm">
                        {vehicle.placa} | {vehicle.marca?.descricao} {vehicle.modelo?.descricao} |
                        Modelo {vehicle.ano_modelo}
                      </span>
                    </div>
                  ))
                ) : (
                  <div className="px-4 py-2 text-gray-500">Nenhum veículo encontrado</div>
                )}
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
}
