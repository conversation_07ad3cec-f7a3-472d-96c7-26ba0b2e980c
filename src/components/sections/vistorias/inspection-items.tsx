import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface InspectionItemsProps {
  items: any[];
  evaluations: Record<string, string>;
  onEvaluationChange: (itemId: string, value: string) => void;
}

export function InspectionItems({ items, evaluations, onEvaluationChange }: InspectionItemsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {items.map((item) => (
        <div key={item.id} className="space-y-2">
          <Label>{item.descricao}</Label>
          <RadioGroup
            value={evaluations[item.id] || ""}
            onValueChange={(value) => onEvaluationChange(item.id, value)}
            className="flex space-x-1">
            {["bom", "reparo", "ausente", "nao-avaliado"].map((status) => (
              <div key={status} className="flex items-center">
                <RadioGroupItem value={status} id={`${status}-${item.id}`} className="sr-only" />
                <Label
                  htmlFor={`${status}-${item.id}`}
                  className={`px-3 py-1.5 text-xs rounded cursor-pointer ${
                    evaluations[item.id] === status
                      ? "border border-green-500"
                      : "border hover:border-blue-500"
                  }`}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>
      ))}
    </div>
  );
}
