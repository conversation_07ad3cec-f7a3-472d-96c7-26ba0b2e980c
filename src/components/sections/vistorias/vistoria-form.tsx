"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { GetVistoriaConfig } from "@/components/forms/inputConfig";
import { vistoriaSchema } from "@/components/forms/schemas";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  CalendarIcon,
  ChevronDown,
  Search,
  Wand2,
  Loader2,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { useVeiculos } from "@/context/veiculos-context";
import { useVistorias } from "@/context/vistoria-context";
import { toast } from "sonner";
import { z } from "zod";
import { useState } from "react";
import { format } from "date-fns";
import { pt } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { useItensDaVistoria } from "@/context/itens-da-vistoria-context";
import { useCredenciado } from "@/context/credenciado-context";
import { FormControl } from "@/components/ui/form";

const mockAvaliacoes = ["bom", "reparo", "ausente", "nao-avaliado"];

export function VistoriaForm() {
  const { veiculos } = useVeiculos();
  const { setVistorias } = useVistorias();
  const { itensDaVistoria } = useItensDaVistoria();
  const { credenciados, setCredenciados } = useCredenciado();
  const [selectedVehicle, setSelectedVehicle] = useState<veiculo | null>(null);
  const [vehicleSearchQuery, setVehicleSearchQuery] = useState("");
  const [isVehicleDropdownOpen, setIsVehicleDropdownOpen] = useState(false);
  const [step, setStep] = useState<"selectVehicle" | "fillItems">(
    "selectVehicle"
  );
  const [date, setDate] = useState<Date>(new Date());
  const [avaliacoes, setAvaliacoes] = useState<Record<string, string>>({});
  const [tipoAvaliacao, setTipoAvaliacao] = useState("inicial");
  const [odometroAtual, setOdometroAtual] = useState<number>(0);
  const [previsaoTrocaOleo, setPrevisaoTrocaOleo] = useState(0);
  const [dataPrevisaoTroca, setDataPrevisaoTroca] = useState<Date | null>(null);
  const [observacoes, setObservacoes] = useState("");
  const [avaliador, setAvaliador] = useState("");
  const [imagem, setImagem] = useState<File | null>(null);
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // Lista de veículos filtrada pelo campo de busca
  const filteredVehicles = (veiculos as veiculo[]).filter((veiculo) => {
    if (!vehicleSearchQuery) return true; // Mostra todos os veículos quando não há texto de busca

    // Acesse com segurança as propriedades que podem não existir
    const placa = veiculo.placa || "";
    const marcaNome = veiculo.marca?.descricao || "";
    const modeloNome = veiculo.modelo?.descricao || "";
    const ano = veiculo.ano_modelo || "";

    const searchableText = `${placa} | ${marcaNome} ${modeloNome} ${ano}`;
    return searchableText
      .toLowerCase()
      .includes(vehicleSearchQuery.toLowerCase());
  });

  const handleVehicleSelect = (veiculo: veiculo) => {
    setSelectedVehicle(veiculo);
    setIsVehicleDropdownOpen(false);
  };

  const handleAvaliacaoChange = (itemId: string, value: string) => {
    setAvaliacoes((prev) => ({
      ...prev,
      [itemId]: value,
    }));
  };

  const uploadFile = async (file: File) => {
    if (!file) return null;

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append("file", file);

      const res = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      });

      if (!res.ok) {
        throw new Error("Erro ao fazer upload do arquivo");
      }

      const data = await res.json();
      setUploadedImageUrl(data.url);
      return data.url;
    } catch (error) {
      toast.error("Erro no upload", {
        description:
          "Não foi possível fazer o upload da imagem. Tente novamente.",
      });
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  const handleUploadImage = async () => {
    if (!imagem) {
      toast.warning("Nenhuma imagem selecionada", {
        description: "Por favor, selecione uma imagem para fazer o upload.",
      });
      return;
    }

    const uploadedUrl = await uploadFile(imagem);

    if (uploadedUrl) {
      toast.success("Upload realizado", {
        description: "A imagem foi enviada com sucesso.",
      });
    }
  };

  const handleSubmit = async () => {
    if (!selectedVehicle) {
      toast.warning("Veículo não selecionado", {
        description: "Por favor, selecione um veículo para a vistoria.",
      });
      return;
    }

    const todosItensAvaliados = itensDaVistoria.every(
      (item) => avaliacoes[item.id]
    );

    if (!todosItensAvaliados) {
      toast.warning("Avaliação incompleta", {
        description:
          "Todos os itens precisam ser avaliados antes de cadastrar a vistoria.",
      });
      return;
    }

    if (!avaliador) {
      toast.warning("Avaliador não selecionado", {
        description: "Por favor, selecione um avaliador.",
      });
      return;
    }

    if (!odometroAtual) {
      toast.warning("Odômetro não informado", {
        description: "Por favor, informe o odômetro atual.",
      });
      return;
    }

    // Se imagem foi selecionada mas não foi feito upload, faça o upload agora
    let imageUrl = uploadedImageUrl;
    if (imagem && !uploadedImageUrl) {
      imageUrl = await uploadFile(imagem);
      if (!imageUrl) {
        toast.error("Falha no upload", {
          description:
            "Não foi possível fazer o upload da imagem. Tente novamente.",
        });
        return;
      }
    }

    // Preparar os itens da vistoria com suas avaliações
    const itensAvaliados = itensDaVistoria.map((item) => ({
      ...item,
      avaliacao: avaliacoes[item.id],
    }));

    try {
      setIsSubmitting(true);

      // Preparar o objeto vistoria para enviar
      const vistoriaData: vistoria = {
        veiculoId: selectedVehicle.id,
        tipo_de_avaliacao:
          tipoAvaliacao === "inicial" ? "Inicial" : "Periódica",
        data: date,
        dataLimite: new Date(), // Provide a default or calculated value here
        credenciadoId: avaliador,
        odometro_atual: odometroAtual,
        previsao_troca_de_oleo: {
          km: previsaoTrocaOleo,
          data: dataPrevisaoTroca ?? new Date(),
        },
        imagens: imageUrl ? [imageUrl] : [],
        observacao: observacoes,
        itens_da_vistoria: itensAvaliados,
      };

      // Enviar para a API
      const response = await fetch("/api/vistorias", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(vistoriaData),
      });

      if (!response.ok) {
        throw new Error("Falha ao cadastrar vistoria");
      }

      const savedVistoria = await response.json();

      // Adicionar ao estado local
      setVistorias((prev: vistoria[]) => [...prev, savedVistoria]);

      toast.success("Vistoria cadastrada com sucesso!", {
        description: "A vistoria foi registrada no sistema.",
      });

      // Reset do formulário após sucesso
      setStep("selectVehicle");
      setSelectedVehicle(null);
      setAvaliacoes({});
      setObservacoes("");
      setImagem(null);
      setUploadedImageUrl(null);
    } catch (error) {
      console.error("Erro ao cadastrar vistoria:", error);
      toast.error("Erro ao cadastrar", {
        description: "Não foi possível cadastrar a vistoria. Tente novamente.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (step === "selectVehicle") {
    return (
      <Card className="m-4">
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="veiculo">Veículo</Label>
              <div className="relative mt-1">
                <Popover
                  open={isVehicleDropdownOpen}
                  onOpenChange={setIsVehicleDropdownOpen}
                >
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      className="w-full justify-between border rounded-md"
                    >
                      {selectedVehicle ? (
                        <span className="text-sm font-medium">
                          {selectedVehicle.placa} |{" "}
                          {selectedVehicle.marca?.descricao}{" "}
                          {selectedVehicle.modelo?.descricao}
                        </span>
                      ) : (
                        <span className="text-sm text-gray-500">
                          Pesquise pela placa, versão, marca ou modelo
                        </span>
                      )}
                      <ChevronDown className="h-4 w-4 text-muted-foreground" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent
                    className="p-0"
                    align="start"
                    style={{ width: "var(--radix-popover-trigger-width)" }}
                  >
                    <div className="p-2">
                      <div className="relative mb-2">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Buscar veículo..."
                          className="pl-8 pr-8 w-full"
                          value={vehicleSearchQuery}
                          onChange={(e) =>
                            setVehicleSearchQuery(e.target.value)
                          }
                          autoFocus
                        />
                      </div>
                      <div className="max-h-60 overflow-y-auto border rounded-md">
                        {filteredVehicles.length > 0 ? (
                          filteredVehicles.map((veiculo) => (
                            <div
                              key={veiculo.id}
                              className="px-4 py-2 cursor-pointer hover:bg-slate-100 hover:text-black"
                              onClick={() => handleVehicleSelect(veiculo)}
                            >
                              <span className="text-sm">
                                {veiculo.placa} | {veiculo.marca?.descricao}{" "}
                                {veiculo.modelo?.descricao} | Modelo{" "}
                                {veiculo.ano_modelo}
                              </span>
                            </div>
                          ))
                        ) : (
                          <div className="px-4 py-2 text-gray-500">
                            Nenhum veículo encontrado
                          </div>
                        )}
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            {selectedVehicle && (
              <div className="p-3 rounded-md mt-4">
                <div className="flex flex-col space-y-1">
                  <h3 className="text-sm font-medium">Veículo selecionado:</h3>
                  <p className="text-sm">
                    <span className="font-medium">Placa:</span>{" "}
                    {selectedVehicle.placa}
                  </p>
                  <p className="text-sm">
                    <span className="font-medium">Modelo:</span>{" "}
                    {selectedVehicle.marca?.descricao}{" "}
                    {selectedVehicle.modelo?.descricao}
                  </p>
                  <p className="text-sm">
                    <span className="font-medium">Ano:</span>{" "}
                    {selectedVehicle.ano_fab} / {selectedVehicle.ano_modelo}
                  </p>
                </div>
                <div className="mt-4 flex justify-end">
                  <Button
                    className="bg-purple-600 hover:bg-purple-700 text-white"
                    onClick={() => setStep("fillItems")}
                  >
                    Avançar
                  </Button>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 m-4">
      <div className="lg:col-span-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="text-xl">Itens a vistoriar</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {itensDaVistoria.slice(0, 9).map((item) => (
                <div key={item.id} className="space-y-2">
                  <Label>{item.descricao} *</Label>
                  <RadioGroup
                    value={avaliacoes[item.id] || ""}
                    onValueChange={(value) =>
                      handleAvaliacaoChange(item.id, value)
                    }
                    className="flex space-x-1"
                  >
                    <div className="flex items-center">
                      <RadioGroupItem
                        value="bom"
                        id={`bom-${item.id}`}
                        className="sr-only"
                      />
                      <Label
                        htmlFor={`bom-${item.id}`}
                        className={`px-3 py-1.5 text-xs rounded cursor-pointer ${
                          avaliacoes[item.id] === "bom"
                            ? "border border-green-500"
                            : " border hover:border-blue-500"
                        }`}
                      >
                        Bom
                      </Label>
                    </div>
                    <div className="flex items-center">
                      <RadioGroupItem
                        value="reparo"
                        id={`reparo-${item.id}`}
                        className="sr-only"
                      />
                      <Label
                        htmlFor={`reparo-${item.id}`}
                        className={`px-3 py-1.5 text-xs rounded cursor-pointer ${
                          avaliacoes[item.id] === "reparo"
                            ? "border border-green-500"
                            : " border hover:border-blue-500"
                        }`}
                      >
                        Reparo
                      </Label>
                    </div>
                    <div className="flex items-center">
                      <RadioGroupItem
                        value="ausente"
                        id={`ausente-${item.id}`}
                        className="sr-only"
                      />
                      <Label
                        htmlFor={`ausente-${item.id}`}
                        className={`px-3 py-1.5 text-xs rounded cursor-pointer ${
                          avaliacoes[item.id] === "ausente"
                            ? "border border-green-500"
                            : " border hover:border-blue-500"
                        }`}
                      >
                        Ausente
                      </Label>
                    </div>
                    <div className="flex items-center">
                      <RadioGroupItem
                        value="nao-avaliado"
                        id={`nao-avaliado-${item.id}`}
                        className="sr-only"
                      />
                      <Label
                        htmlFor={`nao-avaliado-${item.id}`}
                        className={`px-3 py-1.5 text-xs rounded cursor-pointer ${
                          avaliacoes[item.id] === "nao-avaliado"
                            ? "border border-green-500"
                            : " border hover:border-blue-500"
                        }`}
                      >
                        Não avaliado
                      </Label>
                    </div>
                  </RadioGroup>
                </div>
              ))}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
              {itensDaVistoria.slice(9).map((item) => (
                <div key={item.id} className="space-y-2">
                  <Label>{item.descricao} *</Label>
                  <RadioGroup
                    value={avaliacoes[item.id] || ""}
                    onValueChange={(value) =>
                      handleAvaliacaoChange(item.id, value)
                    }
                    className="flex space-x-1"
                  >
                    <div className="flex items-center">
                      <RadioGroupItem
                        value="bom"
                        id={`bom-${item.id}`}
                        className="sr-only"
                      />
                      <Label
                        htmlFor={`bom-${item.id}`}
                        className={`px-3 py-1.5 text-xs rounded cursor-pointer ${
                          avaliacoes[item.id] === "bom"
                            ? "border border-green-500"
                            : " border hover:border-blue-500"
                        }`}
                      >
                        Bom
                      </Label>
                    </div>
                    <div className="flex items-center">
                      <RadioGroupItem
                        value="reparo"
                        id={`reparo-${item.id}`}
                        className="sr-only"
                      />
                      <Label
                        htmlFor={`reparo-${item.id}`}
                        className={`px-3 py-1.5 text-xs rounded cursor-pointer ${
                          avaliacoes[item.id] === "reparo"
                            ? "border border-green-500"
                            : " border hover:border-blue-500"
                        }`}
                      >
                        Reparo
                      </Label>
                    </div>
                    <div className="flex items-center">
                      <RadioGroupItem
                        value="ausente"
                        id={`ausente-${item.id}`}
                        className="sr-only"
                      />
                      <Label
                        htmlFor={`ausente-${item.id}`}
                        className={`px-3 py-1.5 text-xs rounded cursor-pointer ${
                          avaliacoes[item.id] === "ausente"
                            ? "border border-green-500"
                            : " border hover:border-blue-500"
                        }`}
                      >
                        Ausente
                      </Label>
                    </div>
                    <div className="flex items-center">
                      <RadioGroupItem
                        value="nao-avaliado"
                        id={`nao-avaliado-${item.id}`}
                        className="sr-only"
                      />
                      <Label
                        htmlFor={`nao-avaliado-${item.id}`}
                        className={`px-3 py-1.5 text-xs rounded cursor-pointer ${
                          avaliacoes[item.id] === "nao-avaliado"
                            ? "border border-green-500"
                            : " border hover:border-blue-500"
                        }`}
                      >
                        Não avaliado
                      </Label>
                    </div>
                  </RadioGroup>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="lg:col-span-1">
        <Card className="h-full">
          <CardHeader>
            <CardTitle className="text-xl">Avaliação</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="tipo-avaliacao">Tipo de avaliação *</Label>
              <Tabs
                value={tipoAvaliacao}
                onValueChange={setTipoAvaliacao}
                className="w-full"
              >
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="inicial">Inicial</TabsTrigger>
                  <TabsTrigger value="periodica">Periódica</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            <div className="space-y-2">
              <Label htmlFor="data-avaliacao">Data da avaliação</Label>
              <Input
                id="data-avaliacao"
                type="date"
                className="w-full"
                value={date ? format(date, "yyyy-MM-dd") : ""}
                onChange={(e) =>
                  e.target.value
                    ? setDate(new Date(e.target.value))
                    : setDate(new Date())
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="avaliador">Avaliador *</Label>
              <Select value={avaliador} onValueChange={setAvaliador}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Selecione o credenciado" />
                </SelectTrigger>
                <SelectContent>
                  {credenciados &&
                    credenciados.map((credenciado, index) => (
                      <SelectItem key={index} value={credenciado.id}>
                        {credenciado.informacoes[0]?.razao_social}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="odometro-atual">Odômetro atual *</Label>
              <Input
                id="odometro-atual"
                placeholder="12345678 km"
                value={odometroAtual}
                onChange={(e) => setOdometroAtual(Number(e.target.value))}
                type="number"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="previsao-troca">Previsão troca de óleo</Label>
              <div className="grid grid-cols-2 gap-2">
                <Input
                  id="previsao-troca"
                  placeholder="12345678 km"
                  value={previsaoTrocaOleo}
                  type="number"
                  onChange={(e) =>
                    setPrevisaoTrocaOleo(parseInt(e.target.value))
                  }
                />
                <Input
                  id="data-previsao-troca"
                  type="date"
                  value={
                    dataPrevisaoTroca
                      ? format(dataPrevisaoTroca, "yyyy-MM-dd")
                      : ""
                  }
                  onChange={(e) =>
                    e.target.value
                      ? setDataPrevisaoTroca(new Date(e.target.value))
                      : setDataPrevisaoTroca(null)
                  }
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Imagens</Label>
              <div className="flex gap-2">
                <Input
                  type="file"
                  placeholder="Escolha imagens"
                  accept="image/*"
                  className="flex-1"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      setImagem(file);
                      setUploadedImageUrl(null); // Reset the uploaded URL when a new file is selected
                    }
                  }}
                />
                <Button
                  onClick={handleUploadImage}
                  disabled={!imagem || isUploading}
                  className="whitespace-nowrap"
                >
                  {isUploading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Enviando...
                    </>
                  ) : (
                    "Upload"
                  )}
                </Button>
              </div>
              {uploadedImageUrl && (
                <p className="text-xs text-green-600">
                  Imagem enviada com sucesso!
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="observacoes">Observações</Label>
              <textarea
                id="observacoes"
                className="w-full min-h-[100px] p-2 border rounded-md"
                placeholder="Digite alguma observação..."
                value={observacoes}
                onChange={(e) => setObservacoes(e.target.value)}
              />
            </div>

            <div className="pt-4 flex justify-end">
              <Button
                className="bg-teal-500 hover:bg-teal-600 text-white"
                onClick={handleSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Cadastrando...
                  </>
                ) : (
                  "Cadastrar vistoria"
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
