"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { GetItensDaVistoria } from "@/components/forms/inputConfig";
import { itemDaVistoria } from "@/components/forms/schemas";
import { SheetForm } from "@/components/forms/sheet-form";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { Button } from "@/components/ui/button";
import { useTipoDeVeiculo } from "@/context/tipo-de-veiculo-context";
import { useItensDaVistoria } from "@/context/itens-da-vistoria-context";
import { Plus } from "lucide-react";
import { toast } from "sonner";
import { z } from "zod";

export function NovoItemButton() {
  const { tiposDeVeiculo } = useTipoDeVeiculo();
  const { setItensDaVistoria } = useItensDaVistoria();

  // Função para lidar com a adição de novo item
  async function onNewItemVistoria(values: z.infer<typeof itemDaVistoria>) {
    try {
      const response = await fetch("/api/itens-da-vistoria", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });
      
      if (!response.ok) {
        throw new Error("Falha ao criar o item");
      }
      
      const data = await response.json();
      setItensDaVistoria((prev) => [...prev, data.data]);
      
      toast.success("Item criado com sucesso!");
    } catch (error) {
      toast.error("Oops! Alguma coisa deu errado", {
        description: "Não foi possível criar o item",
      });
    }
  }

  return (
    <SheetForm
      title="Novo Item de Vistoria"
      schema={itemDaVistoria}
      onSubmit={onNewItemVistoria}
      triggerLabel={
        <Button className=" rounded-sm px-4 h-9 text-xs gap-1">
          <Plus className="h-4 w-4" />
          Novo item
        </Button>
      }
      defaultValues={defaultValues.itemDaVistoria}
    >
      <GenericFormsInput
        fieldConfig={GetItensDaVistoria(tiposDeVeiculo, "descricao")}
        variants="single"
      />
    </SheetForm>
  );
} 