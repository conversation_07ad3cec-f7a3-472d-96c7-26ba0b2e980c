export const data = [
  {
    os: "OS-0001",
    centro_custo: "BANT",
    veiculo: {
      placa: "ABC-1234",
      marca: "Volkswagen",
      modelo: "Gol",
      versao: "1.0",
    },
    empenho: "123456",
    credenciado: {
      nome: "funilaria e pintura",
      cnpj: "12.345.678/0001-90",
    },
    servico: {
      tipo: "Funilaria",
      desconto: "manutenção",
      codigo: "1234",
      descricao: "Pintura",
      marca: "Tinta",
    },
    orcamento: 1500,
    desconto_percentual: 10,
    desconto_reais: 150,
    valor_negociado: 1350,
    quantidade: 1,
    unidade: "serviço",
    total: 1350
  },
  {
    os: "OS-0002",
    centro_custo: "MANT",
    veiculo: {
      placa: "XYZ-5678",
      marca: "Ford",
      modelo: "Fiesta",
      versao: "1.6",
    },
    empenho: "654321",
    credenciado: {
      nome: "mecânica geral",
      cnpj: "98.765.432/0001-10",
    },
    servico: {
      tipo: "Mecânica",
      desconto: "peças",
      codigo: "5678",
      descricao: "Troca de óleo",
      marca: "Mobil",
    },
    orcamento: 300,
    desconto_percentual: 5,
    desconto_reais: 15,
    valor_negociado: 285,
    quantidade: 1,
    unidade: "serviço",
    total: 285
  },
  {
    os: "OS-0003",
    centro_custo: "LOGI",
    veiculo: {
      placa: "JKL-9876",
      marca: "Chevrolet",
      modelo: "Onix",
      versao: "1.4",
    },
    empenho: "112233",
    credenciado: {
      nome: "auto center",
      cnpj: "23.456.789/0001-99",
    },
    servico: {
      tipo: "Revisão",
      desconto: "serviço",
      codigo: "4321",
      descricao: "Revisão completa",
      marca: "ACDelco",
    },
    orcamento: 800,
    desconto_percentual: 20,
    desconto_reais: 160,
    valor_negociado: 640,
    quantidade: 1,
    unidade: "pacote",
    total: 640
  },
  {
    os: "OS-0004",
    centro_custo: "TRAN",
    veiculo: {
      placa: "MNO-3456",
      marca: "Toyota",
      modelo: "Corolla",
      versao: "2.0",
    },
    empenho: "778899",
    credenciado: {
      nome: "elétrica automotiva",
      cnpj: "11.223.344/0001-55",
    },
    servico: {
      tipo: "Elétrica",
      desconto: "diagnóstico",
      codigo: "8765",
      descricao: "Troca de bateria",
      marca: "Heliar",
    },
    orcamento: 450,
    desconto_percentual: 10,
    desconto_reais: 45,
    valor_negociado: 405,
    quantidade: 1,
    unidade: "item",
    total: 405
  },
  {
    os: "OS-0005",
    centro_custo: "SUPR",
    veiculo: {
      placa: "DEF-2345",
      marca: "Hyundai",
      modelo: "HB20",
      versao: "1.6",
    },
    empenho: "445566",
    credenciado: {
      nome: "oficina mecânica",
      cnpj: "66.777.888/0001-22",
    },
    servico: {
      tipo: "Suspensão",
      desconto: "mão de obra",
      codigo: "1122",
      descricao: "Troca de amortecedor",
      marca: "Monroe",
    },
    orcamento: 950,
    desconto_percentual: 15,
    desconto_reais: 142.5,
    valor_negociado: 807.5,
    quantidade: 2,
    unidade: "par",
    total: 1615
  },
  {
    os: "OS-0006",
    centro_custo: "ENGE",
    veiculo: {
      placa: "GHI-5670",
      marca: "Renault",
      modelo: "Sandero",
      versao: "1.0",
    },
    empenho: "998877",
    credenciado: {
      nome: "centro automotivo",
      cnpj: "55.666.777/0001-33",
    },
    servico: {
      tipo: "Freios",
      desconto: "kit freio",
      codigo: "3344",
      descricao: "Troca de pastilhas",
      marca: "Bosch",
    },
    orcamento: 400,
    desconto_percentual: 10,
    desconto_reais: 40,
    valor_negociado: 360,
    quantidade: 1,
    unidade: "kit",
    total: 360
  },
  {
    os: "OS-0007",
    centro_custo: "FINA",
    veiculo: {
      placa: "UVW-8888",
      marca: "Honda",
      modelo: "Civic",
      versao: "2.0",
    },
    empenho: "223344",
    credenciado: {
      nome: "serviço automotivo",
      cnpj: "88.999.000/0001-77",
    },
    servico: {
      tipo: "Alinhamento",
      desconto: "geometria",
      codigo: "5566",
      descricao: "Alinhamento e balanceamento",
      marca: "Pirelli",
    },
    orcamento: 250,
    desconto_percentual: 0,
    desconto_reais: 0,
    valor_negociado: 250,
    quantidade: 1,
    unidade: "serviço",
    total: 250
  },
  {
    os: "OS-0008",
    centro_custo: "RHUM",
    veiculo: {
      placa: "QRS-9999",
      marca: "Fiat",
      modelo: "Argo",
      versao: "1.3",
    },
    empenho: "665544",
    credenciado: {
      nome: "auto elétrica",
      cnpj: "77.888.999/0001-44",
    },
    servico: {
      tipo: "Injeção",
      desconto: "limpeza",
      codigo: "7788",
      descricao: "Limpeza de bico",
      marca: "Bosch",
    },
    orcamento: 300,
    desconto_percentual: 5,
    desconto_reais: 15,
    valor_negociado: 285,
    quantidade: 1,
    unidade: "serviço",
    total: 285
  },
  {
    os: "OS-0009",
    centro_custo: "ALMO",
    veiculo: {
      placa: "TUV-4444",
      marca: "Nissan",
      modelo: "Kicks",
      versao: "1.6",
    },
    empenho: "334455",
    credenciado: {
      nome: "oficina autorizada",
      cnpj: "33.444.555/0001-66",
    },
    servico: {
      tipo: "Refrigeração",
      desconto: "gás",
      codigo: "8899",
      descricao: "Carga de ar-condicionado",
      marca: "Denso",
    },
    orcamento: 600,
    desconto_percentual: 10,
    desconto_reais: 60,
    valor_negociado: 540,
    quantidade: 1,
    unidade: "serviço",
    total: 540
  },
  {
    os: "OS-0010",
    centro_custo: "TIIN",
    veiculo: {
      placa: "LMN-2222",
      marca: "Peugeot",
      modelo: "208",
      versao: "1.2",
    },
    empenho: "111999",
    credenciado: {
      nome: "multisserviços",
      cnpj: "22.333.444/0001-11",
    },
    servico: {
      tipo: "Lataria",
      desconto: "polimento",
      codigo: "9900",
      descricao: "Desamassamento de porta",
      marca: "3M",
    },
    orcamento: 700,
    desconto_percentual: 12,
    desconto_reais: 84,
    valor_negociado: 616,
    quantidade: 1,
    unidade: "item",
    total: 616
  }
];
