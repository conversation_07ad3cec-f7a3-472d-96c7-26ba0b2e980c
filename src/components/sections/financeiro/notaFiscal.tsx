"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Plus, Search, Filter, Clipboard, Eye, DollarSign } from "lucide-react";
import { DataTable } from "../../tables/data-table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { format } from "date-fns";
import { useEffect, useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../../ui/dialog";
import { Separator } from "../../ui/separator";
import {
  osAnaliseColumn,
  osAutorizadoColumn,
  osCanceladasColumn,
  osExecucaoColumn,
  osFaturadasColumn,
  osFinalizadasColumn,
  osLancadosColumn,
  osOrcamentoColumn,
} from "../../tables/columns";
import { useOS } from "@/context/os-context";
import { useRouter, useSearchParams } from "next/navigation";
import ModalOS from "@/components/modal/modal.os";
import { Badge } from "@/components/ui/badge";
import { Session } from "@/lib/auth/types";
import { getServerSession } from "@/lib/auth/server-session";
import { BsCircleFill, BsCheckCircleFill } from "react-icons/bs";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { useCredenciado } from "@/context/credenciado-context";
import { updateOsCredenciadoAction } from "@/serverActions/orcamentoAction";
import { toast } from "sonner";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { checkSessionActive } from "@/serverActions/checkSession";
import { getCredenciadosByContratoId } from "@/serverActions/credenciadoAction";
import { OSContextMenu } from "../os/context-menu";
import { Detalhes } from "../os/detalhes";
import { MuralOsModal } from "../os/mural-os-modal";

const tabsPT = ["Nota Fiscal Pendente", "Faturadas"];
const tabs = ["finalizada", "faturada"];

export function NotaFiscal() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const selectedTabSearchParam = searchParams.get("selectedTab");
  const osIdSearchParam = searchParams.get("osId");
  const { ordensDeServico, setOrdensDeServico } = useOS();
  const { centrosDeCusto } = useCentroDeCusto();
  const { credenciados } = useCredenciado();
  const [showModal, setShowModal] = useState({ id: "", status: "" });
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDialogDetailsOpen, setIsDialogDetailsOpen] = useState(false);
  const [selectedRowData, setSelectedRowData] = useState<OS | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [sessionData, setSessionData] = useState<Session>();
  const [credenciadosVinculados, setCredenciadosVinculados] = useState<
    credenciado[]
  >([]);
  const [isLoadingCredenciados, setIsLoadingCredenciados] = useState(false);

  // Add these state hooks with your other useState declarations
  const [credenciadoModal, setCredenciadoModal] = useState<{
    show: boolean;
    osData: OS | null;
  }>({
    show: false,
    osData: null,
  });

  const [credenciadoSearch, setCredenciadoSearch] = useState("");
  const [selectedCredenciadoId, setSelectedCredenciadoId] = useState("");

  async function fetchCredenciadosForSelection() {
    if (!sessionData) return;

    setIsLoadingCredenciados(true); // Iniciar loading

    try {
      if (sessionData.roles.includes("ORCAMENTISTA_OFICINA")) {
        // Para orçamentistas, filtrar apenas seu próprio credenciado
        const filtered =
          credenciados?.filter(
            (credenciado) => credenciado.id === sessionData.credenciadoId
          ) || [];
        setCredenciadosVinculados(filtered);
      } else {
        // Para outros usuários, buscar por contrato
        const response = await getCredenciadosByContratoId(
          sessionData.contratoId
        );

        // Verificar a estrutura da resposta e garantir que setamos um array
        if (response.success && Array.isArray(response.data)) {
          setCredenciadosVinculados(response.data);
        } else if (
          response.success &&
          response.data?.credenciados &&
          Array.isArray(response.data.credenciados)
        ) {
          setCredenciadosVinculados(response.data.credenciados);
        } else {
          console.log("Formato de resposta inesperado:", response);
          setCredenciadosVinculados([]);
        }
      }
    } catch (error) {
      console.error("Erro ao buscar credenciados para seleção:", error);
      toast.error("Erro ao carregar orçamentistas");
      setCredenciadosVinculados([]);
    } finally {
      setIsLoadingCredenciados(false); // Finalizar loading independente do resultado
    }
  }

  const [contextMenu, setContextMenu] = useState<{
    show: boolean;
    x: number;
    y: number;
    osData: OS | null;
  }>({
    show: false,
    x: 0,
    y: 0,
    osData: null,
  });

  const [muralModal, setMuralModal] = useState<{
    show: boolean;
    osId: string;
    osNumber: string | number;
    osYear?: string | number;
  }>({
    show: false,
    osId: "",
    osNumber: "",
  });

  const [selectedTab, setSelectedTab] = useState<null | number>(
    Number(selectedTabSearchParam)
  );

  const [selectedOsId, setSelectedOsId] = useState(osIdSearchParam || "");

  useEffect(() => {
    setSelectedTab(Number(selectedTabSearchParam));
    setSelectedOsId(osIdSearchParam || "");
  }, [selectedTabSearchParam, osIdSearchParam]);

  // Adicione este handler para o menu de contexto
  const handleContextMenu = (e: React.MouseEvent, data: OS) => {
    e.preventDefault();
    setContextMenu({
      show: true,
      x: e.clientX,
      y: e.clientY,
      osData: data,
    });
  };

  // Adicione esta função para fechar o menu de contexto
  const handleCloseContextMenu = () => {
    setContextMenu({
      show: false,
      x: 0,
      y: 0,
      osData: null,
    });
  };

  // Adicione esta função para abrir o modal do mural
  const handleOpenMuralModal = (osData: OS) => {
    setMuralModal({
      show: true,
      osId: osData.id,
      osNumber: osData.osNumber,
      osYear: new Date(osData.createdAt).getFullYear(),
    });
  };

  // Adicione esta função para fechar o modal do mural
  const handleCloseMuralModal = () => {
    setMuralModal({
      show: false,
      osId: "",
      osNumber: "",
    });
  };

  // Workflow steps definition
  const workflowSteps = [
    { key: "lançada", label: "Lançadas" },
    { key: "orcamentaçao", label: "Orçamentação" },
    { key: "analise", label: "Análise e Aprovação" },
    { key: "autorizada", label: "Autorizadas" },
    { key: "execucao", label: "Em Execução" },
    { key: "finalizada", label: "Aguardando Nota Fiscal" },
    { key: "faturada", label: "Concluidas" },
    { key: "cancelada", label: "Canceladas" },
  ];

  const currentStatus = selectedRowData?.status ?? "";
  const currentStepIndex = workflowSteps.findIndex(
    (step) => step.key === currentStatus
  );

  // Helper functions for the workflow
  const formatDate = (date: Date | string | undefined) => {
    if (!date) return "";
    return new Date(date).toLocaleDateString("pt-BR");
  };

  const getHoursSince = (date: Date | string | undefined) => {
    if (!date) return 0;
    const start = new Date(date).getTime();
    const now = new Date().getTime();
    return Math.round((now - start) / (1000 * 60 * 60));
  };

  const getRemainingHours = (hoursUsed: number) => {
    const remaining = 72 - hoursUsed;
    return remaining > 0 ? remaining : 0;
  };

  const isWithinTimeLimit = (hoursUsed: number) => hoursUsed <= 72;

  useEffect(() => {
    async function fetchSessionData() {
      const sessionData = await getServerSession();
      setSessionData(sessionData);
    }
    fetchSessionData();
  }, []);

  let osData: OS[] = [];
  if (
    sessionData &&
    !sessionData.roles.includes("ADMIN") &&
    sessionData.centro_de_custoId
  ) {
    if (sessionData.unidade_filha_id) {
      osData = ordensDeServico.filter(
        (os) =>
          os.veiculo?.lotacao_veiculos?.centro_custoID ===
          sessionData.unidade_filha_id
      );
    } else if (sessionData.centro_de_custoId) {
      const unidadesFilhas = centrosDeCusto
        .flatMap((centro) => centro.centro_custos_filhos)
        .filter(
          (uniFilho) =>
            uniFilho.centro_custo_ascdID === sessionData.centro_de_custoId
        );
      const filteredOs = ordensDeServico.filter((os) =>
        unidadesFilhas.some(
          (centro) => centro.id === os.veiculo?.lotacao_veiculos?.centro_custoID
        )
      );
      if (filteredOs.length > 0) {
        osData = filteredOs;
      } else {
        osData = ordensDeServico.filter(
          (os) =>
            os.veiculo?.lotacao_veiculos?.centro_custo?.id ===
            sessionData.centro_de_custoId
        );
      }
    } else {
      osData = ordensDeServico.filter(
        (os) =>
          os.veiculo?.lotacao_veiculos?.centro_custo_ascdID ===
          sessionData.centro_de_custoId
      );
    }
  } else {
    osData = ordensDeServico;
  }

  const showAddButton =
    sessionData?.roles.includes("GESTOR_FROTA") ||
    sessionData?.roles.includes("ABERTURA_OS") ||
    (sessionData?.roles.includes("ORCAMENTISTA_OFICINA") &&
      Boolean(sessionData?.contrato?.abertura_os_credenciado));

  useEffect(() => {
    if (!!selectedTabSearchParam && !!osIdSearchParam) {
      const foundOs = osData.find((os: OS) => os.id === selectedOsId);
      if (foundOs) {
        handleFirstTabRowClick(foundOs);
      }
    }
  }, [selectedTabSearchParam, osIdSearchParam, osData, selectedOsId]);

  const handleFirstTabRowClick = (data: OS) => {
    console.log(data)
    if (
      data.status === "autorizada" ||
      data.status === "finalizada" ||
      data.status === "faturada" ||
      data.status === "pendente" ||
      data.status === "Aguardando Aprovação"
    ) {
      setSelectedRowData(data);
      setIsDialogDetailsOpen(true);
      return;
    }
    if (data.status === "lançada") {
      setSelectedRowData(data);
      setIsDialogOpen(true);
      return;
    }
    router.push(`/dashboard/ordens-de-servico/${data.id}/${data.status}`);
  };

  const handleNovaOS = async () => {
    const isSessionActive = await checkSessionActive();

    if (!isSessionActive) {
      toast.info("Sua sessão expirou. Por favor, faça login novamente.");
      setTimeout(() => {
        window.location.href = "/login";
      }, 1000);
      return;
    }
    router.push("/dashboard/ordens-de-servico/nova-ordem");
  };

  const countByStatus = (status: string | null, secondStatus = "") => {
    if (!status) {
      return osData.length;
    }

    if (status === "finalizada") {
      return sessionData?.roles.includes("ORCAMENTISTA_OFICINA")
        ? osData
            .filter(
              (os) =>
                os.status.toLowerCase() === status.toLowerCase() ||
                os.status === "Aguardando Aprovação"
            )
            .filter((os) => os.credenciadoId === sessionData?.credenciadoId)
            .length
        : osData.filter(
            (os) =>
              os.status.toLowerCase() === status.toLowerCase() ||
              os.status === "Aguardando Aprovação"
          ).length;
    }

    return sessionData?.roles.includes("ORCAMENTISTA_OFICINA")
      ? osData
          .filter(
            (os) =>
              os.status.toLowerCase() === status.toLowerCase() ||
              os.status.toLowerCase() === secondStatus.toLowerCase()
          )
          .filter((os) => os.credenciadoId === sessionData?.credenciadoId)
          .length
      : osData.filter(
          (os) =>
            os.status.toLowerCase() === status.toLowerCase() ||
            os.status.toLowerCase() === secondStatus.toLowerCase()
        ).length;
  };
  const renderDataTable = (status: string, columns: any, secondStatus = "") => {
    const filteredData =
      status === "todas"
        ? osData
            .filter((os) =>
              searchQuery
                ? JSON.stringify(os).toLowerCase().includes(searchQuery.toLowerCase())
                : true
            )
            .sort((a, b) => {
              const dateA = new Date(a.upDateTimedAt || a.createdAt).getTime();
              const dateB = new Date(b.upDateTimedAt || b.createdAt).getTime();
              return dateB - dateA;
            })
        : sessionData?.roles.includes("ORCAMENTISTA_OFICINA")
        ? osData
            .filter(
              (os) =>
                os.status.toLowerCase() === status.toLowerCase() ||
                os.status.toLowerCase() === secondStatus.toLowerCase()
            )
            .filter((os) =>
              os.orcamentos?.find(
                (orcamento) => orcamento.credenciadoId === sessionData?.credenciadoId
              )
            )
            .filter((os) =>
              searchQuery
                ? JSON.stringify(os).toLowerCase().includes(searchQuery.toLowerCase())
                : true
            )
            .sort((a, b) => {
              const dateA = new Date(a.upDateTimedAt || a.createdAt).getTime();
              const dateB = new Date(b.upDateTimedAt || b.createdAt).getTime();
              return dateB - dateA;
            })
        : osData
            .filter(
              (os) =>
                os.status.toLowerCase() === status.toLowerCase() ||
                os.status.toLowerCase() === secondStatus.toLowerCase()
            )
            .filter((os) =>
              searchQuery
                ? JSON.stringify(os).toLowerCase().includes(searchQuery.toLowerCase())
                : true
            )
            .sort((a, b) => {
              const dateA = new Date(a.upDateTimedAt || a.createdAt).getTime();
              const dateB = new Date(b.upDateTimedAt || b.createdAt).getTime();
              return dateB - dateA;
            });

    return (
      <DataTable
        data={filteredData}
        columns={columns}
        exportTo={true}
        handleRowClick={status === "todas" ? undefined : handleFirstTabRowClick}
        showReportButton={true}
        reportType="os"
        onRowContextMenu={handleContextMenu}
      />
    );
  };


  const handleOpenCredenciadoModal = async (osData: OS) => {
    setCredenciadoModal({
      show: true,
      osData,
    });
    setSelectedCredenciadoId("");
    setCredenciadoSearch("");

    // Carregar credenciados quando abrir o modal
    await fetchCredenciadosForSelection();
  };

  const handleCloseCredenciadoModal = () => {
    setCredenciadoModal({
      show: false,
      osData: null,
    });
  };

  const handleConfirmCredenciadoChange = async () => {
    if (!selectedCredenciadoId || !credenciadoModal.osData) return;

    try {
      const response = await updateOsCredenciadoAction(
        credenciadoModal.osData.id,
        selectedCredenciadoId
      );

      if (response.success) {
        toast.success("Orcamentista alterado com sucesso!");
        handleCloseCredenciadoModal();
        window.location.reload();
      } else {
        toast.error(response.error || "Erro ao alterar orcamentista");
      }
    } catch (error) {
      toast.error("Erro ao alterar orcamentista");
      console.error(error);
    }
  };

  const credenciadosFiltered =
    credenciadosVinculados?.filter((credenciado) => {
      if (!credenciadoSearch) return true;

      const searchLower = credenciadoSearch.toLowerCase();
      const razaoSocial =
        credenciado.informacoes?.[0]?.razao_social?.toLowerCase() || "";
      const cnpj = credenciado.informacoes?.[0]?.cnpj?.toLowerCase() || "";

      return razaoSocial.includes(searchLower) || cnpj.includes(searchLower);
    }) || [];

  const hoursUsedInCurrentStep = selectedRowData?.upDateTimedAt
    ? getHoursSince(selectedRowData.upDateTimedAt)
    : 0;

  const isCurrentlyWithinLimit = isWithinTimeLimit(hoursUsedInCurrentStep);

  const remainingHours = getRemainingHours(hoursUsedInCurrentStep);

  return (
    <div className="w-full border rounded-md">
      <div className="p-6 ">
        <Tabs
          defaultValue={selectedTab ? tabs[Number(selectedTab)] : tabs[0]}
          className="w-full"
        >
          <div className="border-b  mb-4 flex justify-between">
            <TabsList className="flex overflow-x-auto space-x-1 h-auto pb-1">
              {tabs.map((tab, index) => (
                <TabsTrigger
                  key={tab}
                  value={tab}
                  className="capitalize text-xs px-4 py-2 border-b-2 border-transparent rounded-none "
                >
                  <h1 className="flex gap-3">
                    {tabsPT[index]}{" "}
                    <Badge
                      variant="default"
                      className="h-5 w-5 p-0 flex items-center justify-center"
                    >
                      {tab === "todas"
                        ? osData.length
                        : countByStatus(
                            tab,
                            tab === "orcamentaçao" ? "pendente" : ""
                          )}
                    </Badge>
                  </h1>
                </TabsTrigger>
              ))}
            </TabsList>
          </div>

          <div className="mb-4 flex w-full p-4 rounded-md gap-2">
            <div className="relative flex ">
              <Input
                placeholder="Buscar rápido"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 h-9 max-w-[240px] rounded-sm"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            </div>
            <Button variant="outline" className="h-9 gap-1 text-xs rounded-sm">
              <Filter size={14} />
              Filtros
            </Button>
            {showAddButton && (
              <Button onClick={handleNovaOS}>
                <Plus size={16} className="mr-1" />
                Nova OS
              </Button>
            )}
          </div>

          {/* Abas e conteúdo */}
          <TabsContent value={tabs[0]}>
            {renderDataTable(
              "finalizada",
              osFinalizadasColumn,
              "Aguardando Aprovação"
            )}
          </TabsContent>
          <TabsContent value={tabs[1]}>
            {renderDataTable("faturada", osFaturadasColumn)}
          </TabsContent>
        </Tabs>
      </div>

      {/* Diálogo de detalhes da OS */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent
          className="overflow-y-auto "
          style={{ maxWidth: "1200px", overflow: "scroll", height: "90vh" }}
        >
          <DialogHeader>
            <DialogTitle>Detalhes da Ordem de Serviço</DialogTitle>
            <DialogDescription>
              Aqui estão os detalhes da ordem de serviço selecionada.
            </DialogDescription>
          </DialogHeader>

          {/* Workflow visualization */}
          {selectedRowData && (
            <div className="overflow-x-auto pb-4">
              <div className="flex items-center gap-2 min-w-max px-4">
                {workflowSteps.map((step, index) => {
                  // Para a etapa "lançada" ou o status atual
                  const currentStepIndex = workflowSteps.findIndex(
                    (s) => s.key === (selectedRowData.status || "lançada")
                  );
                  const isCompleted = index < currentStepIndex;
                  const isCurrent = index === currentStepIndex;

                  // Calcular valores relacionados ao tempo
                  const hoursUsedInCurrentStep = selectedRowData.upDateTimedAt
                    ? getHoursSince(selectedRowData.upDateTimedAt)
                    : getHoursSince(selectedRowData.createdAt);
                  const remainingHours = getRemainingHours(
                    hoursUsedInCurrentStep
                  );
                  const isCurrentlyWithinLimit = isWithinTimeLimit(
                    hoursUsedInCurrentStep
                  );

                  const icon = isCompleted ? (
                    <BsCheckCircleFill className="text-green-500" />
                  ) : isCurrent ? (
                    <BsCheckCircleFill
                      className={
                        isCurrentlyWithinLimit
                          ? "text-yellow-500"
                          : "text-red-500"
                      }
                    />
                  ) : (
                    <BsCircleFill className="text-gray-400" />
                  );

                  const labelClass = isCompleted
                    ? "text-green-600"
                    : isCurrent
                    ? isCurrentlyWithinLimit
                      ? "text-yellow-600 font-medium"
                      : "text-red-600 font-medium"
                    : "text-gray-400";

                  return (
                    <div key={step.key} className="flex items-center gap-2">
                      <div className="flex flex-col items-center">
                        {icon}
                        <span
                          className={`text-xs mt-1 ${labelClass} whitespace-nowrap`}
                        >
                          {step.label}
                        </span>
                        {isCurrent && (
                          <>
                            <span
                              className={`text-xs ${
                                isCurrentlyWithinLimit
                                  ? "text-yellow-600"
                                  : "text-red-600"
                              }`}
                            >
                              {formatDate(
                                selectedRowData.upDateTimedAt ||
                                  selectedRowData.createdAt
                              )}
                            </span>
                            <span
                              className={`text-[10px] font-medium ${
                                isCurrentlyWithinLimit
                                  ? "text-yellow-600"
                                  : "text-red-600"
                              }`}
                            >
                              {isCurrentlyWithinLimit
                                ? `${remainingHours}h restantes`
                                : `Prazo excedido em ${
                                    hoursUsedInCurrentStep - 72
                                  }h`}
                            </span>
                          </>
                        )}
                      </div>
                      {index < workflowSteps.length - 1 && (
                        <div
                          className={`w-6 h-px ${
                            isCompleted
                              ? "bg-green-300"
                              : isCurrent && !isCurrentlyWithinLimit
                              ? "bg-red-300"
                              : "bg-gray-300"
                          } mx-1`}
                        ></div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          <Tabs defaultValue="os">
            <div className="flex justify-between gap-4 w-full mb-4">
              <TabsList>
                <TabsTrigger value="os">Ordem de serviço</TabsTrigger>
                <TabsTrigger value="veiculo">Veículo</TabsTrigger>
              </TabsList>
            </div>
            <TabsContent value="os">
              {selectedRowData && (
                <div className="space-y-2">
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium leading-none">
                      Número da OS
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {`#${selectedRowData.osNumber}/${new Date(
                        selectedRowData.createdAt
                      ).getFullYear()}`}
                    </p>
                  </div>
                  <Separator className="my-4" />

                  <div className="space-y-1">
                    <h4 className="text-sm font-medium leading-none">
                      Centro de custo
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {selectedRowData.veiculo?.lotacao_veiculos?.centro_custo
                        ?.descricao || "Sem informações"}
                    </p>
                  </div>
                  <Separator className="my-4" />

                  <div className="space-y-1">
                    <h4 className="text-sm font-medium leading-none">
                      Data de lançamento
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {format(selectedRowData.createdAt, "dd/MM/yyyy") ||
                        "Sem informações"}
                    </p>
                  </div>
                  <Separator className="my-4" />

                  <div className="space-y-1">
                    <h4 className="text-sm font-medium leading-none">
                      Tipo de serviço
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {selectedRowData.TiposDeOs?.descricao ||
                        "Sem informações"}
                    </p>
                  </div>
                  <Separator className="my-4" />

                  <div className="space-y-1">
                    <h4 className="text-sm font-medium leading-none">
                      Tipo de manutenção
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {selectedRowData.tipo_manutencao || "Sem informações"}
                    </p>
                  </div>
                  <Separator className="my-4" />

                  <div className="space-y-1">
                    <h4 className="text-sm font-medium leading-none">
                      Observações
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {selectedRowData.descricao || "Sem informações"}
                    </p>
                  </div>
                  <Separator className="my-4" />

                  <div className="space-y-1">
                    <h4 className="text-sm font-medium leading-none">
                      Veículo imobilizado
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {selectedRowData.mobilizado ? "Sim" : "Não"}
                    </p>
                  </div>
                  <Separator className="my-4" />

                  <div className="space-y-1">
                    <h4 className="text-sm font-medium leading-none">
                      Localização
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {`${selectedRowData.cidade_loc} - ${selectedRowData.estado_loc}` ||
                        "Sem informações"}
                    </p>
                  </div>
                  <Separator className="my-4" />

                  <div className="space-y-1">
                    <h4 className="text-sm font-medium leading-none">
                      Credenciado orçamentista
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {selectedRowData.credenciado?.informacoes[0]
                        ?.razao_social || "Sem informações"}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {selectedRowData.credenciado?.informacoes[0]?.cnpj ||
                        "Sem informações"}
                    </p>
                  </div>
                </div>
              )}
            </TabsContent>
            <TabsContent value="veiculo">
              {selectedRowData?.veiculo && (
                <div className="space-y-2">
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium leading-none">Placa</h4>
                    <p className="text-sm text-muted-foreground">
                      {selectedRowData.veiculo.placa || "Sem informações"}
                    </p>
                  </div>
                  <Separator className="my-4" />

                  <div className="space-y-1">
                    <h4 className="text-sm font-medium leading-none">Marca</h4>
                    <p className="text-sm text-muted-foreground">
                      {selectedRowData.veiculo.marca?.descricao ||
                        "Sem informações"}
                    </p>
                  </div>
                  <Separator className="my-4" />

                  <div className="space-y-1">
                    <h4 className="text-sm font-medium leading-none">Modelo</h4>
                    <p className="text-sm text-muted-foreground">
                      {selectedRowData.veiculo.modelo?.descricao ||
                        "Sem informações"}
                    </p>
                  </div>
                  <Separator className="my-4" />

                  <div className="space-y-1">
                    <h4 className="text-sm font-medium leading-none">Ano</h4>
                    <p className="text-sm text-muted-foreground">
                      {`${selectedRowData.veiculo.ano_fab} / ${selectedRowData.veiculo.ano_modelo}` ||
                        "Sem informações"}
                    </p>
                  </div>
                  <Separator className="my-4" />

                  <div className="space-y-1">
                    <h4 className="text-sm font-medium leading-none">Chassi</h4>
                    <p className="text-sm text-muted-foreground">
                      {selectedRowData.veiculo.vin || "Sem informações"}
                    </p>
                  </div>
                  <Separator className="my-4" />

                  <div className="space-y-1">
                    <h4 className="text-sm font-medium leading-none">Cor</h4>
                    <p className="text-sm text-muted-foreground">
                      {selectedRowData.veiculo.cor || "Sem informações"}
                    </p>
                  </div>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </DialogContent>
        {showModal.id && <ModalOS tabs={tabsPT} modal={showModal} />}
      </Dialog>

      {/* Diálogo de detalhes da OS */}
      <Dialog open={isDialogDetailsOpen} onOpenChange={setIsDialogDetailsOpen}>
        <DialogContent
          style={{ maxWidth: "1200px", overflow: "auto", maxHeight: "95vh" }}
        >
          <DialogHeader>
            <DialogTitle>Detalhes da Ordem de Serviço</DialogTitle>
            <DialogDescription>
              Aqui estão os detalhes da ordem de serviço selecionada.
            </DialogDescription>
          </DialogHeader>
          <Detalhes
            data={selectedRowData}
            osStatus={selectedRowData?.status}
            displayOnlyInvoice={true}
          />
        </DialogContent>
      </Dialog>

      {contextMenu.show && contextMenu.osData && (
        <OSContextMenu
          x={contextMenu.x}
          y={contextMenu.y}
          onClose={handleCloseContextMenu}
          items={[
            {
              label: "Ver mural da OS",
              onClick: () => handleOpenMuralModal(contextMenu.osData!),
              icon: <Clipboard className="h-4 w-4" />,
            },
            {
              label: "Ver detalhes",
              onClick: () => handleFirstTabRowClick(contextMenu.osData!),
              icon: <Eye className="h-4 w-4" />,
            },
            // New option - only show when status is "lançada"
            ...(contextMenu.osData.status === "lançada"
              ? [
                  {
                    label: "Alterar Orcamentista",
                    onClick: () =>
                      handleOpenCredenciadoModal(contextMenu.osData!),
                    icon: <DollarSign className="h-4 w-4" />,
                  },
                ]
              : []),
          ]}
        />
      )}

      {/* Modal do Mural */}
      {muralModal.show && (
        <MuralOsModal
          isOpen={muralModal.show}
          onClose={handleCloseMuralModal}
          osId={muralModal.osId}
          osNumber={muralModal.osNumber}
          osYear={muralModal.osYear}
        />
      )}

      {/* Credenciado Selection Modal */}
      {credenciadoModal.show && credenciadoModal.osData && (
        <Dialog open={true} onOpenChange={() => handleCloseCredenciadoModal()}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Alterar Orcamentista</DialogTitle>
              <DialogDescription>
                Selecione um novo orcamentista para a OS #
                {credenciadoModal.osData.osNumber}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="credenciado-select">
                  Selecione o Orcamentista
                </Label>

                {/* Campo de busca independente */}
                <div className="relative">
                  <Input
                    placeholder="Buscar por nome ou CNPJ"
                    value={credenciadoSearch}
                    onChange={(e) => setCredenciadoSearch(e.target.value)}
                    className="w-full mb-2"
                    autoFocus
                  />
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                </div>

                <div className="border rounded-md max-h-60 overflow-y-auto">
                  {isLoadingCredenciados ? (
                    <div className="p-6 flex justify-center items-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
                    </div>
                  ) : credenciadosFiltered.length > 0 ? (
                    <div className="p-0 divide-y">
                      {credenciadosFiltered.map((credenciado) => (
                        <div
                          key={credenciado.id}
                          className={`p-3 cursor-pointer transition-colors border ${
                            selectedCredenciadoId === credenciado.id
                              ? "border-green-500"
                              : "border-transparent"
                          }`}
                          onClick={() =>
                            setSelectedCredenciadoId(credenciado.id)
                          }
                        >
                          <div className="flex items-center gap-2">
                            <div
                              className={`h-4 w-4 rounded-full border flex items-center justify-center ${
                                selectedCredenciadoId === credenciado.id
                                  ? "border-green-500 bg-green-500"
                                  : "border-gray-300"
                              }`}
                            >
                              {selectedCredenciadoId === credenciado.id && (
                                <div className="h-2 w-2 rounded-full bg-white" />
                              )}
                            </div>
                            <span>
                              {credenciado.informacoes?.[0]?.razao_social ||
                                "Sem nome"}{" "}
                              -
                              {credenciado.informacoes?.[0]?.cnpj || "Sem CNPJ"}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="p-6 text-center text-sm text-muted-foreground">
                      Nenhum orçamentista encontrado
                    </div>
                  )}
                </div>
                {/* Rodapé com contagem de credenciados */}
                <div className="flex justify-end px-2 py-1 text-xs ">
                  {isLoadingCredenciados
                    ? "Carregando..."
                    : `${credenciadosFiltered.length} orçamentista${
                        credenciadosFiltered.length === 1 ? "" : "s"
                      } disponível${
                        credenciadosFiltered.length === 1 ? "" : "is"
                      }`}
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={handleCloseCredenciadoModal}>
                Cancelar
              </Button>
              <Button
                onClick={handleConfirmCredenciadoChange}
                disabled={!selectedCredenciadoId}
              >
                Confirmar
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
