import { useSession } from "@/components/session/use-session";
import { useOS } from "@/context/os-context";
import React, { useMemo, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DownloadIcon,
  FileTextIcon,
  SearchIcon,
  SlidersHorizontalIcon,
  FileSpreadsheetIcon,
  PrinterIcon,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  formatCurrency,
  generateXLSX,
  generatePDF,
  generatePrintablePDF,
} from "@/utils/export-utils";
import { PDFExportModal, type PDFExportOptions } from "@/components/modal/pdf-export-modal";

const FaturamentoCredenciado = () => {
  // Use the hooks with fallback to sample data
  const { session } = useSession();
  const { ordensDeServico } = useOS();
  const [searchTerm, setSearchTerm] = useState("");
  const [showPDFModal, setShowPDFModal] = useState(false);
  const [exportType, setExportType] = useState<"download" | "print">("download");

  // Filter service orders with "faturada" status
  const osFaturada = useMemo(() => {
    return ordensDeServico.filter((os) => os.status === "faturada");
  }, [ordensDeServico]);
  // Filter budgets for the current credenciado
  const orcamentosDoCredenciado = useMemo(() => {
    return osFaturada
      .flatMap((os) => os.orcamentos || [])
      .filter(
        (orcamento) =>
          orcamento.credenciadoId === session?.credenciadoId &&
          (orcamento.status === "faturada" || orcamento.status === "execucao")
      );
  }, [osFaturada, session?.credenciadoId]);
  // Calculate summary values
  const summary = useMemo(() => {
    return orcamentosDoCredenciado.reduce(
      (acc, orcamento) => {
        const valorPecas =
          orcamento.processedPecas?.reduce((sum, peca) => sum + peca.valorNegociado, 0) || 0;

        const valorServicos =
          orcamento.processedServicos?.reduce((sum, servico) => sum + (servico.valor || 0), 0) || 0;

        return {
          totalPecas: acc.totalPecas + valorPecas,
          totalServicos: acc.totalServicos + valorServicos,
          totalGeral: acc.totalGeral + orcamento.valorTotal,
          totalOrcamentos: acc.totalOrcamentos + 1,
        };
      },
      { totalPecas: 0, totalServicos: 0, totalGeral: 0, totalOrcamentos: 0 }
    );
  }, [orcamentosDoCredenciado]);

  // Filter data based on search term
  const filteredOrcamentos = useMemo(() => {
    if (!searchTerm) return orcamentosDoCredenciado;

    const term = searchTerm.toLowerCase();
    return orcamentosDoCredenciado.filter((orcamento) => {
      // Find the corresponding OS
      const os = osFaturada.find((os) => os.id === orcamento.osId);

      return (
        orcamento.numeroOrcamento?.toLowerCase().includes(term) ||
        os?.osNumber?.toString().includes(term) ||
        os?.descricao?.toLowerCase().includes(term) ||
        os?.veiculo?.placa?.toLowerCase().includes(term) ||
        os?.veiculo?.modelo?.descricao?.toLowerCase().includes(term) ||
        orcamento.processedPecas?.some(
          (peca) =>
            peca.descricao?.toLowerCase().includes(term) ||
            peca.marca?.toLowerCase().includes(term) ||
            peca.codigo?.toLowerCase().includes(term)
        )
      );
    });
  }, [orcamentosDoCredenciado, osFaturada, searchTerm]);

  // Export data as CSV
  const exportToCSV = () => {
    // Headers for the CSV
    const headers = [
      "Nº OS",
      "Nº Orçamento",
      "Data Emissão",
      "Veículo",
      "Placa",
      "Descrição OS",
      "Peças",
      "Valor Peças",
      "Valor Serviços",
      "Valor Total",
      "Status",
    ].join(",");

    // Map data to CSV rows
    const rows = filteredOrcamentos
      .map((orcamento) => {
        const os = osFaturada.find((os) => os.id === orcamento.osId);
        const valorPecas =
          orcamento.processedPecas?.reduce((sum, peca) => sum + peca.valorNegociado, 0) || 0;

        const valorServicos =
          orcamento.processedServicos?.reduce((sum, servico) => sum + (servico.valor || 0), 0) || 0;

        const pecasDescricao = orcamento.processedPecas
          ?.map((peca) => `${peca.quantidade}x ${peca.descricao} (${peca.marca})`)
          .join("; ");

        return [
          os?.osNumber || "",
          orcamento.numeroOrcamento || "",
          orcamento.data_emissao ? format(new Date(orcamento.data_emissao), "dd/MM/yyyy") : "",
          `${os?.veiculo?.marca?.descricao || ""} ${os?.veiculo?.modelo?.descricao || ""}`,
          os?.veiculo?.placa || "",
          os?.descricao?.replace(/,/g, ";") || "",
          pecasDescricao?.replace(/,/g, ";") || "",
          (valorPecas / 100).toFixed(2).replace(".", ","),
          (valorServicos / 100).toFixed(2).replace(".", ","),
          (orcamento.valorTotal / 100).toFixed(2).replace(".", ","),
          "Faturada",
        ].join(",");
      })
      .join("\n");

    // Combine headers and rows
    const csv = `${headers}\n${rows}`;

    // Create a download link
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", `faturamento_${format(new Date(), "yyyy-MM-dd")}.csv`);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Export data as XLSX
  const exportToXLSX = () => {
    const data = filteredOrcamentos.map((orcamento) => {
      const os = osFaturada.find((os) => os.id === orcamento.osId);
      const valorPecas =
        orcamento.processedPecas?.reduce((sum, peca) => sum + peca.valorNegociado, 0) || 0;

      const valorServicos =
        orcamento.processedServicos?.reduce((sum, servico) => sum + (servico.valor || 0), 0) || 0;

      const pecasDescricao = orcamento.processedPecas
        ?.map((peca) => `${peca.quantidade}x ${peca.descricao} (${peca.marca})`)
        .join("; ");

      return {
        osNumber: os?.osNumber || "",
        numeroOrcamento: orcamento.numeroOrcamento || "",
        dataEmissao: orcamento.data_emissao
          ? format(new Date(orcamento.data_emissao), "dd/MM/yyyy")
          : "",
        veiculo: `${os?.veiculo?.marca?.descricao || ""} ${os?.veiculo?.modelo?.descricao || ""}`,
        placa: os?.veiculo?.placa || "",
        descricaoOS: os?.descricao || "",
        pecas: pecasDescricao || "",
        valorPecas: valorPecas / 100,
        valorServicos: valorServicos / 100,
        valorTotal: orcamento.valorTotal / 100,
        status: "Faturada",
      };
    });

    const headers = [
      "Nº OS",
      "Nº Orçamento",
      "Data Emissão",
      "Veículo",
      "Placa",
      "Descrição OS",
      "Peças",
      "Valor Peças",
      "Valor Serviços",
      "Valor Total",
      "Status",
    ];

    generateXLSX(data, headers, `faturamento_${format(new Date(), "yyyy-MM-dd")}.xlsx`);
  };

  const handlePDFExport = (options?: PDFExportOptions) => {
    const data = filteredOrcamentos.map((orcamento) => {
      const os = osFaturada.find((os) => os.id === orcamento.osId);
      const valorPecas =
        orcamento.processedPecas?.reduce((sum, peca) => sum + peca.valorNegociado, 0) || 0;

      const valorServicos =
        orcamento.processedServicos?.reduce((sum, servico) => sum + (servico.valor || 0), 0) || 0;

      return {
        osNumber: os?.osNumber || "",
        numeroOrcamento: orcamento.numeroOrcamento || "",
        dataEmissao: orcamento.data_emissao
          ? format(new Date(orcamento.data_emissao), "dd/MM/yyyy")
          : "",
        veiculo: `${os?.veiculo?.marca?.descricao || ""} ${
          os?.veiculo?.modelo?.descricao || ""
        }`.trim(),
        placa: os?.veiculo?.placa || "",
        valorPecas: valorPecas / 100,
        valorServicos: valorServicos / 100,
        valorTotal: orcamento.valorTotal / 100,
      };
    });

    if (exportType === "download") {
      // Use CSV export as a fallback since we can't generate proper PDFs client-side
      generatePDF(
        data,
        summary,
        session,
        `faturamento_${format(new Date(), "yyyy-MM-dd")}.pdf`,
        options
      );
    } else {
      // Use print functionality for a better PDF experience
      generatePrintablePDF(data, summary, session, options);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <DownloadIcon className="mr-2 h-4 w-4" />
                  Exportar
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={exportToCSV}>
                  <FileTextIcon className="mr-2 h-4 w-4" />
                  Exportar como CSV
                </DropdownMenuItem>
                <DropdownMenuItem onClick={exportToXLSX}>
                  <FileSpreadsheetIcon className="mr-2 h-4 w-4" />
                  Exportar como XLSX
                </DropdownMenuItem>
                {/* <DropdownMenuItem
                  onClick={() => {
                    setExportType("download");
                    setShowPDFModal(true);
                  }}>
                  <DownloadIcon className="mr-2 h-4 w-4" />
                  Baixar como CSV
                </DropdownMenuItem> */}
                <DropdownMenuItem
                  onClick={() => {
                    setExportType("print");
                    setShowPDFModal(true);
                  }}>
                  <PrinterIcon className="mr-2 h-4 w-4" />
                  Imprimir Relatório
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Buscar por OS, orçamento, placa, descrição..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button variant="outline" size="icon">
              <SlidersHorizontalIcon className="h-4 w-4" />
              <span className="sr-only">Filtros</span>
            </Button>
          </div>

          <div className="rounded-md border overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nº OS</TableHead>
                  <TableHead>Nº Orçamento</TableHead>
                  <TableHead>Data Emissão</TableHead>
                  <TableHead>Veículo</TableHead>
                  <TableHead>Placa</TableHead>
                  <TableHead className="text-right">Valor Peças</TableHead>
                  <TableHead className="text-right">Valor Serviços</TableHead>
                  <TableHead className="text-right">Valor Total</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredOrcamentos.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-6 text-muted-foreground">
                      Nenhum orçamento encontrado
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredOrcamentos.map((orcamento) => {
                    // Find the corresponding OS
                    const os = osFaturada.find((os) => os.id === orcamento.osId);

                    // Calculate values
                    const valorPecas =
                      orcamento.processedPecas?.reduce(
                        (sum, peca) => sum + peca.valorNegociado,
                        0
                      ) || 0;

                    const valorServicos =
                      orcamento.processedServicos?.reduce(
                        (sum, servico) => sum + (servico.valor || 0),
                        0
                      ) || 0;

                    return (
                      <TableRow key={orcamento.id}>
                        <TableCell>{os?.osNumber || "-"}</TableCell>
                        <TableCell>{orcamento.numeroOrcamento || "-"}</TableCell>
                        <TableCell>
                          {orcamento.data_emissao
                            ? format(new Date(orcamento.data_emissao), "dd/MM/yyyy", {
                                locale: ptBR,
                              })
                            : "-"}
                        </TableCell>
                        <TableCell>
                          {os?.veiculo
                            ? `${os.veiculo.marca?.descricao || ""} ${
                                os.veiculo.modelo?.descricao || ""
                              }`
                            : "-"}
                        </TableCell>
                        <TableCell>{os?.veiculo?.placa || "-"}</TableCell>
                        <TableCell className="text-right">{formatCurrency(valorPecas)}</TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(valorServicos)}
                        </TableCell>
                        <TableCell className="text-right font-medium">
                          {formatCurrency(orcamento.valorTotal)}
                        </TableCell>
                        <TableCell>
                          <Badge variant="default">Faturada</Badge>
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="border-t bg-muted/50 p-4">
          <div className="w-full flex flex-col gap-2 sm:flex-row sm:justify-between">
            <div className="text-sm">
              <span className="text-muted-foreground">Total de Orçamentos:</span>{" "}
              <span className="font-medium">{summary.totalOrcamentos}</span>
            </div>
            <div className="flex flex-col gap-2 sm:flex-row sm:gap-6">
              <div className="text-sm">
                <span className="text-muted-foreground">Total Peças:</span>{" "}
                <span className="font-medium">{formatCurrency(summary.totalPecas)}</span>
              </div>
              <div className="text-sm">
                <span className="text-muted-foreground">Total Serviços:</span>{" "}
                <span className="font-medium">{formatCurrency(summary.totalServicos)}</span>
              </div>
              <div className="text-sm">
                <span className="text-muted-foreground">Total Geral:</span>{" "}
                <span className="font-medium">{formatCurrency(summary.totalGeral)}</span>
              </div>
            </div>
          </div>
        </CardFooter>
      </Card>
      <PDFExportModal
        isOpen={showPDFModal}
        onClose={() => setShowPDFModal(false)}
        onExport={handlePDFExport}
        summary={summary}
      />
    </div>
  );
};

export default FaturamentoCredenciado;
