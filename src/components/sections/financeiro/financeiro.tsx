"use client";

import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { DateRange } from "react-day-picker";

import { ColumnDef } from "@tanstack/react-table";
import { useEffect, useState } from "react";
import { useOS } from "@/context/os-context";
import { useCredenciado } from "@/context/credenciado-context";
import { Session } from "@/lib/auth/types";
import { getServerSession } from "@/lib/auth/server-session";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { getCredenciadoById } from "@/serverActions/credenciadoAction";

export const financeiroColumns: ColumnDef<any>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Selecionar tudo"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Selecionar linha"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "os",
    header: ({ column }) => <DataTableColumnHeader column={column} title="OS" />,
  },
  {
    accessorKey: "centro_custo",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Centro de Custo" />,
  },
  {
    accessorKey: "veiculo.placa",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Placa" />,
  },
  {
    accessorKey: "veiculo.marca",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Marca" />,
  },
  {
    accessorKey: "veiculo.modelo",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Modelo" />,
  },
  {
    accessorKey: "empenho",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Empenho - Nota" />,
  },
  {
    accessorKey: "credenciado.nome",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Credenciado (Razão Social)" />
    ),
  },
  {
    accessorKey: "credenciado.cnpj",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Credenciado (CNPJ)" />,
  },
  {
    accessorKey: "servico.tipo",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Tipo" />,
  },
  {
    accessorKey: "servico.deconto",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Grupo Desconto" />,
    cell: ({ row }) => {
      return `${row.original.desconto_percentual}%`;
    },
  },
  {
    accessorKey: "servico.codigo",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Código" />,
  },
  {
    accessorKey: "servico.descricao",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Descrição" />,
  },
  {
    accessorKey: "servico.marca",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Marca" />,
  },
  {
    accessorKey: "orcamento",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Orçamento" />,
    cell: ({ row }) =>
      row.original.orcamento.toLocaleString("pt-BR", {
        style: "currency",
        currency: "BRL",
      }),
  },
  {
    accessorKey: "quantidade",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Quantidade" />,
  },
  {
    accessorKey: "unidade",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Unidade" />,
  },
  {
    accessorKey: "total_bruto",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Total Bruto" />,
    cell: ({ row }) =>
      row.original.total_bruto?.toLocaleString("pt-BR", {
        style: "currency",
        currency: "BRL",
      }),
  },
  {
    accessorKey: "desconto_reais",
    header: ({ column }) => <DataTableColumnHeader column={column} title="(R$) Desconto" />,
    cell: ({ row }) => {
      return row.original.desconto_reais.toLocaleString("pt-BR", {
        style: "currency",
        currency: "BRL",
      });
    },
  },
  {
    accessorKey: "total",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Total" />,
    cell: ({ row }) =>
      row.original.total.toLocaleString("pt-BR", {
        style: "currency",
        currency: "BRL",
      }),
  },
  {
    accessorKey: "desconto_percentual",
    header: ({ column }) => <DataTableColumnHeader column={column} title="(%) Desconto Total" />,
    cell: ({ row }) => {
      return `${row.original.desconto_percentual}%`;
    },
  },
];

export function FinanceiroTable() {
  const [sessionData, setSessionData] = useState<Session>();
  const { ordensDeServico } = useOS();
  const { centrosDeCusto } = useCentroDeCusto();
  const { credenciados } = useCredenciado();
  const [financeiroData, setFinanceiroData] = useState<any[]>([]);
  const [dadosOriginais, setDadosOriginais] = useState<any[]>([]);

  // Estados para os filtros
  const [filtroOS, setFiltroOS] = useState<string>("");
  const [filtroPlaca, setFiltroPlaca] = useState<string>("");
  const [filtroCentroCusto, setFiltroCentroCusto] = useState<string>("all");
  const [filtroCredenciado, setFiltroCredenciado] = useState<string>("all");
  const [filtroDateRange, setFiltroDateRange] = useState<DateRange | undefined>(undefined);

  // Listas para os selects
  const [centrosCusto, setCentrosCusto] = useState<string[]>([]);
  const [listaCredenciados, setListaCredenciados] = useState<string[]>([]);

  useEffect(() => {
    async function fetchSessionData() {
      const sessionData = await getServerSession();
      setSessionData(sessionData);
    }
    fetchSessionData();
  }, []);

  useEffect(() => {
    const processarDados = async () => {
      if (!ordensDeServico || !credenciados) return;

      // Filtrar ordens de serviço com base na sessão
      let filteredOS = ordensDeServico;

      if (sessionData && !sessionData.roles.includes("ADMIN") && sessionData.centro_de_custoId) {
        if (sessionData.unidade_filha_id) {
          filteredOS = ordensDeServico.filter(
            (os) => os.veiculo?.lotacao_veiculos?.centro_custoID === sessionData.unidade_filha_id
          );
        } else if (sessionData.centro_de_custoId) {
          const unidadesFilhas = centrosDeCusto
            .flatMap((centro) => centro.centro_custos_filhos)
            .filter((uniFilho) => uniFilho.centro_custo_ascdID === sessionData.centro_de_custoId);

          filteredOS = ordensDeServico.filter((os) =>
            unidadesFilhas.some(
              (centro) => centro.id === os.veiculo?.lotacao_veiculos?.centro_custoID
            )
          );
        } else {
          filteredOS = ordensDeServico.filter(
            (os) =>
              os.veiculo?.lotacao_veiculos?.centro_custo_ascdID === sessionData.centro_de_custoId
          );
        }
      }

      // Filtrar apenas OS faturadas
      const osFaturadas = filteredOS.filter((os) => os.status === "faturada");

      const dadosFinanceiros: any[] = [];

      for (const os of osFaturadas) {
        const orcamentosFaturados =
          os.orcamentos?.filter((orc) => orc.status === "faturada" || orc.status === "execucao") ||
          [];
        console.log(orcamentosFaturados);
        for (const orc of orcamentosFaturados) {
          const credenciadoId = orc.credenciadoId;
          const credenciadoData = await getCredenciadoById(credenciadoId);
          const credenciadoInfo = credenciadoData?.data?.credenciado?.informacoes?.[0];

          // Processar peças
          if (orc.processedPecas && orc.processedPecas.length > 0) {
            orc.processedPecas.forEach((peca) => {
              const isLubrificante =
                peca.descricao?.toLowerCase().includes("óleo") ||
                peca.descricao?.toLowerCase().includes("oleo") ||
                peca.descricao?.toLowerCase().includes("lubrificante") ||
                peca.marca?.toLowerCase().includes("lubrax") ||
                peca.marca?.toLowerCase().includes("mobil");

              const valorUnitario = peca.valorUnitario / 100;
              const valorNegociado = peca.valorNegociado / 100;
              const valorDesconto = peca.valorDesconto / 100;

              const valorOriginal = valorUnitario * peca.quantidade;
              const desconto = valorOriginal - valorNegociado;

              const desconto_reais = valorUnitario * peca.quantidade - valorNegociado;
              console.log(
                "Valor Unitario: ",
                valorUnitario,
                "Quantidade: ",
                peca.quantidade,
                "Valor negociado: ",
                valorNegociado
              );

              const desconto_percentual = Math.round((desconto / valorOriginal) * 100);

              dadosFinanceiros.push({
                os: os.osNumber.toString(),
                centro_custo: os.veiculo?.lotacao_veiculos?.centro_custo?.descricao || "N/A",
                veiculo: {
                  placa: os.veiculo?.placa || "N/A",
                  marca: os.veiculo?.marca?.descricao || "N/A",
                  modelo: os.veiculo?.modelo?.descricao || "N/A",
                  versao: os.veiculo?.versao?.descricao || "N/A",
                },
                empenho: orc.numeroOrcamento || "N/A",
                credenciado: {
                  nome: credenciadoInfo?.razao_social || "N/A",
                  cnpj: credenciadoInfo?.cnpj || "N/A",
                },
                servico: {
                  tipo: isLubrificante ? "Lubrificante" : "Peça",
                  desconto: peca.tipoPecas ?? "Peças",
                  codigo: peca.codigo || "N/A",
                  descricao: peca.descricao || "N/A",
                  marca: peca.marca || "N/A",
                },
                orcamento: valorUnitario,
                desconto_percentual: desconto_percentual,
                desconto_reais: desconto_reais,
                valor_negociado: valorNegociado,
                quantidade: peca.quantidade || 1,
                unidade: isLubrificante ? "LUB" : "UNI",
                total_bruto: valorUnitario * peca.quantidade,
                total: valorNegociado,
                data_integracao: os.upDateTimedAt,
              });
            });
          }

          // Processar serviços
          if (orc.processedServicos && orc.processedServicos.length > 0) {
            orc.processedServicos.forEach((servico) => {
              const valorTotal = servico.valor / 100;

              dadosFinanceiros.push({
                os: os.osNumber.toString(),
                centro_custo: os.veiculo?.lotacao_veiculos?.centro_custo?.descricao || "N/A",
                veiculo: {
                  placa: os.veiculo?.placa || "N/A",
                  marca: os.veiculo?.marca?.descricao || "N/A",
                  modelo: os.veiculo?.modelo?.descricao || "N/A",
                  versao: os.veiculo?.versao?.descricao || "N/A",
                },
                empenho: orc.numeroOrcamento || "N/A",
                credenciado: {
                  nome: credenciadoInfo?.razao_social || "N/A",
                  cnpj: credenciadoInfo?.cnpj || "N/A",
                },
                servico: {
                  tipo: "Serviço",
                  desconto: "Mão de Obra",
                  codigo: servico.tipoServico || "N/A",
                  descricao: servico.descricao || "N/A",
                  marca: "Serviço",
                },
                orcamento: valorTotal,
                desconto_percentual: 0,
                desconto_reais: 0,
                valor_negociado: valorTotal,
                quantidade: 1,
                unidade: "HOR",
                total: valorTotal,
                total_bruto: valorTotal,
                data_integracao: os.upDateTimedAt,
              });
            });
          }
        }
      }

      // Extrair listas únicas para filtros
      const uniqueCentrosCusto = [...new Set(dadosFinanceiros.map((item) => item.centro_custo))];
      const uniqueCredenciados = [
        ...new Set(dadosFinanceiros.map((item) => item.credenciado.nome)),
      ];

      setCentrosCusto(uniqueCentrosCusto);
      setListaCredenciados(uniqueCredenciados);
      setDadosOriginais(dadosFinanceiros);
      setFinanceiroData(dadosFinanceiros);
    };

    processarDados();
  }, [ordensDeServico, credenciados, sessionData]);

  // Função para aplicar filtros
  const aplicarFiltros = () => {
    let dadosFiltrados = [...dadosOriginais];

    // Filtro por número da OS
    if (filtroOS) {
      dadosFiltrados = dadosFiltrados.filter((item) =>
        item.os.toString().toLowerCase().includes(filtroOS.toLowerCase())
      );
    }

    // Filtro por placa do veículo
    if (filtroPlaca) {
      dadosFiltrados = dadosFiltrados.filter((item) =>
        item.veiculo.placa.toLowerCase().includes(filtroPlaca.toLowerCase())
      );
    }

    // Filtro por centro de custo
    if (filtroCentroCusto && filtroCentroCusto !== "all") {
      dadosFiltrados = dadosFiltrados.filter((item) => item.centro_custo === filtroCentroCusto);
    }

    // Filtro por credenciado
    if (filtroCredenciado && filtroCredenciado !== "all") {
      dadosFiltrados = dadosFiltrados.filter((item) => item.credenciado.nome === filtroCredenciado);
    }

    // Filtro por intervalo de data
    if (filtroDateRange && filtroDateRange.from) {
      const dataInicio = new Date(filtroDateRange.from);
      dataInicio.setHours(0, 0, 0, 0); // Ajusta para o início do dia

      dadosFiltrados = dadosFiltrados.filter((item) => {
        const dataIntegracao = new Date(item.data_integracao);
        return dataIntegracao >= dataInicio;
      });

      // Se tiver data final também, aplica o filtro de data final
      if (filtroDateRange.to) {
        const dataFim = new Date(filtroDateRange.to);
        dataFim.setHours(23, 59, 59, 999); // Ajusta para o final do dia

        dadosFiltrados = dadosFiltrados.filter((item) => {
          const dataIntegracao = new Date(item.data_integracao);
          return dataIntegracao <= dataFim;
        });
      }
    }

    setFinanceiroData(dadosFiltrados);
  };

  // Função para limpar filtros
  const limparFiltros = () => {
    setFiltroOS("");
    setFiltroPlaca("");
    setFiltroCentroCusto("all");
    setFiltroCredenciado("all");
    setFiltroDateRange(undefined);
    setFinanceiroData(dadosOriginais);
  };

  return (
    <div className="space-y-4">
      <div className="p-4 rounded-md shadow">
        <h3 className="text-lg font-medium mb-3">Filtros</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 w-1/2">
          {/* Filtro por número da OS */}
          <div>
            <Label htmlFor="filtroOS">Número da OS</Label>
            <Input
              id="filtroOS"
              placeholder="Digite o número da OS"
              value={filtroOS}
              onChange={(e) => setFiltroOS(e.target.value)}
              className="mt-1"
            />
          </div>

          {/* Filtro por placa do veículo */}
          <div>
            <Label htmlFor="filtroPlaca">Placa do Veículo</Label>
            <Input
              id="filtroPlaca"
              placeholder="Digite a placa"
              value={filtroPlaca}
              onChange={(e) => setFiltroPlaca(e.target.value)}
              className="mt-1"
            />
          </div>

          {/* Filtro por centro de custo */}
          <div>
            <Label htmlFor="filtroCentroCusto">Centro de Custo</Label>
            <Select value={filtroCentroCusto} onValueChange={setFiltroCentroCusto}>
              <SelectTrigger id="filtroCentroCusto" className="mt-1">
                <SelectValue placeholder="Selecione o centro de custo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                {centrosCusto.map((centro, index) => (
                  <SelectItem key={index} value={centro || `centro_${index}`}>
                    {centro || "N/A"}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Filtro por credenciado */}
          <div>
            <Label htmlFor="filtroCredenciado">Credenciado</Label>
            <Select value={filtroCredenciado} onValueChange={setFiltroCredenciado}>
              <SelectTrigger id="filtroCredenciado" className="mt-1">
                <SelectValue placeholder="Selecione o credenciado" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                {listaCredenciados.map((credenciado, index) => (
                  <SelectItem key={index} value={credenciado || `credenciado_${index}`}>
                    {credenciado || "N/A"}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Filtro por intervalo de data usando o DateRangePicker */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 w-1/2">
          <div className="md:col-span-2">
            <Label>Período de Integração</Label>
            <div className="mt-1">
              <DateRangePicker
                value={filtroDateRange || { from: undefined, to: undefined }}
                onChange={setFiltroDateRange}
              />
            </div>
          </div>

          <div className="flex items-end space-x-2">
            <Button onClick={aplicarFiltros} className="flex-1">
              Aplicar Filtros
            </Button>
            <Button onClick={limparFiltros} variant="outline" className="flex-1">
              Limpar
            </Button>
          </div>
        </div>
      </div>

      <DataTable
        data={financeiroData}
        exportTo={true}
        columns={financeiroColumns}
        reportType="resumo-financeiro"
      />
    </div>
  );
}
