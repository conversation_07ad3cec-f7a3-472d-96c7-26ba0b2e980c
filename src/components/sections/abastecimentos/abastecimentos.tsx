"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { GetAbastecimentoConfig } from "@/components/forms/inputConfig";
import { abastecimentoSchema } from "@/components/forms/schemas";

import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";

import { Checkbox } from "@/components/ui/checkbox";
import { useAbastecimento } from "@/context/abatecimento-context";
import { useCondutor } from "@/context/condutor-context";
import { useCredenciado } from "@/context/credenciado-context";
import { useVeiculos } from "@/context/veiculos-context";

import { ColumnDef } from "@tanstack/react-table";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { z } from "zod";
import { ChevronDown, Filter, FileText, Plus, Search, ChevronRight, ChevronLeft, RefreshCcw } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";

export const abastecimentoColumn: ColumnDef<abastecimento>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "id",
    header: ({ column }) => <DataTableColumnHeader column={column} title="ID" />,
  },
  {
    accessorKey: "centro_de_custo",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Centro de Custo" />,
    accessorFn: (row) => {
      return row.veiculo?.faturamentoVeiculo?.centro_custo?.descricao || "N/A";
    },
  },
  {
    accessorKey: "veiculo",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Veículo" />,
    accessorFn: (row) =>
      `${row.veiculo?.marca?.descricao} ${row.veiculo?.modelo?.descricao}` || "Não informado",
  },
  {
    accessorKey: "odometro",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Odômetro" />
    ),
    cell: ({ row }) => {
      const valor = row.getValue("odometro");
      return <span>{String(valor)} km</span>;
    },
  },
  {
    accessorKey: "posto",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Posto" />
    ),
    accessorFn: (row) => row.credenciado?.informacoes?.[0]?.nome_fantasia || row.credenciado?.informacoes?.[0]?.razao_social || "N/A",
  },
  {
    accessorKey: "tanque_completo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Tanque Completo" />
    ),
    accessorFn: (row) => {
      return row.tanque_completo ? "Sim" : "Não";
    },
  },
  {
    accessorKey: "credenciado.nome",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Posto" />,
    accessorFn: (row) => row.credenciado?.informacoes[0].razao_social,
  },
  {
    accessorKey: "combustivel",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Combustivel" />,
    accessorFn: (row) => (row.combustivel.map((combustivel) => { return combustivel.label; })),
  },
];

export function Abastecimento() {
  const router = useRouter();
  const { abastecimentos, setAbastecimentos } = useAbastecimento();
  const { veiculos } = useVeiculos();
  const { condutores } = useCondutor();
  const { credenciados } = useCredenciado();
  async function onNewAbastecimento(
    values: z.infer<typeof abastecimentoSchema>
  ) {
    const response = await fetch("/api/abastecimento", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });

    if (!response.ok) {
      toast("Ops, algo deu errado", {
        description: "Houve um erro ao criar o abastecimento",
      });
      return;
    }

    const data = await response.json();
    setAbastecimentos((prev) => [...prev, data.abastecimento]);
  }

  return (
    <DataTable
      data={abastecimentos}
      onClick={() => router.push("/dashboard/abastecimentos/novo-abastecimento")}
      exportTo={true}
      columns={abastecimentoColumn}
    />
  );
}
