"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { GetAbastecimentoConfig } from "@/components/forms/inputConfig";
import { PageForm } from "@/components/forms/page-form";
import { abastecimentoSchema } from "@/components/forms/schemas";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { Button } from "@/components/ui/button";
import { useAbastecimento } from "@/context/abatecimento-context";
import { useCondutor } from "@/context/condutor-context";
import { useCredenciado } from "@/context/credenciado-context";
import { useVeiculos } from "@/context/veiculos-context";
import { toast } from "sonner";
import { z } from "zod";

export function AbastecimentoForm() {
  const { veiculos } = useVeiculos();
  const { condutores } = useCondutor();
  const { credenciados } = useCredenciado();
  const { setAbastecimentos } = useAbastecimento();

  const undoAbastecimento = async (id: string) => {
    const res = await fetch(`/api/abastecimentos/${id}`, {
      method: "DELETE",
    });

    if (!res.ok) {
      toast("Ops, houve um erro", {
        description: "Não foi possível cancelar o abastecimento",
      });
      return;
    }

    // Atualização do estado após undo
    setAbastecimentos((prev) => prev.filter((item) => item.id !== id));
    toast("Abastecimento cancelado!", {
      description: "O registro foi removido com sucesso",
    });
  };
  const uploadFile = async (file: File) => {
    const formData = new FormData();
    formData.append("file", file);

    const res = await fetch("/api/upload", {
      method: "POST",
      body: formData,
    });

    if (!res.ok) {
      throw new Error("Erro ao fazer upload do arquivo");
    }

    return await res.json();
  };
  async function onSubmit(values: z.infer<typeof abastecimentoSchema>) {
    if (!values.veiculoId) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Veículo",
      });
      return;
    }

    if (!values.credenciadoId) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Credenciado",
      });
      return;
    }

    if (!values.condutorId) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Condutor",
      });
      return;
    }

    if (!values.data_e_hora) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Data e Hora",
      });
      return;
    }

    if (!values.odometro) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Odometro",
      });
      return;
    }

    if (!values.odometro) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Odometro",
      });
      return;
    }

    if (!values.combustivel) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Combustível",
      });
      return;
    }

    if (!values.preco_l) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Preço L",
      });
      return;
    }

    if (!values.valor_total) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Valor Total",
      });
      return;
    }

    if (!values.litros) {
      toast("Preencha todos os campos obrigatórios!", {
        description: "Campo vazio: Litros",
      });
      return;
    }

    try {
      // Faz o upload dos arquivos, se houver
      let arquivosUrls: string[] = [];
      if (values.arquivos && values.arquivos.length > 0) {
        const uploadPromises = values.arquivos.map((file: any) =>
          uploadFile(file)
        );
        const uploadResponses = await Promise.all(uploadPromises);
        arquivosUrls = uploadResponses.map((response) => response.file.path);
      }

      // Envia os dados do abastecimento para o backend
      const res = await fetch("/api/abastecimento", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...values,
          arquivos: arquivosUrls, // Substitui os arquivos pelas URLs dos arquivos
        }),
      });

      if (!res.ok) {
        toast("Erro no abastecimento", {
          description: "Falha ao registrar o abastecimento",
        });
        return;
      }

      const data = await res.json();

      setAbastecimentos((prev) => [...prev, data.data]);

      toast("Abastecimento registrado!", {
        description: "O registro foi feito com sucesso",
        action: (
          <Button
            variant="outline"
            onClick={async () => await undoAbastecimento(data.data.id)}
          >
            Desfazer
          </Button>
        ),
      });
      window.location.href =
        "/dashboard/abastecimentos/consultar-abastecimentos";
    } catch (error) {
      toast("Erro no upload", {
        description: "Falha ao enviar os arquivos",
      });
    }
  }

  return (
    <div className="p-4">
      <PageForm
        onSubmit={onSubmit}
        schema={abastecimentoSchema}
        defaultValues={defaultValues.abastecimentoSchema}
      >
        <GenericFormsInput
          fieldConfig={GetAbastecimentoConfig(
            [veiculos, condutores, credenciados],
            ["modelo.descricao", "nome", "razao_social"]
          )}
        />
      </PageForm>
    </div>
  );
}
