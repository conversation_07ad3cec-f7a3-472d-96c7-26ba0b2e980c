"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useLembrete } from "@/context/lembrete-context";
import { useVeiculos } from "@/context/veiculos-context";
import { useTipoDeDespesa } from "@/context/tipos-de-despesa-context";
import { useTiposDeOs } from "@/context/tipos-de-os-context";

import { useRouter } from "next/navigation";
import { useState, useRef, useEffect } from "react";
import { toast } from "sonner";
import { Calendar, Check, ChevronDown, Search } from "lucide-react";

// Tipos auxiliares
type TipoLembrete = "DESPESA" | "SERVICO";
type Frequencia = "UMA_VEZ" | "REPETIR";

interface TipoItem {
  id: string;
  descricao: string;
}

export function LembreteForm() {
  const router = useRouter();
  const { lembretes, setLembretes } = useLembrete();
  const { veiculos } = useVeiculos();
  const { tiposDeDespesa } = useTipoDeDespesa();
  const { tiposDeOs } = useTiposDeOs();

  const [veiculoId, setVeiculoId] = useState("");
  const [tipo, setTipo] = useState<TipoLembrete>("DESPESA");
  const [referencialId, setReferencialId] = useState("");
  const [frequencia, setFrequencia] = useState<Frequencia>("UMA_VEZ");
  const [odometro, setOdometro] = useState("");
  const [dataInput, setDataInput] = useState(new Date().toISOString().split("T")[0]);
  const [observacoes, setObservacoes] = useState("");

  const [isVeiculoDropdownOpen, setIsVeiculoDropdownOpen] = useState(false);
  const [veiculoSearchTerm, setVeiculoSearchTerm] = useState("");
  const [veiculoSelecionado, setVeiculoSelecionado] = useState<string>("");
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Dropdown fora de foco
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsVeiculoDropdownOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Atualizar nome do veículo
  useEffect(() => {
    if (veiculoId) {
      const v = veiculos.find((v) => v.id === veiculoId);
      setVeiculoSelecionado(
        v ? `${v.matricula} | ${v.modelo?.descricao || ""}` : ""
      );
    } else {
      setVeiculoSelecionado("");
    }
  }, [veiculoId, veiculos]);

  const veiculosFiltrados = veiculos.filter((v) => {
    const s = veiculoSearchTerm.toLowerCase();
    return (
      v.matricula?.toLowerCase().includes(s) ||
      v.modelo?.descricao?.toLowerCase().includes(s) ||
      v.modelo?.marca?.descricao?.toLowerCase().includes(s)
    );
  });

  const selecionarVeiculo = (id: string) => {
    setVeiculoId(id);
    setIsVeiculoDropdownOpen(false);
    setVeiculoSearchTerm("");
  };

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!veiculoId) return toast.error("Selecione um veículo");
    if (!referencialId) return toast.error("Selecione um tipo");
    if (!odometro) return toast.error("Informe o odômetro");

    const payload = {
      veiculoId,
      lembrete_tipo: tipo,
      odometro,
      data: new Date(dataInput),
      data_desejada: new Date(dataInput),
      observacao: observacoes,
      repeticao: { frequencia },
      referencial_id_desp: tipo === "DESPESA" ? referencialId : null,
      referencial_id_os: tipo === "SERVICO" ? referencialId : null,
      quantidade: 1,
      periodo: { dias: 30 },
    };

    try {
      const res = await fetch("/api/lembrete", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });

      if (!res.ok) throw new Error("Erro ao cadastrar lembrete");

      const data = await res.json();
      setLembretes((prev) => [...prev, data.data]);
      toast.success("Lembrete cadastrado com sucesso!");
      router.push("/dashboard/lembretes");
    } catch (err) {
      console.error(err);
      toast.error("Erro ao cadastrar lembrete");
    }
  };

  const tipos: TipoItem[] = tipo === "DESPESA" ? tiposDeDespesa : tiposDeOs;

  return (
    <form onSubmit={onSubmit} className="p-8 max-w-4xl space-y-8">
      {/* Seletor de Veículo */}
      <div className="rounded-md p-6 shadow-sm" ref={dropdownRef}>
        <Label className="text-sm mb-2 block">Veículo</Label>
        <div className="relative">
          <div
            className="flex items-center justify-between w-full h-10 px-3 border rounded-md cursor-pointer"
            onClick={() => setIsVeiculoDropdownOpen(!isVeiculoDropdownOpen)}
          >
            <span>{veiculoSelecionado || "Pesquise pela placa, versão ou modelo"}</span>
            <ChevronDown className="h-4 w-4" />
          </div>
          {isVeiculoDropdownOpen && (
            <div className="absolute z-10 w-full mt-1 border rounded-md bg-white shadow-lg">
              <div className="p-2 border-b">
                <Search className="absolute left-4 top-[13px] h-4 w-4" />
                <Input
                  autoFocus
                  className="pl-8"
                  placeholder="Digite para pesquisar..."
                  value={veiculoSearchTerm}
                  onChange={(e) => setVeiculoSearchTerm(e.target.value)}
                />
              </div>
              <div className="max-h-96 overflow-y-auto">
                {veiculosFiltrados.length > 0 ? (
                  veiculosFiltrados.map((v) => (
                    <div
                      key={v.id}
                      className="px-4 py-2 cursor-pointer flex items-center justify-between"
                      onClick={() => selecionarVeiculo(v.id)}
                    >
                      <div>
                        <div className="font-medium">{v.matricula}</div>
                        <div className="text-sm">{v.modelo?.descricao} | {v.modelo?.marca?.descricao}</div>
                      </div>
                      {v.id === veiculoId && <Check className="h-4 w-4 text-green-600" />}
                    </div>
                  ))
                ) : (
                  <div className="px-4 py-2 text-sm">Nenhum veículo encontrado</div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Tipo: Despesa ou Serviço */}
      <div className="p-6">
        <div className="flex border rounded-md overflow-hidden">
          {(["DESPESA", "SERVICO"] as TipoLembrete[]).map((t) => (
            <button
              key={t}
              type="button"
              onClick={() => {
                setTipo(t);
                setReferencialId("");
              }}
              className={`flex-1 py-2 px-4 ${tipo === t ? "bg-gray-200" : ""}`}
            >
              {t === "DESPESA" ? "Despesa" : "Serviço"}
            </button>
          ))}
        </div>
      </div>

      {/* Select Dinâmico */}
      <div className="rounded-md p-6 shadow-sm">
        <Label className="text-sm font-medium mb-2 block">
          {tipo === "DESPESA" ? "Tipo de despesa" : "Tipo de serviço"}
        </Label>
        <select
          value={referencialId}
          onChange={(e) => setReferencialId(e.target.value)}
          className="w-full h-10 px-3 border rounded-md"
        >
          <option value="">Selecione uma opção</option>
          {tipos.map((item) => (
            <option key={item.id} value={item.id}>
              {item.descricao}
            </option>
          ))}
        </select>
      </div>

      {/* Frequência */}
      <div className="rounded-md p-6 shadow-sm">
        <div className="flex border rounded-md overflow-hidden">
          {(["UMA_VEZ", "REPETIR"] as Frequencia[]).map((f) => (
            <button
              key={f}
              type="button"
              onClick={() => setFrequencia(f)}
              className={`flex-1 py-2 px-4 ${frequencia === f ? "bg-gray-200" : ""}`}
            >
              {f === "UMA_VEZ" ? "Apenas uma vez" : "Repetir a cada"}
            </button>
          ))}
        </div>
      </div>

      {/* Odômetro + Data */}
      <div className="rounded-md p-6 shadow-sm">
        <p className="text-sm mb-4">Você pode informar a quilometragem, a data desejada ou ambos.</p>
        <div className="mb-4">
          <Label htmlFor="odometro">Odômetro</Label>
          <Input
            id="odometro"
            type="text"
            placeholder="123456 km"
            value={odometro}
            onChange={(e) => setOdometro(e.target.value)}
          />
        </div>
        <div>
          <Label htmlFor="data">Data</Label>
          <div className="relative">
            <Input
              id="data"
              type="date"
              value={dataInput}
              onChange={(e) => setDataInput(e.target.value)}
            />
            <Calendar className="absolute right-3 top-2.5 h-4 w-4" />
          </div>
        </div>
      </div>

      {/* Observações */}
      <div className="rounded-md p-6 shadow-sm">
        <Label htmlFor="observacoes">Observações</Label>
        <Textarea
          id="observacoes"
          value={observacoes}
          onChange={(e) => setObservacoes(e.target.value)}
          placeholder="Digite alguma observação..."
          className="min-h-24 w-full"
        />
      </div>

      {/* Botões */}
      <div className="flex justify-end gap-4 border-t pt-6">
        <Button
          type="button"
          className="bg-red-500 hover:bg-red-600 text-white"
          onClick={() => router.push("/dashboard/lembretes")}
        >
          Cancelar
        </Button>
        <Button
          type="submit"
          className="bg-emerald-500 hover:bg-emerald-600 text-white"
        >
          Cadastrar lembrete
        </Button>
      </div>
    </form>
  );
}
