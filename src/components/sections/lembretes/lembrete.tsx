"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DataTableColumnHeader } from "@/components/tables/column-header";
import { Checkbox } from "@/components/ui/checkbox";
import { useLembrete } from "@/context/lembrete-context";
import { ColumnDef } from "@tanstack/react-table";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { z } from "zod";
import { lembreteSchema } from "@/components/forms/schemas";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { ChevronDown, FileText, Search, Loader2, Plus, Settings2 } from "lucide-react";
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import { format } from "date-fns";

// Interface para tipagem dos lembretes formatados para exibição
interface LembreteDisplay {
  id: string;
  centro_de_custo: string;
  veiculo: string;
  odometro: string;
  tipo_de_despesa: string;
  tipo_de_servico: string;
}

export const lembreteColumns: ColumnDef<lembrete>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "id",
    header: ({ column }) => <DataTableColumnHeader column={column} title="ID" />,
  },
  // Centro de Custo (via veículo)
  {
    accessorKey: "centro_de_custo",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Centro de Custo" />,
    accessorFn: (row) => row.veiculo?.lotacao_veiculos?.centro_custo?.descricao || "Não vinculado",
  },
  // Veículo
  {
    accessorKey: "veiculo",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Veículo" />,
    accessorFn: (row) => row.veiculo?.matricula || "Não informado",
  },
  // Odômetro
  {
    accessorKey: "odometro",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Odômetro" />,
  },
  // Tipo de Despesa
  {
    accessorKey: "tipo_de_despesa",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Tipo de Despesa" />,
    accessorFn: (row) => (row.tipo === "DESPESA" ? "Despesa" : "Serviço"),
  },
  // Tipo de Serviço (se aplicável)
  {
    accessorKey: "tipo_de_servico",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Tipo de Serviço" />,
    accessorFn: (row) => {
      if (row.tipo === "SERVICO") {
        return row.referencialId ? "Serviço Vinculado" : "Serviço Geral";
      }
      return "-";
    },
  },
];

export function Lembrete() {
  const router = useRouter();
  const { lembretes, setLembretes, loading, error } = useLembrete();
  
  const [searchQuery, setSearchQuery] = useState("");
  const [formattedLembretes, setFormattedLembretes] = useState<LembreteDisplay[]>([]);
  const [filteredLembretes, setFilteredLembretes] = useState<LembreteDisplay[]>([]);
  const [isExporting, setIsExporting] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [columnVisibility, setColumnVisibility] = useState<Record<string, boolean>>({
    'id': true,
    'centro_de_custo': true,
    'veiculo': true,
    'odometro': true,
    'tipo_de_despesa': true,
    'tipo_de_servico': true
  });
  
  // Formatar dados para exibição
  useEffect(() => {
    if (lembretes && lembretes.length > 0) {
      const formatted = lembretes.map(lembrete => {
        return {
          id: lembrete.id,
          centro_de_custo: lembrete.veiculo?.lotacao_veiculos?.centro_custo?.descricao || "Não vinculado",
          veiculo: lembrete.veiculo?.matricula || "Não informado",
          odometro: lembrete.odometro?.toString() || "-",
          tipo_de_despesa: lembrete.tipo === "DESPESA" ? "Despesa" : "Serviço",
          tipo_de_servico: lembrete.tipo === "SERVICO" 
            ? (lembrete.referencialId ? "Serviço Vinculado" : "Serviço Geral") 
            : "-"
        };
      });
      
      setFormattedLembretes(formatted);
      setFilteredLembretes(formatted);
    }
  }, [lembretes]);
  
  // Filtrar conforme busca
  useEffect(() => {
    if (formattedLembretes.length > 0) {
      if (!searchQuery) {
        setFilteredLembretes(formattedLembretes);
      } else {
        const lowercasedQuery = searchQuery.toLowerCase();
        const filtered = formattedLembretes.filter(lembrete => {
          return (
            lembrete.id.toLowerCase().includes(lowercasedQuery) ||
            lembrete.centro_de_custo.toLowerCase().includes(lowercasedQuery) ||
            lembrete.veiculo.toLowerCase().includes(lowercasedQuery) ||
            lembrete.odometro.toLowerCase().includes(lowercasedQuery) ||
            lembrete.tipo_de_despesa.toLowerCase().includes(lowercasedQuery) ||
            lembrete.tipo_de_servico.toLowerCase().includes(lowercasedQuery)
          );
        });
        setFilteredLembretes(filtered);
      }
      // Voltar para a primeira página após filtrar
      setCurrentPage(1);
    }
  }, [searchQuery, formattedLembretes]);
  
  // Calcular paginação
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredLembretes.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredLembretes.length / itemsPerPage);
  
  // Limpar os filtros de busca
  const handleLimparFiltros = () => {
    setSearchQuery("");
    toast.success("Filtros limpos com sucesso");
  };
  
  // Criar novo lembrete
  const handleNovoLembrete = () => {
    router.push("/dashboard/lembretes/novo-lembrete");
  };
  
  // Navegação de página
  const goToPage = (page: number) => {
    if (page > 0 && page <= totalPages) {
      setCurrentPage(page);
    }
  };
  
  // Exportar para Excel
  const exportToExcel = async () => {
    try {
      setIsExporting(true);
      
      // Preparar dados para exportação
      const dataToExport = filteredLembretes.map(({ id, ...rest }) => rest);
      
      // Criar worksheet
      const ws = XLSX.utils.json_to_sheet(dataToExport);
      
      // Ajustar largura das colunas
      const wscols = [
        { wch: 5 },  // ID
        { wch: 20 }, // Centro de custo
        { wch: 15 }, // Veículo
        { wch: 12 }, // Odômetro
        { wch: 15 }, // Tipo de despesa
        { wch: 20 }, // Tipo de serviço
      ];
      ws['!cols'] = wscols;
      
      // Criar workbook e adicionar worksheet
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Lembretes");
      
      // Gerar arquivo Excel
      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      
      // Baixar arquivo
      saveAs(data, `lembretes_${format(new Date(), 'dd-MM-yyyy')}.xlsx`);
      
      toast.success("Exportação para Excel concluída!");
    } catch (error) {
      toast.error("Erro ao exportar para Excel", {
        description: "Ocorreu um erro durante a exportação. Tente novamente."
      });
      console.error("Erro ao exportar para Excel:", error);
    } finally {
      setIsExporting(false);
    }
  };
  
  // Exportar para PDF
  const exportToPDF = async () => {
    try {
      setIsExporting(true);
      
      // Criar documento PDF
      const doc = new jsPDF('landscape');
      
      // Adicionar título
      doc.setFontSize(18);
      doc.text("Relatório de Lembretes", 14, 22);
      
      // Adicionar data
      doc.setFontSize(11);
      doc.text(`Gerado em: ${format(new Date(), 'dd/MM/yyyy HH:mm')}`, 14, 30);
      
      // Preparar dados para a tabela
      const tableColumn = ["ID", "Centro de Custo", "Veículo", "Odômetro", "Tipo de Despesa", "Tipo de Serviço"];
      const tableRows = filteredLembretes.map(lembrete => [
        lembrete.id,
        lembrete.centro_de_custo,
        lembrete.veiculo,
        lembrete.odometro,
        lembrete.tipo_de_despesa,
        lembrete.tipo_de_servico
      ]);
      
      // Adicionar tabela ao PDF
      (doc as any).autoTable({
        head: [tableColumn],
        body: tableRows,
        startY: 40,
        styles: {
          fontSize: 8,
          cellPadding: 3,
          lineColor: [44, 62, 80],
          lineWidth: 0.1,
        },
        headStyles: {
          fillColor: [41, 128, 185],
          textColor: 255,
          fontStyle: 'bold',
        },
        alternateRowStyles: {
          fillColor: [245, 245, 245]
        },
      });
      
      // Salvar o PDF
      doc.save(`lembretes_${format(new Date(), 'dd-MM-yyyy')}.pdf`);
      
      toast.success("Exportação para PDF concluída!");
    } catch (error) {
      toast.error("Erro ao exportar para PDF", {
        description: "Ocorreu um erro durante a exportação. Tente novamente."
      });
      console.error("Erro ao exportar para PDF:", error);
    } finally {
      setIsExporting(false);
    }
  };
  
  // Exportar para CSV
  const exportToCSV = async () => {
    try {
      setIsExporting(true);
      
      // Preparar dados para exportação
      const dataToExport = filteredLembretes.map(({ id, ...rest }) => rest);
      
      // Criar CSV
      const ws = XLSX.utils.json_to_sheet(dataToExport);
      const csv = XLSX.utils.sheet_to_csv(ws);
      
      // Criar blob e baixar
      const csvBlob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      saveAs(csvBlob, `lembretes_${format(new Date(), 'dd-MM-yyyy')}.csv`);
      
      toast.success("Exportação para CSV concluída!");
    } catch (error) {
      toast.error("Erro ao exportar para CSV", {
        description: "Ocorreu um erro durante a exportação. Tente novamente."
      });
      console.error("Erro ao exportar para CSV:", error);
    } finally {
      setIsExporting(false);
    }
  };
  
  // Renderização condicional para loading
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64 w-full">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2 text-lg">Carregando lembretes...</span>
      </div>
    );
  }
  
  // Renderização condicional para erro
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64 w-full">
        <p className="text-red-500 text-lg">Erro ao carregar lembretes</p>
        <p className="text-sm text-gray-500 mt-2">{error}</p>
        <Button 
          className="mt-4" 
          onClick={() => window.location.reload()}
        >
          Tentar novamente
        </Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col p-6 rounded-md w-full">
      <div className="flex justify-between items-center mb-6">
        <div className="flex gap-2 items-center">
          <div className="relative w-80">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input 
              placeholder="Busca rápida" 
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="ml-2 h-9 flex items-center"
              >
                <Settings2 className="h-4 w-4 mr-2" />
                Colunas
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px]">
              <DropdownMenuLabel>Colunas visíveis</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {lembreteColumns.slice(1).map((column, index) => {
                const columnId = (column as any).accessorKey || column.id || `column-${index}`;
                const columnTitle = (column.header as any)?.title || columnId;
                
                return (
                  <DropdownMenuCheckboxItem
                    key={index}
                    className="capitalize"
                    checked={columnVisibility[columnId] !== false}
                    onCheckedChange={(value) => {
                      setColumnVisibility(prev => ({
                        ...prev,
                        [columnId]: value
                      }));
                    }}
                  >
                    {columnTitle}
                  </DropdownMenuCheckboxItem>
                );
              })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        
        <div className="flex gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                className="text-xs h-9 rounded-sm"
                disabled={isExporting}
              >
                {isExporting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Exportando...
                  </>
                ) : (
                  <>
                    <FileText className="mr-2 h-4 w-4" />
                    Exportar
                    <ChevronDown className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={exportToExcel} disabled={isExporting}>
                <FileText className="mr-2 h-4 w-4" />
                Excel
              </DropdownMenuItem>
              <DropdownMenuItem onClick={exportToPDF} disabled={isExporting}>
                <FileText className="mr-2 h-4 w-4" />
                PDF
              </DropdownMenuItem>
              <DropdownMenuItem onClick={exportToCSV} disabled={isExporting}>
                <FileText className="mr-2 h-4 w-4" />
                CSV
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          <Button 
            className="text-xs h-9 rounded-sm"
          >
            <Search className="mr-2 h-4 w-4" />
            Pesquisar
          </Button>
          
          <Button 
            className="text-xs h-9 rounded-sm"
            onClick={handleNovoLembrete}
          >
            <Plus className="mr-2 h-4 w-4" />
            Novo Lembrete
          </Button>
        </div>
      </div>
      
      <div className="border rounded-md overflow-hidden">
        <table className="w-full">
          <thead>
            <tr className="border-b">
              <th className="px-4 py-3 text-left text-sm font-medium">
                ID
              </th>
              {columnVisibility['centro_de_custo'] !== false && (
                <th className="px-4 py-3 text-left text-sm font-medium">
                  Centro de custo
                </th>
              )}
              {columnVisibility['veiculo'] !== false && (
                <th className="px-4 py-3 text-left text-sm font-medium">
                  Veículo
                </th>
              )}
              {columnVisibility['odometro'] !== false && (
                <th className="px-4 py-3 text-left text-sm font-medium">
                  Odômetro
                </th>
              )}
              {columnVisibility['tipo_de_despesa'] !== false && (
                <th className="px-4 py-3 text-left text-sm font-medium">
                  Tipo de despesa
                </th>
              )}
              {columnVisibility['tipo_de_servico'] !== false && (
                <th className="px-4 py-3 text-left text-sm font-medium">
                  Tipo de serviço
                </th>
              )}
            </tr>
          </thead>
          <tbody>
            {currentItems.length > 0 ? (
              currentItems.map((lembrete, index) => (
                <tr key={lembrete.id || index} className="border-b hover cursor-pointer" onClick={() => router.push(`/dashboard/lembretes/detalhes/${lembrete.id}`)}>
                  <td className="px-4 py-3 text-sm">{lembrete.id}</td>
                  {columnVisibility['centro_de_custo'] !== false && (
                    <td className="px-4 py-3 text-sm">{lembrete.centro_de_custo}</td>
                  )}
                  {columnVisibility['veiculo'] !== false && (
                    <td className="px-4 py-3 text-sm font-medium">{lembrete.veiculo}</td>
                  )}
                  {columnVisibility['odometro'] !== false && (
                    <td className="px-4 py-3 text-sm">{lembrete.odometro}</td>
                  )}
                  {columnVisibility['tipo_de_despesa'] !== false && (
                    <td className="px-4 py-3 text-sm">{lembrete.tipo_de_despesa}</td>
                  )}
                  {columnVisibility['tipo_de_servico'] !== false && (
                    <td className="px-4 py-3 text-sm">{lembrete.tipo_de_servico}</td>
                  )}
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={Object.values(columnVisibility).filter(Boolean).length + 1} className="px-4 py-6 text-sm text-center">
                  Nenhum lembrete encontrado.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      
      <div className="flex justify-between items-center mt-4">
        <div className="text-sm">
          {filteredLembretes.length > 0 ? (
            `Exibindo de ${indexOfFirstItem + 1} a ${Math.min(indexOfLastItem, filteredLembretes.length)} de ${filteredLembretes.length} registros`
          ) : (
            "Exibindo de 0 a 0 de 0 registros"
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => goToPage(1)} 
            disabled={currentPage === 1}
          >
            «
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => goToPage(currentPage - 1)} 
            disabled={currentPage === 1}
          >
            ‹
          </Button>
          
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            // Lógica para mostrar as páginas centralizadas na página atual
            let pageToShow;
            if (totalPages <= 5) {
              pageToShow = i + 1;
            } else {
              const startPage = Math.max(1, currentPage - 2);
              const endPage = Math.min(totalPages, currentPage + 2);
              if (endPage - startPage < 4) {
                pageToShow = i + Math.max(1, totalPages - 4);
              } else {
                pageToShow = startPage + i;
              }
              if (pageToShow > totalPages) return null;
            }
            
            return (
              <Button 
                key={pageToShow}
                variant={currentPage === pageToShow ? "default" : "outline"} 
                size="sm" 
                className={currentPage === pageToShow ? "bg-blue-600" : ""}
                onClick={() => goToPage(pageToShow)}
              >
                {pageToShow}
              </Button>
            );
          })}
          
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => goToPage(currentPage + 1)} 
            disabled={currentPage === totalPages || totalPages === 0}
          >
            ›
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => goToPage(totalPages)} 
            disabled={currentPage === totalPages || totalPages === 0}
          >
            »
          </Button>
          
          <select
            className="ml-4 border rounded p-1 text-sm"
            value={itemsPerPage}
            onChange={(e) => {
              setItemsPerPage(Number(e.target.value));
              setCurrentPage(1);
            }}
          >
            <option value={10}>10</option>
            <option value={25}>25</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
          </select>
        </div>
      </div>
    </div>
  );
}
