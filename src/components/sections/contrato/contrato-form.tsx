"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import {
  GetContratoConfig,
  getFinanceiroContratoConfig,
} from "@/components/forms/inputConfig";
import { PageForm } from "@/components/forms/page-form";
import {
  contratoSchema,
  fincanceiroContratoSchema,
} from "@/components/forms/schemas";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { useSession } from "@/components/session/use-session";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { useContrato } from "@/context/contrato-context";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { z } from "zod";

export function ContratoForm() {
  const { setContratos } = useContrato();
  const { centrosDeCusto } = useCentroDeCusto();

  const { session } = useSession();

  console.log("session", session);

  const router = useRouter();

  const [currentStep, setCurrentStep] = useState(0);

  const formSteps =
    session?.roles.includes("FINANCEIRO") || session?.roles.includes("ADMIN")
      ? ["Operacional", "Validações Financeiras"]
      : ["Operacional"];

  const normalizedFormStepsDict = [
    "operacional",
    "financial_validations",
    "endereco",
    "estrutura",
    "servicos",
    "configuracoes",
  ];

  const centrosDeCustoFilhos = centrosDeCusto.flatMap(
    (centro) => centro.centro_custos_filhos
  );

  const undoContrato = async (id: string) => {
    const res = await fetch(`/api/contratos/${id}`, {
      method: "DELETE",
    });

    if (!res.ok) {
      toast("Erro ao desfazer", {
        description: "Falha ao cancelar o contrato",
      });
      return;
    }

    setContratos((prev) => prev.filter((contrato) => contrato.id !== id));

    toast("Contrato cancelado", {
      description: "O contrato foi removido com sucesso",
    });
  };

  const uploadFile = async (file: File) => {
    const formData = new FormData();
    formData.append("file", file);

    const res = await fetch("/api/upload", {
      method: "POST",
      body: formData,
    });

    if (!res.ok) {
      throw new Error("Erro ao fazer upload do arquivo");
    }

    return await res.json();
  };

  async function onSubmit(values: z.infer<any>) {
    try {
      let logotipoUrl = null;

      if (currentStep === 0 && formSteps.length > 1) {
        setCurrentStep((prev) => prev + 1);
        return;
      }

      if (values.logotipo) {
        const uploadResponse = await uploadFile(values.logotipo);
        logotipoUrl = uploadResponse.file.path;
      }

      const res = await fetch("/api/contratos", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...values,
          logotipo: logotipoUrl,
        }),
      });

      if (!res.ok) {
        toast("Erro na criação", {
          description: "Falha ao registrar o contrato",
        });
        return;
      }

      const data = await res.json();

      setContratos((prev) => [...prev, data.data]);

      toast("Contrato registrado!", {
        description: "Contrato criado com sucesso",
        action: (
          <Button
            variant="outline"
            onClick={async () => await undoContrato(data.data.id)}
          >
            Desfazer
          </Button>
        ),
      });
      window.location.href = "/dashboard/contratos/consultar-contrato";
    } catch (error) {
      toast("Erro no upload", {
        description: "Falha ao enviar o logotipo",
      });
    }
  }

  return (
    <div className="p-4">
      <PageForm
        onSubmit={onSubmit}
        schema={{ ...contratoSchema, ...fincanceiroContratoSchema } as any}
        defaultValues={defaultValues.contratoSchema as any}
        submitButtonText={
          currentStep === 0 && formSteps.length > 1 ? "Próximo" : "Criar"
        }
      >
        <Tabs
          defaultValue={formSteps[0]}
          value={formSteps[currentStep]}
          onValueChange={(value) => {
            const index = formSteps.indexOf(value);
            if (index >= 0) setCurrentStep(index);
          }}
          className="w-full"
        >
          <TabsList className="flex flex-wrap gap-2 p-2 h-34 items-center justify-start">
            {formSteps.map((step, index) => {
              return (
                <TabsTrigger
                  key={step}
                  className="flex-1 sm:flex-none px-4 py-2 text-sm  relative"
                  value={step}
                >
                  {step}
                </TabsTrigger>
              );
            })}
          </TabsList>
          <TabsContent value={formSteps[0]} className="space-y-4 pt-4">
            <GenericFormsInput fieldConfig={GetContratoConfig()} />
          </TabsContent>
          <TabsContent value={formSteps[1]} className="space-y-4 pt-4">
            <GenericFormsInput
              fieldConfig={getFinanceiroContratoConfig([
                ...centrosDeCusto,
                ...centrosDeCustoFilhos,
              ])}
            />
          </TabsContent>
        </Tabs>
      </PageForm>
    </div>
  );
}
