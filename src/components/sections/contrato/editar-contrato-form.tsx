"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import {
  GetContratoConfig,
  getFinanceiroContratoConfig,
} from "@/components/forms/inputConfig";
import { PageForm } from "@/components/forms/page-form";
import {
  contratoSchema,
  fincanceiroContratoSchema,
} from "@/components/forms/schemas";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { Button } from "@/components/ui/button";
import { useContrato } from "@/context/contrato-context";
import {
  updateContratoAction,
  DescontoContrato,
} from "@/serverActions/contratoAction";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { z } from "zod";
import { DescontosContrato } from "./descontos-contratos";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { useSession } from "@/components/session/use-session";

interface EditContratoFormProps {
  contratoId: string;
}

const normalizedFormStepsDict = ["operacional", "financial_validations"];

export function EditContratoForm({ contratoId }: EditContratoFormProps) {
  const { setContratos } = useContrato();
  const { centrosDeCusto } = useCentroDeCusto();
  const { session } = useSession();

  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [contratoData, setContratoData] = useState<any>(null);
  const [descontos, setDescontos] = useState<DescontoContrato[]>([]);
  const [currentStep, setCurrentStep] = useState(0);

  const centrosDeCustoFilhos = centrosDeCusto.flatMap(
    (centro) => centro.centro_custos_filhos
  );

  const formSteps =
    session?.roles.includes("FINANCEIRO") || session?.roles.includes("ADMIN")
      ? ["Operacional", "Validações Financeiras"]
      : ["Operacional"];

  useEffect(() => {
    const fetchContrato = async () => {
      try {
        setIsLoading(true);
        const res = await fetch(`/api/contratos/${contratoId}`);
        if (!res.ok) {
          throw new Error("Erro ao buscar dados do contrato");
        }

        const data = await res.json();
        console.log(data);
        setContratoData(data.data);
        if (data.data.descontos) {
          setDescontos(data.data.descontos);
        }
      } catch (error) {
        toast("Erro ao carregar", {
          description: "Não foi possível carregar os dados do contrato",
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (contratoId) {
      fetchContrato();
    }
  }, [contratoId]);

  const uploadFile = async (file: File) => {
    const formData = new FormData();
    formData.append("file", file);

    const res = await fetch("/api/upload", {
      method: "POST",
      body: formData,
    });

    if (!res.ok) {
      throw new Error("Erro ao fazer upload do arquivo");
    }

    return await res.json();
  };

  async function onSubmit(values: z.infer<any>) {
    try {
      let logotipoUrl = values.logotipo as any;

      if (currentStep === 0 && formSteps.length > 1) {
        setCurrentStep((prev) => prev + 1);
        return;
      }

      // Se o logotipo for um arquivo (File), faz o upload
      if (values.logotipo instanceof File) {
        const uploadResponse = await uploadFile(values.logotipo);
        logotipoUrl = uploadResponse.file.path;
      }

      const contratoData = {
        nome_contrato: values.nome,
        numero: values.numero,
        data_inicial: values.data_inicial,
        data_final: values.data_final,
        valor_contrato: values.valor_do_contrato,
        taxa_admin: values.taxa_administrativa,
        cnpj: values.cnpj,
        razao_social: values.razao_social,

        // Contato fields are nested in the form but flat in the API
        responsavel: values.contato?.responsavel || "",
        telefone: values.contato?.telefone || "",
        email: values.contato?.email || "",

        // Endereco fields are nested in the form but flat in the API
        cep: values.endereco?.cep || "",
        logradouro: values.endereco?.logradouro || "",
        bairro: values.endereco?.bairro || "",
        estado: values.endereco?.estado || "",
        cidade: values.endereco?.cidade || "",

        cnpj_financial: values.cnpj_financial,
        cep_financial: values.endereco_financial?.cep || "",
        logradouro_financial: values.endereco_financial?.logradouro || "",
        numero_contrato_financial: values.numero_contrato_financial || "",
        bairro_financial: values.endereco_financial?.bairro || "",
        estado_financial: values.endereco_financial?.estado || "",
        cidade_financial: values.endereco_financial?.cidade || "",
        send_nf_to: values.send_nf_to || "",

        // Other fields
        logotipo: logotipoUrl ?? undefined,
        limite_gastos_percent: values.limite_gastos_percent || 0,

        // Boolean fields
        placa_veiculo: values.validacao_nf.placa_do_veiculo,
        modelo_veiculo: values.validacao_nf.modelo_do_veiculo,
        numero_os: values.validacao_nf.numero_da_os,
        numero_contrato: values.validacao_nf.numero_do_contrato,
        contrato_ativo: values.configuracao.ativo,
        abertura_via_os: values.configuracao.abertura_os_credenciado,
        envio_emails: values.configuracao.envio_de_emails,
        veiculo_rfid: values.configuracao.veiculos_com_rfid,
        parametrizacao_obg: values.configuracao.parametrizacao_obrigatoria,
        checklist_simplificado_pecas:
          values.configuracao.checklist_simplificado_pecas,
        restringir_veiculos: values.configuracao.restringir_veiculos,
        // Discount data
        descontos: descontos,
      };

      // Chama o server action para atualizar o contrato
      const result = await updateContratoAction(contratoId, contratoData);

      if (!result.success) {
        toast("Erro na atualização", {
          description: result.error || "Falha ao atualizar o contrato",
        });
        return;
      }

      // Rest of your existing code
      setContratos((prev) =>
        prev.map((contrato) =>
          contrato.id === contratoId ? result.data : contrato
        )
      );

      toast("Contrato atualizado!", {
        description: "As alterações foram salvas com sucesso",
      });

      window.location.href = "/dashboard/contratos/consultar-contrato";
    } catch (error) {
      toast("Erro na atualização", {
        description: "Falha ao atualizar o contrato",
      });
    }
  }

  const handleDescontosChange = async (
    updatedDescontos: DescontoContrato[]
  ) => {
    setDescontos(updatedDescontos);
    setContratoData((prev: any) => ({
      ...prev,
      descontos: updatedDescontos,
    }));

    try {
      const result = await updateContratoAction(contratoId, {
        ...contratoData,
        descontos: updatedDescontos,
      });

      if (result.success) {
        toast.success("Contrato atualizado com sucesso");
      } else {
        toast.error(result.error || "Erro ao atualizar contrato");
      }
    } catch (error) {
      toast.error("Erro ao atualizar contrato");
    }
  };

  if (isLoading) {
    return <div className="p-4">Carregando dados do contrato...</div>;
  }

  if (!contratoData) {
    return <div className="p-4">Contrato não encontrado</div>;
  }

  return (
    <div className="p-4 space-y-6">
      <PageForm
        onSubmit={onSubmit}
        schema={{ ...contratoSchema, ...fincanceiroContratoSchema } as any}
        defaultValues={contratoData}
        submitButtonText={
          currentStep === 0 && formSteps.length > 1 ? "Próximo" : "Criar"
        }
      >
        <Tabs
          defaultValue={formSteps[0]}
          value={formSteps[currentStep]}
          onValueChange={(value) => {
            const index = formSteps.indexOf(value);
            if (index >= 0) setCurrentStep(index);
          }}
          className="w-full"
        >
          <TabsList className="flex flex-wrap gap-2 p-2 h-34 items-center justify-start">
            {formSteps.map((step, index) => {
              return (
                <TabsTrigger
                  key={step}
                  className="flex-1 sm:flex-none px-4 py-2 text-sm  relative"
                  value={step}
                >
                  {step}
                </TabsTrigger>
              );
            })}
          </TabsList>
          <TabsContent value={formSteps[0]} className="space-y-4 pt-4">
            <GenericFormsInput fieldConfig={GetContratoConfig()} />
          </TabsContent>
          <TabsContent value={formSteps[1]} className="space-y-4 pt-4">
            <GenericFormsInput
              fieldConfig={getFinanceiroContratoConfig([
                ...centrosDeCusto,
                ...centrosDeCustoFilhos,
              ])}
            />
          </TabsContent>
        </Tabs>
        <DescontosContrato
          contratoId={contratoId}
          onDescontosChange={handleDescontosChange}
          initialDescontos={contratoData.descontos || []}
        />
      </PageForm>
    </div>
  );
}
