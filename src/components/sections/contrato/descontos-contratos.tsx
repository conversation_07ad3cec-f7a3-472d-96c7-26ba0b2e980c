"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Pagin<PERSON>,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { DescontoContrato, getDescontosContratoAction } from "@/serverActions/contratoAction";
import { PencilIcon, Trash2Icon, PlusIcon, SearchIcon } from "lucide-react";
import { useTipoDeVeiculo } from "@/context/tipo-de-veiculo-context";
import { CurrencyInput } from "@/components/inputs/currency-input";

const descontoSchema = z.object({
  descricao: z.string().min(1, "Descrição é obrigatória"),
  tipoItem: z.string().min(1, "Tipo de item é obrigatório"),
  tipoVeiculo: z.string().min(1, "Tipo de veículo é obrigatório"),
  desconto: z.number(),
  precoMaximo: z.number().optional(),
});

interface DescontosContratoProps {
  contratoId: string;
  onDescontosChange: (descontos: DescontoContrato[]) => void;
  initialDescontos?: DescontoContrato[];
}

export function DescontosContrato({
  contratoId,
  onDescontosChange,
  initialDescontos = [],
}: DescontosContratoProps) {
  const { tiposDeVeiculo } = useTipoDeVeiculo();
  const [descontos, setDescontos] = useState<DescontoContrato[]>(initialDescontos);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [precoMaximo, setPrecoMaximo] = useState<number | null>(null);

  const form = useForm<z.infer<typeof descontoSchema>>({
    resolver: zodResolver(descontoSchema),
    defaultValues: {
      descricao: "",
      tipoItem: "",
      tipoVeiculo: "",
      desconto: 0,
      precoMaximo: 0,
    },
  });

  // Opções para os selects
  const tiposItem = ["Peças", "Serviços"];
  // Using tiposDeVeiculo from context as fallback if not available
  const tiposVeiculoArray =
    tiposDeVeiculo?.length > 0
      ? tiposDeVeiculo.map((tipo) => tipo.descricao)
      : ["Veículo Leves", "Veículo Médios", "Veículo Pesados", "Motocicletas", "Outro"];

  useEffect(() => {
    if (contratoId && initialDescontos.length === 0) {
      fetchDescontos();
    }
  }, [contratoId]);

  const fetchDescontos = async () => {
    if (!contratoId) return;

    setIsLoading(true);
    try {
      const result = await getDescontosContratoAction(contratoId);
      console.log(result);
      if (result.success && result.data.descontos) {
        setDescontos(result.data.descontos);
        onDescontosChange(result.data.descontos);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddDesconto = (data: z.infer<typeof descontoSchema>) => {
    const precoMaximoValue = precoMaximo !== null ? Number(precoMaximo / 100) : data.precoMaximo;

    const newData: DescontoContrato = {
      descricao: data.descricao,
      tipoItem: data.tipoItem,
      tipoVeiculo: data.tipoVeiculo,
      desconto: data.desconto,
      precoMaximo: precoMaximoValue,
    };

    const newDescontos = [...descontos];

    if (editingIndex !== null) {
      newDescontos[editingIndex] = newData;
    } else {
      newDescontos.push(newData);
    }

    setDescontos(newDescontos);
    onDescontosChange(newDescontos); // Isso já notifica o componente pai

    // Fechar o diálogo e resetar o form
    form.reset();
    setPrecoMaximo(null);
    setOpenDialog(false);
    setEditingIndex(null);

    toast.success(editingIndex !== null ? "Desconto atualizado" : "Desconto adicionado");

    return newDescontos; // Retorna os novos descontos
  };

  const handleEditDesconto = (index: number) => {
    console.log("Editing desconto at index:", index);
    const desconto = descontos[index];
    form.reset(desconto);

    // Se houver preço máximo, converter para centavos para o CurrencyInput
    if (desconto.precoMaximo) {
      const precoNumerico = desconto.precoMaximo * 100;
      setPrecoMaximo(precoNumerico);
    } else {
      setPrecoMaximo(null);
    }

    setEditingIndex(index);
    setOpenDialog(true);
  };

  const handleDeleteDesconto = (index: number) => {
    const newDescontos = descontos.filter((_, i) => i !== index);
    setDescontos(newDescontos);
    onDescontosChange(newDescontos);

    toast("Desconto removido", {
      description: "O desconto foi removido com sucesso",
    });
  };

  // Filtrar descontos com base no termo de busca
  const filteredDescontos = descontos.filter(
    (desconto) =>
      desconto.descricao.toLowerCase().includes(searchTerm.toLowerCase()) ||
      desconto.tipoItem.toLowerCase().includes(searchTerm.toLowerCase()) ||
      desconto.tipoVeiculo.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Paginação
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentDescontos = filteredDescontos.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredDescontos.length / itemsPerPage);

  return (
    <div className="border rounded-md p-4 space-y-4">
      <div>
        <h2 className="text-lg font-semibold">Descontos sobre peças e serviços</h2>
        <p className="text-sm text-gray-500">
          Configure descontos e defina preços máximos personalizados para peças e serviços
          específicos.
        </p>
      </div>

      <div className="flex justify-between items-center">
        <Dialog open={openDialog} onOpenChange={setOpenDialog}>
          <DialogTrigger asChild>
            <Button variant="default" className="bg-emerald-500 hover:bg-emerald-600">
              <PlusIcon className="h-4 w-4 mr-2" />
              Adicionar
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>
                {editingIndex !== null ? "Editar desconto" : "Adicionar novo desconto"}
              </DialogTitle>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleAddDesconto)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="descricao"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Descrição</FormLabel>
                      <FormControl>
                        <Input placeholder="Ex: Peças de freio" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="tipoItem"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tipo de item</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o tipo de item" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {tiposItem.map((tipo) => (
                            <SelectItem key={tipo} value={tipo}>
                              {tipo}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="tipoVeiculo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tipo de veículo</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o tipo de veículo" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {tiposVeiculoArray.map((tipo) => (
                            <SelectItem key={tipo} value={tipo}>
                              {tipo}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="desconto"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Desconto (%)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          max="100"
                          placeholder="Ex: 10"
                          value={field.value}
                          onChange={(e) => {
                            const value = e.target.valueAsNumber;
                            field.onChange(isNaN(value) ? 0 : value);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="precoMaximo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Preço máximo (opcional)</FormLabel>
                      <FormControl>
                        <CurrencyInput
                          value={precoMaximo}
                          onChange={(value) => {
                            setPrecoMaximo(value);
                            if (value !== null) {
                              field.onChange(value / 100);
                            } else {
                              field.onChange("");
                            }
                          }}
                          placeholder="R$ 0,00"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button type="submit" className="w-full">
                  {editingIndex !== null ? "Atualizar" : "Adicionar"}
                </Button>
              </form>
            </Form>
          </DialogContent>
        </Dialog>

        <div className="flex items-center w-72">
          <Input
            placeholder="Busca rápida"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full"
          />
          <Button variant="ghost" className="ml-1">
            <SearchIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="text-center py-4">Carregando descontos...</div>
      ) : (
        <>
          <div className="rounded-md border">
            <table className="w-full text-sm">
              <thead className="bg-muted/50">
                <tr className="[&_th]:p-2 [&_th]:text-left [&_th]:font-medium">
                  <th>Descrição</th>
                  <th>Tipo de item</th>
                  <th>Tipo de veículo</th>
                  <th>Desconto</th>
                  <th>Preço máximo (Unit)</th>
                  <th className="w-[100px]"></th>
                </tr>
              </thead>
              <tbody>
                {currentDescontos.length > 0 ? (
                  currentDescontos.map((desconto, index) => {
                    const actualIndex = indexOfFirstItem + index;
                    return (
                      <tr key={index} className="border-t [&_td]:p-2">
                        <td>{desconto.descricao}</td>
                        <td>{desconto.tipoItem}</td>
                        <td>{desconto.tipoVeiculo}</td>
                        <td>{desconto.desconto}%</td>
                        <td>{desconto.precoMaximo ? `R$ ${desconto.precoMaximo}` : "-"}</td>
                        <td className="text-right">
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.preventDefault();
                              handleEditDesconto(actualIndex);
                            }}>
                            <PencilIcon className="h-4 w-4 text-emerald-600" />
                          </Button>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.preventDefault();
                              handleDeleteDesconto(actualIndex);
                            }}>
                            <Trash2Icon className="h-4 w-4 text-red-600" />
                          </Button>
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr className="border-t">
                    <td colSpan={6} className="p-2 text-center text-muted-foreground">
                      {descontos.length === 0
                        ? "Nenhum desconto configurado"
                        : "Nenhum resultado encontrado"}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {filteredDescontos.length > 0 && (
            <div className="flex justify-between items-center">
              <div className="text-sm text-muted-foreground">
                {filteredDescontos.length > 0 && (
                  <>
                    1 a {Math.min(indexOfLastItem, filteredDescontos.length)} de{" "}
                    {filteredDescontos.length}
                  </>
                )}
              </div>
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    {currentPage > 1 ? (
                      <PaginationPrevious
                        onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                      />
                    ) : (
                      <PaginationPrevious
                        onClick={() => {}}
                        className="opacity-50 pointer-events-none"
                      />
                    )}
                  </PaginationItem>

                  {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                    const pageNumber = i + 1;
                    return (
                      <PaginationItem key={i}>
                        <PaginationLink
                          onClick={() => setCurrentPage(pageNumber)}
                          isActive={currentPage === pageNumber}>
                          {pageNumber}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  })}

                  {totalPages > 5 && (
                    <PaginationItem>
                      <PaginationEllipsis />
                    </PaginationItem>
                  )}

                  <PaginationItem>
                    {currentPage === totalPages || totalPages === 0 ? (
                      <PaginationNext
                        onClick={() => {}}
                        className="opacity-50 pointer-events-none"
                      />
                    ) : (
                      <PaginationNext
                        onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                      />
                    )}
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </>
      )}
    </div>
  );
}
