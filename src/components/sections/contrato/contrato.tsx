"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { GetContratoConfig } from "@/components/forms/inputConfig";
import { contratoSchema } from "@/components/forms/schemas";

import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { useContrato } from "@/context/contrato-context";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { Pencil } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { z } from "zod";

export const contratoColumns: ColumnDef<contrato>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  // CNPJ
  {
    accessorKey: "cnpj",
    header: ({ column }) => <DataTableColumnHeader column={column} title="CNPJ" />,
    cell: ({ row }) => {
      const cnpj = row.getValue("cnpj") as string;
      return (
        <span>{cnpj?.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/, "$1.$2.$3/$4-$5")}</span>
      );
    },
  },
  // Razão Social
  {
    accessorKey: "razao_social",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Razão Social" />,
  },
  // Cidade
  {
    accessorKey: "cidade",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Cidade" />,
    accessorFn: (row) => row.cidade || "Não informada",
  },
  // Valor do Contrato
  {
    accessorKey: "valor_do_contrato",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Valor (R$)" />,
    accessorFn: (row) => {
      const valor = row.valor_contrato ? row.valor_contrato / 100 : 0;
      return valor.toLocaleString("pt-BR", {
        style: "currency",
        currency: "BRL",
      });
    },
  },
  // Início do Contrato
  {
    accessorKey: "data_inicial",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Início" />,
    cell: ({ row }) => {
      const data = new Date(row.getValue("data_inicial"));
      if (isNaN(data.getTime())) {
        return <span>N/A</span>;
      }
      return <span>{format(data, "dd/MM/yyyy")}</span>;
    },
  },
  // Término do Contrato
  {
    accessorKey: "data_final",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Término" />,
    cell: ({ row }) => {
      const data = new Date(row.getValue("data_final"));
      if (isNaN(data.getTime())) {
        return <span>N/A</span>;
      }
      return <span>{format(data, "dd/MM/yyyy")}</span>;
    },
  },
  {
    id: "actions",
    header: "Ações",
    cell: ({ row }) => {
      const router = useRouter();
      const contrato = row.original;

      return (
        <div className="flex items-center justify-center">
          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.stopPropagation();
              router.push(`/dashboard/contratos/editar-contrato/${contrato.id}`);
            }}
            title="Editar contrato">
            <Pencil className="h-4 w-4" />
          </Button>
        </div>
      );
    },
  },
];

export function ContratoTable() {
  const { contratos, setContratos } = useContrato();
  const router = useRouter();

  return (
    <DataTable
      data={contratos}
      onClick={() => router.push("/dashboard/contratos/novo-contrato")}
      exportTo={true}
      columns={contratoColumns}
    />
  );
}
