"use client";

import { useRef, useState } from "react";
import { DataTable } from "@/components/tables/data-table";
import { useEmpenho } from "@/context/empenho-context";
import { EmpenhoActionsMenu } from "@/components/modal/EmpenhoActionsMenu";
import { empenhoColumns } from "@/components/tables/columns/empenhoColumns";
import ModalEmpenho from "@/components/modal/modal.empenho";
import ModalExtratoEmpenho from "@/components/modal/modal.extrato.empenho";

import { useRouter } from "next/navigation";
import { toast } from "sonner";
import ModalExcluirEmpenho from "@/components/modal/modal-excluir-empenho";

export function Empenhos() {
  const router = useRouter();
  const { empenhos } = useEmpenho();

  const containerRef = useRef<HTMLDivElement>(null);
  const [menuPosition, setMenuPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [selectedRowData, setSelectedRowData] = useState<any | null>(null);
  const [showModal, setShowModal] = useState<
    "" | "editar" | "valores" | "extrato" | "excluir"
  >("");

  const handleRowContextMenu = (event: React.MouseEvent, rowData: any) => {
    event.preventDefault();
    if (!containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    setMenuPosition({ x, y });
    setSelectedRowData(rowData);
  };

  const closeMenu = () => {
    setMenuPosition(null);
  };

  const handleCloseModal = () => {
    setShowModal("");
    setSelectedRowData(null);
  };

  const handleDeletionSuccess = () => {
    window.location.reload();
    toast.success("Empenho excluído com sucesso.");
  };

  return (
    <div ref={containerRef} className="relative">
      <DataTable
        className="relative"
        data={empenhos}
        columns={empenhoColumns}
        exportTo={true}
        onRowContextMenu={handleRowContextMenu}
        onClick={() => router.push("/dashboard/empenhos/novo-empenho")}
      />

      {menuPosition && selectedRowData && (
        <EmpenhoActionsMenu
          x={menuPosition.x}
          y={menuPosition.y}
          onClose={closeMenu}
          showModal={setShowModal}
          data={selectedRowData}
        />
      )}

      {(showModal === "editar" || showModal === "valores") &&
        selectedRowData && (
          <ModalEmpenho
            id={selectedRowData.id}
            onClose={handleCloseModal}
            initialTab={showModal === "valores" ? "ajustar" : "editar"}
          />
        )}
      {showModal === "extrato" && (
        <ModalExtratoEmpenho
          id={selectedRowData.id}
          onClose={handleCloseModal}
        />
      )}
      {showModal === "excluir" && selectedRowData && (
        <ModalExcluirEmpenho
          id={selectedRowData.id}
          onClose={handleCloseModal}
          onDelete={handleDeletionSuccess}
        />
      )}
    </div>
  );
}