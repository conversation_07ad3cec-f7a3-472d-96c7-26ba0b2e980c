"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { GetEmpenhoConfig } from "@/components/forms/inputConfig";

import { empenhoSchema } from "@/components/forms/schemas";

import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { Checkbox } from "@/components/ui/checkbox";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { useEmpenho } from "@/context/empenho-context";
import { ColumnDef } from "@tanstack/react-table";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { z } from "zod";

export const empenhoColumns: ColumnDef<empenho>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "centro_custo.descricao",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Centro de Custo" />
    ),
  },
  {
    accessorKey: "nota_empenho",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Nota do Empenho" />
    ),
  },
  {
    accessorKey: "dotacao_orcamentaria",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Dotação Orçamentada" />
    ),
  },
  {
    accessorKey: "valor_pecas",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Valor Para Peças" />
    ),
    accessorFn: (row) => {
      return row.valor_pecas.toLocaleString("pt-BR", {
        style: "currency",
        currency: "BRL",
      });
    },
  },
  {
    accessorKey: "valor_servicos",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Valor Para Serviços" />
    ),
    accessorFn: (row) => {
      return row.valor_servicos.toLocaleString("pt-BR", {
        style: "currency",
        currency: "BRL",
      });
    },
  },
  {
    accessorKey: "empenho_ativo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Situação" />
    ),
    accessorFn: (row) => {
      return row.empenho_ativo ? "Ativo" : "Bloqueado";
    },
  },
];
export function EmpenhoTable() {
  const route = useRouter();
  const { empenhos, setEmpenhos } = useEmpenho();
  const { centrosDeCusto } = useCentroDeCusto();
  async function onNewEmpenho(values: z.infer<typeof empenhoSchema>) {
    const response = await fetch("/api/empenhos/empenho", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });

    if (!response.ok) {
      toast("Ops, algo deu errado", {
        description: "Houve um erro ao criar o empenho",
      });
      return;
    }

    const data = await response.json();
    setEmpenhos((prev) => [...prev, data.data]);
  }

  return (
    <DataTable
      data={empenhos}
      onClick={() => route.push("/dashboard/empenhos/extrato-empenho")}
      exportTo={true}
      columns={empenhoColumns}
    />
  );
}
