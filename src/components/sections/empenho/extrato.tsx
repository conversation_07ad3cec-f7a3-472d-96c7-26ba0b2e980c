"use client";

import { extratoEmpenhoMock } from "@/components/modal/extratoEmpenho";
import { DataTable } from "@/components/tables/data-table";
import { ColumnDef } from "@tanstack/react-table";

interface Extrato {
  data: string;
  tipo: string;
  observacao: string;
  valor: number;
}

const columns: ColumnDef<Extrato>[] = [
  {
    accessorKey: "data",
    header: "Data",
    cell: ({ row }) => row.original.data,
  },
  {
    accessorKey: "tipo",
    header: "Tipo de lançamento",
    cell: ({ row }) => row.original.tipo,
  },
  {
    accessorKey: "observacao",
    header: "Observação",
    cell: ({ row }) => (
      <div className="whitespace-pre-wrap text-sm text-muted-foreground">
        {row.original.observacao}
      </div>
    ),
  },
  {
    accessorKey: "valor",
    header: "Valor",
    cell: ({ row }) => {
      const valor = row.original.valor;
      return (
        <span
          className={`text-sm font-semibold ${
            valor < 0 ? "text-red-600" : "text-green-600"
          }`}
        >
          {valor.toLocaleString("pt-BR", {
            style: "currency",
            currency: "BRL",
          })}
        </span>
      );
    },
  },
];

export function ExtratoDeEmpenho() {
  const totalPositivo = extratoEmpenhoMock
    .filter((e) => e.valor > 0)
    .reduce((acc, e) => acc + e.valor, 0);

  const totalNegativo = extratoEmpenhoMock
    .filter((e) => e.valor < 0)
    .reduce((acc, e) => acc + e.valor, 0);

  const saldoFinal = totalPositivo + totalNegativo;

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Extrato de Empenhos</h1>

      <DataTable
        data={extratoEmpenhoMock}
        columns={columns}
        exportTo
        total={{
          recebido: totalPositivo,
          gasto: totalNegativo,
          saldo: saldoFinal,
        }}
      />
    </div>
  );
}
