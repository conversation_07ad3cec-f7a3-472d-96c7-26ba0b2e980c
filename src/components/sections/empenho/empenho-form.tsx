"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { GetEmpenhoConfig } from "@/components/forms/inputConfig";
import { PageForm } from "@/components/forms/page-form";
import { empenhoSchema } from "@/components/forms/schemas";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { Button } from "@/components/ui/button";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { useEmpenho } from "@/context/empenho-context";
import api from "@/service/api";
import { toast } from "sonner";
import { z } from "zod";

export function EmpenhoForm() {
  const { centrosDeCusto } = useCentroDeCusto();
  const { setEmpenhos } = useEmpenho();

  const undoEmpenho = async (id: string) => {
    const res = await fetch(`/api/empenho/${id}`, {
      method: "DELETE",
    });

    if (!res.ok) {
      toast("Ops, houve um erro", {
        description: "Não foi possível cancelar o empenho",
      });
      return;
    }

    setEmpenhos((prev) => prev.filter((empenho) => empenho.id !== id));
    toast("Tudo certo!", {
      description: "O empenho foi cancelado com sucesso",
    });
  };

  async function onSubmit(values: z.infer<typeof empenhoSchema>) {
    if (!values.centro_de_custoId) {
      toast("Preencha todos os campos", {
        description: "Campo vazio: Centro de Custo",
      });
      return;
    }

    if (!values.nota_empenho) {
      toast("Preencha todos os campos", {
        description: "Campo vazio: Nota Empenho",
      });
      return;
    }

    if (!values.ano_de_competencia) {
      toast("Preencha todos os campos", {
        description: "Campo vazio: Ano Competencia",
      });
      return;
    }

    if (!values.data_inicial) {
      toast("Preencha todos os campos", {
        description: "Campo vazio: Data Inicial",
      });
      return;
    }

    if (!values.data_final) {
      toast("Preencha todos os campos", {
        description: "Campo vazio: Data Final",
      });
      return;
    }

    if (!values.dotacao_orcamentada) {
      toast("Preencha todos os campos", {
        description: "Campo vazio: Dotação Orçamentária",
      });
      return;
    }

    if (!values.valor_destinado_aos_servicos) {
      toast("Preencha todos os campos", {
        description: "Campo vazio: Valor Serviços",
      });
      return;
    }

    if (!values.valor_destinado_as_pecas) {
      toast("Preencha todos os campos", {
        description: "Campo vazio: Valor Peças",
      });
      return;
    }

    const response = await fetch("/api/empenho", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });

    if (!response.ok) {
      toast("Ops, algo deu errado", {
        description: "Houve um erro ao criar o empenho",
      });
      return;
    }

    toast("Empenho registrado!", {
      description: "Registro realizado com sucesso",
    });
    window.location.href = "/dashboard/empenhos/cadastro-empenho";
  }
  const centrosDeCustoFilhos = centrosDeCusto.flatMap((centro) => [
    centro,
    ...(centro.centro_custos_filhos || []),
  ]);
  return (
    <div className="p-4">
      <PageForm
        onSubmit={onSubmit}
        schema={empenhoSchema}
        defaultValues={defaultValues.empenhoSchema as any}>
        <GenericFormsInput fieldConfig={GetEmpenhoConfig([centrosDeCustoFilhos], ["descricao"])} />
      </PageForm>
    </div>
  );
}
