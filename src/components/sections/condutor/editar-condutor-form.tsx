"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { CondutorConfig } from "@/components/forms/inputConfig";
import { PageForm } from "@/components/forms/page-form";
import { condutorSchema } from "@/components/forms/schemas";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { FormDescription } from "@/components/ui/form";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useCondutor } from "@/context/condutor-context";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { z } from "zod";
import { Combobox } from "@/components/inputs/combo-box";

interface EditarCondutorFormProps {
  condutorData: any;
  condutorId: string;
}

export function EditarCondutorForm({ condutorData, condutorId }: EditarCondutorFormProps) {
  const { setCondutores } = useCondutor();
  const [activeTab, setActiveTab] = useState("cnh");
  const { centrosDeCusto } = useCentroDeCusto();
  const router = useRouter();

  // Função para formatar os dados recebidos para o formato do formulário
  const formatInitialData = (data: any) => {
    return {
      nome: data.nome,
      matricula: data.matricula,
      cpf: data.cpf ? data.cpf : "",
      contato: data.contato ? data.contato : "",
      email: data.email,
      status: data.status,
      ativo: true, // Pode ajustar conforme necessário

      endereco: {
        cep: data.endereco?.cep || "",
        logradouro: data.endereco?.logradouro || "",
        bairro: data.endereco?.bairro || "",
        estado: data.endereco?.estado || "",
        cidade: data.endereco?.cidade || "",
      },

      cnh: {
        numero: data.numero_cnh?.numero || "",
        categoria: data.numero_cnh?.categoria || "",
        estado: data.numero_cnh?.estado || "",
        data_emissao: data.numero_cnh?.data_emissao
          ? new Date(data.numero_cnh.data_emissao)
          : undefined,
        data_vencimento: data.numero_cnh?.data_vencimento
          ? new Date(data.numero_cnh.data_vencimento)
          : undefined,
      },

      mopp: {
        numero: data.numero_mopp || "",
        emissor: "",
        data_emissao: undefined,
        data_vencimento: undefined,
      },

      lotacao_condutorId: data.lotacao_condutor?.centro_custoID || "",
    };
  };

  const updateCondutor = async (id: string, values: any) => {
    const formattedValues = {
      nome: values.nome,
      matricula: values.matricula,
      cpf: values.cpf || "",
      contato: values.contato,
      email: values.email,
      status: values.status,

      // Endereço
      endereco: {
        cep: values.endereco?.cep || "",
        logradouro: values.endereco?.logradouro || "",
        bairro: values.endereco?.bairro || "",
        estado: values.endereco?.estado || "",
        cidade: values.endereco?.cidade || "",
      },

      // CNH
      numero_cnh: {
        numero: values.cnh.numero,
        categoria: values.cnh.categoria,
        estado: values.cnh.estado,
        data_emissao: values.cnh.data_emissao,
        data_vencimento: values.cnh.data_vencimento,
      },

      // MOPP (se existir)
      numero_mopp: values.mopp?.numero || null,

      // Lotação
      centro_custoID: values.lotacao_condutorId || null,
    };

    const res = await fetch(`/api/condutores/${id}`, {
      method: "PUT",
      headers: {
        "Content-type": "application/json",
      },
      body: JSON.stringify(formattedValues),
    });

    if (!res.ok) {
      toast("Ops, houve um erro", {
        description: "Não foi possível atualizar o condutor",
      });
      return false;
    }
    return true;
  };

  async function onSubmit(values: z.infer<typeof condutorSchema>) {
    try {
      const success = await updateCondutor(condutorId, values);

      if (success) {
        toast("Sucesso!", {
          description: "Condutor atualizado com sucesso",
        });

        // Atualiza o contexto
        setCondutores((prev) =>
          prev.map((condutor) => {
            if (condutor.id !== condutorId) return condutor;

            // Mantém todas as propriedades existentes e atualiza apenas as necessárias
            const updatedCondutor: any = {
              ...condutor,
              nome: values.nome,
              matricula: values.matricula,
              cpf: values.cpf,
              contato: values.contato,
              email: values.email,
              status: values.status,
              endereco: {
                ...condutor.endereco,
                cep: values.endereco?.cep || condutor.endereco.cep,
                logradouro: values.endereco?.logradouro || condutor.endereco.logradouro,
                bairro: values.endereco?.bairro || condutor.endereco.bairro,
                estado: values.endereco?.estado || condutor.endereco.estado,
                cidade: values.endereco?.cidade || condutor.endereco.cidade,
              },
              cnh: {
                ...condutor.cnh,
                numero: values.cnh.numero,
                categoria: values.cnh.categoria,
                estado: values.cnh.estado,
                data_emissao: values.cnh.data_emissao,
                data_vencimento: values.cnh.data_vencimento,
              },
              lotacao_condutor: condutor.lotacao_condutor
                ? {
                    ...condutor.lotacao_condutor,
                    centro_custoID:
                      values.lotacao_condutorId || condutor.lotacao_condutor.centro_custoID,
                  }
                : undefined,
            };

            return updatedCondutor;
          })
        );

        router.push("/dashboard/condutores/consultar-condutor");
      }
    } catch (error) {
      console.error("Erro ao atualizar condutor:", error);
      toast("Erro de conexão", {
        description: "Não foi possível conectar ao servidor",
      });
    }
  }

  const endereco = CondutorConfig.endereco?.fields;
  const cnh = CondutorConfig.cnh?.fields;
  const mopp = CondutorConfig.mopp?.fields;

  if (!endereco || !cnh || !mopp) {
    console.error("Configuração de campos incompleta");
    return <div>Erro: Configuração de campos incompleta</div>;
  }

  const centrosDeCustoFilhos = centrosDeCusto.flatMap((centro) => centro.centro_custos_filhos);

  return (
    <div className="p-4">
      <PageForm
        onSubmit={onSubmit}
        schema={condutorSchema}
        defaultValues={formatInitialData(condutorData)}>
        {/* Informações Básicas */}
        <div className="mb-6">
          <Card className="border p-4 rounded-lg">
            <CardContent className="m-0 p-0">
              <h4 className="font-semibold mb-2">Informações Básicas</h4>
              <FormDescription>Dados pessoais do condutor</FormDescription>
              <GenericFormsInput
                fieldConfig={{
                  nome: CondutorConfig.nome,
                  cpf: {
                    ...CondutorConfig.cpf,
                    inputType: "cpf",
                    label: "CPF",
                    placeholder: "Informe o CPF",
                    description: "CPF do condutor",
                  },
                  matricula: CondutorConfig.matricula,
                  contato: {
                    ...CondutorConfig.contato,
                    inputType: "celular",
                  },
                  email: CondutorConfig.email,
                }}
              />
            </CardContent>
          </Card>
        </div>

        {/* Lotação e Status */}
        <div className="mb-6">
          <Card className="border p-4 rounded-lg">
            <CardContent className="m-0 p-0">
              <h4 className="font-semibold mb-2">Lotação e Status</h4>
              <FormDescription>Defina a lotação e status do condutor</FormDescription>

              <Combobox
                datas={centrosDeCustoFilhos}
                title="Lotação"
                placeholder="Selecione o centro de custo"
                referenceId="centro_de_custoId"
                description="Centro de custo ao qual o condutor está vinculado"
                chave="descricao"
                name="lotacao_condutorId"
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-4">
                <GenericFormsInput
                  fieldConfig={{
                    status: CondutorConfig.status,
                  }}
                  variants="single"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Endereço */}
        <div className="mb-6">
          <Card className="border p-4 rounded-lg">
            <CardContent className="m-0 p-0">
              <h4 className="font-semibold mb-2">Endereço</h4>
              <FormDescription>Informe o endereço do condutor</FormDescription>
              <GenericFormsInput
                fieldConfig={{
                  "endereco.cep": {
                    ...endereco.cep,
                    inputType: "cep",
                  },
                  "endereco.logradouro": endereco.logradouro,
                  "endereco.bairro": endereco.bairro,
                  "endereco.estado": endereco.estado,
                  "endereco.cidade": endereco.cidade,
                }}
              />
            </CardContent>
          </Card>
        </div>

        {/* CNH e MOPP */}
        <div className="mb-6">
          <Card className="border p-4 rounded-lg">
            <CardContent className="m-0 p-0">
              <Tabs defaultValue="cnh" value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid grid-cols-2 mb-4">
                  <TabsTrigger value="cnh">CNH</TabsTrigger>
                  <TabsTrigger value="mopp">MOPP</TabsTrigger>
                </TabsList>

                <TabsContent value="cnh">
                  <FormDescription>Informe os dados da CNH</FormDescription>
                  <GenericFormsInput
                    fieldConfig={{
                      "cnh.numero": cnh.numero,
                      "cnh.categoria": cnh.categoria,
                      "cnh.estado": cnh.estado,
                    }}
                  />
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-4">
                    <GenericFormsInput
                      fieldConfig={{
                        "cnh.data_emissao": cnh.data_emissao,
                      }}
                      variants="single"
                    />
                    <GenericFormsInput
                      fieldConfig={{
                        "cnh.data_vencimento": cnh.data_vencimento,
                      }}
                      variants="single"
                    />
                  </div>
                </TabsContent>

                <TabsContent value="mopp">
                  <FormDescription>Informe os dados do MOPP (opcional)</FormDescription>
                  <GenericFormsInput
                    fieldConfig={{
                      "mopp.numero": mopp.numero,
                      "mopp.emissor": mopp.emissor,
                    }}
                  />
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-4">
                    <GenericFormsInput
                      fieldConfig={{
                        "mopp.data_emissao": mopp.data_emissao,
                      }}
                      variants="single"
                    />
                    <GenericFormsInput
                      fieldConfig={{
                        "mopp.data_vencimento": mopp.data_vencimento,
                      }}
                      variants="single"
                    />
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </PageForm>
    </div>
  );
}
