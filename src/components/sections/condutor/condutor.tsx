"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { CondutorConfig } from "@/components/forms/inputConfig";
import { condutorSchema } from "@/components/forms/schemas";

import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { useCondutor } from "@/context/condutor-context";
import { getServerSession } from "@/lib/auth/server-session";
import { Session } from "@/lib/auth/types";
import { ColumnDef } from "@tanstack/react-table";
import { Edit, Power } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { z } from "zod";

export const condutorColumn: ColumnDef<condutor>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "nome",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Nome" />,
  },
  {
    accessorKey: "matricula",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Matrícula" />,
  },
  {
    accessorKey: "contato",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Contato" />,
  },
  {
    accessorKey: "email",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Email" />,
  },
  {
    accessorKey: "lotacao_condutor",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Centro de Custo" />,
    cell: ({ row }) => {
      return (
        <div>
          {row.original.lotacao_condutor?.centro_custo?.descricao}
        </div>
      );
    },
  },
  {
    accessorKey: "ativo",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      return (
        <div className={`font-medium ${row.original.status ? "text-green-600" : "text-red-600"}`}>
          {row.original.status ? "Ativo" : "Inativo"}
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "Ações",
    cell: ({ row }) => {
      const handleStatusUpdate = async () => {
        try {
          const newStatus = !row.original.status;
          const res = await fetch(`/api/condutores/${row.original.id}`, {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ ...row.original, status: newStatus }),
          });

          if (!res.ok) throw new Error("Falha ao atualizar status");
          toast.success(`Condutor ${newStatus ? "ativado" : "inativado"} com sucesso`);
          window.location.reload();
        } catch (error) {
          toast.error("Erro ao atualizar status");
        }
      };

      return (
        <div className="flex gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() =>
              (window.location.href = `/dashboard/condutores/editar/${row.original.id}`)
            }>
            <Edit className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={handleStatusUpdate}>
            <Power
              className={`h-4 w-4 ${!row.original.status ? "text-red-500" : "text-green-500"}`}
            />
          </Button>
        </div>
      );
    },
  },
];

export function CondutorTable() {
  const router = useRouter();
  const { condutores, setCondutores } = useCondutor();
  const [sessionData, setSessionData] = useState<Session>();
  const {centrosDeCusto} = useCentroDeCusto();

  useEffect(() => {
    async function fetchSessionData() {
      const sessionData = await getServerSession();
      setSessionData(sessionData);
    }
    fetchSessionData();
  }, []);

  let condutoresData: condutor[] = condutores;
    if (sessionData && !sessionData.roles.includes("ADMIN") && sessionData.centro_de_custoId) {
      if (sessionData.unidade_filha_id) {
        let filteredData = condutores.filter(
          (condutor) =>
            condutor.lotacao_condutor?.centro_custoID === sessionData.unidade_filha_id
        );
        if (filteredData.length === 0) {
          filteredData = condutores.filter(
            (condutor) =>
              condutor.lotacao_condutor?.centro_custoID === sessionData.centro_de_custoId
          );
        }
        condutoresData = filteredData;
      } else if (sessionData.centro_de_custoId) {
        const unidadesFilhas = centrosDeCusto
          .flatMap((centro) => centro.centro_custos_filhos)
          .filter((uniFilho) => uniFilho.centro_custo_ascdID === sessionData.centro_de_custoId);
        const filteredCondutores = condutores.filter((condutor) =>
          unidadesFilhas.some((centro) => centro.id === condutor?.lotacao_condutor?.centro_custoID)
        );
        if (filteredCondutores.length > 0) {
          condutoresData = filteredCondutores;
        } else {
          condutoresData = condutores.filter(
            (condutor) =>
              condutor.lotacao_condutor?.centro_custoID === sessionData.centro_de_custoId
          );
        }
      } else {
        condutoresData = condutores
      }
    }

  return (
    <DataTable
      data={condutoresData}
      exportTo={true}
      columns={condutorColumn}
      onClick={() => router.push("/dashboard/condutores/novo-condutor")}
      showReportButton={true}
      reportType="condutores"
    />
  );
}
