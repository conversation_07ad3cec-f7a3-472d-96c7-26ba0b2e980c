"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { CondutorConfig } from "@/components/forms/inputConfig";
import { PageForm } from "@/components/forms/page-form";
import { condutorSchema } from "@/components/forms/schemas";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { FormDescription } from "@/components/ui/form";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useCondutor } from "@/context/condutor-context";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { z } from "zod";
import { Combobox } from "@/components/inputs/combo-box";

export function CondutorForm() {
  const { setCondutores } = useCondutor();
  const [activeTab, setActiveTab] = useState("cnh");
  const { centrosDeCusto } = useCentroDeCusto();
  const router = useRouter();

  const undo = async (id: string) => {
    const res = await fetch(`/api/condutores/${id}`, { method: "DELETE" });
    if (!res.ok) {
      toast("Ops, houve um erro", {
        description: "Não foi possível cancelar a criação do condutor",
      });
    }
    toast("Tudo certo!", {
      description: "Seu condutor não foi criado",
    });
  };

  async function onSubmit(values: z.infer<typeof condutorSchema>) {
    // Adaptar objeto de valores para o formato esperado pelo backend

    if (!values.nome) {
      toast("Preencha todos os campos obrigatórios", {
        description: "Campo vazio: Nome",
      });
      return;
    }

    if (!values.matricula) {
      toast("Preencha todos os campos obrigatórios", {
        description: "Campo vazio: Matricula",
      });
      return;
    }

    if (!values.contato) {
      toast("Preencha todos os campos obrigatórios", {
        description: "Campo vazio: Contato",
      });
      return;
    }

    if (!values.email) {
      toast("Preencha todos os campos obrigatórios", {
        description: "Campo vazio: Email",
      });
      return;
    }

    if (!values.cnh.numero) {
      toast("Preencha todos os campos obrigatórios", {
        description: "Campo vazio: Número CNH",
      });
      return;
    }

    if (!values.cnh.categoria) {
      toast("Preencha todos os campos obrigatórios", {
        description: "Campo vazio: Categoria CNH",
      });
      return;
    }

    if (!values.cnh.estado) {
      toast("Preencha todos os campos obrigatórios", {
        description: "Campo vazio: Estado CNH",
      });
      return;
    }

    if (!values.cnh.data_emissao) {
      toast("Preencha todos os campos obrigatórios", {
        description: "Campo vazio: Data de emissão CNH",
      });
      return;
    }

    if (!values.cnh.data_vencimento) {
      toast("Preencha todos os campos obrigatórios", {
        description: "Campo vazio: Data vencimento CNH",
      });
      return;
    }

    if (!values.status) {
      toast("Preencha todos os campos obrigatórios", {
        description: "Campo vazio: Status",
      });
      return;
    }

    const formattedValues = {
      nome: values.nome,
      matricula: values.matricula,
      cpf: values.cpf || "", // Garantir que o CPF seja enviado
      contato: values.contato,
      email: values.email,

      // Dados de endereço
      cep: values.endereco?.cep || "",
      logradouro: values.endereco?.logradouro || "",
      bairro: values.endereco?.bairro || "",
      estado: values.endereco?.estado || "",
      cidade: values.endereco?.cidade || "",

      // Dados da CNH
      numero_cnh: values.cnh.numero,
      categoria: values.cnh.categoria,
      estado_cnh: values.cnh.estado,
      data_emissao_cnh: values.cnh.data_emissao,
      data_vencimento_cnh: values.cnh.data_vencimento,

      // Dados do MOPP (se existir)
      numero_mopp: values.mopp?.numero || "",
      emissor_mopp: values.mopp?.emissor || "",
      data_emissao_mopp: values.mopp?.data_emissao || null,
      data_vencimento_mopp: values.mopp?.data_vencimento || null,

      // Status
      status: values.status,

      // Dados de lotação
      centro_custoID: values.lotacao_condutorId || "",
    };

    try {
      const res = await fetch("/api/condutores", {
        method: "POST",
        headers: {
          "Content-type": "application/json",
        },
        body: JSON.stringify(formattedValues),
      });

      if (!res.ok) {
        const errorData = await res.json();
        toast("Ops, alguma coisa deu errado", {
          description:
            errorData.message || "Houve um erro na criação do condutor",
        });
        return;
      }

      const data = await res.json();
      setCondutores((prev) => [...prev, data.condutor]);

      toast("Sucesso!", {
        description: "Seu condutor foi criado com sucesso",
        action: (
          <Button onClick={async () => await undo(data.condutor.id)}>
            Cancelar criação
          </Button>
        ),
      });

      window.location.href = "/dashboard/condutores/consultar-condutor";
    } catch (error) {
      console.error("Erro ao criar condutor:", error);
      toast("Erro de conexão", {
        description: "Não foi possível conectar ao servidor",
      });
    }
  }

  // Verificar se os campos existem para satisfazer o TypeScript
  const endereco = CondutorConfig.endereco?.fields;
  const cnh = CondutorConfig.cnh?.fields;
  const mopp = CondutorConfig.mopp?.fields;

  // Garantir que os campos existem antes de usar
  if (!endereco || !cnh || !mopp) {
    console.error("Configuração de campos incompleta");
    return <div>Erro: Configuração de campos incompleta</div>;
  }

  const centrosDeCustoFilhos = centrosDeCusto.flatMap(
    (centro) => centro.centro_custos_filhos
  );

  return (
    <div className="p-4">
      <PageForm
        onSubmit={onSubmit}
        schema={condutorSchema}
        defaultValues={defaultValues.condutorSchema as any}
      >
        {/* Basic information section */}
        <div className="mb-6">
          <Card className="border p-4 rounded-lg">
            <CardContent className="m-0 p-0">
              <h4 className="font-semibold mb-2">Informações Básicas</h4>
              <FormDescription>Dados pessoais do condutor</FormDescription>
              <GenericFormsInput
                fieldConfig={{
                  nome: CondutorConfig.nome,
                  cpf: {
                    ...CondutorConfig.cpf,
                    inputType: "cpf", // Usar input formatado para CPF
                    label: "CPF",
                    placeholder: "Informe o CPF",
                    description: "CPF do condutor",
                  },
                  matricula: CondutorConfig.matricula,
                  contato: {
                    ...CondutorConfig.contato,
                    inputType: "celular",
                  },
                  email: CondutorConfig.email,
                }}
              />
            </CardContent>
          </Card>
        </div>

        {/* Lotação e Status section */}
        <div className="mb-6">
          <Card className="border p-4 rounded-lg">
            <CardContent className="m-0 p-0">
              <h4 className="font-semibold mb-2">Lotação e Status</h4>
              <FormDescription>
                Defina a lotação e status do condutor
              </FormDescription>

              {/* Usando Combobox para seleção de centro de custo */}
              <Combobox
                datas={[...centrosDeCusto, ...centrosDeCustoFilhos]}
                title="Lotação"
                placeholder="Selecione o centro de custo"
                referenceId="centro_de_custoId"
                description="Centro de custo ao qual o condutor está vinculado"
                chave="descricao"
                name="lotacao_condutorId"
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-4">
                <GenericFormsInput
                  fieldConfig={{
                    ativo: CondutorConfig.ativo,
                  }}
                  variants="single"
                />
                {/* <GenericFormsInput
                  fieldConfig={{
                    status: CondutorConfig.status,
                  }}
                  variants="single"
                /> */}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Address section */}
        <div className="mb-6">
          <Card className="border p-4 rounded-lg">
            <CardContent className="m-0 p-0">
              <h4 className="font-semibold mb-2">Endereço</h4>
              <FormDescription>Informe o endereço do condutor</FormDescription>
              <GenericFormsInput
                fieldConfig={{
                  "endereco.cep": {
                    ...endereco.cep,
                    inputType: "cep", // Usar input formatado para CEP
                  },
                  "endereco.logradouro": endereco.logradouro,
                  "endereco.bairro": endereco.bairro,
                  "endereco.estado": endereco.estado,
                  "endereco.cidade": endereco.cidade,
                }}
              />
            </CardContent>
          </Card>
        </div>

        {/* CNH and MOPP tabbed section */}
        <div className="mb-6">
          <Card className="border p-4 rounded-lg">
            <CardContent className="m-0 p-0">
              <Tabs
                defaultValue="cnh"
                value={activeTab}
                onValueChange={setActiveTab}
              >
                <TabsList className="grid grid-cols-2 mb-4">
                  <TabsTrigger value="cnh">CNH</TabsTrigger>
                  <TabsTrigger value="mopp">MOPP</TabsTrigger>
                </TabsList>

                <TabsContent value="cnh">
                  <FormDescription>Informe os dados da CNH</FormDescription>
                  <GenericFormsInput
                    fieldConfig={{
                      "cnh.numero": cnh.numero,
                      "cnh.categoria": cnh.categoria,
                      "cnh.estado": cnh.estado,
                    }}
                  />
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-4">
                    <GenericFormsInput
                      fieldConfig={{
                        "cnh.data_emissao": cnh.data_emissao,
                      }}
                      variants="single"
                    />
                    <GenericFormsInput
                      fieldConfig={{
                        "cnh.data_vencimento": cnh.data_vencimento,
                      }}
                      variants="single"
                    />
                  </div>
                </TabsContent>

                <TabsContent value="mopp">
                  <FormDescription>
                    Informe os dados do MOPP (opcional)
                  </FormDescription>
                  <GenericFormsInput
                    fieldConfig={{
                      "mopp.numero": mopp.numero,
                      "mopp.emissor": mopp.emissor,
                    }}
                  />
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-4">
                    <GenericFormsInput
                      fieldConfig={{
                        "mopp.data_emissao": mopp.data_emissao,
                      }}
                      variants="single"
                    />
                    <GenericFormsInput
                      fieldConfig={{
                        "mopp.data_vencimento": mopp.data_vencimento,
                      }}
                      variants="single"
                    />
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </PageForm>
    </div>
  );
}
