"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { loginSchema } from "../forms/schemas/login-schema";
import { Form } from "../ui/form";
import { Button } from "../ui/button";
import { useState, useRef, useEffect } from "react";
import { toast } from "sonner";
import { useSession } from "../session/use-session";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Checkbox } from "../ui/checkbox";
import { Input } from "../ui/input";
import { Eye, EyeOff } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "../ui/dialog";
import { Label } from "../ui/label";
import api from "@/service/api";

// Textos para cada slide
const slidesContent = [
  {
    text: "tudo em um só lugar! Gerencia inspeções, ordens de serviço, cronogramas de revisões, estoque de peças e muito mais em um único painel.",
    image: "/images/auth-one-bg-NPV2GVIP.jpg",
  },
  {
    text: "Simplifique a gestão da sua frota com nossa plataforma completa. Monitore veículos, controle custos e otimize manutenções em tempo real.",
    image: "/images/auth-one-bg-NPV2GVIP.jpg",
  },
  {
    text: "Reduza custos e maximize a eficiência com nosso sistema integrado de gestão de frotas. Decisões mais inteligentes, processos mais ágeis.",
    image: "/images/auth-one-bg-NPV2GVIP.jpg",
  },
];

// Schema para o formulário de recuperação de senha
const forgotPasswordSchema = z.object({
  email: z.string().email({ message: "Email inválido" }),
});

// Constantes para armazenamento local
const REMEMBER_ME_KEY = "bc_remember_me";
const SAVED_EMAIL_KEY = "bc_saved_email";

export function Login() {
  const { setSession } = useSession();
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [loading, setLoading] = useState(false);
  const [forgotPasswordOpen, setForgotPasswordOpen] = useState(false);
  const [resetPasswordLoading, setResetPasswordLoading] = useState(false);
  const [autoLoginChecked, setAutoLoginChecked] = useState(false);

  const form = useForm<z.infer<typeof loginSchema>>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      senha: "",
    },
  });

  const forgotPasswordForm = useForm<z.infer<typeof forgotPasswordSchema>>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: "",
    },
  });

  // Carregar dados salvos quando a página é carregada
  useEffect(() => {
    const loadSavedCredentials = () => {
      if (typeof window !== "undefined") {
        try {
          // Verifica se 'Lembrar de mim' estava ativado
          const savedRememberMe = localStorage.getItem(REMEMBER_ME_KEY);
          if (savedRememberMe === "true") {
            setRememberMe(true);

            // Recupera o email salvo
            const savedEmail = localStorage.getItem(SAVED_EMAIL_KEY);
            if (savedEmail) {
              form.setValue("email", savedEmail);
              // Foca no campo de senha para facilitar o login
              setTimeout(() => {
                const passwordInput = document.getElementById("senha");
                passwordInput?.focus();
              }, 100);
            }
          }
        } catch (error) {
          console.error("Erro ao recuperar credenciais salvas:", error);
        }
      }
    };

    // Chama a função apenas se ainda não verificamos o auto-login
    if (!autoLoginChecked) {
      loadSavedCredentials();
      setAutoLoginChecked(true);
    }
  }, [form, autoLoginChecked]);

  // Alternar para o próximo slide automaticamente a cada 5 segundos
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slidesContent.length);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // Função para ir para um slide específico
  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  // Abre o modal de recuperação de senha e preenche com o email já digitado (se houver)
  const handleForgotPasswordClick = () => {
    // Preenche o formulário de recuperação com o email do formulário de login, se disponível
    const loginEmail = form.getValues("email");
    if (loginEmail) {
      forgotPasswordForm.setValue("email", loginEmail);
    }
    setForgotPasswordOpen(true);
  };

  // Processa o pedido de recuperação de senha
  const handleResetPassword = async (
    data: z.infer<typeof forgotPasswordSchema>
  ) => {
    try {
      setResetPasswordLoading(true);

      // Aqui faria a chamada API real para solicitar recuperação de senha
      const res = await fetch("/api/auth/reset-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: data.email,
        }),
      });

      // Simulando uma resposta bem-sucedida (para demonstração)
      // Na implementação real, verificar se res.ok
      toast.success("Email enviado com sucesso", {
        description:
          "Verifique sua caixa de entrada ou spam para redefinir sua senha",
      });

      // Fecha o diálogo após o sucesso
      setForgotPasswordOpen(false);
      forgotPasswordForm.reset();
    } catch (error) {
      toast.error("Erro ao enviar email", {
        description:
          "Não foi possível processar sua solicitação. Tente novamente mais tarde.",
      });
    } finally {
      setResetPasswordLoading(false);
    }
  };

  // Função para lidar com a mudança no checkbox de "Lembrar de mim"
  const handleRememberMeChange = (checked: boolean) => {
    setRememberMe(checked);
  };

  async function onSubmit(data: z.infer<typeof loginSchema>) {
    try {
      setLoading(true);

      const res = await fetch("/api/auth/signin", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: data.email,
          password: data.senha,
          remember: rememberMe,
        }),
      });

      if (!res.ok) {
        toast.error("Falha no login", {
          description: "Email ou senha incorretos",
        });
        return;
      }

      const session = await res.json();

      // ✅ SALVA O TOKEN NO LOCALSTORAGE
      if (typeof window !== "undefined" && session?.token) {
        localStorage.setItem("token", session.token);
      }

      // ✅ SALVA PREFERÊNCIAS DE "LEMBRAR DE MIM"
      if (rememberMe) {
        localStorage.setItem(REMEMBER_ME_KEY, "true");
        localStorage.setItem(SAVED_EMAIL_KEY, data.email);
      } else {
        localStorage.removeItem(REMEMBER_ME_KEY);
        localStorage.removeItem(SAVED_EMAIL_KEY);
      }
      try {
        await fetch("/api/auditoria", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            userId: session.user.id,
            acao_do_usuario: "login",
          }),
        });
      } catch (error) {
        console.error("Erro ao registrar atividade de login:", error);
      }

      toast.success("Login realizado com sucesso!");
      window.location.href = "/dashboard";
    } catch (error) {
      console.error("Erro ao fazer login:", error);
      toast.error("Erro de conexão", {
        description: "Não foi possível conectar ao servidor",
      });
    } finally {
      setLoading(false);
    }
  }

  return (
    <div
      className="w-screen h-screen flex items-center justify-center bg-black bg-opacity-90"
      style={{
        backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.8)), url('/images/abstract.jpg')`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
      }}
    >
      {/* Rodapé com copyright */}
      <div className="absolute bottom-4 text-xs text-white text-center w-full opacity-70">
        2025 © CARLETTO GESTÃO DE SERVIÇOS LTDA - CNPJ 08.469.404/0001-30
      </div>

      {/* Card principal */}
      <div className="flex flex-col lg:flex-row overflow-hidden rounded-md shadow-xl mx-auto w-[90%] md:w-[85%] lg:w-[1000px]">
        {/* Lado esquerdo - área do carrossel */}
        <div className="w-full lg:w-[55%] relative bg-black">
          <div className="relative w-full lg:min-h-[500px] min-h-[300px]">
            {/* Background com imagem e overlay escuro */}
            <div
              className="absolute inset-0 z-0"
              style={{
                backgroundImage: `url(${slidesContent[currentSlide].image})`,
                backgroundSize: "cover",
                backgroundPosition: "center",
              }}
            >
              <div className="absolute inset-0 bg-black bg-opacity-65"></div>
            </div>

            {/* Conteúdo do slide */}
            <div className="relative z-10 h-full flex flex-col p-8">
              {/* Logo no canto superior esquerdo */}
              <div className="absolute top-8 left-8">
                <Image
                  src="/images/logo-login.png"
                  width={70}
                  height={70}
                  alt="Carletto"
                />
              </div>

              {/* Conteúdo centralizado */}
              <div className="flex-grow flex flex-col mt-52 items-center justify-center">
                {/* Citação */}
                <div className="text-white text-center max-w-md">
                  <p className="text-base italic font-light leading-relaxed mb-4">
                    * {slidesContent[currentSlide].text} *
                  </p>
                </div>
              </div>

              {/* Indicadores de slide */}
              <div className="flex justify-center gap-2 pt-4 pb-2">
                {slidesContent.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToSlide(index)}
                    className={`w-8 h-[2px] transition-all ${
                      currentSlide === index ? "bg-white" : "bg-gray-600"
                    }`}
                    aria-label={`Ir para slide ${index + 1}`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Lado direito - formulário de login */}
        <div className="w-full lg:w-[45%] bg-white p-4 sm:p-6 lg:p-8 flex items-center">
          <div className="w-full max-w-sm mx-auto">
            <div className="mb-6">
              <h1 className="text-lg font-semibold text-gray-800 mb-1">
                Bem vindo à Carletto Gestão de Frotas!
              </h1>
              <p className="text-sm text-gray-500">
                Faça seu login para continuar.
              </p>
            </div>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                {/* Campo de e-mail */}
                <div className="space-y-1.5">
                  <label
                    htmlFor="email"
                    className="text-sm font-medium text-gray-700 block"
                  >
                    Email
                  </label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    {...form.register("email")}
                    className="h-10 rounded-sm border-gray-300"
                  />
                  {form.formState.errors.email && (
                    <p className="text-sm text-red-500">
                      {form.formState.errors.email.message}
                    </p>
                  )}
                </div>

                {/* Campo de senha */}
                <div className="space-y-1.5">
                  <div className="flex justify-between">
                    <label
                      htmlFor="senha"
                      className="text-sm font-medium text-gray-700"
                    >
                      Senha
                    </label>
                    <button
                      type="button"
                      onClick={handleForgotPasswordClick}
                      className="text-xs text-blue-500 hover:underline"
                    >
                      Esqueceu a senha?
                    </button>
                  </div>

                  <div className="relative">
                    <Input
                      id="senha"
                      type={showPassword ? "text" : "password"}
                      placeholder="****"
                      {...form.register("senha")}
                      className="h-10 pr-10 rounded-sm border-gray-300"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400"
                    >
                      {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                    </button>
                  </div>

                  {form.formState.errors.senha && (
                    <p className="text-sm text-red-500">
                      {form.formState.errors.senha.message}
                    </p>
                  )}
                </div>

                {/* Checkbox "Lembrar de mim" */}
                <div className="flex items-center">
                  <Checkbox
                    id="remember"
                    checked={rememberMe}
                    onCheckedChange={handleRememberMeChange}
                    className="h-4 w-4 rounded-sm border-gray-300 text-emerald-500"
                  />
                  <label
                    htmlFor="remember"
                    className="ml-2 text-sm text-gray-600"
                  >
                    Lembrar de mim
                  </label>
                </div>

                {/* Botão de entrar */}
                <Button
                  type="submit"
                  disabled={loading}
                  className="w-full h-10 rounded-sm bg-emerald-500 hover:bg-emerald-600 text-white font-medium"
                >
                  {loading ? "Entrando..." : "Entrar"}
                </Button>
              </form>
            </Form>
          </div>
        </div>
      </div>

      {/* Modal de Recuperação de Senha */}
      <Dialog open={forgotPasswordOpen} onOpenChange={setForgotPasswordOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Recuperação de Senha</DialogTitle>
          </DialogHeader>

          <Form {...forgotPasswordForm}>
            <form
              onSubmit={forgotPasswordForm.handleSubmit(handleResetPassword)}
              className="space-y-4"
            >
              <div className="space-y-4 py-2">
                <div className="space-y-2">
                  <Label htmlFor="reset-email">
                    Informe seu email cadastrado para receber instruções de
                    recuperação de senha
                  </Label>
                  <Input
                    id="reset-email"
                    type="email"
                    placeholder="<EMAIL>"
                    {...forgotPasswordForm.register("email")}
                    className="w-full"
                  />
                  {forgotPasswordForm.formState.errors.email && (
                    <p className="text-sm text-red-500">
                      {forgotPasswordForm.formState.errors.email.message}
                    </p>
                  )}
                </div>
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setForgotPasswordOpen(false)}
                  className="mt-2 sm:mt-0"
                >
                  Cancelar
                </Button>
                <Button
                  type="submit"
                  disabled={resetPasswordLoading}
                  className="bg-emerald-500 hover:bg-emerald-600 text-white"
                >
                  {resetPasswordLoading ? "Enviando..." : "Enviar"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
