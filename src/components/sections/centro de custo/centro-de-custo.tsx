"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { GetCentroDeCustoConfig } from "@/components/forms/inputConfig";

import { centroDeCustoSchema } from "@/components/forms/schemas";

import { DataTableColumnHeader } from "@/components/tables/column-header";
import { DataTable } from "@/components/tables/data-table";
import { Checkbox } from "@/components/ui/checkbox";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { ColumnDef } from "@tanstack/react-table";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { z } from "zod";
import { CentroCustoAccordionTable } from "./centro-custo-accordion-table";
import { Edit, Edit2Icon } from "lucide-react";

export const centroCustoColumns: ColumnDef<centro_de_custo>[] = [
  {
    accessorKey: "descricao",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Descrição" />
    ),
  },
  {
    accessorKey: "telefone",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Telefone" />
    ),
    accessorFn: (row) => {
      return row.contato || "N/A";
    },
  },
  {
    accessorKey: "email",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="E-mail" />
    ),
    accessorFn: (row) => row.email || "N/A",
  },
  {
    accessorKey: "nome_responsavel",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Responsável" />
    ),
    accessorFn: (row) => row.nome_responsavel || "Não informado",
  },
  {
    id: "actions",
    header: "Ações",
    cell: ({ row }) => (
      <Edit
        className="mr-2 h-4 w-4"
        style={{ cursor: "pointer" }}
        onClick={() =>
          (window.location.href = `/dashboard/centro-de-custo/editar/${row.original.id}`)
        }
      />
    ),
  },
];

// CentroDeCustoTable - Componente principal modificado
export function CentroDeCustoTable() {
  const { centrosDeCusto, setCentrosDeCusto } = useCentroDeCusto();
  const router = useRouter();

  async function onNewCentroDeCusto(
    values: z.infer<typeof centroDeCustoSchema>
  ) {
    const response = await fetch("/api/centro-de-custo", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });

    if (!response.ok) {
      toast("Ops, algo deu errado", {
        description: "Houve um erro ao criar o centro de custo",
      });
      return;
    }

    const data = await response.json();

    if (!data.success) {
      toast("Ops, algo deu errado", {
        description: data.message || "Houve um erro ao criar o centro de custo",
      });
      return;
    }

    // Atualizar com a nova estrutura de dados
    if (data.data) {
      setCentrosDeCusto((prev) => [...prev, data.data]);
      toast("Centro de custo criado", {
        description: data.message || "Centro de custo criado com sucesso",
      });
    }
  }

  return (
    <CentroCustoAccordionTable
      data={centrosDeCusto}
      columns={centroCustoColumns}
      onClick={() =>
        router.push("/dashboard/centro-de-custo/cadastro-centro-custo")
      }
      exportTo={true}
      showReportButton={true}
      reportType="centro-custo"
    />
  );
}
