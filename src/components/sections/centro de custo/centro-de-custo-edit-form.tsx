"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { GetCentroDeCustoConfig } from "@/components/forms/inputConfig";
import { PageForm } from "@/components/forms/page-form";
import { centroDeCustoSchema } from "@/components/forms/schemas";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { Button } from "@/components/ui/button";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { editCentroCusto } from "@/serverActions/centroCustoAction";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { z } from "zod";

export function CentroDeCustoEditForm() {
  const { centrosDeCusto, setCentrosDeCusto } = useCentroDeCusto();
  const router = useRouter();
  const { id } = useParams();

  const [defaultValues, setDefaultValues] = useState({
    descricao: "",
    centro_de_custo_ascendenteId: "",
    dotacao_orcamentista: "",
    valor_dotacao: 0,
    nome_responsavel: "",
    contato: "",
    email: "",
    cnpj: "",
    razao_social: "",
    endereco: {
      cep: "",
      logradouro: "",
      bairro: "",
      estado: "",
      cidade: "",
    },
    centro_de_custo_tomadorId: "",
    active: true,
    veiculos_com_rfid: null,
    abertura_os: null,
  });

  useEffect(() => {
    const getCentroCusto = async () => {
      try {
        const res = await fetch(`/api/centro-de-custo/${id}`, {
          method: "GET",
        });

        if (!res.ok) {
          toast("Erro ao buscar centro de custo", {
            description: "Falha ao buscar o centro de custo",
          });
          return;
        }

        const json = await res.json();
        console.log("Resposta da API:", json); // Debug

        // Verifica se os dados existem antes de acessar
        if (!json.success || !json.data) {
          toast("Erro ao carregar dados", {
            description: "Dados do centro de custo não encontrados",
          });
          return;
        }

        const centroCusto = json.data;

        setDefaultValues({
          descricao: centroCusto.descricao || "",
          centro_de_custo_ascendenteId: centroCusto.centro_custo_ascdID || "",
          dotacao_orcamentista: centroCusto.dotacao_orcamentista || "",
          valor_dotacao: centroCusto.valor_dotacao || 0,
          nome_responsavel: centroCusto.nome_responsavel || "",
          contato: centroCusto.contato || "",
          email: centroCusto.email || "",
          cnpj: centroCusto.cnpj || "",
          razao_social: centroCusto.razao_social || "",
          endereco: {
            cep: centroCusto.endereco?.[0]?.cep || "",
            logradouro: centroCusto.endereco?.[0]?.logradouro || "",
            bairro: centroCusto.endereco?.[0]?.bairro || "",
            estado: centroCusto.endereco?.[0]?.estado || "",
            cidade: centroCusto.endereco?.[0]?.cidade || "",
          },
          centro_de_custo_tomadorId: centroCusto.centro_custo_tomadorID || "",
          active: centroCusto.ativo !== undefined ? centroCusto.ativo : true,
          veiculos_com_rfid: centroCusto.veiculos_com_rfid || null,
          abertura_os: centroCusto.abertura_os || null,
        });
      } catch (error) {
        console.error("Erro ao buscar centro de custo:", error);
        toast("Erro ao carregar dados", {
          description: "Erro interno do servidor",
        });
      }
    };

    if (id) {
      getCentroCusto();
    }
  }, [id]);

  async function onSubmit(values: z.infer<typeof centroDeCustoSchema>) {
    // Validação de campos obrigatórios
    const requiredFields = [
      { field: 'descricao', label: 'Descrição' },
      { field: 'dotacao_orcamentista', label: 'Dotação Orçamentista' },
      { field: 'valor_dotacao', label: 'Valor Dotação' },
      { field: 'nome_responsavel', label: 'Nome Responsável' },
      { field: 'contato', label: 'Contato' },
      { field: 'email', label: 'Email' },
      { field: 'cnpj', label: 'CNPJ' },
      { field: 'razao_social', label: 'Razão Social' },
    ];

    for (const { field, label } of requiredFields) {
      if (!values[field as keyof typeof values]) {
        toast("Preencha todos os campos obrigatórios", {
          description: `Campo vazio: ${label}`,
        });
        return;
      }
    }

    try {
      // Usar a nova API do BFF
      const response = await fetch(`/api/centro-de-custo/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        toast("Erro ao editar centro de custo", {
          description: result.message || "Não foi possível editar o centro de custo",
        });
        return;
      }

      toast("Centro de custo editado com sucesso!", {
        description: "As alterações foram salvas",
      });

      // Atualizar o contexto se necessário
      if (setCentrosDeCusto && result.data) {
        setCentrosDeCusto(prev =>
          prev.map(centro =>
            centro.id === id ? result.data : centro
          )
        );
      }

      // Redirecionar
      router.push("/dashboard/centro-de-custo/centros-custo");
    } catch (error) {
      console.error("Erro ao editar centro de custo:", error);
      toast("Erro ao editar centro de custo", {
        description: "Erro interno do servidor",
      });
    }
  }

  return (
    <div className="p-4">
      <PageForm
        submitButtonText="Editar"
        onSubmit={onSubmit}
        schema={centroDeCustoSchema}
        defaultValues={defaultValues as any}
      >
        <GenericFormsInput
          fieldConfig={GetCentroDeCustoConfig(
            [centrosDeCusto, centrosDeCusto],
            ["descricao", "descricao"]
          )}
        />
      </PageForm>
    </div>
  );
}
