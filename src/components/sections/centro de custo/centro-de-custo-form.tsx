"use client";

import { defaultValues } from "@/components/forms/defaultValues";
import { GetCentroDeCustoConfig } from "@/components/forms/inputConfig";
import { PageForm } from "@/components/forms/page-form";
import { centroDeCustoSchema } from "@/components/forms/schemas";
import { GenericFormsInput } from "@/components/inputs/generic-forms-input";
import { Button } from "@/components/ui/button";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { z } from "zod";

export function CentroDeCustoForm() {
  const { centrosDeCusto, setCentrosDeCusto } = useCentroDeCusto();
  const router = useRouter();

  const undoCentroDeCusto = async (id: string) => {
    try {
      const res = await fetch(`/api/centro-de-custo/${id}`, {
        method: "DELETE",
      });

      const data = await res.json();

      if (!res.ok || !data.success) {
        toast("Erro ao reverter", {
          description: data.message || "Falha ao remover o centro de custo",
        });
        return;
      }

      setCentrosDeCusto((prev) => prev.filter((centro) => centro.id !== id));
      toast("Centro de custo removido", {
        description: data.message || "A ação foi revertida com sucesso",
      });
    } catch (error) {
      console.error("Erro ao reverter centro de custo:", error);
      toast("Erro ao reverter", {
        description: "Erro interno do servidor",
      });
    }
  };

  async function onSubmit(values: z.infer<typeof centroDeCustoSchema>) {
    if (!values.descricao) {
      toast("Preencha todos os campos obrigatórios", {
        description: "Campo vazio: Descrição",
      });
      return;
    }

    if (!values.dotacao_orcamentista) {
      toast("Preencha todos os campos obrigatórios", {
        description: "Campo vazio: Dotação Orçamentista",
      });
      return;
    }

    if (!values.valor_dotacao) {
      toast("Preencha todos os campos obrigatórios", {
        description: "Campo vazio: Valor Dotação",
      });
      return;
    }

    if (!values.nome_responsavel) {
      toast("Preencha todos os campos obrigatórios", {
        description: "Campo vazio: Nome Responsável",
      });
      return;
    }

    if (!values.contato) {
      toast("Preencha todos os campos obrigatórios", {
        description: "Campo vazio: Contato",
      });
      return;
    }

    if (!values.email) {
      toast("Preencha todos os campos obrigatórios", {
        description: "Campo vazio: Email",
      });
      return;
    }

    if (!values.cnpj) {
      toast("Preencha todos os campos obrigatórios", {
        description: "Campo vazio: CNPJ",
      });
      return;
    }

    if (!values.razao_social) {
      toast("Preencha todos os campos obrigatórios", {
        description: "Campo vazio: Razão Social",
      });
      return;
    }

    try {
      const res = await fetch("/api/centro-de-custo", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      const data = await res.json();

      if (!res.ok || !data.success) {
        toast("Erro na criação", {
          description: data.message || "Falha ao registrar o centro de custo",
        });
        return;
      }

      // Atualizar o contexto com o novo centro de custo
      if (data.data) {
        setCentrosDeCusto((prev) => [...prev, data.data]);
      }

      toast("Centro de custo criado", {
        description: data.message || "Registro realizado com sucesso",
        action: (
          <Button
            variant="outline"
            onClick={async () => await undoCentroDeCusto(data.data.id)}
          >
            Desfazer
          </Button>
        ),
      });

      // Usar router.push em vez de window.location.href
      router.push("/dashboard/centro-de-custo/centros-custo");
    } catch (error) {
      console.error("Erro ao criar centro de custo:", error);
      toast("Erro na criação", {
        description: "Erro interno do servidor",
      });
    }
  }

  return (
    <div className="p-4">
      <PageForm
        onSubmit={onSubmit}
        schema={centroDeCustoSchema}
        defaultValues={defaultValues.centroDeCusto as any}
      >
        <GenericFormsInput
          fieldConfig={GetCentroDeCustoConfig(
            [centrosDeCusto, centrosDeCusto],
            ["descricao", "descricao"]
          )}
        />
      </PageForm>
    </div>
  );
}
