"use client";

import { useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/tables/data-table";
import { ChevronDown, ChevronRight, Edit } from "lucide-react";
import { TableCell, TableRow } from "@/components/ui/table";

interface CentroCustoAccordionTableProps {
  data: centro_de_custo[];
  columns: ColumnDef<centro_de_custo>[];
  onClick: () => void;
  exportTo?: boolean;
  showReportButton?: boolean;
  reportType?: "credenciados" | "condutores" | "veiculos" | "os" | "centro-custo" | "resumo-financeiro";
}

export function CentroCustoAccordionTable({
  data,
  columns,
  onClick,
  exportTo,
  showReportButton,
  reportType,
}: CentroCustoAccordionTableProps) {
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});

  const toggleRowExpansion = (id: string) => {
    setExpandedRows((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  return (
    <div className="w-full">
      <DataTable
        data={data}
        columns={[
          // Coluna de expansão
          {
            id: "expand",
            header: "",
            cell: ({ row }) => {
              const isExpanded = expandedRows[row.original.id];
              const hasChildren = row.original.centro_custos_filhos?.length > 0;

              return hasChildren ? (
                <button
                  onClick={() => toggleRowExpansion(row.original.id)}
                  className="p-1 rounded hover:bg-gray-100"
                >
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </button>
              ) : null;
            },
          },
          // Colunas normais
          ...columns,
        ]}
        renderSubComponent={({ row }) => {
          const isExpanded = expandedRows[row.original.id];
          const children = row.original.centro_custos_filhos;

          if (!isExpanded || !children || children.length === 0) return null;

          return (
            <>
              {children.map((child: any) => (
                <TableRow key={child.id}>
                  {/* Célula vazia para a coluna de expansão */}
                  <TableCell className="pl-8">
                    <span className="text-gray-500">↳</span>
                  </TableCell>
                  {/* Células para cada coluna */}
                  <TableCell>{child.descricao}</TableCell>
                  <TableCell>{child.contato || "N/A"}</TableCell>
                  <TableCell>{child.email || "N/A"}</TableCell>
                  <TableCell>
                    {child.nome_responsavel || "Não informado"}
                  </TableCell>
                  <TableCell>
                    {" "}
                    <Edit
                      className="mr-2 h-4 w-4"
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        (window.location.href = `/dashboard/centro-de-custo/editar/${child.id}`)
                      }
                    />
                  </TableCell>
                </TableRow>
              ))}
            </>
          );
        }}
        onClick={onClick}
        exportTo={exportTo}
        showReportButton={showReportButton}
        reportType={reportType}
      />
    </div>
  );
}
