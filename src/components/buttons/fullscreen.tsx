"use client";

import { Expand, Shrink } from "lucide-react";
import { useState } from "react";
import { Button } from "../ui/button";

export const Fullscreen = () => {
  const [isFullscreen, setIsFullscreen] = useState(false);

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().then(() => {
        setIsFullscreen(true);
      });
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen().then(() => {
          setIsFullscreen(false);
        });
      }
    }
  };

  return (
    <Button onClick={toggleFullscreen} size={"icon"} variant={"ghost"}>
      {isFullscreen ? <Shrink /> : <Expand />}
    </Button>
  );
};
