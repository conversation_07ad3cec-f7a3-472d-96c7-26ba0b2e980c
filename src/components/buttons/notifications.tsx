"use client";

import { useState, useRef } from "react";
import { Bell } from "lucide-react";
import { Button } from "../ui/button";
import { useTheme } from "next-themes";
import ModalAlertasRevisao from "../modal/modal.alertas-revisao";

interface NotificationsProps {
  roles: string[];
  maintenances: any[];
  budgets: Orcamento[];
}

const ORCAMENTISTA = "ORCAMENTISTA_OFICINA";
const ADMIN = "ADMIN";
const GESTOR = "GESTOR_FROTA";

const getOrcamentoNotifications = (budgets: Orcamento[], isOrcamentista: boolean) =>
  isOrcamentista ? budgets.filter(b => b.status === "lançada") : [];

const getMaintenanceNotifications = (maintenances: any[], isGestorOuAdmin: boolean) =>
  isGestorOuAdmin ? maintenances : [];

export const Notifications = ({ roles, maintenances, budgets }: NotificationsProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const anchorRef = useRef<HTMLDivElement>(null);
  const { resolvedTheme } = useTheme();

  const isOrcamentista = roles.includes(ORCAMENTISTA);
  const isGestorOuAdmin = roles.includes(ADMIN) || roles.includes(GESTOR);

  const osNotifications = getOrcamentoNotifications(budgets, isOrcamentista);
  const manutencaoNotifications = getMaintenanceNotifications(maintenances, isGestorOuAdmin);
  const totalNotifications = osNotifications.length + manutencaoNotifications.length;

  if (!isOrcamentista && !isGestorOuAdmin) return null;

  return (
    <div className="relative" ref={anchorRef}>
      <Button
        onClick={() => setIsOpen(true)}
        size="icon"
        variant="ghost"
        aria-label={`Notificações (${totalNotifications} novas)`}
        className="relative"
      >
        <Bell
          className={`h-[1.2rem] w-[1.2rem] transition-colors ${
            resolvedTheme === "dark" ? "text-white" : "text-black"
          }`}
        />
        {totalNotifications > 0 && (
          <span
            role="status"
            aria-live="polite"
            className="absolute -top-1 -right-1 bg-red-600 text-white text-xs font-bold w-5 h-5 rounded-full flex items-center justify-center"
          >
            {totalNotifications}
          </span>
        )}
      </Button>

      <ModalAlertasRevisao
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        manutencoes={manutencaoNotifications}
        budgets={osNotifications}
        anchorRef={anchorRef}
      />
    </div>
  );
};
