"use client";
import React, { useEffect, useRef, useState } from "react";
import { LatLngExpression } from "leaflet";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ile<PERSON>ayer, useMap } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import L from "leaflet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { TooltipWrapper } from "@/components/cards/tooltip";
import "leaflet-routing-machine";
import "leaflet-routing-machine/dist/leaflet-routing-machine.css";
import { useRouter } from "next/navigation";
import { getAddressFromCoordinates } from "@/lib/address";

const credenciadoIcon = L.divIcon({
  html: `
    <svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="20px" height="20px" viewBox="0 0 256 256">
<g transform="translate(1.4 1.4) scale(2.81 2.90)">
<path d="M 45 90 c -1.415 0 -2.725 -0.748 -3.444 -1.966 l -4.385 -7.417 C 28.167 65.396 19.664 51.02 16.759 45.189 c -2.112 -4.331 -3.175 -8.955 -3.175 -13.773 C 13.584 14.093 27.677 0 45 0 c 17.323 0 31.416 14.093 31.416 31.416 c 0 4.815 -1.063 9.438 -3.157 13.741 c -0.025 0.052 -0.053 0.104 -0.08 0.155 c -2.961 5.909 -11.41 20.193 -20.353 35.309 l -4.382 7.413 C 47.725 89.252 46.415 90 45 90 z" fill="rgb(4,136,219)"/>
<path d="M 45 45.678 c -8.474 0 -15.369 -6.894 -15.369 -15.368 S 36.526 14.941 45 14.941 c 8.474 0 15.368 6.895 15.368 15.369 S 53.474 45.678 45 45.678 z" fill="rgb(255,255,255)"/>
</g>
</svg>
  `,
  className: "",
  iconSize: [32, 32],
});

const yourIcon = L.divIcon({
  html: `
    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="36px" height="36px" viewBox="0 0 24 24" version="1.1">
    <title>ic_fluent_my_location_24_regular</title>
    <desc>Created with Sketch.</desc>
    <g id="🔍-Product-Icons" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="ic_fluent_my_location_24_regular" fill="#212121" fill-rule="nonzero">
            <path fill="#0000FF" d="M12,2 C12.3796958,2 12.693491,2.28215388 12.7431534,2.64822944 L12.75,2.75 L12.7490685,4.53770881 L12.7490685,4.53770881 C16.292814,4.******** 19.1124257,7.******** 19.4632195,11.2525316 L19.5,11.25 L21.25,11.25 C21.6642136,11.25 22,11.5857864 22,12 C22,12.3796958 21.7178461,12.693491 21.3517706,12.7431534 L21.25,12.75 L19.4616558,12.7490368 L19.4616558,12.7490368 C19.1124257,16.292814 16.292814,19.1124257 12.7474684,19.4632195 L12.75,19.5 L12.75,21.25 C12.75,21.6642136 12.4142136,22 12,22 C11.6203042,22 11.306509,21.7178461 11.2568466,21.3517706 L11.25,21.25 L11.2509632,19.4616558 L11.2509632,19.4616558 C7.********,19.1124257 4.********,16.292814 4.********,12.7474684 L4.5,12.75 L2.75,12.75 C2.********,12.75 2,12.4142136 2,12 C2,11.6203042 2.28215388,11.306509 2.64822944,11.2568466 L2.75,11.25 L4.53770881,11.2509315 L4.53770881,11.2509315 C4.********,7.******** 7.********,4.******** 11.2525316,4.******** L11.25,4.5 L11.25,2.75 C11.25,2.******** 11.5857864,2 12,2 Z M12,6 C8.6862915,6 6,8.6862915 6,12 C6,15.3137085 8.6862915,18 12,18 C15.3137085,18 18,15.3137085 18,12 C18,8.6862915 15.3137085,6 12,6 Z M12,8 C14.209139,8 16,9.790861 16,12 C16,14.209139 14.209139,16 12,16 C9.790861,16 8,14.209139 8,12 C8,9.790861 9.790861,8 12,8 Z" id="🎨-Color">

</path>
        </g>
    </g>
</svg>
  `,
  className: "",
  iconSize: [32, 32],
});

const Routing = ({
  from,
  to,
}: {
  from: LatLngExpression;
  to: LatLngExpression;
}) => {
  const map = useMap();
  const routingControlRef = useRef<L.Routing.Control | null>(null);

  useEffect(() => {
    if (routingControlRef.current) {
      map.removeControl(routingControlRef.current);
    }

    const routingControl = L.Routing.control({
      waypoints: [L.latLng(from), L.latLng(to)],
      createMarker: () => null,
    } as CustomRoutingOptions).addTo(map);

    routingControlRef.current = routingControl;
  }, [from, to, map]);

  return null;
};

export const Mapa = ({ informacoesCredenciados }: Params) => {
  const [location, setLocation] = useState<LatLngExpression>([
    -15.793889, -47.882778,
  ]);
  const [hasPermission, setPermission] = useState(false);
  const [destination, setDestination] = useState<LatLngExpression | null>(null);

  const router = useRouter();

  const getLocation = () => {
    navigator.geolocation.getCurrentPosition(
      ({ coords: { latitude, longitude } }) =>
        setLocation([latitude, longitude]),
      (err) => console.log(err)
    );
  };

  const checkPermission = () => {
    if (navigator.permissions) getLocation();
  };

  useEffect(checkPermission, []);

  const askForPermission = () => {
    navigator.permissions.query({ name: "geolocation" }).then((result) => {
      setPermission(true);
      if (result.state === "granted") getLocation();
      else if (result.state === "prompt") getLocation();
    });
  };

  const startRoute = async (
    latitude: string | undefined,
    longitude: string | undefined
  ) => {
    const currentDate = new Date();
    const [currentLatitude, currentLongitude] = location as [number, number];

    const destinationAddress = await getAddressFromCoordinates(
      latitude,
      longitude
    );
    const currentLocationAddress = await getAddressFromCoordinates(
      String(currentLatitude),
      String(currentLongitude)
    );

    const queryParams = new URLSearchParams({
      departureDate: currentDate.toISOString(),
      placeOrigin: currentLocationAddress.road,
      destinationLocation: destinationAddress.road,
    });

    if (latitude && longitude) {
      setDestination([parseFloat(latitude), parseFloat(longitude)]);
      // setTimeout(() => {
      //   router.push(
      //     `/dashboard/percursos/novo-percurso?${queryParams.toString()}`
      //   );
      // }, 3000);
      return;
    }

    console.warn("Latitude ou longitude não estão disponíveis");
  };

  return (
    <div className="w-full h-full">
      {!hasPermission && (
        <TooltipWrapper message="Para melhor experiência do usuário é recomendado que conceda permissão para acesso a sua localização.">
          <Button
            onClick={askForPermission}
            className="absolute top-0 right-0 m-4"
          >
            <b>Permitir acesso a minha localização</b>
          </Button>
        </TooltipWrapper>
      )}
      <MapContainer
        center={location}
        zoom={6}
        scrollWheelZoom
        style={{ height: "100vh" }}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        <Marker position={location} icon={yourIcon}>
          <Popup>
            <b>Você está aqui!</b>
          </Popup>
        </Marker>
        {informacoesCredenciados.map(
          (credenciado, index) =>
            credenciado.endereco.latitude &&
            credenciado.endereco.longitude && (
              <Marker
                key={`${credenciado.endereco.latitude}-${credenciado.endereco.longitude}-${index}`}
                position={[
                  parseFloat(credenciado.endereco.latitude),
                  parseFloat(credenciado.endereco.longitude),
                ]}
                icon={credenciadoIcon}
              >
                <Popup>
                  <b>{credenciado.informacoesAdicionais.razao_social}</b>
                  <br />
                  <b>{credenciado.informacoesAdicionais.email}</b>
                  <br />
                  <b>{credenciado.informacoesAdicionais.celular}</b>
                  <br />
                  <b>{credenciado.endereco.logradouro}</b>
                  <br />
                  <b>{credenciado.endereco.cep}</b>
                  <br />
                  <b>{credenciado.endereco.cidade}</b>
                  <br />
                  <b>{credenciado.endereco.estado}</b>
                  <br />
                  <br />
                  <Button
                    onClick={() =>
                      startRoute(
                        credenciado.endereco.latitude,
                        credenciado.endereco.longitude
                      )
                    }
                  >
                    Iniciar Percurso
                  </Button>
                </Popup>
              </Marker>
            )
        )}
        {destination && <Routing from={location} to={destination} />}
      </MapContainer>
    </div>
  );
};

type Params = {
  informacoesCredenciados: {
    endereco: endereco_do_credenciado;
    informacoesAdicionais: informacoes_do_credenciado & contatos_do_credenciado;
  }[];
};

interface CustomRoutingOptions extends L.Routing.RoutingControlOptions {
  createMarker?: () => L.Marker | null;
}
