"use client";

import { useEffect, useState } from "react";
import { Button } from "../ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Image } from "lucide-react";
import { useContrato } from "@/context/contrato-context";
import { switchDatabase } from "@/serverActions/switchDatabaseAction";
import { getServerSession } from "@/lib/auth/server-session";
import { useCredenciado } from "@/context/credenciado-context";

export default function CentroDeCustoCard() {
  const { credenciados } = useCredenciado();
  const { contratos } = useContrato();
  const [credenciadoData, setCredenciadoData] = useState<any>(null);
  const [selectedContrato, setSelectedContrato] = useState<contrato | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [session, setSession] = useState<any>(null);
  const [contratosData, setContratosData] = useState<any>([]);
  useEffect(() => {
    const fetchSessionAndContrato = async () => {
      const sessionData = await getServerSession();
      setSession(sessionData);

      if (sessionData?.contrato) {
        setSelectedContrato(sessionData.contrato);
      }

      if (sessionData?.contratos) {
        if (sessionData.roles.includes("ADMIN")) {
          setContratosData(contratos);
        } else {
          setContratosData(sessionData.contratos);
          if (sessionData?.credenciadoId && credenciados.length > 0) {
            const credenciado = credenciados.find(
              (credenciado) => credenciado.id === sessionData.credenciadoId
            );
            if (credenciado) {
              setCredenciadoData(credenciado);
            }
          }
        }
      }
    };

    fetchSessionAndContrato();
  }, [credenciados, contratos]);

  const hasPermission = (requiredRoles: string[]) => {
    const userRoles = session?.roles || [];
    return requiredRoles.some((role) => userRoles.includes(role));
  };

  const handleSelectContrato = async (contrato: contrato) => {
    setLoading(true);
    setError(null);

    try {
      const result = await switchDatabase(contrato.id);

      if (result.success) {
        await fetch("/api/auth/refresh");
        window.location.reload();
      } else {
        setError(result.error || "Erro ao trocar o banco de dados");
      }
    } catch (err) {
      setError("Erro ao trocar o banco de dados");
    } finally {
      setLoading(false);
    }
  };

  const fullPath = selectedContrato?.logotipo;
  const uploadsPath = fullPath?.replace(/^.*?(\/uploads\/.*)$/, "$1");
  const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL
    ? process.env.NEXT_PUBLIC_API_BASE_URL.replace(/\/api$/, "")
    : "https://api.sistema.grupocarletto.net.br";
  const imagePath = `${baseUrl}${uploadsPath}`;
  const isAdmin = hasPermission(["ADMIN"]);
  const isCredenciado = hasPermission(["ORCAMENTISTA_OFICINA"]);

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-auto p-0 hover:bg-transparent">
            <Avatar>
              <AvatarImage src={imagePath} alt="imagem do contratado selecionado" />
              <AvatarFallback>
                <Image />
              </AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel key="label">Escolha um Contrato</DropdownMenuLabel>
          <DropdownMenuSeparator key="separator" />
          {contratosData.length === 0 && (
            <DropdownMenuLabel key="empty-label" className="text-semibold">
              Sem contratos cadastrados
            </DropdownMenuLabel>
          )}
          {contratosData.map((contrato: any) => (
            <DropdownMenuItem
              key={contrato.id}
              onClick={() => handleSelectContrato(contrato)}
              className="cursor-pointer">
              {contrato.nome_contrato}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
        {loading && <p>Carregando...</p>}
        {error && <p className="text-red-500">{error}</p>}
      </DropdownMenu>
    </>
  );
}
