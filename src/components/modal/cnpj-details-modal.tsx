import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "../ui/dialog";
import { But<PERSON> } from "../ui/button";
import { CnpjData } from "@/interfaces/cnpj.interface";
import { Separator } from "@/components/ui/separator";
import React from "react";

interface CnpjDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  data: CnpjData;
}

export function CnpjDetailsModal({ isOpen, onClose, onConfirm, data }: CnpjDetailsModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose} >
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Detalhes da Empresa</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="font-semibold">Informações Gerais</h3>
              <p>Razão Social: {data.company.name}</p>
              <p>CNPJ: {data.taxId}</p>
              <p>Nome Fantasia: {data.alias}</p>
              <p>Data de Abertura: {data.founded}</p>
            </div>

            <div className="space-y-2">
              <h3 className="font-semibold">Situação</h3>
              <p>Situação Cadastral: {data.status.text}</p>
              <p>
                Capital Social:{" "}
                {data.company.equity != null
                  ? data.company.equity.toLocaleString("pt-BR", {
                      style: "currency",
                      currency: "BRL",
                    })
                  : "N/A"}
              </p>
              <p>Situação Atual: {data.company.nature.text}</p>
              <p>
                Natureza: {data.company.size.text} - {data.company.size.acronym}
              </p>
            </div>
          </div>
          <Separator />

          <div className="space-y-2">
            <h3 className="font-semibold">Endereço</h3>
            <p>
              {data.address.street}, {data.address.number}
            </p>
            <p>
              {data.address.district} - {data.address.city}/{data.address.state}
            </p>
            <p>CEP: {data.address.zip}</p>
          </div>
          <Separator />

          <div className="space-y-2">
            <h3 className="font-semibold">Contato</h3>
            {data.phones.map((phone, index) => (
              <p key={index}>
                Telefone: ({phone.area}) {phone.number}
              </p>
            ))}
            {data.emails.map((email, index) => (
              <p key={index}>Email: {email.address}</p>
            ))}
          </div>
          <Separator />
          <div className="space-y-2">
            <h3 className="font-semibold">Atividades</h3>
            <p>
              Atividade Principal (CNAE): <br /> {data.mainActivity.id} - {data.mainActivity.text}
            </p>
            <p>
              Atividades Secundárias (CNAE):
              {data.sideActivities.slice(0, 3).map((activity, index) => (
              <React.Fragment key={index}>
                <br />
                <span>
                {activity.id} - {activity.text}
                </span>
              </React.Fragment>
              ))}
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button onClick={onConfirm}>Confirmar e Preencher</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
