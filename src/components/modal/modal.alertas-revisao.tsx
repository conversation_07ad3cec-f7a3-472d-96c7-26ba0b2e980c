"use client";

import { useEffect, useState } from "react";
import { Car } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useSession } from "../session/use-session";

export function calcularKmRestantes(odometroAtual: number, kmManutencao: number): number {
  const odometro = Number(odometroAtual) || 0;
  const kmAlvo = Number(kmManutencao) || 0;
  return Math.max(0, kmAlvo - odometro);
}

interface manutencao {
  id: string;
  km: number;
  veiculo: {
    placa: string;
    odometro_atual: number;
    lotacao_veiculos: lotacao_do_veiculo;
  };
}

interface ModalAlertasRevisaoProps {
  isOpen: boolean;
  onClose: () => void;
  manutencoes: manutencao[];
  budgets?: Orcamento[];
  anchorRef?: React.RefObject<HTMLDivElement | null>;
}

export default function ModalAlertasRevisao({
  isOpen,
  onClose,
  manutencoes,
  budgets = [],
  anchorRef,
}: ModalAlertasRevisaoProps) {
  const { session } = useSession();
  const [modalPosition, setModalPosition] = useState({ top: 0, left: 0 });

  useEffect(() => {
    function updatePosition() {
      if (isOpen && anchorRef?.current) {
        const rect = anchorRef.current.getBoundingClientRect();

        setModalPosition({
          top: rect.bottom + 8 + window.scrollY,
          left: rect.left + window.scrollX - 50,
        });
      }
    }

    updatePosition();
    window.addEventListener("resize", updatePosition);
    window.addEventListener("scroll", updatePosition);

    return () => {
      window.removeEventListener("resize", updatePosition);
      window.removeEventListener("scroll", updatePosition);
    };
  }, [isOpen, anchorRef]);

  let manutencaoData: manutencao[] = [];
  if (session && !session.roles.includes("ADMIN") && session.centro_de_custoId) {
    if (session.unidade_filha_id) {
      manutencaoData = manutencoes.filter(
        (m) => m.veiculo.lotacao_veiculos?.centro_custoID === session.unidade_filha_id
      );
    } else {
      manutencaoData = manutencoes.filter(
        (m) =>
          m.veiculo.lotacao_veiculos?.centro_custo?.centro_custo_ascdID ===
          session.centro_de_custoId
      );
    }
  } else {
    manutencaoData = manutencoes;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="w-80 sm:max-w-md"
        style={{
          position: "absolute",
          top: modalPosition.top,
          left: modalPosition.left,
          marginTop: 120,
          zIndex: 1000,
        }}
      >

        <DialogHeader>
          <DialogTitle className="text-red-700">Notificações</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {budgets.length > 0 && (
            <div>
              <p className="text-sm font-semibold">Orçamentos</p>
              <ul className="mt-1 space-y-2">
                {budgets.map((orc, idx) => (
                  <li key={orc.id} className="text-sm text-muted-foreground pb-2">
                    Você recebeu um novo orçamento número: <strong>{orc.numeroOrcamento}</strong>
                    {idx !== budgets.length - 1 && <hr className="my-2 border-gray-300" />}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {manutencaoData.length > 0 && (
            <div>
              <p className="text-sm font-semibold mt-4">Revisões por KM</p>
              <ul className="mt-1 space-y-2">
                {manutencaoData.map((m) => {
                  const kmRestantes = calcularKmRestantes(
                    m.veiculo.odometro_atual,
                    m.km
                  );
                  return (
                    <li
                      key={m.id}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-accent transition-colors"
                    >
                      <Car
                        className="text-green-600 flex-shrink-0"
                        size={18}
                      />
                      <div className="flex-1">
                        <p className="font-medium text-sm">
                          Veículo {m.veiculo.placa}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {kmRestantes === 0
                            ? "Revisão necessária agora"
                            : `Revisão em ${kmRestantes.toLocaleString()} km`}
                        </p>
                      </div>
                    </li>
                  );
                })}
              </ul>
            </div>
          )}

          {budgets.length === 0 && manutencaoData.length === 0 && (
            <p className="text-sm text-muted-foreground text-center py-4">
              Sem notificações
            </p>
          )}
        </div>

        <div className="flex justify-end mt-4">
          <Button variant="outline" onClick={onClose}>
            Fechar
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
