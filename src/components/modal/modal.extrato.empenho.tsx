import { ColumnDef } from "@tanstack/react-table";
import { Dialog, DialogContent, DialogTitle } from "../ui/dialog";
import { DataTable } from "../tables/data-table";
import { extratoEmpenhoMock } from "./extratoEmpenho";

interface Extrato {
  data: string;
  tipo: string;
  observacao: string;
  valor: number;
}

const columns: ColumnDef<Extrato>[] = [
  {
    accessorKey: "data",
    header: "Data",
    cell: ({ row }) => row.original.data,
  },
  {
    accessorKey: "tipo",
    header: "Tipo de lançamento",
    cell: ({ row }) => row.original.tipo,
  },
  {
    accessorKey: "observacao",
    header: "Observação",
    cell: ({ row }) => (
      <div className="whitespace-pre-wrap text-sm text-muted-foreground">
        {row.original.observacao}
      </div>
    ),
  },
  {
    accessorKey: "valor",
    header: "Valor",
    cell: ({ row }) => (
      <span
        className={`text-sm font-semibold ${
          row.original.valor < 0 ? "text-red-600" : ""
        }`}
      >
        {row.original.valor.toLocaleString("pt-BR", {
          style: "currency",
          currency: "BRL",
        })}
      </span>
    ),
  },
];

export default function ModalExtratoEmpenho({
  id,
  onClose,
}: {
  id: string;
  onClose?: () => void;
}) {
  console.log({ extratoEmpenhoMock });
  return (
    <Dialog open onOpenChange={(open) => !open && onClose?.()}>
      <DialogContent className="min-w-[90%] h-[70%]">
        <DialogTitle>Extrato do Empenho</DialogTitle>
        <div className="p-4 overflow-y-auto">
          <DataTable data={extratoEmpenhoMock} columns={columns} exportTo />
        </div>
      </DialogContent>
    </Dialog>
  );
}
