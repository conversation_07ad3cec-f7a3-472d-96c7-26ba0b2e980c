"use client";
import { useState } from "react";
import { DialogContent } from "../ui/dialog";
import api from "@/service/api";

export default function ModalOS({
  tabs,
  modal,
}: {
  tabs: string[];
  modal: { id: string; status: string };
}) {
  const [status, setStatus] = useState(modal.status);

  const handleChangeStatus = async (newStatus: string) => {
    try {
      const token = localStorage.getItem("token");

      const dataOs = { status: newStatus };

      await api.put(`/os/${modal.id}`, dataOs, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      setStatus(newStatus);

      console.log("Status alterado com sucesso");
    } catch (err) {
      console.error("Erro geral:", err);
    }
  };

  return (
    <DialogContent>
      {tabs.map((tab, index) => {
        return (
          <button
            key={index}
            className={`w-full py-2 px-4 text-left ${
              status.toLowerCase() === tab.toLowerCase()
                ? "border-black border-2"
                : ""
            }`}
            onClick={() => handleChangeStatus(tab)}
          >
            {tab}
          </button>
        );
      })}
    </DialogContent>
  );
}
