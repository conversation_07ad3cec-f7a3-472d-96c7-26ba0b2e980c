"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Loader2Icon, PrinterIcon } from "lucide-react";

interface PDFExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (options: PDFExportOptions) => void;
  summary: {
    totalOrcamentos: number;
    totalPecas: number;
    totalServicos: number;
    totalGeral: number;
  };
}

export interface PDFExportOptions {
  includeHeader: boolean;
  includeSummary: boolean;
  includeDetails: boolean;
  includeFooter: boolean;
  orientation: "portrait" | "landscape";
}

export function PDFExportModal({ isOpen, onClose, onExport, summary }: PDFExportModalProps) {
  const [options, setOptions] = useState<PDFExportOptions>({
    includeHeader: true,
    includeSummary: true,
    includeDetails: true,
    includeFooter: true,
    orientation: "portrait",
  });
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = () => {
    setIsExporting(true);

    // Add a small delay to show the loading state
    setTimeout(() => {
      onExport(options);
      setIsExporting(false);
      onClose();
    }, 500);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value / 100);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <PrinterIcon className="h-5 w-5" />
            Exportar Relatório
          </DialogTitle>
          <DialogDescription>Configure as opções do relatório antes de exportar</DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Preview Summary */}
          <div className="bg-muted/50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">Resumo do Relatório</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                Total de Orçamentos: <span className="font-medium">{summary.totalOrcamentos}</span>
              </div>
              <div>
                Total Peças:{" "}
                <span className="font-medium">{formatCurrency(summary.totalPecas)}</span>
              </div>
              <div>
                Total Serviços:{" "}
                <span className="font-medium">{formatCurrency(summary.totalServicos)}</span>
              </div>
              <div className="col-span-2 pt-1 border-t">
                Total Geral: <span className="font-bold">{formatCurrency(summary.totalGeral)}</span>
              </div>
            </div>
          </div>

          {/* Export Options */}
          <div className="space-y-4">
            <h4 className="font-medium">Opções de Exportação</h4>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeHeader"
                  checked={options.includeHeader}
                  onCheckedChange={(checked) =>
                    setOptions((prev) => ({ ...prev, includeHeader: checked as boolean }))
                  }
                />
                <Label htmlFor="includeHeader">Incluir cabeçalho com informações da empresa</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeSummary"
                  checked={options.includeSummary}
                  onCheckedChange={(checked) =>
                    setOptions((prev) => ({ ...prev, includeSummary: checked as boolean }))
                  }
                />
                <Label htmlFor="includeSummary">Incluir resumo financeiro</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeDetails"
                  checked={options.includeDetails}
                  onCheckedChange={(checked) =>
                    setOptions((prev) => ({ ...prev, includeDetails: checked as boolean }))
                  }
                />
                <Label htmlFor="includeDetails">Incluir tabela detalhada</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeFooter"
                  checked={options.includeFooter}
                  onCheckedChange={(checked) =>
                    setOptions((prev) => ({ ...prev, includeFooter: checked as boolean }))
                  }
                />
                <Label htmlFor="includeFooter">Incluir rodapé</Label>
              </div>
            </div>

            <Separator />

            <div className="space-y-3">
              <Label className="text-sm font-medium">Orientação da Página</Label>
              <div className="flex gap-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="portrait"
                    name="orientation"
                    value="portrait"
                    checked={options.orientation === "portrait"}
                    onChange={(e) =>
                      setOptions((prev) => ({
                        ...prev,
                        orientation: e.target.value as "portrait" | "landscape",
                      }))
                    }
                  />
                  <Label htmlFor="portrait">Retrato</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="landscape"
                    name="orientation"
                    value="landscape"
                    checked={options.orientation === "landscape"}
                    onChange={(e) =>
                      setOptions((prev) => ({
                        ...prev,
                        orientation: e.target.value as "portrait" | "landscape",
                      }))
                    }
                  />
                  <Label htmlFor="landscape">Paisagem</Label>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <Button variant="outline" onClick={onClose} disabled={isExporting}>
              Cancelar
            </Button>
            <Button onClick={handleExport} disabled={isExporting} className="gap-2">
              {isExporting ? (
                <>
                  <Loader2Icon className="h-4 w-4 animate-spin" />
                  Gerando relatório...
                </>
              ) : (
                <>
                  <PrinterIcon className="h-4 w-4" />
                  Gerar Relatório
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
