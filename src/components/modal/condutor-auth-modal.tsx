"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { validateOrCreateCondutorPassword } from "@/serverActions/condutorAction";
import { updateOrcamentoStatus, updateOsStatus } from "@/serverActions/orcamentoAction";
import { useState } from "react";
import { toast } from "sonner";

interface ConductorAuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (matricula: string, senha: string) => Promise<void>;
  title?: string;
  description?: string;
  buttonText?: string;
  hasPassword?: boolean;
  osCondutor?: condutor;
}

export function ConductorAuthModal({
  isOpen,
  onClose,
  onSuccess,
  title = "Credenciais do Condutor",
  description = "Por favor, insira a matrícula e senha do condutor para continuar.",
  buttonText = "Confirmar",
  hasPassword = false,
  osCondutor,
}: ConductorAuthModalProps) {
  const [conductorMatricula, setConductorMatricula] = useState("");
  const [conductorSenha, setConductorSenha] = useState("");
  const [passwordAttempts, setPasswordAttempts] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleConductorSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Verifica se ambos os campos foram preenchidos
    if (!conductorMatricula || !conductorSenha) {
      toast.error("Preencha a matrícula e a senha para continuar.");
      return;
    }

    if (osCondutor?.matricula !== conductorMatricula) {
      toast.error("A matrícula informada não corresponde ao condutor da OS.");
      return;
    }

    setIsSubmitting(true);

    try {
      const validateCondutor = await validateOrCreateCondutorPassword(
        conductorMatricula,
        conductorSenha
      );
      if (!validateCondutor.success) {
        throw new Error("Credenciais inválidas.");
      }
      await onSuccess(conductorMatricula, conductorSenha);
      setPasswordAttempts(0);
      setConductorMatricula("");
      setConductorSenha("");

      onClose();
    } catch (error) {
      const newAttempts = passwordAttempts + 1;
      setPasswordAttempts(newAttempts);

      if (newAttempts >= 3) {
        toast.error(
          "Número máximo de tentativas excedido. Por favor, tente novamente mais tarde ou entre em contato com seu gestor.",
          { duration: 5000 }
        );
        setTimeout(() => {
          setPasswordAttempts(0);
          setConductorMatricula("");
          setConductorSenha("");
        }, 2000);
      } else {
        toast.error(`Erro ao validar as credenciais do condutor. Tentativa ${newAttempts} de 3.`, {
          duration: 3000,
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent onClick={(e) => e.stopPropagation()} onMouseDown={(e) => e.stopPropagation()}>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
          {!hasPassword && (
            <p className="mt-2 text-sm text-gray-500">
              Esta é a primeira autorização do condutor. A senha utilizada será salva como sua senha
              pessoal e intransferível.
            </p>
          )}
        </DialogHeader>

        <form
          onSubmit={(e) => {
            e.stopPropagation();
            handleConductorSubmit(e);
          }}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="matricula" className="text-right text-sm font-medium">
                Matrícula
              </label>
              <input
                id="matricula"
                type="text"
                value={conductorMatricula}
                onChange={(e) => {
                  e.stopPropagation();
                  setConductorMatricula(e.target.value);
                }}
                autoComplete="off"
                className="col-span-3 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                required
                disabled={isSubmitting}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="senha" className="text-right text-sm font-medium">
                Senha
              </label>
              <input
                id="senha"
                type="password"
                value={conductorSenha}
                onChange={(e) => {
                  e.stopPropagation();
                  setConductorSenha(e.target.value);
                }}
                autoComplete="off"
                className="col-span-3 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                required
                disabled={isSubmitting}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                onClose();
              }}
              disabled={isSubmitting}>
              Cancelar
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Processando..." : buttonText}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
