// mocks/extratoEmpenho.ts
export const extratoEmpenhoMock = [
  {
    data: "29/01/2025 18:47",
    tipo: "Implantação de saldo",
    observacao: "Implantação de saldos",
    valor: 19240.94,
  },
  {
    data: "29/01/2025 18:50",
    tipo: "Implantação de saldo",
    observacao: "Implantação de saldos",
    valor: 2280.3,
  },
  {
    data: "29/01/2025 18:52",
    tipo: "Implantação de saldo",
    observacao: "Implantação de saldos",
    valor: 21882.0,
  },
  {
    data: "11/02/2025 17:45",
    tipo: "Reforço de saldo",
    observacao:
      "Reforço de saldo: Peças: 6476.13 | Serviços: 0\nObservações: Inclusão de saldo conforme solicitado pelo gestor do contrato via whats 10/02",
    valor: 6476.13,
  },
  {
    data: "14/02/2025 13:06",
    tipo: "Valor bloqueado",
    observacao: "Valor bloqueado: Peças: 412.07 | Serviços: 384.33 | OS: #41",
    valor: -796.4,
  },
  {
    data: "19/02/2025 12:04",
    tipo: "Valor bloqueado",
    observacao: "Valor bloqueado: Peças: 412.07 | Serviços: 384.33 | OS: #51",
    valor: -796.4,
  },
  {
    data: "19/02/2025 12:30",
    tipo: "Valor bloqueado",
    observacao: "Valor bloqueado: Peças: 852.78 | Serviços: 0 | OS: #5",
    valor: -852.78,
  },
  {
    data: "19/02/2025 14:11",
    tipo: "Valor bloqueado",
    observacao: "Valor bloqueado: Peças: 2235.22 | Serviços: 540 | OS: #17",
    valor: -2775.22,
  },
  {
    data: "19/02/2025 14:11",
    tipo: "Valor bloqueado",
    observacao: "Valor bloqueado: Peças: 1580.38 | Serviços: 270 | OS: #18",
    valor: -1850.38,
  },
  {
    data: "21/02/2025 14:34",
    tipo: "Valor bloqueado",
    observacao: "Valor bloqueado: Peças: 752.64 | Serviços: 0 | OS: #86",
    valor: -752.64,
  },
  {
    data: "24/02/2025 13:09",
    tipo: "Valor bloqueado",
    observacao: "Valor bloqueado: Peças: 1511.56 | Serviços: 628.22 | OS: #87",
    valor: -2139.78,
  },
  {
    data: "24/02/2025 16:34",
    tipo: "Valor bloqueado",
    observacao: "Valor bloqueado: Peças: 2171.6 | Serviços: 155.62 | OS: #82",
    valor: -2327.22,
  },
];
