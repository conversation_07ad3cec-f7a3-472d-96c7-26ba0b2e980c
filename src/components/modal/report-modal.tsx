import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { FileText } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { addDays } from "date-fns";
import { DateRange } from "react-day-picker";
import { DateRangePicker } from "../ui/date-range-picker";
import { Checkbox } from "@/components/ui/checkbox";

interface ReportModalProps {
  onGenerateReport: (filters: ReportFilters) => void;
  availableFields: Array<{ value: string; label: string }>;
  reportType:
    | "os"
    | "veiculos"
    | "credenciados"
    | "condutores"
    | "centro-custo"
    | "resumo-financeiro";
}

export interface ReportFilters {
  dateRange: {
    from: Date;
    to: Date;
  };
  sortBy: string;
  selectedFields: string[]; // Campos selecionados para incluir no relatório
}

export function ReportModal({
  onGenerateReport,
  availableFields,
  reportType,
}: ReportModalProps) {
  const [open, setOpen] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange>({
    from: new Date(),
    to: addDays(new Date(), 30),
  });

  // Create a memoized filtered version of availableFields
  const filteredAvailableFields = React.useMemo(() => {
    if (reportType === "credenciados") {
      return availableFields.filter(
        (field) => field.value !== "endreco.cidade"
      );
    }
    return availableFields;
  }, [availableFields, reportType]);

  const [sortBy, setSortBy] = useState(filteredAvailableFields[0]?.value || "");
  const [selectedFields, setSelectedFields] = useState<string[]>([]);

  // Garantir que odômetro esteja disponível para veículos
  const enhancedFields = React.useMemo(() => {
    if (
      reportType === "veiculos" &&
      !filteredAvailableFields.some((f) => f.value === "odometro_atual")
    ) {
      return [...filteredAvailableFields];
    }
    return filteredAvailableFields;
  }, [filteredAvailableFields, reportType]);

  // Inicializar campos selecionados quando o modal abrir
  useEffect(() => {
    if (open) {
      const uniqueFields = filteredAvailableFields.filter(
        (field, index, self) =>
          index ===
          self.findIndex((f) =>
            f.label === "Endereço/Cidade" && field.label === "Endereço.cidade"
              ? false
              : f.value === field.value
          )
      );
      // Selecionar campos padrão com base no tipo de relatório
      let defaultFields: string[] = [];

      if (reportType === "veiculos") {
        // Campos principais para composição de frota por órgão/unidade contratante
        defaultFields = [
          "centro_de_custo", // Centro de custo/órgão contratante
          "ano_fab", // Ano de fabricação
          "tipo_de_veiculo", // Tipo de veículo
          "modelo", // Modelo do veículo
          "marca", // Marca
          "placa", // Placas
          "data_compra", // Data de compra
          "data_cedencia", // Data de cedência
          "valor_depreciacao", // Depreciação
          "valor_mercado", // Valor de mercado
          "condutor_principal", // Motorista
          "renovam", // Renavam (dados de identificação)
          "vin", // VIN/Chassi (dados de identificação)
          "numero_do_motor", // Número do motor (dados de identificação)
          "matricula", // Matrícula (dados de identificação)
          "odometro_atual", // Odômetro atual
          "status", // Status
        ];
      } else if (reportType === "os") {
        const camposDesejados = [
          "osNumber",
          "status",
          "centro_de_custo",
          "tipo_de_serviço",
          "veículo",
          "orçamentista",
          "data",
          "cidade",
        ];

        // Filtramos para pegar apenas os que existem nos campos disponíveis
        defaultFields = enhancedFields
          .filter((field) => camposDesejados.includes(field.value))
          .map((field) => field.value);

        // Se selecionou poucos campos, vamos garantir que pelo menos alguns importantes estejam lá
        if (defaultFields.length < 3) {
          // Campos mais críticos para relatórios de OS - adaptados aos nomes reais
          const camposCriticos = ["status", "veículo", "cidade", "osNumber"];
          camposCriticos.forEach((campo) => {
            if (
              enhancedFields.some((f) => f.value === campo) &&
              !defaultFields.includes(campo)
            ) {
              defaultFields.push(campo);
            }
          });
        }
      } else if (reportType === "credenciados") {
        defaultFields = [
          "razao_social",
          "nome_fantasia",
          "cnpj",
          "cidade",
          "telefone",
          "email",
        ];
      } else if (reportType === "condutores") {
        defaultFields = [
          "nome",
          "matricula",
          "cpf",
          "email",
          "contato",
          "centro_de_custo",
          "status",
        ];
      } else if (reportType === "centro-custo") {
        defaultFields = [
          "descricao",
          "cnpj",
          "razao_social",
          "responsavel",
          "contato",
          "email",
          "status",
        ];
      }
      const validFields = defaultFields.filter((field) =>
        enhancedFields.some((f) => f.value === field)
      );
      setSelectedFields(validFields);
    }
  }, [open, reportType, enhancedFields]);

  const handleGenerateReport = () => {
    // Garantir que temos as duas datas antes de gerar o relatório
    if (dateRange.from && dateRange.to) {
      onGenerateReport({
        dateRange: {
          from: dateRange.from,
          to: dateRange.to,
        },
        sortBy,
        selectedFields:
          selectedFields.length > 0
            ? selectedFields
            : enhancedFields.map((f) => f.value),
      });
      setOpen(false);
    }
  };

  const handleDateRangeChange = (range: DateRange | undefined) => {
    if (range?.from) {
      setDateRange({
        from: range.from,
        to: range.to || addDays(range.from, 30),
      });
    }
  };

  const toggleField = (field: string) => {
    setSelectedFields((prev) =>
      prev.includes(field) ? prev.filter((f) => f !== field) : [...prev, field]
    );
  };

  const getReportTitle = () => {
    switch (reportType) {
      case "os":
        return "Ordem de Serviço";
      case "veiculos":
        return "Veículos - Composição de Frota (Req. 47)";
      case "credenciados":
        return "Credenciados";
      case "condutores":
        return "Condutores";
      case "centro-custo":
        return "Centros de Custo";
      default:
        return "";
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="flex items-center">
          <FileText className="mr-2 h-4 w-4" />
          Emitir Relatório
        </Button>
      </DialogTrigger>
      <DialogContent className="w-full max-w-3xl">
        <DialogHeader>
          <DialogTitle>Gerar Relatório de {getReportTitle()}</DialogTitle>
          <DialogDescription>
            {reportType === "veiculos"
              ? "Configure os parâmetros para gerar o relatório em PDF com gráficos."
              : "Configure os parâmetros para gerar o relatório em PDF com gráficos."
            }
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="date-range">Intervalo de Datas</Label>
            <DateRangePicker
              value={dateRange}
              onChange={handleDateRangeChange}
              className="w-full"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="sort-by">Ordenar por</Label>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Selecione um campo para ordenação" />
              </SelectTrigger>
              <SelectContent>
                {enhancedFields.map((field) => (
                  <SelectItem key={field.value} value={field.value}>
                    {field.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Campos a incluir no relatório</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2 max-h-60 overflow-y-auto border rounded-md p-2">
              {enhancedFields.map((field) => (
                <div key={field.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`field-${field.value}`}
                    checked={selectedFields.includes(field.value)}
                    onCheckedChange={() => toggleField(field.value)}
                  />
                  <Label
                    htmlFor={`field-${field.value}`}
                    className="text-sm cursor-pointer"
                  >
                    {field.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => setOpen(false)}
          >
            Cancelar
          </Button>
          <Button
            type="button"
            onClick={handleGenerateReport}
            disabled={
              !dateRange.from || !dateRange.to || selectedFields.length === 0
            }
          >
            Gerar Relatório
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
