"use client";

import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";

interface ResumoItem {
  tipo: string;
  grupo: string;
  descricao: string;
  codigo: string;
  marca: string;
  garantia: string;
  quantidade: number;
  valorUnitario: string;
  valorDesconto: string;
  valorNegociado: string;
}

interface ModalResumoOrcamentoProps {
  open: boolean;
  onClose: () => void;
  data: ResumoItem[];
}

export default function ModalResumoOrcamento({ open, onClose, data }: ModalResumoOrcamentoProps) {
  return (
    <Dialog open={open} onOpenChange={(v) => !v && onClose()}>
      <DialogContent className="max-w-7xl h-[90vh]">
        <DialogHeader>
          <DialogTitle className="text-xl">Resu<PERSON> do orçamento</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="pecas" className="w-full mt-4">
          <TabsList className="mb-4">
            <TabsTrigger value="os">Ordem de serviço</TabsTrigger>
            <TabsTrigger value="veiculo">Veículo</TabsTrigger>
            <TabsTrigger value="orcamento">Orçamento</TabsTrigger>
            <TabsTrigger value="pecas">Peças e serviços</TabsTrigger>
            <TabsTrigger value="credenciado">Credenciado</TabsTrigger>
          </TabsList>

          <TabsContent value="pecas">
            <ScrollArea className="h-[60vh] w-full">
              <table className="w-full text-sm border">
                <thead className="bg-muted text-left">
                  <tr>
                    <th className="p-2">Tipo</th>
                    <th className="p-2">Grupo</th>
                    <th className="p-2">Descrição</th>
                    <th className="p-2">Código</th>
                    <th className="p-2">Marca</th>
                    <th className="p-2">Garantia</th>
                    <th className="p-2">Quantidade</th>
                    <th className="p-2">Valor unitário</th>
                    <th className="p-2">Valor desconto</th>
                    <th className="p-2">Valor negociado</th>
                  </tr>
                </thead>
                <tbody>
                  {data.map((item, i) => (
                    <tr key={i} className="border-b hover:bg-muted/50">
                      <td className="p-2">{item.tipo}</td>
                      <td className="p-2">{item.grupo}</td>
                      <td className="p-2">{item.descricao}</td>
                      <td className="p-2">{item.codigo}</td>
                      <td className="p-2">{item.marca}</td>
                      <td className="p-2">{item.garantia}</td>
                      <td className="p-2">{item.quantidade}</td>
                      <td className="p-2">{item.valorUnitario}</td>
                      <td className="p-2">{item.valorDesconto}</td>
                      <td className="p-2">{item.valorNegociado}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}