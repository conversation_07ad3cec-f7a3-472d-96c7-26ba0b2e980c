import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from "../ui/dialog";
import { But<PERSON> } from "../ui/button";
import { Separator } from "@/components/ui/separator";
import { VehicleInfoResponse } from "@/interfaces/suiv.interface";

interface VehicleDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  data: VehicleInfoResponse;
}

export function VehicleDetailsModal({ isOpen, onClose, onConfirm, data }: VehicleDetailsModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Detalhes do Veículo</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto pr-2">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="font-semibold">Informações Gerais</h3>
              <p><span className="font-medium">Fabricante:</span> {data.maker}</p>
              <p><span className="font-medium">Modelo:</span> {data.model}</p>
              <p><span className="font-medium">Versão:</span> {data.version}</p>
              <p><span className="font-medium">Placa:</span> {data.plate}</p>
              <p><span className="font-medium">Ano de Fabricação:</span> {data.yearFab}</p>
              <p><span className="font-medium">Ano do Modelo:</span> {data.yearModel}</p>
              <p><span className="font-medium">Linha:</span> {data.line || "Não informado"}</p>
              <p><span className="font-medium">Tipo:</span> {data.type}</p>
              <p><span className="font-medium">Espécie:</span> {data.species}</p>
            </div>

            <div className="space-y-2">
              <h3 className="font-semibold">Identificação</h3>
              <p><span className="font-medium">Chassi/VIN:</span> {data.vin || "Não informado"}</p>
              <p><span className="font-medium">Número do Motor:</span> {data.engineNumber || "Não informado"}</p>
              <p><span className="font-medium">Cor:</span> {data.color}</p>
              <p><span className="font-medium">Origem:</span> {data.isNational ? "Nacional" : "Importado"}</p>
              <p><span className="font-medium">Carroceria:</span> {data.bodywork}</p>
            </div>
          </div>
          <Separator />

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="font-semibold">Especificações do Motor</h3>
              <p><span className="font-medium">Potência:</span> {data.power ? `${data.power} cv` : "Não informado"}</p>
              <p><span className="font-medium">Cilindradas:</span> {data.cubicCentimeters} cc</p>
              <p><span className="font-medium">Litros do Motor:</span> {data.engineLiters ? `${data.engineLiters}L` : "Não informado"}</p>
              <p><span className="font-medium">Válvulas:</span> {data.engineValves || "Não informado"}</p>
              <p><span className="font-medium">Combustível:</span> {data.fuel}</p>
            </div>

            <div className="space-y-2">
              <h3 className="font-semibold">Transmissão e Eixos</h3>
              <p><span className="font-medium">Transmissão:</span> {data.transmission || "Não informado"}</p>
              <p><span className="font-medium">Número de Câmbio:</span> {data.gearBoxNumber || "Não informado"}</p>
              <p><span className="font-medium">Quantidade de Eixos:</span> {data.axisNumber}</p>
              <p><span className="font-medium">Eixos Traseiros:</span> {data.backAxisNumber || "Não informado"}</p>
              <p><span className="font-medium">Eixos Auxiliares:</span> {data.auxAxisNumber || "Não informado"}</p>
            </div>
          </div>
          <Separator />

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="font-semibold">Capacidades</h3>
              <p><span className="font-medium">Quantidade de Assentos:</span> {data.seatCount}</p>
              <p><span className="font-medium">Capacidade de Carga:</span> {data.loadCapacity} kg</p>
              <p><span className="font-medium">Peso Bruto Total:</span> {data.totalGrossWeight} kg</p>
              <p><span className="font-medium">Capacidade Máxima de Tração:</span> {data.maximumTractionCapacity} kg</p>
            </div>
            
            {data.fipeDataCollection && data.fipeDataCollection.length > 0 && (
              <div className="space-y-2">
                <h3 className="font-semibold">Informações FIPE</h3>
                <p><span className="font-medium">Código FIPE:</span> {data.fipeDataCollection[0].fipeId}</p>
                <p><span className="font-medium">Valor Atual:</span> {new Intl.NumberFormat('pt-BR', { 
                  style: 'currency', 
                  currency: 'BRL' 
                }).format(data.fipeDataCollection[0].currentValue)}</p>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button onClick={onConfirm}>Confirmar e Preencher</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}