"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import FormPassword from "../forms/password";

export default function Modal({
  type,
  title,
  onClose,
}: {
  type: string;
  title: string;
  onClose: () => void;
}) {
  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        {type === "newPassword" && <FormPassword />}
      </DialogContent>
    </Dialog>
  );
}
