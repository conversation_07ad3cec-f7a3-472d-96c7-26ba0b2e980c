"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogTrigger,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { updateVehiclesInBatch } from "@/serverActions/vehicleAction";
import { Loader2 } from "lucide-react";

export interface Veiculo {
  id: string;
  placa: string;
  odometro_atual: number;
  cor: string;
  ano_modelo: number;
  status: string;
  combustivel: string;
  renavam: string;
  vin: string;
  numero_motor: string;
  ano_fab: number;
  matricula: string;
  tag_rfid: string;
  valor_venal: number;
}

interface BatchUpdateModalProps {
  selectedVehicles: Veiculo[];
  onBatchUpdate?: (data: Veiculo[]) => void;
}
export function BatchUpdateModal({ selectedVehicles, onBatchUpdate }: BatchUpdateModalProps) {
  const [editedVehicles, setEditedVehicles] = useState<Veiculo[]>([]);
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    // Inicializa os dados para edição com os veículos selecionados
    setEditedVehicles(selectedVehicles);
  }, [selectedVehicles]);

  const handleChange = (id: string, field: keyof Veiculo, value: any) => {
    setEditedVehicles((prev) =>
      prev.map((veiculo) => (veiculo.id === id ? { ...veiculo, [field]: value } : veiculo))
    );
  };

  const handleBatchUpdate = async () => {
    try {
      setIsSubmitting(true);
      const result = await updateVehiclesInBatch(editedVehicles);

      if (result.success) {
        toast.success(result.message || "Veículos atualizados com sucesso!");
        setOpen(false);
      } else {
        toast.error(result.message || "Erro ao atualizar veículos");

        if (result.erros && result.erros.length > 0) {
          result.erros.forEach((err) => {
            toast.error(`Erro ao atualizar ${err.placa}: ${err.erro}`);
          });
        }
      }
    } catch (error) {
      toast.error("Ocorreu um erro ao processar a atualização");
    } finally {
      setIsSubmitting(false);
    }
  };

  const combustivelOptions = ["Gasolina", "Etanol", "Diesel", "Híbrido", "Elétrico", "Flex"];
  const statusOptions = ["Ativo", "Inativo", "Em Manutenção", "Sem condições de uso"];

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          Atualização em Lote {selectedVehicles?.length > 0 && `(${selectedVehicles.length})`}
        </Button>
      </DialogTrigger>
      <DialogContent className="w-full max-w-[98vw]">
        <DialogHeader>
          <DialogTitle>Atualização em Lote de Veículos</DialogTitle>
        </DialogHeader>

        {selectedVehicles.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-8 text-center">
            <p className="text-lg font-medium mb-2">Nenhum veículo selecionado</p>
            <p className="">
              Selecione veículos na tabela utilizando as caixas de seleção à esquerda para realizar
              a atualização em lote.
            </p>
          </div>
        ) : (
          <div className="overflow-auto max-h-[70vh]">
            <table className="min-w-full divide-y">
              <thead className=" sticky top-0">
                <tr>
                  <th className="px-4 py-2 text-left text-xs font-medium  uppercase tracking-wider">
                    Placa
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium  uppercase tracking-wider">
                    Odômetro Atual
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium  uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium  uppercase tracking-wider">
                    Cor
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium  uppercase tracking-wider">
                    Ano Modelo
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium  uppercase tracking-wider">
                    Ano Fabricação
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium  uppercase tracking-wider">
                    Combustível
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium  uppercase tracking-wider">
                    Renavam
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium  uppercase tracking-wider">
                    VIN
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium  uppercase tracking-wider">
                    Número do Motor
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider">
                    Matrícula
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider">
                    Tag RFID
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider">
                    Valor Venal (R$)
                  </th>
                </tr>
              </thead>
              <tbody className=" divide-y ">
                {editedVehicles.map((veiculo) => (
                  <tr key={veiculo.id}>
                    <td className="px-4 py-2 whitespace-nowrap">{veiculo.placa}</td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      <Input
                        type="number"
                        value={veiculo.odometro_atual}
                        onChange={(e) =>
                          handleChange(veiculo.id, "odometro_atual", Number(e.target.value))
                        }
                        className="w-28"
                      />
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      <select
                        value={veiculo.status}
                        onChange={(e) => handleChange(veiculo.id, "status", e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-2 py-1">
                        {statusOptions.map((status) => (
                          <option key={status} value={status}>
                            {status}
                          </option>
                        ))}
                      </select>
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      <Input
                        type="text"
                        value={veiculo.cor || ""}
                        onChange={(e) => handleChange(veiculo.id, "cor", e.target.value)}
                        className="w-28"
                      />
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      <Input
                        type="number"
                        value={veiculo.ano_modelo || ""}
                        onChange={(e) =>
                          handleChange(veiculo.id, "ano_modelo", Number(e.target.value))
                        }
                        className="w-24"
                      />
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      <Input
                        type="number"
                        value={veiculo.ano_fab || ""}
                        onChange={(e) =>
                          handleChange(veiculo.id, "ano_fab", Number(e.target.value))
                        }
                        className="w-24"
                      />
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      <select
                        value={veiculo.combustivel || ""}
                        onChange={(e) => handleChange(veiculo.id, "combustivel", e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-2 py-1">
                        <option value="">Selecione</option>
                        {combustivelOptions.map((combustivel) => (
                          <option key={combustivel} value={combustivel}>
                            {combustivel}
                          </option>
                        ))}
                      </select>
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      <Input
                        type="text"
                        value={veiculo.renavam || ""}
                        onChange={(e) => handleChange(veiculo.id, "renavam", e.target.value)}
                        className="w-40"
                      />
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      <Input
                        type="text"
                        value={veiculo.vin || ""}
                        onChange={(e) => handleChange(veiculo.id, "vin", e.target.value)}
                        className="w-40"
                      />
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      <Input
                        type="text"
                        value={veiculo.numero_motor || ""}
                        onChange={(e) => handleChange(veiculo.id, "numero_motor", e.target.value)}
                        className="w-40"
                      />
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      <Input
                        type="text"
                        value={veiculo.matricula || ""}
                        onChange={(e) => handleChange(veiculo.id, "matricula", e.target.value)}
                        className="w-32"
                      />
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      <Input
                        type="text"
                        value={veiculo.tag_rfid || ""}
                        onChange={(e) => handleChange(veiculo.id, "tag_rfid", e.target.value)}
                        className="w-32"
                      />
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      <Input
                        type="number"
                        value={veiculo.valor_venal || ""}
                        onChange={(e) =>
                          handleChange(veiculo.id, "valor_venal", Number(e.target.value))
                        }
                        className="w-32"
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
        <DialogFooter className="mt-4 flex justify-end gap-2">
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancelar
          </Button>
          <Button
            onClick={handleBatchUpdate}
            disabled={selectedVehicles.length === 0}
            className="min-w-[120px]">
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Atualizando...
              </>
            ) : (
              "Confirmar Atualização"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
