"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON>List, <PERSON><PERSON>Trigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import api from "@/service/api";
import { exemploEmpenho } from "../sections/empenho/mocks";
import { CurrencyInput } from "@/components/inputs/currency-input";
import { DateInput } from "@/components/inputs/date-input";
import { cn } from "@/lib/utils";
import { ptBR } from "date-fns/locale";
import { updateEmpenhoAction, updateSaldosPecasEServicos } from "@/serverActions/empenhoAction";

type TipoAjuste = "reforco" | "anulacao";

interface ModalEmpenhoProps {
  id: string;
  onClose?: () => void;
  initialTab?: "editar" | "ajustar";
}

// Função utilitária para formatar moeda
const formatCurrency = (value: number) => {
  return (value / 100).toLocaleString("pt-BR", {
    style: "currency",
    currency: "BRL",
  });
};

export default function ModalEmpenho({ id, onClose, initialTab = "editar" }: ModalEmpenhoProps) {
  const [tab, setTab] = useState<"editar" | "ajustar">(initialTab);
  const [tipoAjuste, setTipoAjuste] = useState<TipoAjuste>("reforco");
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [empenhoData, setEmpenhoData] = useState<any | null>(null);

  const [formData, setFormData] = useState({
    centroCusto: "",
    notaEmpenho: "",
    anoCompetencia: "",
    dataInicial: null as Date | null,
    dataFinal: null as Date | null,
    dotacaoOrcamentaria: "",
    valorPecas: 0,
    valorServicos: 0,
    empenhoAtivo: true,
    motivo: "",
  });

  // Carregar dados do empenho
  useEffect(() => {
    const fetchEmpenhoData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/empenho/${id}`);
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        const data = await response.json();
        setEmpenhoData(data.data.empenho);

        // Preencher o formulário com os dados recebidos
        setFormData({
          centroCusto: data.data.empenho.centro_custo?.descricao || "",
          notaEmpenho: data.data.empenho.nota_empenho || "",
          anoCompetencia: data.data.empenho.ano_de_competencia || "",
          dataInicial: data.data.empenho.data_inicial
            ? new Date(data.data.empenho.data_inicial)
            : null,
          dataFinal: data.data.empenho.data_final ? new Date(data.data.empenho.data_final) : null,
          dotacaoOrcamentaria: data.data.empenho.dotacao_orcamentaria || "",
          valorPecas: 0,
          valorServicos: 0,
          empenhoAtivo: data.data.empenho_ativo || false,
          motivo: "",
        });
      } catch (error) {
        console.error("Erro ao carregar dados do empenho:", error);
        toast.error("Não foi possível carregar os dados do empenho");
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchEmpenhoData();
    }
  }, [id]);

  const updateField = (name: keyof typeof formData, value: any) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handler para converter string de data para objeto Date
  const handleDateChange = (name: "dataInicial" | "dataFinal", dateString: string) => {
    if (!dateString) {
      updateField(name, null);
      return;
    }

    try {
      // A string vem no formato ISO YYYY-MM-DD do componente DateInput
      const date = new Date(dateString);
      updateField(name, date);
    } catch (error) {
      console.error("Erro ao converter data:", error);
    }
  };

  // Calcular valores ajustados com base no tipo de ajuste
  const calcularValoresAjustados = () => {
    if (!empenhoData)
      return {
        pecas: 0,
        servicos: 0,
        total: 0,
        saldoPecas: 0,
        saldoServicos: 0,
        saldoTotal: 0,
      };

    // Valores atuais
    const pecasAtual = empenhoData.valor_pecas || 0;
    const servicosAtual = empenhoData.valor_servicos || 0;

    // Ajustes inseridos pelo usuário
    const ajustePecas = formData.valorPecas || 0;
    const ajusteServicos = formData.valorServicos || 0;

    // Calcular com base no tipo de ajuste
    const pecasAjustado =
      tipoAjuste === "reforco" ? pecasAtual + ajustePecas : pecasAtual - ajustePecas;

    const servicosAjustado =
      tipoAjuste === "reforco" ? servicosAtual + ajusteServicos : servicosAtual - ajusteServicos;

    // Calcular saldos
    const saldoPecasAtual = empenhoData.saldo_pecas || 0;
    const saldoServicosAtual = empenhoData.saldo_servicos || 0;

    const saldoPecasAjustado =
      tipoAjuste === "reforco" ? saldoPecasAtual + ajustePecas : saldoPecasAtual - ajustePecas;

    const saldoServicosAjustado =
      tipoAjuste === "reforco"
        ? saldoServicosAtual + ajusteServicos
        : saldoServicosAtual - ajusteServicos;

    return {
      pecas: pecasAjustado,
      servicos: servicosAjustado,
      total: pecasAjustado + servicosAjustado,
      saldoPecas: saldoPecasAjustado,
      saldoServicos: saldoServicosAjustado,
      saldoTotal: saldoPecasAjustado + saldoServicosAjustado,
    };
  };

  const valoresAjustados = calcularValoresAjustados();

  // Preparar dados para ajuste
  const prepareAdjustData = () => {
    return {
      tipo_ajuste: tipoAjuste,
      valor_pecas: formData.valorPecas,
      valor_servicos: formData.valorServicos,
      motivo: formData.motivo,
    };
  };

  const handleSubmit = async () => {
      try {
          setIsSaving(true);
  
          if (tab === "editar") {
              const editData = {
                  centroCustoId: empenhoData?.centro_custo?.id || "",
                  nota_empenho: formData.notaEmpenho,
                  ano_competencia: Number(formData.anoCompetencia),
                  data_inicial: formData.dataInicial ? formData.dataInicial.toISOString() : "",
                  data_final: formData.dataFinal ? formData.dataFinal.toISOString() : "",
                  dotacao_orcamentaria: formData.dotacaoOrcamentaria,
                  valor_pecas: empenhoData?.valor_pecas || 0,
                  valor_servicos: empenhoData?.valor_servicos || 0,
                  bloqueado_pecas: empenhoData?.bloqueado_pecas || false,
                  bloqueado_servicos: empenhoData?.bloqueado_servicos || false,
                  faturado_pecas: empenhoData?.faturado_pecas || false,
                  faturado_servicos: empenhoData?.faturado_servicos || false,
                  empenho_bloqueado: empenhoData?.empenho_bloqueado || false,
                  empenho_ativo: formData.empenhoAtivo,
              };
  
              await updateEmpenhoAction(id, editData);
              toast.success("Empenho atualizado com sucesso!");
          } else {
              // Para a aba "ajustar", usamos os valores inseridos no input diretamente
              await updateSaldosPecasEServicos(id, {
                  saldo_pecas: formData.valorPecas,
                  saldo_servicos: formData.valorServicos,
              });
              toast.success("Ajuste de empenho registrado com sucesso!");
          }
  
          if (onClose) onClose();
          window.location.reload();
      } catch (error) {
          console.error("Erro ao processar operação:", error);
          toast.error("Ocorreu um erro ao processar a solicitação");
      } finally {
          setIsSaving(false);
      }
  };

  if (isLoading) {
    return (
      <Dialog open>
        <DialogContent>
          <DialogTitle className="flex justify-center p-4">Carregando dados...</DialogTitle>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open onOpenChange={(open) => !open && onClose?.()}>
      <DialogContent className="max-w-3xl">
        <DialogTitle>
          {tab === "editar"
            ? `Editar empenho ${empenhoData?.nota_empenho || ""}/${
                empenhoData?.ano_de_competencia || ""
              }`
            : `Ajustar empenho ${empenhoData?.nota_empenho || ""}/${
                empenhoData?.ano_de_competencia || ""
              }`}
        </DialogTitle>
        <Tabs value={tab} onValueChange={(v) => setTab(v as "editar" | "ajustar")}>
          <div className="flex items-center justify-between mb-4">
            <TabsList>
              <TabsTrigger value="editar">Editar</TabsTrigger>
              <TabsTrigger value="ajustar">Ajustar</TabsTrigger>
            </TabsList>
          </div>

          {/* Editar */}
          <TabsContent value="editar">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Centro de Custo</Label>
                <Input value={formData.centroCusto} disabled />
              </div>
              <div>
                <Label>Nota de Empenho</Label>
                <Input
                  value={formData.notaEmpenho}
                  onChange={(e) => updateField("notaEmpenho", e.target.value)}
                />
              </div>
              <div>
                <Label>Ano de Competência</Label>
                <Input
                  value={formData.anoCompetencia}
                  onChange={(e) => updateField("anoCompetencia", e.target.value)}
                />
              </div>
              <div>
                <Label>Dotação Orçamentária</Label>
                <Input
                  value={formData.dotacaoOrcamentaria}
                  onChange={(e) => updateField("dotacaoOrcamentaria", e.target.value)}
                />
              </div>
              <div>
                <Label>Data Inicial</Label>
                <DateInput
                  value={
                    formData.dataInicial ? formData.dataInicial.toISOString().split("T")[0] : ""
                  }
                  onChange={(value) => handleDateChange("dataInicial", value)}
                />
              </div>
              <div>
                <Label>Data Final</Label>
                <DateInput
                  value={formData.dataFinal ? formData.dataFinal.toISOString().split("T")[0] : ""}
                  onChange={(value) => handleDateChange("dataFinal", value)}
                />
              </div>
              <div className="col-span-2 flex items-center space-x-2">
                <Switch
                  id="empenhoAtivo"
                  checked={formData.empenhoAtivo}
                  onCheckedChange={(checked) => updateField("empenhoAtivo", checked)}
                />
                <Label htmlFor="empenhoAtivo">Empenho Ativo</Label>
              </div>
            </div>

            <div className="flex justify-end gap-2 mt-6">
              <Button variant="outline" onClick={onClose}>
                Cancelar
              </Button>
              <Button onClick={handleSubmit} disabled={isSaving}>
                {isSaving ? "Salvando..." : "Salvar Alterações"}
              </Button>
            </div>
          </TabsContent>

          {/* Ajustar */}
          <TabsContent value="ajustar">
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <Label>Centro de custo</Label>
                <Input value={formData.centroCusto} disabled />
              </div>
              <div>
                <Label>Tipo de ajuste</Label>
                <div className="flex gap-2 mt-1">
                  <Button
                    variant={tipoAjuste === "reforco" ? "secondary" : "outline"}
                    onClick={() => setTipoAjuste("reforco")}>
                    Reforço de empenho
                  </Button>
                  <Button
                    variant={tipoAjuste === "anulacao" ? "secondary" : "outline"}
                    onClick={() => setTipoAjuste("anulacao")}>
                    Anulação de empenho
                  </Button>
                </div>
              </div>
              <div>
                <Label>Valor sobre peças</Label>
                <CurrencyInput
                  value={formData.valorPecas}
                  onChange={(value) => updateField("valorPecas", value)}
                />
                <p className="text-xs text-muted-foreground">
                  Saldo disponível: {formatCurrency(empenhoData?.saldo_pecas || 0)}
                </p>
              </div>
              <div>
                <Label>Valor sobre serviços</Label>
                <CurrencyInput
                  value={formData.valorServicos}
                  onChange={(value) => updateField("valorServicos", value)}
                />
                <p className="text-xs text-muted-foreground">
                  Saldo disponível: {formatCurrency(empenhoData?.saldo_servicos || 0)}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 border-4 rounded-md text-sm">
                <strong className="block mb-1">Resumo dos valores atuais</strong>
                <p>Peças: {formatCurrency(empenhoData?.valor_pecas || 0)}</p>
                <p>Serviços: {formatCurrency(empenhoData?.valor_servicos || 0)}</p>
                <p>
                  Total:{" "}
                  {formatCurrency(
                    (empenhoData?.valor_pecas || 0) + (empenhoData?.valor_servicos || 0)
                  )}
                </p>
                <hr className="my-2" />
                <p>Saldo disponível:</p>
                <p>Peças: {formatCurrency(empenhoData?.saldo_pecas || 0)}</p>
                <p>Serviços: {formatCurrency(empenhoData?.saldo_servicos || 0)}</p>
              </div>

              <div className="p-4 border-4 rounded-md text-sm">
                <strong className="block mb-1">Resumo dos valores ajustados</strong>
                <p>Peças: {formatCurrency(valoresAjustados.pecas)}</p>
                <p>Serviços: {formatCurrency(valoresAjustados.servicos)}</p>
                <p>Total: {formatCurrency(valoresAjustados.total)}</p>
                <hr className="my-2" />
                <p>Saldo disponível:</p>
                <p>Peças: {formatCurrency(valoresAjustados.saldoPecas)}</p>
                <p>Serviços: {formatCurrency(valoresAjustados.saldoServicos)}</p>
              </div>
            </div>

            <div className="mt-4">
              <Label>Motivo</Label>
              <Textarea
                value={formData.motivo}
                onChange={(e) => updateField("motivo", e.target.value)}
                placeholder="Digite o motivo do ajuste"
                required
              />
            </div>

            <div className="flex justify-end gap-2 mt-6">
              <Button variant="outline" onClick={onClose}>
                Fechar
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={
                  isSaving ||
                  !formData.motivo ||
                  (formData.valorPecas === 0 && formData.valorServicos === 0)
                }>
                {isSaving ? "Processando..." : "Ajustar empenho"}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
