"use client";

import { useRouter } from "next/navigation";
import { useEffect, useRef } from "react";

interface EmpenhoActionsMenuProps {
  x: number;
  y: number;
  onClose: () => void;
  data?: any;
  showModal: (tab: "editar" | "valores" | "extrato" | "excluir") => void;
}

export function EmpenhoActionsMenu({
  x,
  y,
  onClose,
  data,
  showModal,
}: EmpenhoActionsMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClose]);

  return (
    <div
      ref={menuRef}
      className="absolute z-50 bg-white shadow-md border rounded-md w-48 p-2"
      style={{ top: y, left: x }}
    >
      <ul className="text-sm text-gray-700">
        <li
          className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
          onClick={() => showModal("editar")}
        >
          ✏️ Editar
        </li>
        <li 
          className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
          onClick={() => showModal("excluir")}
        >
          ❌ Excluir
        </li>
        <li className="border-t my-2"></li>
        <li
          className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
          onClick={() => showModal("extrato")}
        >
          📄 Extrato
        </li>
        <li
          className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
          onClick={() => showModal("valores")}
        >
          🔧 Ajustar valores
        </li>
      </ul>
    </div>
  );
}