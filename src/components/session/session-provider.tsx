"use client";

import { SESSION_CHECK_INTERVAL } from "@/lib/auth/helper";
import { Session } from "@/lib/auth/types";
import { checkSessionActive } from "@/serverActions/checkSession";

import { useRouter } from "next/navigation";
import React, { ReactNode, useEffect, useRef, useState } from "react";
import { toast } from "sonner";

interface SessionProvider {
  session: Session | undefined;
  setSession: (session: Session | undefined) => void;
  logout: () => void;
}

export const SessionContext = React.createContext<SessionProvider>({
  session: undefined,
  setSession: () => {},
  logout: () => {},
});

interface Props {
  children: ReactNode;
  initialSession: Session | undefined;
}

const SessionProvider = ({ children, initialSession }: Props) => {
  const intervalRef = useRef<number>(0);
  const [session, setSession] = useState<Session | undefined>(initialSession);
  const router = useRouter();

  const logout = async () => {
    if (!session) return router.push("/login");

    const res = await fetch("/api/auth/logout", { method: "POST" });
    const result = await res.json();
    if (typeof window !== "undefined") {
      const preservedKeys = ["bc_remember_me", "bc_saved_email"];
      Object.keys(localStorage).forEach((key) => {
        if (!preservedKeys.includes(key) && !key.startsWith("orcamento-items")) {
          localStorage.removeItem(key);
        }
      });
    }
    if (res.ok && !!result?.success) {
      setSession(undefined);
      window.location.href = "/login";
    }
  };

  async function checkSession() {
    console.log("Checking session");
    const isSessionActive = await checkSessionActive();

    if (!isSessionActive) {
      clearInterval(intervalRef.current);
      setSession(undefined);
      toast.error("Sessão expirada. Faça login novamente.");
      await new Promise((resolve) => setTimeout(resolve, 1000));
      window.location.href = "/login";
    }

  }

  useEffect(() => {
    checkSession();
    //@ts-expect-error
    intervalRef.current = setInterval(checkSession, SESSION_CHECK_INTERVAL);

    return () => clearInterval(intervalRef.current);
  }, []);

  return (
    <SessionContext.Provider value={{ session, setSession, logout }}>
      {children}
    </SessionContext.Provider>
  );
};

export default SessionProvider;
