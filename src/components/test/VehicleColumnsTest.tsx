"use client";

import React from 'react';
import { veiculoColumns } from '@/components/sections/veiculos/veiculos';

export function VehicleColumnsTest() {
  const visibleColumns = veiculoColumns.filter(col => 
    col.id !== 'select' && col.id !== 'actions'
  );

  return (
    <div className="p-4 bg-gray-50 rounded-lg">
      <h2 className="text-xl font-bold mb-4">🧪 Teste de Colunas de Veículos</h2>
      
      <div className="mb-4">
        <h3 className="text-lg font-semibold mb-2">📊 Total de Colunas: {visibleColumns.length}</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
        {visibleColumns.map((column, index) => (
          <div key={index} className="bg-white p-2 rounded border">
            <div className="text-sm font-medium text-blue-600">
              {typeof column.header === 'function' ? 
                `Coluna ${index + 1}` : 
                String(column.header)
              }
            </div>
            <div className="text-xs text-gray-500">
              accessorKey: {(column as any).accessorKey || 'N/A'}
            </div>
            <div className="text-xs text-gray-400">
              {(column as any).accessorFn ? 'Com accessorFn' : 'Sem accessorFn'}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 p-3 bg-blue-50 rounded">
        <h4 className="font-semibold text-blue-800 mb-2">📋 Lista de Colunas por Categoria:</h4>
        
        <div className="text-sm space-y-1">
          <div><strong>Identificação:</strong> Placa, Marca, Modelo, Tipo de Veículo</div>
          <div><strong>Especificações:</strong> Ano Fabricação, Ano Modelo, Cor, Tipo de Frota</div>
          <div><strong>Técnico:</strong> Renavam, VIN/Chassi, Número do Motor, Matrícula</div>
          <div><strong>Localização:</strong> Centro de Custo, Cidade</div>
          <div><strong>Datas:</strong> Data de Compra, Data de Cedência</div>
          <div><strong>Motor:</strong> Combustível, Potência, Cilindradas, Transmissão</div>
          <div><strong>Estrutura:</strong> Portas, Assentos</div>
          <div><strong>Controle:</strong> Odômetro, Valor Venal, Código FIPE, Tag RFID, Status</div>
        </div>
      </div>
    </div>
  );
}

// Componente para testar dados de exemplo
export function VehicleDataTest({ sampleVehicle }: { sampleVehicle?: any }) {
  if (!sampleVehicle) {
    return (
      <div className="p-4 bg-yellow-50 rounded-lg">
        <h3 className="text-lg font-semibold text-yellow-800 mb-2">⚠️ Nenhum dado de veículo disponível</h3>
        <p className="text-yellow-700">Carregue a página de veículos para ver os dados da API.</p>
      </div>
    );
  }

  const testFields = [
    { key: 'placa', label: 'Placa', value: sampleVehicle.placa },
    { key: 'marca', label: 'Marca', value: sampleVehicle.marca?.descricao },
    { key: 'modelo', label: 'Modelo', value: sampleVehicle.modelo?.descricao },
    { key: 'tipo_de_veiculo', label: 'Tipo de Veículo', value: sampleVehicle.tipo_de_veiculo?.descricao },
    { key: 'ano_fab', label: 'Ano Fabricação', value: sampleVehicle.ano_fab },
    { key: 'ano_modelo', label: 'Ano Modelo', value: sampleVehicle.ano_modelo },
    { key: 'cor', label: 'Cor', value: sampleVehicle.cor },
    { key: 'renovam', label: 'Renavam', value: sampleVehicle.renovam },
    { key: 'vin', label: 'VIN/Chassi', value: sampleVehicle.vin },
    { key: 'numero_do_motor', label: 'Número do Motor', value: sampleVehicle.numero_do_motor },
    { key: 'matricula', label: 'Matrícula', value: sampleVehicle.matricula },
    { key: 'centro_custo', label: 'Centro de Custo', value: sampleVehicle.lotacao_veiculos?.centro_custo?.descricao },
    { key: 'cidade', label: 'Cidade', value: sampleVehicle.lotacao_veiculos?.cidade },
    { key: 'data_compra', label: 'Data de Compra', value: sampleVehicle.data_compra },
    { key: 'data_cedencia', label: 'Data de Cedência', value: sampleVehicle.data_cedencia },
    { key: 'tipo_de_frota', label: 'Tipo de Frota', value: sampleVehicle.tipo_de_frota?.descricao },
    { key: 'combustivel', label: 'Combustível', value: sampleVehicle.combustivel?.tipos_de_combustiveis },
    { key: 'potencia', label: 'Potência', value: sampleVehicle.definicoes?.potencia },
    { key: 'cilindradas', label: 'Cilindradas', value: sampleVehicle.definicoes?.cilindradas },
    { key: 'transmissao', label: 'Transmissão', value: sampleVehicle.definicoes?.transmissao },
    { key: 'quantidade_de_portas', label: 'Portas', value: sampleVehicle.definicoes?.quantidade_de_portas },
    { key: 'quantidade_de_assentos', label: 'Assentos', value: sampleVehicle.definicoes?.quantidade_de_assentos },
    { key: 'odometro_atual', label: 'Odômetro', value: sampleVehicle.odometro_atual },
    { key: 'valor_venal', label: 'Valor Venal', value: sampleVehicle.valor_venal },
    { key: 'codigo_fipe', label: 'Código FIPE', value: sampleVehicle.codigo_fipe?.codigo_fipe },
    { key: 'tag_rfid', label: 'Tag RFID', value: sampleVehicle.tag_rfid },
    { key: 'status', label: 'Status', value: sampleVehicle.status },
  ];

  const fieldsWithData = testFields.filter(field => field.value !== undefined && field.value !== null && field.value !== '');
  const fieldsWithoutData = testFields.filter(field => field.value === undefined || field.value === null || field.value === '');

  return (
    <div className="p-4 bg-green-50 rounded-lg">
      <h3 className="text-lg font-semibold text-green-800 mb-4">✅ Teste de Dados da API</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h4 className="font-semibold text-green-700 mb-2">
            📊 Campos com Dados ({fieldsWithData.length})
          </h4>
          <div className="space-y-1 max-h-60 overflow-y-auto">
            {fieldsWithData.map(field => (
              <div key={field.key} className="text-sm bg-white p-2 rounded border">
                <span className="font-medium">{field.label}:</span>{' '}
                <span className="text-gray-600">
                  {Array.isArray(field.value) ? field.value.join(', ') : String(field.value)}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h4 className="font-semibold text-red-700 mb-2">
            ❌ Campos sem Dados ({fieldsWithoutData.length})
          </h4>
          <div className="space-y-1 max-h-60 overflow-y-auto">
            {fieldsWithoutData.map(field => (
              <div key={field.key} className="text-sm bg-red-50 p-2 rounded border border-red-200">
                <span className="font-medium text-red-600">{field.label}</span>
                <span className="text-red-400 ml-2">(vazio)</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="mt-4 p-3 bg-blue-50 rounded">
        <h4 className="font-semibold text-blue-800 mb-2">📈 Estatísticas:</h4>
        <div className="text-sm space-y-1">
          <div>Total de campos testados: {testFields.length}</div>
          <div>Campos com dados: {fieldsWithData.length} ({Math.round((fieldsWithData.length / testFields.length) * 100)}%)</div>
          <div>Campos vazios: {fieldsWithoutData.length} ({Math.round((fieldsWithoutData.length / testFields.length) * 100)}%)</div>
        </div>
      </div>
    </div>
  );
}
