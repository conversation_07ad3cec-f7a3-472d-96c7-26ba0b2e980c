import { Active, DataRef, Over } from "@dnd-kit/core";
import { ColumnDragData } from "./ui/board-colum";
import { ServiceDragData } from "./cards/service-card";

type DraggableData = ColumnDragData | ServiceDragData;

export function hasDraggableData<T extends Active | Over>(
  entry: T | null | undefined
): entry is T & {
  data: DataRef<DraggableData>;
} {
  if (!entry) {
    return false;
  }

  const data = entry.data.current;

  if (data?.type === "Column" || data?.type === "Service") {
    return true;
  }

  return false;
}
