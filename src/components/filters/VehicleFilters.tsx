"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { X, Filter } from 'lucide-react';
import { useVeiculos } from '@/context/veiculos-context';
import { useCentroDeCusto } from '@/context/centro-de-custo-context';

export interface VehicleFilters {
  placa?: string;
  marca?: string;
  modelo?: string;
  tipoDeVeiculo?: string;
  anoFabricacao?: string;
  anoModelo?: string;
  cor?: string;
  renovam?: string;
  vin?: string;
  numeroDoMotor?: string;
  matricula?: string;
  centroCusto?: string;
  cidade?: string;
  dataCompra?: string;
  dataCedencia?: string;
  tipoDeFrota?: string;
  combustivel?: string;
  potencia?: string;
  cilindradas?: string;
  transmissao?: string;
  quantidadePortas?: string;
  quantidadeAssentos?: string;
  odometro?: string;
  valorVenalMin?: string;
  valorVenalMax?: string;
  codigoFipe?: string;
  tagRfid?: string;
  status?: string;
}

interface VehicleFiltersProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onFiltersChange: (filters: VehicleFilters) => void;
  currentFilters: VehicleFilters;
}

export function VehicleFiltersDialog({ 
  open, 
  onOpenChange, 
  onFiltersChange, 
  currentFilters 
}: VehicleFiltersProps) {
  const [filters, setFilters] = useState<VehicleFilters>(currentFilters);
  const { veiculos } = useVeiculos();
  const { centrosDeCusto } = useCentroDeCusto();

  // Extrair valores únicos dos dados para os selects
  const uniqueMarcas = [...new Set(veiculos.map(v => v.marca?.descricao).filter((item): item is string => Boolean(item)))].sort();
  const uniqueModelos = [...new Set(veiculos.map(v => v.modelo?.descricao).filter((item): item is string => Boolean(item)))].sort();
  const uniqueTiposVeiculo = [...new Set(veiculos.map(v => v.tipo_de_veiculo?.descricao).filter((item): item is string => Boolean(item)))].sort();
  const uniqueCidades = [...new Set(veiculos.map(v => v.lotacao_veiculos?.cidade).filter((item): item is string => Boolean(item)))].sort();
  const uniqueAnosFabricacao = [...new Set(veiculos.map(v => v.ano_fab).filter((item): item is string => Boolean(item)))].sort();
  const uniqueAnosModelo = [...new Set(veiculos.map(v => v.ano_modelo).filter((item): item is string => Boolean(item)))].sort();
  const uniqueCores = [...new Set(veiculos.map(v => v.cor).filter((item): item is string => Boolean(item)))].sort();
  const uniqueTiposFrota = [...new Set(veiculos.map(v => v.tipo_de_frota?.descricao).filter((item): item is string => Boolean(item)))].sort();
  const uniqueCombustiveis = [...new Set(veiculos.flatMap(v =>
    Array.isArray(v.combustivel?.tipos_de_combustiveis)
      ? v.combustivel.tipos_de_combustiveis
      : [v.combustivel?.tipos_de_combustiveis]
  ).filter(Boolean))].sort();
  const uniquePotencias = [...new Set(veiculos.map(v => v.definicoes?.potencia).filter((item): item is string => Boolean(item)))].sort();
  const uniqueTransmissoes = [...new Set(veiculos.map(v => v.definicoes?.transmissao).filter((item): item is string => Boolean(item)))].sort();

  useEffect(() => {
    setFilters(currentFilters);
  }, [currentFilters]);

  const handleFilterChange = (key: keyof VehicleFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined
    }));
  };

  const handleApplyFilters = () => {
    onFiltersChange(filters);
    onOpenChange(false);
  };

  const handleClearFilters = () => {
    const emptyFilters: VehicleFilters = {};
    setFilters(emptyFilters);
    onFiltersChange(emptyFilters);
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value && value.trim() !== '').length;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros de Veículos
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary">
                {getActiveFiltersCount()} filtro(s) ativo(s)
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 py-4">
          {/* Seção: Identificação Básica */}
          <div className="col-span-full">
            <h3 className="text-lg font-semibold mb-2">Identificação Básica</h3>
          </div>

          {/* Filtro por Placa */}
          <div className="space-y-2">
            <Label htmlFor="placa">Placa</Label>
            <Input
              id="placa"
              placeholder="Ex: ABC-1234"
              value={filters.placa || ''}
              onChange={(e) => handleFilterChange('placa', e.target.value)}
            />
          </div>

          {/* Filtro por Marca */}
          <div className="space-y-2">
            <Label htmlFor="marca">Marca</Label>
            <Select value={filters.marca || ''} onValueChange={(value) => handleFilterChange('marca', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione uma marca" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todas as marcas</SelectItem>
                {uniqueMarcas.map(marca => (
                  <SelectItem key={marca} value={marca}>{marca}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Filtro por Modelo */}
          <div className="space-y-2">
            <Label htmlFor="modelo">Modelo</Label>
            <Select value={filters.modelo || ''} onValueChange={(value) => handleFilterChange('modelo', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione um modelo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todos os modelos</SelectItem>
                {uniqueModelos.map(modelo => (
                  <SelectItem key={modelo} value={modelo}>{modelo}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Filtro por Tipo de Veículo */}
          <div className="space-y-2">
            <Label htmlFor="tipoDeVeiculo">Tipo de Veículo</Label>
            <Select value={filters.tipoDeVeiculo || ''} onValueChange={(value) => handleFilterChange('tipoDeVeiculo', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione um tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todos os tipos</SelectItem>
                {uniqueTiposVeiculo.map(tipo => (
                  <SelectItem key={tipo} value={tipo}>{tipo}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Filtro por Ano de Fabricação */}
          <div className="space-y-2">
            <Label htmlFor="anoFabricacao">Ano de Fabricação</Label>
            <Select value={filters.anoFabricacao || ''} onValueChange={(value) => handleFilterChange('anoFabricacao', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione um ano" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todos os anos</SelectItem>
                {uniqueAnosFabricacao.map(ano => (
                  <SelectItem key={ano} value={ano}>{ano}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Filtro por Ano do Modelo */}
          <div className="space-y-2">
            <Label htmlFor="anoModelo">Ano do Modelo</Label>
            <Select value={filters.anoModelo || ''} onValueChange={(value) => handleFilterChange('anoModelo', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione um ano" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todos os anos</SelectItem>
                {uniqueAnosModelo.map(ano => (
                  <SelectItem key={ano} value={ano}>{ano}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Seção: Especificações Técnicas */}
          <div className="col-span-full mt-4">
            <h3 className="text-lg font-semibold mb-2">Especificações Técnicas</h3>
          </div>

          {/* Filtro por Cor */}
          <div className="space-y-2">
            <Label htmlFor="cor">Cor</Label>
            <Select value={filters.cor || ''} onValueChange={(value) => handleFilterChange('cor', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione uma cor" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todas as cores</SelectItem>
                {uniqueCores.map(cor => (
                  <SelectItem key={cor} value={cor}>{cor}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Filtro por Tipo de Frota */}
          <div className="space-y-2">
            <Label htmlFor="tipoDeFrota">Tipo de Frota</Label>
            <Select value={filters.tipoDeFrota || ''} onValueChange={(value) => handleFilterChange('tipoDeFrota', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione um tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todos os tipos</SelectItem>
                {uniqueTiposFrota.map(tipo => (
                  <SelectItem key={tipo} value={tipo}>{tipo}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Filtro por Combustível */}
          <div className="space-y-2">
            <Label htmlFor="combustivel">Combustível</Label>
            <Select value={filters.combustivel || ''} onValueChange={(value) => handleFilterChange('combustivel', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione um combustível" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todos os combustíveis</SelectItem>
                {uniqueCombustiveis.map(combustivel => (
                  <SelectItem key={combustivel} value={combustivel}>{combustivel}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Filtro por Potência */}
          <div className="space-y-2">
            <Label htmlFor="potencia">Potência</Label>
            <Select value={filters.potencia || ''} onValueChange={(value) => handleFilterChange('potencia', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione uma potência" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todas as potências</SelectItem>
                {uniquePotencias.map(potencia => (
                  <SelectItem key={potencia} value={potencia}>{potencia}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Filtro por Transmissão */}
          <div className="space-y-2">
            <Label htmlFor="transmissao">Transmissão</Label>
            <Select value={filters.transmissao || ''} onValueChange={(value) => handleFilterChange('transmissao', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione uma transmissão" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todas as transmissões</SelectItem>
                {uniqueTransmissoes.map(transmissao => (
                  <SelectItem key={transmissao} value={transmissao}>{transmissao}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Seção: Localização e Centro de Custo */}
          <div className="col-span-full mt-4">
            <h3 className="text-lg font-semibold mb-2">Localização e Centro de Custo</h3>
          </div>

          {/* Filtro por Centro de Custo */}
          <div className="space-y-2">
            <Label htmlFor="centroCusto">Centro de Custo</Label>
            <Select value={filters.centroCusto || ''} onValueChange={(value) => handleFilterChange('centroCusto', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione um centro de custo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todos os centros de custo</SelectItem>
                {centrosDeCusto.map(centro => (
                  <SelectItem key={centro.id} value={centro.descricao}>{centro.descricao}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Filtro por Cidade */}
          <div className="space-y-2">
            <Label htmlFor="cidade">Cidade</Label>
            <Select value={filters.cidade || ''} onValueChange={(value) => handleFilterChange('cidade', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione uma cidade" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todas as cidades</SelectItem>
                {uniqueCidades.map(cidade => (
                  <SelectItem key={cidade} value={cidade}>{cidade}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Seção: Campos de Texto */}
          <div className="col-span-full mt-4">
            <h3 className="text-lg font-semibold mb-2">Campos de Identificação</h3>
          </div>

          {/* Filtro por Renavam */}
          <div className="space-y-2">
            <Label htmlFor="renovam">Renavam</Label>
            <Input
              id="renovam"
              placeholder="Ex: 123456789"
              value={filters.renovam || ''}
              onChange={(e) => handleFilterChange('renovam', e.target.value)}
            />
          </div>

          {/* Filtro por VIN */}
          <div className="space-y-2">
            <Label htmlFor="vin">VIN/Chassi</Label>
            <Input
              id="vin"
              placeholder="Ex: 1HGBH41JXMN109186"
              value={filters.vin || ''}
              onChange={(e) => handleFilterChange('vin', e.target.value)}
            />
          </div>

          {/* Filtro por Matrícula */}
          <div className="space-y-2">
            <Label htmlFor="matricula">Matrícula</Label>
            <Input
              id="matricula"
              placeholder="Ex: MAT123456"
              value={filters.matricula || ''}
              onChange={(e) => handleFilterChange('matricula', e.target.value)}
            />
          </div>

          {/* Seção: Valores e Status */}
          <div className="col-span-full mt-4">
            <h3 className="text-lg font-semibold mb-2">Valores e Status</h3>
          </div>

          {/* Filtro por Valor Venal Mínimo */}
          <div className="space-y-2">
            <Label htmlFor="valorVenalMin">Valor Venal Mínimo (R$)</Label>
            <Input
              id="valorVenalMin"
              type="number"
              placeholder="Ex: 10000"
              value={filters.valorVenalMin || ''}
              onChange={(e) => handleFilterChange('valorVenalMin', e.target.value)}
            />
          </div>

          {/* Filtro por Valor Venal Máximo */}
          <div className="space-y-2">
            <Label htmlFor="valorVenalMax">Valor Venal Máximo (R$)</Label>
            <Input
              id="valorVenalMax"
              type="number"
              placeholder="Ex: 100000"
              value={filters.valorVenalMax || ''}
              onChange={(e) => handleFilterChange('valorVenalMax', e.target.value)}
            />
          </div>

          {/* Filtro por Status */}
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select value={filters.status || ''} onValueChange={(value) => handleFilterChange('status', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione um status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todos os status</SelectItem>
                <SelectItem value="Ativo">Ativo</SelectItem>
                <SelectItem value="Inativo">Inativo</SelectItem>
                <SelectItem value="Em Manuntenção">Em Manutenção</SelectItem>
                <SelectItem value="Sem condições de uso">Sem condições de uso</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={handleClearFilters}>
            Limpar Filtros
          </Button>
          <Button onClick={handleApplyFilters}>
            Aplicar Filtros
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Componente para exibir filtros ativos
export function ActiveFilters({ 
  filters, 
  onRemoveFilter, 
  onClearAll 
}: { 
  filters: VehicleFilters;
  onRemoveFilter: (key: keyof VehicleFilters) => void;
  onClearAll: () => void;
}) {
  const activeFilters = Object.entries(filters).filter(([_, value]) => value && value.trim() !== '');

  if (activeFilters.length === 0) return null;

  const getFilterLabel = (key: string, value: string) => {
    const labels: Record<string, string> = {
      placa: 'Placa',
      marca: 'Marca',
      modelo: 'Modelo',
      tipoDeVeiculo: 'Tipo de Veículo',
      anoFabricacao: 'Ano Fabricação',
      anoModelo: 'Ano Modelo',
      cor: 'Cor',
      renovam: 'Renavam',
      vin: 'VIN/Chassi',
      numeroDoMotor: 'Número do Motor',
      matricula: 'Matrícula',
      centroCusto: 'Centro de Custo',
      cidade: 'Cidade',
      dataCompra: 'Data Compra',
      dataCedencia: 'Data Cedência',
      tipoDeFrota: 'Tipo de Frota',
      combustivel: 'Combustível',
      potencia: 'Potência',
      cilindradas: 'Cilindradas',
      transmissao: 'Transmissão',
      quantidadePortas: 'Portas',
      quantidadeAssentos: 'Assentos',
      odometro: 'Odômetro',
      valorVenalMin: 'Valor Min',
      valorVenalMax: 'Valor Max',
      codigoFipe: 'Código FIPE',
      tagRfid: 'Tag RFID',
      status: 'Status'
    };
    return `${labels[key] || key}: ${value}`;
  };

  return (
    <div className="flex flex-wrap items-center gap-2 mb-4">
      <span className="text-sm text-gray-600">Filtros ativos:</span>
      {activeFilters.map(([key, value]) => (
        <Badge key={key} variant="secondary" className="flex items-center gap-1">
          {getFilterLabel(key, value)}
          <X 
            className="h-3 w-3 cursor-pointer" 
            onClick={() => onRemoveFilter(key as keyof VehicleFilters)}
          />
        </Badge>
      ))}
      <Button variant="ghost" size="sm" onClick={onClearAll}>
        Limpar todos
      </Button>
    </div>
  );
}
