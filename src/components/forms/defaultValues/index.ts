export const defaultValues = {
  osSchema: {
    condutorId: "",
    veiculoId: "",
    tipo_de_osId: "",
    tipo_de_manutencao: "Corretiva" as
      | "Corretiva"
      | "Preventiva"
      | "Preditiva"
      | "Sinistro",
    estado_de_localizacao: "",
    cidade_de_localizacao: "",
    odometro_atual: 0,
    mobilizado: false,
    descricao: "",
    orcamento_individual: false,
    minimun_orcamento: 3,
    OSId: undefined,
    quoteExpiration: 24,
    quoteExpirationDate: new Date(),
    tipo_de_frota_display: "",
  },
  tipoDeOsSchema: {
    descricao: "",
    obs: "",
    prazo_para_execucao: "",
  },
  veiculoSchema: {
    renovam: "",
    vin: "",
    numero_do_motor: "",
    ano_de_fabricacao: "",
    ano_do_modelo: "",
    cor: "",
    odometro_atual: 0,
    matricula: "",
    tag_rfid: "",
    data_compra: "", // Nova data de compra
    data_cedencia: "", // Nova data de cedência
    capacidade_do_tanque: "",
    tipos_de_combustiveis: [],
    numero_de_cambio: "",
    cilindradas: 0,
    potencia: "",
    segmento: "",
    carroceria: "",
    transmissao: "",
    quantidade_de_portas: 0,
    quantidade_de_assentos: 0,
    quantidade_de_eixos: 0,
    numero_de_valvulas: "",
    litragem: 0,
    combustivel: [],
    origem_do_veiculo: "nacional",
    centro_de_custoId: "",
    estado: "",
    cidade: "",
    codigo_fipe: "",
    valor_venal: "",
    status: "Ativo",
  },
  centroDeCusto: {
    descricao: "",
    centro_de_custo_ascendenteId: "",
    dotacao_orcamentista: "",
    valor_dotacao: 0,
    nome_responsavel: "",
    contato: "",
    email: "",
    cnpj: "",
    razao_social: "",
    endereco: {
      cep: "",
      logradouro: "",
      bairro: "",
      estado: "",
      cidade: "",
    },
    centro_de_custo_tomadorId: "",
    active: true,
  },
  condutorSchema: {
    id: "",
    nome: "",
    matricula: "",
    cpf: "", // Campo adicionado
    contato: "",
    email: "",
    endereco: {
      cep: "",
      logradouro: "",
      bairro: "",
      estado: "",
      cidade: "",
    },
    cnh: {
      numero: "",
      categoria: "",
      estado: "",
      data_emissao: new Date(),
      data_vencimento: new Date(),
    },
    mopp: {
      numero: "",
      emissor: "",
      data_emissao: new Date(),
      data_vencimento: new Date(),
    },
    lotacao_condutorId: "", // Campo adicionado
    status: true, // Campo adicionado
    ativo: true,
    createdAt: new Date(), // Campo adicionado
  },
  tipoDeVeiculo: {
    descricao: "",
    tipo_de_veiculosId: "",
  },
  tipoDeFrota: {
    descricao: "",
  },
  empenhoSchema: {
    centro_de_custoId: "",
    nota_empenho: "",
    ano_de_competencia: "",
    data_inicial: new Date(),
    data_final: new Date(),
    dotacao_orcamentada: "",
    valor_destinado_as_pecas: "",
    valor_destinado_aos_servicos: "",
    bloqueado: false,
    ativo: true,
  },
  credenciadoSchema: {
    polo_regionalId: "",
    informacoes: {
      cnpj: "",
      razao_social: "",
      inscricao_estadual: "",
      inscricao_municipal: "",
      nome_fantasia: "",
      atividade_principal: "",
      tipos_de_veiculos: [],
      horario_funcionamento: "",
      data_abertura: new Date(),
      porte_empresarial: "",
      capital_social: "",
      patrimonio_liquido: "",
      observacoes_gerais: "",
      logotipo_empresa: "",
    },
    contatos: {
      telefone: "",
      celular: "",
      email: "",
      nome_do_gerente: "",
      telefone_do_gerente: "",
      nome_do_proprietario: "",
      telefone_do_proprietario: "",
    },
    endereco: {
      cep: "",
      logradouro: "",
      bairro: "",
      estado: "",
      cidade: "",
      latitude: "",
      longitude: "",
    },
    estrutura: {
      capacidade_total_de_atendimento: 0,
      box_para_veiculos_leves: 0,
      box_para_veiculos_pesados: 0,
      elevadores_para_veiculos: 0,
      estufas_para_pintura: 0,
    },
    orcamentista: false,
    atende_placa_verde: false,
    concessionaria: false,
    orcamentacao: false,
    prazo_contratualId: "",
  },
  itemDaVistoria: {
    descricao: "",
  },
  vistoriaSchema: {
    veiculoId: "",
    tipo_de_avaliacao: "Inicial" as "Inicial" | "Periódica",
    data: new Date(),
    odometro_atual: "",
    previsao_troca_de_oleo: {
      km: "",
      data: new Date(),
    },
    observacao: "",
  },
  checklistSchema: {
    situacao_do_veiculo: {
      pneus: "Conforme",
      rodas: "Conforme",
      estepe: "Conforme",
      freio_de_mão: "Conforme",
      freio_de_pé: "Conforme",
      embreagem: "Conforme",
      luzes_do_painel: "Conforme",
      buzina: "Conforme",
      lanternas_dianteiras: "Conforme",
      lanternas_traseiras: "Conforme",
      farol_baixo: "Conforme",
      farol_alto: "Conforme",
      setas_dianteiras: "Conforme",
      setas_traseiras: "Conforme",
      luzes_de_emergência: "Conforme",
      luzes_de_freio: "Conforme",
      luzes_de_ré: "Conforme",
      espelhos_retrovisores: "Conforme",
      estado_geral_da_carroceria: "Conforme",
      nível_de_água: "Conforme",
      limpador_para_brisa: "Conforme",
      nível_de_fluído: "Conforme",
      óleo_do_motor: "Conforme",
    },
    aparencia_do_veiculo: {
      situação_geral_do_veículo: "Conforme",
      pintura: "Conforme",
      limpeza: "Conforme",
      para_brisa: "Conforme",
      sem_arranhões: "Conforme",
      sem_amassados: "Conforme",
      lateral_motorista: "Conforme",
      lateral_passageiro: "Conforme",
      traseira: "Conforme",
      dianteira: "Conforme",
    },
    tipo: "Entrada",
    data: new Date(),
    previsao_entrega: new Date(),
    odometro_atual: "",
    nivel_do_combustivel: "Vazio",
    imagens: [],
  },
  abastecimentoSchema: {
    data_e_hora: new Date(),
    odometro: "",
    combustivel: undefined, // ou "Gasolina", "Etanol / Alcool", "Diesel", "Querosene"
    preco_l: "",
    valor_total: "",
    litros: "",
    observacoes: "",
    veiculoId: "", // Adicione os campos que estão faltando
    condutorId: "", // Adicione os campos que estão faltando
    credenciadoId: "", // Adicione os campos que estão faltando
    arquivos: [], // Adicione os campos que estão faltando
  },
  despesaSchema: {
    data_e_hora: new Date(),
    odometro: "",
    valor: "",
    local: "",
    observacoes: "",
  },
  percursoSchema: {
    motivo: "",
    origem: {
      local: "",
      data: new Date(),
      odometro: 0,
    },
    destino: {
      local: "",
      data: new Date(),
      odometro: 0,
    },
    arquivos: [],
    observacoes: "",
  },
  lembreteSchema: {
    veiculoId: "",
    tipo: "DESPESA",
    referencialId: "",
    odometro: "",
    repeticao: {
      tipo: "UNICO",
      intervalo: undefined,
    },
    data: new Date(),
    dataDesejada: undefined,
    quantidade: undefined,
    periodo: undefined,
    observacoes: "",
  },
  contratoSchema: {
    nome: "",
    numero: "",
    data_inicial: new Date(),
    data_final: new Date(),
    valor_do_contrato: 0,
    taxa_administrativa: 0,
    cnpj: "",
    razao_social: "",
    logotipo: null,
    limite_gastos_percent: 0,
    send_nf_to: null,
    contato: {
      responsavel: "",
      telefone: "",
      email: "",
    },
    endereco: {
      cep: "",
      logradouro: "",
      bairro: "",
      estado: "",
      cidade: "",
    },
    validacao_nf: {
      placa_do_veiculo: false,
      numero_da_os: false,
      modelo_do_veiculo: false,
      numero_do_contrato: false,
    },
    configuracao: {
      ativo: true,
      abertura_os_credenciado: false,
      envio_de_emails: false,
      veiculos_com_rfid: false,
      parametrizacao_obrigatoria: false,
      checklist_simplificado_pecas: false,
      aprovacao_gestor: false,
      ajuste_gestor: false,
      restringir_veiculos: false,
    },
  },
  usuarioSchema: {
    nome: "",
    matricula: "",
    email: "",
    telefone: "",
    celular: "",
    ativo: true,
    centro_de_custoId: "",
    unidade_filha_id: [], // Changed from string to empty array
    unidades_filha_ids: [], // Add this field as well
  },
  acessoSchema: {
    permissao: [],
    contratoId: "",
    centro_de_custoId: "",
  },
};

export const usuarioDefaultValues = {
  nome: "",
  matricula: "",
  email: "",
  telefone: "",
  celular: "",
  senha: "",
  contratos: [],
  roles: [],
  centro_de_custoId: "",
  unidade_filha_id: "",
  ativo: true,
};
