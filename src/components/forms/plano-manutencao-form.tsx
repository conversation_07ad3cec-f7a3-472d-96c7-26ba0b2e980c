// "use client";

// import { useForm, useFieldArray } from "react-hook-form";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { z } from "zod";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Label } from "@/components/ui/label";
// import { Textarea } from "@/components/ui/textarea";
// import { Card } from "@/components/ui/card";
// import { Plus, Trash2 } from "lucide-react";
// import { useVeiculos } from "@/context/veiculos-context";
// import { usePlanoManutencao } from "@/context/plano-manutencao-context";
// import { toast } from "sonner";
// import { planoManutencaoSchema } from "./schemas/plano-manutencao.schemas";
// import { useCliente } from "@/context/cliente-context";
// import { useTipoVeiculo } from "@/context/tipo-veiculo-context";
// import { useModeloVeiculo } from "@/context/modelo-veiculo-context";
// import { Combobox2 } from "../inputs/combobox2";

// type PlanoManutencao = z.infer<typeof planoManutencaoSchema>;

// export function PlanoManutencaoForm() {
//   const { clientes } = useCliente();
//   const { tiposVeiculo } = useTipoVeiculo();
//   const { modelosVeiculo } = useModeloVeiculo();
//   const { veiculos } = useVeiculos();
//   const { adicionarPlano } = usePlanoManutencao();

//   const form = useForm<PlanoManutencao>({
//     resolver: zodResolver(planoManutencaoSchema),
//     defaultValues: {
//       nome: "",
//       descricao: "",
//       clienteId: "",
//       tipo_veiculoId: "",
//       modelo_veiculoId: "",
//       veiculoId: "",
//       intervalo_km: 0,
//       intervalo_tempo: 0,
//       itens: [],
//       ativo: true
//     }
//   });

//   const { fields, append, remove } = useFieldArray({
//     control: form.control,
//     name: "itens"
//   });

//   const onSubmit = async (data: PlanoManutencao) => {
//     try {
//       await adicionarPlano(data);
//       toast.success("Plano de manutenção criado com sucesso!");
//       form.reset();
//     } catch (error) {
//       toast.error("Erro ao criar plano de manutenção");
//     }
//   };

//   return (
//     <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
//       <div className="grid grid-cols-2 gap-4">
//         <div className="space-y-2">
//           <Label htmlFor="nome">Nome do Plano</Label>
//           <Input
//             id="nome"
//             {...form.register("nome")}
//             placeholder="Digite o nome do plano"
//           />
//           {form.formState.errors.nome && (
//             <span className="text-sm text-red-500">
//               {form.formState.errors.nome.message}
//             </span>
//           )}
//         </div>

//         <div className="space-y-2">
//           <Label htmlFor="descricao">Descrição</Label>
//           <Textarea
//             id="descricao"
//             {...form.register("descricao")}
//             placeholder="Digite a descrição do plano"
//           />
//         </div>
//       </div>

//       <div className="grid grid-cols-2 gap-4">
//         <Combobox2
//           options={clientes.map(cliente => ({
//             value: String(cliente.id),
//             label: cliente.nome
//           }))}
//           value={String(form.watch("clienteId"))}
//           onValueChange={(value) => form.setValue("clienteId", value)}
//           placeholder="Selecione um cliente"
//         />

//         <Combobox2
//           options={tiposVeiculo.map(tipo => ({
//             value: String(tipo.id),
//             label: tipo.descricao
//           }))}
//           value={String(form.watch("tipo_veiculoId"))}
//           onValueChange={(value) => form.setValue("tipo_veiculoId", value)}
//           placeholder="Selecione o tipo"
//         />
//       </div>

//       <div className="grid grid-cols-2 gap-4">
//         <Combobox2
//           options={modelosVeiculo.map(modelo => ({
//             value: String(modelo.id),
//             label: modelo.descricao
//           }))}
//           value={String(form.watch("modelo_veiculoId"))}
//           onValueChange={(value) => form.setValue("modelo_veiculoId", value)}
//           placeholder="Selecione o modelo"
//         />

//         <Combobox2
//           options={veiculos.map(veiculo => ({
//             value: String(veiculo.id),
//             label: veiculo.placa
//           }))}
//           value={String(form.watch("veiculoId"))}
//           onValueChange={(value) => form.setValue("veiculoId", value)}
//           placeholder="Selecione o veículo"
//         />
//       </div>

//       <div className="grid grid-cols-2 gap-4">
//         <div className="space-y-2">
//           <Label htmlFor="intervalo_km">Intervalo (KM)</Label>
//           <Input
//             id="intervalo_km"
//             type="number"
//             {...form.register("intervalo_km", { valueAsNumber: true })}
//             placeholder="Digite o intervalo em KM"
//           />
//         </div>

//         <div className="space-y-2">
//           <Label htmlFor="intervalo_tempo">Intervalo (Meses)</Label>
//           <Input
//             id="intervalo_tempo"
//             type="number"
//             {...form.register("intervalo_tempo", { valueAsNumber: true })}
//             placeholder="Digite o intervalo em meses"
//           />
//         </div>
//       </div>

//       <div className="space-y-4">
//         <div className="flex items-center justify-between">
//           <Label>Itens do Plano</Label>
//           <Button
//             type="button"
//             variant="outline"
//             size="sm"
//             onClick={() =>
//               append({
//                 descricao: "",
//                 tipo: "PECAS",
//                 periodicidade_km: 0,
//                 periodicidade_tempo: 0,
//                 observacoes: ""
//               })
//             }
//           >
//             <Plus className="w-4 h-4 mr-2" />
//             Adicionar Item
//           </Button>
//         </div>

//         {fields.map((field, index) => (
//           <Card key={field.id} className="p-4">
//             <div className="grid grid-cols-2 gap-4">
//               <div className="space-y-2">
//                 <Label>Descrição do Item</Label>
//                 <Input
//                   {...form.register(`itens.${index}.descricao`)}
//                   placeholder="Digite a descrição do item"
//                 />
//               </div>

//               <div className="space-y-2">
//                 <Label>Tipo</Label>
//                 <select
//                   {...form.register(`itens.${index}.tipo`)}
//                   className="w-full p-2 border rounded"
//                 >
//                   <option value="PECAS">Peças</option>
//                   <option value="SERVICOS">Serviços</option>
//                 </select>
//               </div>

//               <div className="space-y-2">
//                 <Label>Periodicidade (KM)</Label>
//                 <Input
//                   type="number"
//                   {...form.register(`itens.${index}.periodicidade_km`, {
//                     valueAsNumber: true
//                   })}
//                   placeholder="Digite a periodicidade em KM"
//                 />
//               </div>

//               <div className="space-y-2">
//                 <Label>Periodicidade (Meses)</Label>
//                 <Input
//                   type="number"
//                   {...form.register(`itens.${index}.periodicidade_tempo`, {
//                     valueAsNumber: true
//                   })}
//                   placeholder="Digite a periodicidade em meses"
//                 />
//               </div>

//               <div className="col-span-2 space-y-2">
//                 <Label>Observações</Label>
//                 <Textarea
//                   {...form.register(`itens.${index}.observacoes`)}
//                   placeholder="Digite as observações do item"
//                 />
//               </div>

//               <Button
//                 type="button"
//                 variant="destructive"
//                 size="sm"
//                 onClick={() => remove(index)}
//                 className="col-span-2"
//               >
//                 <Trash2 className="w-4 h-4 mr-2" />
//                 Remover Item
//               </Button>
//             </div>
//           </Card>
//         ))}
//       </div>

//       <Button type="submit" className="w-full">
//         Criar Plano de Manutenção
//       </Button>
//     </form>
//   );
// } 