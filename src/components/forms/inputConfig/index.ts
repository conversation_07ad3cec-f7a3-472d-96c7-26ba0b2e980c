"use client";

import { z } from "zod";
import { UseFormReturn } from "react-hook-form";
import { apenasNumerosValidator } from "@/lib/validators";

// Função utilitária para validar veículos e evitar erros de null/undefined
const isValidVeiculo = (veiculo: any): boolean => {
  return (
    veiculo &&
    veiculo.placa &&
    veiculo.modelo &&
    veiculo.modelo.descricao &&
    typeof veiculo.modelo.descricao === "string"
  );
};

// Função para formatar display do veículo de forma segura
const formatVeiculoDisplay = (veiculo: any): string => {
  if (!isValidVeiculo(veiculo)) {
    return veiculo?.placa || "Veículo sem identificação";
  }
  return `${veiculo.placa} | ${veiculo.modelo.descricao}`;
};
import {
  abastecimentoSchema,
  acessoSchema,
  centroDeCustoSchema,
  checklistSchema,
  cidadeSchema,
  condutorSchema,
  contratoSchema,
  credenciadoSchema,
  despesaSchema,
  empenhoSchema,
  fincanceiroContratoSchema,
  itemDaVistoria,
  lembreteSchema,
  marcaSchema,
  modeloSchema,
  osSchema,
  percursoSchema,
  permissoesValidas,
  poloRegionalSchema,
  prazoDePagamentoSchema,
  servicoCredenciadoSchema,
  tipoDeFrotaSchema,
  tipoDeOsSchema,
  tipoDeVeiculoSchema,
  tiposDeCombustiveis,
  tiposDeDespesaSchema,
  usuarioSchema,
  veiculoSchema,
  versaoSchema,
  vistoriaSchema,
} from "../schemas";
import { FieldConfig } from "@/components/inputs/generic-forms-input";

const tipo_de_manutencao = ["Corretiva", "Preventiva", "Preditiva", "Sinistro"];
function GetOsConfig(datas: any[], chave: string[]) {
  const OsConfig: Record<
    keyof z.infer<typeof osSchema>,
    FieldConfig<typeof osSchema>
  > = {
    condutorId: {
      label: "Condutor",
      description: "Escolha o condutor para a ordem de serviço",
      placeholder: "",
      inputType: "combobox",
      referenceId: "condutorId",
      chave: chave[0],
      datas: datas[0],
    },
    veiculoId: {
      label: "Veículo",
      description: "Escolha um veículo para a ordem de serviço",
      placeholder: "",
      inputType: "combobox",
      referenceId: "veiculoId",
      chave: chave[1],
      datas: datas[1],
    },
    tipo_de_osId: {
      label: "Tipo de Serviço",
      description: "Escolha um tipo de serviço para a ordem de serviço",
      placeholder: "",
      inputType: "combobox",
      referenceId: "tipo_de_osId",
      chave: chave[2],
      datas: datas[2],
    },
    credenciadoId: {
      label: "Credenciado",
      description: "Escolha um credenciado para a ordem de serviço",
      placeholder: "",
      inputType: "combobox",
      referenceId: "credenciadoId",
      chave: chave[3],
      datas: datas[3],
    },
    tipo_de_manutencao: {
      label: "Tipo de Manutenção",
      placeholder: "Selecione o tipo de manutenção",
      description: "Escolha o tipo de manutenção que será realizada",
      inputType: "select",
      options: tipo_de_manutencao.map((tipo) => ({ label: tipo, value: tipo })),
    },
    estado_de_localizacao: {
      label: "Estado de Localização",
      placeholder: " São Paulo",
      description: "Escreva o estado onde o serviço será realizado",
      inputType: "text",
    },
    cidade_de_localizacao: {
      label: "Cidade de Localização",
      placeholder: " São Paulo",
      description: "Escreva a cidade onde o serviço será realizado",
      inputType: "text",
    },
    odometro_atual: {
      label: "Odômetro Atual",
      placeholder: "0 KM",
      description: "Escreva o valor atual do odômetro",
      inputType: "number",
    },
    mobilizado: {
      label: "Mobilizado",
      placeholder: " true",
      description: "Indique se o veículo está mobilizado",
      inputType: "checkbox",
    },
    descricao: {
      label: "Descrição",
      placeholder: " Descrição do serviço",
      description: "Escreva uma descrição detalhada do serviço",
      inputType: "textarea",
    },
    orcamento_individual: {
      label: "Orçamento Individual",
      placeholder: " true",
      description: "Indique se o orçamento é individual",
      inputType: "checkbox",
    },
    minimun_orcamento: {
      label: "Orçamento Mínimo",
      placeholder: " 1000",
      description: "Escreva o valor mínimo do orçamento",
      inputType: "number",
    },
    OSId: {
      label: "OS ID",
      placeholder: "",
      description: "Identificador da OS",
      inputType: "hidden",
      className: "hidden",
    },
    quoteExpiration: {
      label: "Prazo para cotação",
      placeholder: "24h",
      description: "Prazo para cotação (horas)",
      inputType: "number",
    },
    tipo_de_frota_display: {
      label: "Tipo de Frota",
      placeholder: "",
      description: "Tipo de frota do veículo selecionado (somente leitura)",
      inputType: "text",
      className: "hidden", // Campo derivado, não deve ser exibido
    },
  };
  return OsConfig;
}

function GetVeiculoConfig(datas: any[], chave: string[]) {
  const VeiculoConfig: Record<
    keyof z.infer<typeof veiculoSchema>,
    FieldConfig<typeof veiculoSchema>
  > = {
    placa: {
      label: "Placa *",
      placeholder: "ABC-123",
      description: "Informe a placa do veiculo",
      inputType: "placa-lookup",
      registerName: "placa",
      fieldClassName: "uppercase",
      className: "col-span-2 ",
    },
    tipo_de_veiculoId: {
      label: "Tipo de Veículo *",
      placeholder: "Selecione um tipo de veículo",
      description: "Escolha o tipo de veículo",
      inputType: "combobox",
      referenceId: "tipo_de_veiculoId",
      chave: chave[0],
      datas: datas[0],
      registerName: "tipo_de_veiculoId",
      name: "tipo_de_veiculoId",
      fieldClassName: "col-span-2",
    },
    marcaId: {
      label: "Marca *",
      placeholder: "Selecione uma marca",
      description: "Escolha a marca do veículo",
      inputType: "combobox",
      referenceId: "marcaId",
      chave: chave[1],
      datas: datas[1],
      registerName: "marcaId",
      name: "marcaId",
      fieldClassName: "col-span-2 col-start-1 row-start-2",
    },
    modeloId: {
      label: "Modelo *",
      placeholder: "Selecione um modelo",
      description: "Escolha o modelo do veículo",
      inputType: "combobox",
      referenceId: "modeloId",
      chave: chave[2],
      datas: datas[2],
      registerName: "modeloId",
      name: "modeloId",
      filterFn: (data) =>
        !datas[0] ||
        data.marcaId ===
          datas[0].find((marca: marca) => marca.id === data.marcaId)?.id,
      fieldClassName: "col-span-2 col-start-3 row-start-2",
    },
    versaoId: {
      label: "Versão",
      placeholder: "Selecione uma versão",
      description: "Escolha a versão do modelo",
      inputType: "combobox",
      referenceId: "versaoId",
      chave: chave[3],
      datas: datas[3],
      registerName: "versaoId",
      name: "versaoId",
      fieldClassName: "col-span-4 col-start-1 row-start-3",
      filterFn: (data) =>
        !datas[1] ||
        data.modeloId ===
          datas[1].find((modelo: modelo) => modelo.id === data.modeloId)?.id,
    },
    renovam: {
      label: "Renavam",
      placeholder: " 123456789",
      description: "Informe o Renavam do veículo",
      inputType: "text",
      registerName: "renovam",
      className: "col-span-2 col-start-1 row-start-4",
    },
    vin: {
      label: "VIN",
      placeholder: " 1HGCM82633A123456",
      description: "Informe o número do VIN",
      inputType: "text",
      registerName: "vin",
      className: "col-span-2 col-start-3 row-start-4",
    },

    numero_do_motor: {
      label: "Número do Motor",
      placeholder: " 123456789",
      description: "Informe o número do motor",
      inputType: "text",
      registerName: "numero_do_motor",
      className: "col-span-2 col-start-1 row-start-5",
    },
    ano_de_fabricacao: {
      label: "Ano de Fabricação",
      placeholder: " 2020",
      description: "Informe o ano de fabricação",
      inputType: "text",
      registerName: "ano_de_fabricacao",
      className: "col-start-3 row-start-5",
    },
    ano_do_modelo: {
      label: "Ano do Modelo",
      placeholder: " 2021",
      description: "Informe o ano do modelo",
      inputType: "text",
      registerName: "ano_do_modelo",
      className: "col-start-4 row-start-5",
    },
    cor: {
      label: "Cor",
      placeholder: " Preto",
      description: "Informe a cor do veículo",
      inputType: "text",
      registerName: "cor",
      className: "col-span-2 col-start-1 row-start-6",
    },
    tipo_de_frotaId: {
      label: "Tipo de Frota *",
      placeholder: "Selecione um tipo de frota",
      description: "Escolha o tipo de frota",
      inputType: "combobox",
      referenceId: "tipo_de_frotaId",
      chave: chave[4],
      datas: datas[4],
      registerName: "tipo_de_frotaId",
      name: "tipo_de_frotaId",
      fieldClassName: "col-span-2 col-start-3 row-start-6",
    },

    odometro_atual: {
      label: "Odômetro Atual",
      placeholder: " 15000",
      description: "Informe o valor atual do odômetro",
      inputType: "number",
      registerName: "odometro_atual",
      className: "col-span-2 col-start-1 row-start-7",
    },
    matricula: {
      label: "Matrícula",
      placeholder: " ABC1234",
      description: "Informe a matrícula do veículo",
      inputType: "text",
      registerName: "matricula",
      className: "col-start-3 row-start-7",
    },
    tag_rfid: {
      label: "Tag RFID",
      placeholder: " 123456789",
      description: "Informe o número do Tag RFID",
      inputType: "text",
      registerName: "tag_rfid",
      className: "col-start-4 row-start-7",
    },
    data_compra: {
      label: "Data de Compra",
      placeholder: "dd/mm/aaaa",
      description: "Informe a data de compra do veículo (não obrigatório)",
      inputType: "date",
      registerName: "data_compra",
      className: "col-span-2 col-start-1 row-start-8",
    },
    data_cedencia: {
      label: "Data de Cedência",
      placeholder: "dd/mm/aaaa",
      description: "Informe a data de cedência do veículo (não obrigatório)",
      inputType: "date",
      registerName: "data_cedencia",
      className: "col-span-2 col-start-3 row-start-8",
    },
    combustivel: {
      label: "Combustível",
      placeholder: "",
      description: "Informe os dados de combustível",
      inputType: "object",
      className: "col-span-2 row-span-2 col-start-5 row-start-7",
      fields: {
        capacidade_do_tanque: {
          label: "Capacidade do Tanque",
          placeholder: " 50",
          description: "Informe a capacidade do tanque",
          inputType: "number",
          registerName: "combustivel.capacidade_do_tanque",
        },
        tipos_de_combustiveis: {
          label: "Tipos de Combustíveis",
          placeholder: " Gasolina, Diesel",
          description: "Selecione os tipos de combustíveis",
          inputType: "multiple-select",
          options: ["Gasolina", "Etanol / Alcool", "Diesel", "Querosene"].map(
            (tipo) => ({
              label: tipo,
              value: tipo,
            })
          ),
          registerName: "combustivel.tipos_de_combustiveis",
        },
      },
    },
    definicoes: {
      label: "Definições",
      placeholder: "",
      description: "Informe as definições do veículo",
      inputType: "object",
      className: "col-span-4 row-span-4 row-start-9",
      fields: {
        numero_de_cambio: {
          label: "Número do Câmbio",
          placeholder: " 123456",
          description: "Informe o número do câmbio",
          inputType: "text",
          registerName: "definicoes.numero_de_cambio",
        },
        cilindradas: {
          label: "Cilindradas",
          placeholder: " 2000",
          description: "Informe as cilindradas",
          inputType: "number",
          registerName: "definicoes.cilindradas",
        },
        potencia: {
          label: "Potência",
          placeholder: " 150cv",
          description: "Informe a potência",
          inputType: "text",
          registerName: "definicoes.potencia",
        },
        segmento: {
          label: "Segmento",
          placeholder: " SUV",
          description: "Informe o segmento",
          inputType: "text",
          registerName: "definicoes.segmento",
        },
        carroceria: {
          label: "Carroceria",
          placeholder: " Sedan",
          description: "Informe o tipo de carroceria",
          inputType: "text",
          registerName: "definicoes.carroceria",
        },
        transmissao: {
          label: "Transmissão",
          placeholder: " Automática",
          description: "Informe o tipo de transmissão",
          inputType: "text",
          registerName: "definicoes.transmissao",
        },
        quantidade_de_portas: {
          label: "Quantidade de Portas",
          placeholder: " 4",
          description: "Informe a quantidade de portas",
          inputType: "number",
          registerName: "definicoes.quantidade_de_portas",
        },
        quantidade_de_assentos: {
          label: "Quantidade de Assentos",
          placeholder: " 5",
          description: "Informe a quantidade de assentos",
          inputType: "number",
          registerName: "definicoes.quantidade_de_assentos",
        },
        quantidade_de_eixos: {
          label: "Quantidade de Eixos",
          placeholder: " 2",
          description: "Informe a quantidade de eixos",
          inputType: "number",
          registerName: "definicoes.quantidade_de_eixos",
        },
        numero_de_valvulas: {
          label: "Número de Válvulas",
          placeholder: " 16",
          description: "Informe o número de válvulas",
          inputType: "text",
          registerName: "definicoes.numero_de_valvulas",
        },
        litragem: {
          label: "Litragem",
          placeholder: " 2.0",
          description: "Informe a litragem",
          inputType: "number",
          registerName: "definicoes.litragem",
        },
        combustivel: {
          label: "Combustível",
          placeholder: " Gasolina",
          description: "Informe o tipo de combustível",
          inputType: "text",
          registerName: "definicoes.combustivel",
        },
        origem_do_veiculo: {
          label: "Origem do Veículo",
          placeholder: " Nacional",
          description: "Selecione a origem do veículo",
          inputType: "select",
          options: [
            { label: "Nacional", value: "nacional" },
            { label: "Importado", value: "importado" },
          ],
          registerName: "definicoes.origem_do_veiculo",
        },
      },
    },
    lotacao: {
      label: "Lotação",
      placeholder: "",
      description: "Informe a lotação do veículo",
      inputType: "object",
      className: "col-span-2 row-span-3 col-start-5 row-start-1",
      fields: {
        centro_de_custoId: {
          label: "Centro de Custo *",
          placeholder: "Selecione um centro de custo",
          description: "Escolha o centro de custo",
          inputType: "combobox",
          referenceId: "centro_de_custoId",
          chave: chave[5],
          datas: datas[5],
          name: "Centro de custo",
          registerName: "centro_de_custoId",
        },
        estado: {
          label: "Estado *",
          placeholder: " São Paulo",
          description: "Informe o estado",
          inputType: "text",
          registerName: "lotacao.estado",
        },
        cidade: {
          label: "Cidade *",
          placeholder: " São Paulo",
          description: "Informe a cidade",
          inputType: "text",
          registerName: "lotacao.cidade",
        },
      },
    },
    faturamento: {
      label: "Faturamento",
      placeholder: "",
      description: "Informe onde o veiculo será faturado",
      inputType: "object",
      className: "col-span-2 row-span-3 col-start-5 row-start-4",
      fields: {
        centro_custoId: {
          label: "Centro de Custo",
          placeholder: "Selecione um centro de custo",
          description: "Escolha o centro de custo",
          inputType: "combobox",
          referenceId: "centro_custoId",
          chave: chave[5],
          datas: datas[5],
          registerName: "centro_custoId",
        },
        empenho: {
          label: "Empenho",
          placeholder: "Selecione um empenho",
          description: "Escolha o empenho",
          inputType: "combobox",
          referenceId: "empenhoId",
          chave: chave[6],
          datas: datas[6],
          registerName: "empenhoId",
        },
      },
    },
    fipe: {
      label: "FIPE",
      placeholder: "",
      description: "Informe os dados da FIPE",
      inputType: "object",
      className: "col-span-2 row-span-1 col-start-5 row-start-9",
      fields: {
        codigo_fipe: {
          label: "Código FIPE",
          placeholder: " 123456",
          description: "Informe o código FIPE",
          inputType: "text",
          registerName: "fipe.codigo_fipe",
        },
        valor_venal: {
          label: "Valor Venal",
          placeholder: " 50000",
          description: "Informe o valor venal",
          inputType: "text",
          registerName: "fipe.valor_venal",
        },
      },
    },
    status: {
      label: "Status",
      placeholder: " Ativo",
      description: "Selecione o status do veículo",
      inputType: "select",
      className:
        "border rounded-lg p-4 col-span-2 row-span-1 col-start-5 row-start-10",
      options: [
        { label: "Ativo", value: "Ativo" },
        { label: "Inativo", value: "Inativo" },
        { label: "Em Manutenção", value: "Em Manuntenção" },
        { label: "Sem condições de uso", value: "Sem condições de uso" },
      ],
      registerName: "status",
    },
  };
  return VeiculoConfig;
}

const CondutorConfig: Record<
  keyof z.infer<typeof condutorSchema>,
  FieldConfig<typeof condutorSchema>
> = {
  nome: {
    label: "Nome *",
    placeholder: " João Silva",
    description: "Informe o nome do condutor",
    inputType: "text",
    registerName: "nome",
  },
  matricula: {
    label: "Matrícula *",
    placeholder: " 123456",
    description: "Informe a matrícula do condutor",
    inputType: "text",
    registerName: "matricula",
  },
  contato: {
    label: "Contato *",
    placeholder: " (11) 99999-9999",
    description: "Informe o contato do condutor",
    inputType: "text",
    registerName: "contato",
  },
  email: {
    label: "E-mail *",
    placeholder: " <EMAIL>",
    description: "Informe o e-mail do condutor",
    inputType: "email",
    registerName: "email",
  },
  endereco: {
    label: "Endereço",
    placeholder: "",
    description: "Informe o endereço do condutor",
    inputType: "object",
    fields: {
      cep: {
        label: "CEP",
        placeholder: " 12345-678",
        description: "Informe o CEP",
        inputType: "cep",
        registerName: "endereco.cep",
        cepAddressMapping: {
          street: "endereco.logradouro",
          neighborhood: "endereco.bairro",
          city: "endereco.cidade",
          state: "endereco.estado",
        },
      },
      logradouro: {
        label: "Logradouro",
        placeholder: " Rua das Flores",
        description: "Informe o logradouro",
        inputType: "text",
        registerName: "endereco.logradouro",
      },
      bairro: {
        label: "Bairro",
        placeholder: " Centro",
        description: "Informe o bairro",
        inputType: "text",
        registerName: "endereco.bairro",
      },
      estado: {
        label: "Estado",
        placeholder: " São Paulo",
        description: "Informe o estado",
        inputType: "text",
        registerName: "endereco.estado",
      },
      cidade: {
        label: "Cidade",
        placeholder: " São Paulo",
        description: "Informe a cidade",
        inputType: "text",
        registerName: "endereco.cidade",
      },
    },
  },
  cnh: {
    label: "CNH",
    placeholder: "",
    description: "Informe os dados da CNH",
    inputType: "object",
    fields: {
      numero: {
        label: "Número da CNH *",
        placeholder: " 123456789",
        description: "Informe o número da CNH",
        inputType: "text",
        registerName: "cnh.numero",
      },
      categoria: {
        label: "Categoria da CNH *",
        placeholder: " AB",
        description: "Informe a categoria da CNH",
        inputType: "text",
        registerName: "cnh.categoria",
      },
      estado: {
        label: "Estado da CNH *",
        placeholder: " SP",
        description: "Informe o estado da CNH",
        inputType: "text",
        registerName: "cnh.estado",
      },
      data_emissao: {
        label: "Data de Emissão *",
        placeholder: " 01/01/2020",
        description: "Informe a data de emissão da CNH",
        inputType: "date",
        registerName: "cnh.data_emissao",
      },
      data_vencimento: {
        label: "Data de Vencimento *",
        placeholder: " 01/01/2030",
        description: "Informe a data de vencimento da CNH",
        inputType: "date",
        registerName: "cnh.data_vencimento",
      },
    },
  },
  mopp: {
    label: "MOPP",
    placeholder: "",
    description: "Informe os dados do MOPP",
    inputType: "object",
    fields: {
      numero: {
        label: "Número do MOPP",
        placeholder: " 123456",
        description: "Informe o número do MOPP",
        inputType: "text",
        registerName: "mopp.numero",
      },
      emissor: {
        label: "Emissor do MOPP",
        placeholder: " Detran",
        description: "Informe o emissor do MOPP",
        inputType: "text",
        registerName: "mopp.emissor",
      },
      data_emissao: {
        label: "Data de Emissão",
        placeholder: " 01/01/2020",
        description: "Informe a data de emissão do MOPP",
        inputType: "date",
        registerName: "mopp.data_emissao",
      },
      data_vencimento: {
        label: "Data de Vencimento",
        placeholder: " 01/01/2030",
        description: "Informe a data de vencimento do MOPP",
        inputType: "date",
        registerName: "mopp.data_vencimento",
      },
    },
  },
  ativo: {
    label: "Ativo",
    placeholder: " true",
    description: "Indique se o condutor está ativo",
    inputType: "checkbox",
    registerName: "ativo",
  },
  status: {
    label: "Status *",
    placeholder: " Ativo",
    description: "Informe o status do condutor",
    inputType: "select",
    options: [
      { label: "Ativo", value: "Ativo" },
      { label: "Inativo", value: "Inativo" },
    ],
    registerName: "status",
  },
  cpf: {
    label: "CPF",
    placeholder: " 000.000.000-00",
    description: "Informe o CPF do condutor",
    inputType: "text",
    registerName: "cpf",
  },
  centro_de_custoId: {
    label: "Centro de Custo",
    placeholder: "Selecione o centro de custo",
    description: "Informe o centro de custo do condutor",
    inputType: "combobox",
    registerName: "centro_de_custoId",
  },
  lotacao_condutorId: {
    label: "Lotação do Condutor",
    placeholder: "Selecione a lotação",
    description: "Informe a lotação do condutor",
    inputType: "combobox",
    registerName: "lotacao_condutorId",
  },
  createdAt: {
    label: "Data de Criação",
    placeholder: "",
    description: "Data de criação do registro",
    inputType: "date",
    registerName: "createdAt",
  },
};

function GetCredenciadoConfig(
  datas: any[],
  chave: string[],
  onCnpjDataConfirmed?: (data: {
    dataAbertura?: string;
    porteEmpresarial?: string;
  }) => void
) {
  const CredenciadoConfig: Record<
    keyof z.infer<typeof credenciadoSchema>,
    FieldConfig<typeof credenciadoSchema>
  > = {
    informacoes: {
      label: "Informações",
      placeholder: "",
      description: "Informações do credenciado",
      inputType: "object",
      fields: {
        cnpj: {
          label: "CNPJ",
          placeholder: " 12345678000199",
          description: "CNPJ do credenciado",
          inputType: "cnpj-lookup",
          registerName: "informacoes.cnpj",
          // Passe o callback como uma propriedade extra
          onCnpjDataConfirmed: onCnpjDataConfirmed,
        },
        razao_social: {
          label: "Razão Social",
          placeholder: " Empresa XYZ Ltda.",
          description: "Razão social do credenciado",
          inputType: "text",
          registerName: "informacoes.razao_social",
        },
        inscricao_estadual: {
          label: "Inscrição Estadual",
          placeholder: " Digite apenas números",
          description: "Inscrição estadual do credenciado (apenas números)",
          inputType: "text",
          registerName: "informacoes.inscricao_estadual",
          onKeyDown: apenasNumerosValidator,
        },
        inscricao_municipal: {
          label: "Inscrição Municipal",
          placeholder: " Digite apenas números",
          description: "Inscrição municipal do credenciado (apenas números)",
          inputType: "text",
          registerName: "informacoes.inscricao_municipal",
          onKeyDown: apenasNumerosValidator,
        },
        nome_fantasia: {
          label: "Nome Fantasia",
          placeholder: " Empresa XYZ",
          description: "Nome fantasia do credenciado",
          inputType: "text",
          registerName: "informacoes.nome_fantasia",
        },
        atividade_principal: {
          label: "Atividade Principal",
          placeholder: " Manutenção de Veículos",
          description: "Atividade principal do credenciado",
          inputType: "text",
          registerName: "informacoes.atividade_principal",
        },
        tipos_de_veiculos: {
          label: "Tipos de Veículos",
          placeholder: " Carros, Caminhões",
          description: "Tipos de veículos atendidos",
          inputType: "combobox",
          referenceId: "tipos_de_veiculosId",
          registerName: "informacoes.tipos_de_veiculos",
          datas: datas[1],
          chave: chave[1],
        },
        horario_funcionamento: {
          label: "Horário de Funcionamento",
          placeholder: " 08:00 - 18:00",
          description: "Horário de funcionamento do credenciado",
          inputType: "text",
          registerName: "informacoes.horario_funcionamento",
        },
        data_abertura: {
          label: "Data de Abertura",
          placeholder: " 01/01/2020",
          description: "Data de abertura do credenciado",
          inputType: "date",
          registerName: "informacoes.data_abertura",
        },
        porte_empresarial: {
          label: "Porte Empresarial",
          placeholder: "Microempresa",
          description: "Porte empresarial do credenciado",
          inputType: "select",
          options: [
            {
              label: "Microempreendedor Individual",
              value: "Microempreendedor Individual",
            },
            { label: "Microempresa", value: "Microempresa" },
            { label: "Empresário Individual", value: "Empresário Individual" },
            {
              label: "Empresa de pequeno porte",
              value: "Empresa de pequeno porte",
            },
            {
              label: "Empresa Individual de Responsabilidade Limitada",
              value: "Empresa Individual de Responsabilidade Limitada",
            },
            { label: "Sociedade Anônima", value: "Sociedade Anônima" },
            { label: "Empresa Limitada", value: "Empresa Limitada" },
          ],
          registerName: "informacoes.porte_empresarial",
        },
        capital_social: {
          label: "Capital Social",
          placeholder: "Somente números",
          description: "Capital social do credenciado (apenas números)",
          inputType: "currency",
          registerName: "informacoes.capital_social",
          hidden: true,
        },
        patrimonio_liquido: {
          label: "Patrimônio Líquido",
          placeholder: "Somente números",
          description: "Patrimônio líquido do credenciado (apenas números)",
          inputType: "currency",
          registerName: "informacoes.patrimonio_liquido",
          hidden: true,
        },
        observacoes_gerais: {
          label: "Observações Gerais",
          placeholder: " Observações sobre o credenciado",
          description: "Observações gerais do credenciado",
          inputType: "textarea",
          registerName: "informacoes.observacoes_gerais",
        },
        // logotipo_empresa: {
        //   label: "Logotipo da Empresa",
        //   placeholder: "",
        //   description: "Logotipo da empresa",
        //   inputType: "file",
        //   registerName: "informacoes.logotipo_empresa",
        // },
      },
    },
    contatos: {
      label: "Contatos",
      placeholder: "",
      description: "Contatos do credenciado",
      inputType: "object",
      fields: {
        telefone: {
          label: "Telefone",
          placeholder: " 11 1234-5678",
          description: "Telefone do credenciado",
          inputType: "telefone",
          registerName: "contatos.telefone",
        },
        celular: {
          label: "Celular",
          placeholder: " 11 98765-4321",
          description: "Celular do credenciado",
          inputType: "celular",
          registerName: "contatos.celular",
        },
        email: {
          label: "E-mail",
          placeholder: " <EMAIL>",
          description: "E-mail do credenciado",
          inputType: "email",
          registerName: "contatos.email",
        },
        nome_do_gerente: {
          label: "Nome do Gerente",
          placeholder: " João Silva",
          description: "Nome do gerente do credenciado",
          inputType: "text",
          registerName: "contatos.nome_do_gerente",
        },
        telefone_do_gerente: {
          label: "Telefone do Gerente",
          placeholder: " 11 1234-5678",
          description: "Telefone do gerente do credenciado",
          inputType: "telefone",
          registerName: "contatos.telefone_do_gerente",
        },
        nome_do_proprietario: {
          label: "Nome do Proprietário",
          placeholder: " Maria Souza",
          description: "Nome do proprietário do credenciado",
          inputType: "text",
          registerName: "contatos.nome_do_proprietario",
        },
        telefone_do_proprietario: {
          label: "Telefone do Proprietário",
          placeholder: " 11 1234-5678",
          description: "Telefone do proprietário do credenciado",
          inputType: "telefone",
          registerName: "contatos.telefone_do_proprietario",
        },
      },
    },
    endereco: {
      label: "Endereço",
      placeholder: "",
      description: "Endereço do credenciado",
      inputType: "object",
      fields: {
        cep: {
          label: "CEP",
          placeholder: "Digite apenas números",
          description: "CEP do credenciado",
          inputType: "cep",
          registerName: "endereco.cep",
          cepAddressMapping: {
            street: "endereco.logradouro",
            neighborhood: "endereco.bairro",
            city: "endereco.cidade",
            state: "endereco.estado",
          },
        },
        logradouro: {
          label: "Logradouro",
          placeholder: " Rua das Flores",
          description: "Logradouro do credenciado",
          inputType: "text",
          registerName: "endereco.logradouro",
        },
        bairro: {
          label: "Bairro",
          placeholder: " Centro",
          description: "Bairro do credenciado",
          inputType: "text",
          registerName: "endereco.bairro",
        },
        estado: {
          label: "Estado",
          placeholder: " São Paulo",
          description: "Estado do credenciado",
          inputType: "text",
          registerName: "endereco.estado",
        },
        cidade: {
          label: "Cidade",
          placeholder: " São Paulo",
          description: "Cidade do credenciado",
          inputType: "text",
          registerName: "endereco.cidade",
        },
        latitude: {
          label: "Latitude",
          placeholder: " -23.5505",
          description: "Latitude do credenciado",
          inputType: "text",
          registerName: "endereco.latitude",
        },
        longitude: {
          label: "Longitude",
          placeholder: " -46.6333",
          description: "Longitude do credenciado",
          inputType: "text",
          registerName: "endereco.longitude",
        },
      },
    },
    estrutura: {
      label: "Estrutura",
      placeholder: "",
      description: "Estrutura do credenciado",
      inputType: "object",
      fields: {
        capacidade_total_de_atendimento: {
          label: "Capacidade Total de Atendimento",
          placeholder: " 100",
          description: "Capacidade total de atendimento",
          inputType: "number",
          registerName: "estrutura.capacidade_total_de_atendimento",
        },
        box_para_veiculos_leves: {
          label: "Box para Veículos Leves",
          placeholder: " 10",
          description: "Número de boxes para veículos leves",
          inputType: "number",
          registerName: "estrutura.box_para_veiculos_leves",
        },
        box_para_veiculos_pesados: {
          label: "Box para Veículos Pesados",
          placeholder: " 5",
          description: "Número de boxes para veículos pesados",
          inputType: "number",
          registerName: "estrutura.box_para_veiculos_pesados",
        },
        elevadores_para_veiculos: {
          label: "Elevadores para Veículos",
          placeholder: " 2",
          description: "Número de elevadores para veículos",
          inputType: "number",
          registerName: "estrutura.elevadores_para_veiculos",
        },
        estufas_para_pintura: {
          label: "Estufas para Pintura",
          placeholder: " 1",
          description: "Número de estufas para pintura",
          inputType: "number",
          registerName: "estrutura.estufas_para_pintura",
        },
        // imagens: {
        //   label: "Imagens",
        //   placeholder: "",
        //   description: "Imagens da estrutura do credenciado",
        //   inputType: "file",
        //   registerName: "estrutura.imagens",
        // },
      },
    },
    documentacao: {
      label: "Documentação",
      placeholder: "",
      description: "Documentação do credenciado",
      inputType: "file",
      registerName: "documentacao",
    },
    configuracoes: {
      label: "Configurações",
      placeholder: "",
      description: "Configurações do credenciado",
      inputType: "object",
      fields: {
        orcamentista: {
          label: "Orçamentista",
          placeholder: "",
          description: "Possui orçamentista?",
          inputType: "checkbox",
          registerName: "configuracoes.orcamentista",
        },
        atende_placa_verde: {
          label: "Atende Placa Verde",
          placeholder: "",
          description: "Atende veículos com placa verde?",
          inputType: "checkbox",
          registerName: "configuracoes.atende_placa_verde",
        },
        concessionaria: {
          label: "Concessionária",
          placeholder: "",
          description: "É uma concessionária?",
          inputType: "checkbox",
          registerName: "configuracoes.concessionaria",
        },
        orcamentacao: {
          label: "Orçamentação",
          placeholder: "",
          description: "Realiza orçamentação?",
          inputType: "checkbox",
          registerName: "configuracoes.orcamentacao",
        },
        prazo_contratualId: {
          label: "Prazo Contratual",
          placeholder: " 30 dias",
          description: "Prazo contratual do credenciado",
          inputType: "combobox",
          referenceId: "prazo_contratualId",
          registerName: "configuracoes.prazo_contratualId",
          datas: datas[2],
          chave: chave[2],
        },
        polo_regionalId: {
          label: "Polo Regional",
          placeholder: " Polo Norte",
          description: "Selecione o polo regional",
          inputType: "combobox",
          referenceId: "polo_regionalId",
          registerName: "configuracoes.polo_regionalId",
          datas: datas[0],
          chave: chave[0],
        },
      },
    },
    servicos: {
      label: "Serviços",
      placeholder: "",
      description: "Serviços oferecidos pelo credenciado",
      inputType: "multiple-select",
      options:
        datas[3]?.map((servico: any) => ({
          value: servico.id,
          label: servico[chave[3]] || servico.nome || servico.descricao,
        })) || [],
      registerName: "servicos",
    },
  };

  return CredenciadoConfig;
}

function GetVistoriaConfig(datas: any[], chave: string) {
  const VistoriaConfig: Record<
    keyof z.infer<typeof vistoriaSchema>,
    FieldConfig<typeof vistoriaSchema>
  > = {
    veiculoId: {
      label: "Escolha o veículo",
      placeholder: " Ford Ka",
      description: "Escolha um veículo",
      inputType: "combobox",
      datas: datas,
      chave: chave,
    },
    tipo_de_avaliacao: {
      label: "Tipo de Avaliação",
      placeholder: " Inicial",
      description: "Escolha o tipo de avaliação",
      inputType: "select",
      options: [
        { label: "Inicial", value: "inicial" },
        { label: "Periódica", value: "periódica" },
      ],
    },
    data: {
      label: "Data",
      placeholder: " 01/01/2023",
      description: "Escreva a data da vistoria",
      inputType: "date",
    },
    odometro_atual: {
      label: "Odômetro Atual",
      placeholder: "0 KM",
      description: "Escreva o valor atual do odômetro",
      inputType: "number",
    },
    previsao_troca_de_oleo: {
      label: "Previsão de Troca de Óleo",
      placeholder: "0 KM",
      description: "Informações sobre a troca de óleo",
      inputType: "object",
      fields: {
        km: {
          label: "KM",
          placeholder: "Digite a quilometragem",
          description: "Quilometragem para troca de óleo",
          inputType: "text",
          registerName: "previsao_troca_de_oleo.km",
        },
        data: {
          label: "Data",
          placeholder: "Selecione a data",
          description: "Data prevista para troca de óleo",
          inputType: "date",
          registerName: "previsao_troca_de_oleo.data",
        },
      },
    },
    observacao: {
      label: "Observação",
      placeholder: " Observações sobre a vistoria",
      description: "Escreva observações sobre a vistoria",
      inputType: "textarea",
    },
  };
  return VistoriaConfig;
}

function GetItensDaVistoria(datas: any[], chave: string) {
  const itemDaVistoriaConfig: Record<
    keyof z.infer<typeof itemDaVistoria>,
    FieldConfig<typeof itemDaVistoria>
  > = {
    descricao: {
      label: "Descrição do item",
      placeholder: " Pneus",
      description: "Descreva o nome do item para a vistoria",
      inputType: "text",
    },
    tipo_de_veiculosId: {
      label: "Tipo de Veículos",
      description: "Selecione os tipos de veiculos para esse item",
      placeholder: " Todos",
      inputType: "combobox",
      datas: datas,
      chave: chave,
    },
  };
  return itemDaVistoriaConfig;
}

const TiposDeDeDespesaConfig: Record<
  keyof z.infer<typeof tiposDeDespesaSchema>,
  FieldConfig<typeof usuarioSchema>
> = {
  descricao: {
    label: "Descrição do item",
    placeholder: " Pneus",
    inputType: "text",
    description: "Descreva o tipo de desapesa",
  },
};

function GetChecklistConfig(datas: any[], chave: string[]) {
  const ChecklistConfig: Record<
    keyof z.infer<typeof checklistSchema>,
    FieldConfig<typeof checklistSchema>
  > = {
    veiculoId: {
      label: "Escolha o veículo *",
      placeholder: " Ford Ka",
      description: "Escolha um veículo",
      inputType: "combobox",
      datas: datas[0].filter(isValidVeiculo).map((veiculo: any) => ({
        ...veiculo,
        display: formatVeiculoDisplay(veiculo),
      })),
      chave: "display",
      fieldClassName: "col-span-full sm:col-span-6",
      onChange: (value: string, form: any) => {
        const selectedVehicle = datas[0].find((v: any) => v.id === value);
        if (selectedVehicle) {
          // Optional: set odometer to the vehicle's current value as a starting point
          form.setValue(
            "odometro_atual",
            parseInt(selectedVehicle.odometro_atual)
          );

          // Validate current value (in case user manually edited it before selecting vehicle)
          const currentOdometer = form.getValues("odometro_atual");
          if (
            currentOdometer &&
            currentOdometer < parseInt(selectedVehicle.odometro_atual)
          ) {
            form.setError("odometro_atual", {
              type: "manual",
              message: `O valor do odômetro não pode ser menor que ${selectedVehicle.odometro_atual}`,
            });
          }
        }
      },
    },
    condutorId: {
      label: "Escolha o condutor *",
      placeholder: " João Silva",
      description: "Escolha um condutor",
      inputType: "combobox",
      datas: datas[1],
      chave: chave[1],
      fieldClassName: "col-span-full sm:col-span-6",
    },
    credenciadoId: {
      label: "Selecione o Credenciado *",
      placeholder: " AUTO PEÇAS LTDA",
      description: "Escolha um Credenciado",
      inputType: "combobox",
      datas: datas[2].map((credenciado: any) => ({
        ...credenciado,
        display: credenciado.informacoes?.[0]?.razao_social || "N/A",
      })),
      chave: "display",
      referenceId: "id",
      fieldClassName: "col-span-full sm:col-span-6",
    },
    osId: {
      label: "Selecione o número da OS",
      placeholder: " 123456",
      description: "Escolha uma OS",
      inputType: "combobox",
      datas: datas[3],
      chave: chave[3],
    },
    situacao_do_veiculo: {
      label: "Situação do Veículo",
      placeholder: "",
      description: "Verifique a situação do veículo",
      inputType: "object",
      className: "col-span-full sm:col-span-6",
      fields: {
        pneus: {
          label: "Pneus",
          placeholder: "",
          description: "Estado dos pneus",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.pneus",
        },
        rodas: {
          label: "Rodas",
          placeholder: "",
          description: "Estado das rodas",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.rodas",
        },
        estepe: {
          label: "Estepe",
          placeholder: "",
          description: "Estado do estepe",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.estepe",
        },
        freio_de_mão: {
          label: "Freio de Mão",
          placeholder: "",
          description: "Estado do freio de mão",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.freio_de_mão",
        },
        freio_de_pé: {
          label: "Freio de Pé",
          placeholder: "",
          description: "Estado do freio de pé",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.freio_de_pé",
        },
        embreagem: {
          label: "Embreagem",
          placeholder: "",
          description: "Estado da embreagem",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.embreagem",
        },
        luzes_do_painel: {
          label: "Luzes do Painel",
          placeholder: "",
          description: "Estado das luzes do painel",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.luzes_do_painel",
        },
        buzina: {
          label: "Buzina",
          placeholder: "",
          description: "Estado da buzina",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.buzina",
        },
        lanternas_dianteiras: {
          label: "Lanternas Dianteiras",
          placeholder: "",
          description: "Estado das lanternas dianteiras",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.lanternas_dianteiras",
        },
        lanternas_traseiras: {
          label: "Lanternas Traseiras",
          placeholder: "",
          description: "Estado das lanternas traseiras",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.lanternas_traseiras",
        },
        farol_baixo: {
          label: "Farol Baixo",
          placeholder: "",
          description: "Estado do farol baixo",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.farol_baixo",
        },
        farol_alto: {
          label: "Farol Alto",
          placeholder: "",
          description: "Estado do farol alto",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.farol_alto",
        },
        setas_dianteiras: {
          label: "Setas Dianteiras",
          placeholder: "",
          description: "Estado das setas dianteiras",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.setas_dianteiras",
        },
        setas_traseiras: {
          label: "Setas Traseiras",
          placeholder: "",
          description: "Estado das setas traseiras",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.setas_traseiras",
        },
        luzes_de_emergência: {
          label: "Luzes de Emergência",
          placeholder: "",
          description: "Estado das luzes de emergência",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.luzes_de_emergência",
        },
        luzes_de_freio: {
          label: "Luzes de Freio",
          placeholder: "",
          description: "Estado das luzes de freio",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.luzes_de_freio",
        },
        luzes_de_ré: {
          label: "Luzes de Ré",
          placeholder: "",
          description: "Estado das luzes de ré",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.luzes_de_ré",
        },
        espelhos_retrovisores: {
          label: "Espelhos Retrovisores",
          placeholder: "",
          description: "Estado dos espelhos retrovisores",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.espelhos_retrovisores",
        },
        estado_geral_da_carroceria: {
          label: "Estado Geral da Carroceria",
          placeholder: "",
          description: "Estado geral da carroceria",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.estado_geral_da_carroceria",
        },
        nível_de_água: {
          label: "Nível de Água",
          placeholder: "",
          description: "Estado do nível de água",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.nível_de_água",
        },
        limpador_para_brisa: {
          label: "Limpador de Para-brisa",
          placeholder: "",
          description: "Estado do limpador de para-brisa",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.limpador_para_brisa",
        },
        nível_de_fluído: {
          label: "Nível de Fluído",
          placeholder: "",
          description: "Estado do nível de fluído",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.nível_de_fluído",
        },
        óleo_do_motor: {
          label: "Óleo do Motor",
          placeholder: "",
          description: "Estado do óleo do motor",
          inputType: "select",
          options: [
            { label: "Conforme", value: "Conforme" },
            { label: "Não Conforme", value: "Não Conforme" },
          ],
          registerName: "situacao_do_veiculo.óleo_do_motor",
        },
        observacoes: {
          label: "Observações",
          placeholder: " Observações",
          description: "Informe observações",
          inputType: "textarea",
          registerName: "observacoes",
        },
        observacoesRodas: {
          label: "Observações (Rodas)",
          placeholder: " Observações sobre as rodas",
          description: "Informe observações sobre as rodas",
          inputType: "textarea",
          registerName: "observacoes_rodas",
        },
        observacoesLuzesExternas: {
          label: "Observações (Luzes Externas)",
          placeholder: " Observações sobre as luzes externas",
          description: "Informe observações sobre as luzes externas",
          inputType: "textarea",
          registerName: "observacoes_luzes_externas",
        },
        observacoesLuzDoPainel: {
          label: "Observações (Luz do Painel)",
          placeholder: " Observações sobre a luz do painel",
          description: "Informe observações sobre a luz do painel",
          inputType: "textarea",
          registerName: "observacoes_luz_do_painel",
        },
        observacoesLimpeza: {
          label: "Observações (Limpeza)",
          placeholder: " Observações sobre as limpeza",
          description: "Informe observações sobre a limpeza",
          inputType: "textarea",
          registerName: "observacoes_limpeza",
        },
        observacoesEstadoGeral: {
          label: "Observações (Estado Geral)",
          placeholder: " Observações sobre o estado geral",
          description: "Informe observações sobre o estado geral",
          inputType: "textarea",
          registerName: "observacoes_estado_geral",
        },
      },
    },
    tipo: {
      label: "Tipo",
      placeholder: " Entrada",
      description: "Escolha o tipo de checklist",
      inputType: "select",
      fieldClassName: "col-span-full sm:col-span-3",
      options: [
        { label: "Entrada", value: "Entrada" },
        { label: "Saída", value: "Saída" },
      ],
      registerName: "tipo",
    },
    data: {
      label: "Data",
      placeholder: " 2023-01-01",
      description: "Data do checklist",
      inputType: "date",
      fieldClassName: "col-span-full sm:col-span-3",
    },
    previsao_entrega: {
      label: "Previsão de Entrega",
      placeholder: " 2023-01-01",
      description: "Previsão de entrega do veículo",
      inputType: "date",
      fieldClassName: "col-span-full sm:col-span-3",
    },
    odometro_atual: {
      label: "Odômetro Atual *",
      placeholder: " 15000",
      description: "Valor atual do odômetro",
      inputType: "number",
      fieldClassName: "col-span-full sm:col-span-3",
      validate: (value: number, form: any) => {
        const veiculoId = form.getValues("veiculoId");
        if (!veiculoId) return true;

        const selectedVehicle = datas[0].find((v: any) => v.id === veiculoId);
        if (!selectedVehicle) return true;

        const currentOdometer = parseInt(selectedVehicle.odometro_atual);
        if (value < currentOdometer) {
          return `O valor do odômetro não pode ser menor que ${currentOdometer}`;
        }

        return true;
      },
      onChange: (value: number, form: any) => {
        const veiculoId = form.getValues("veiculoId");
        if (!veiculoId) return;

        const selectedVehicle = datas[0].find((v: any) => v.id === veiculoId);
        if (!selectedVehicle) return;

        const currentOdometer = parseInt(selectedVehicle.odometro_atual);
        if (value < currentOdometer) {
          form.setError("odometro_atual", {
            type: "manual",
            message: `O valor do odômetro não pode ser menor que ${currentOdometer}`,
          });
        } else {
          form.clearErrors("odometro_atual");
        }
      },
    },
    nivel_do_combustivel: {
      label: "Nível do Combustível",
      placeholder: " 1/2",
      description: "Nível atual do combustível",
      inputType: "select",
      fieldClassName: "col-span-full sm:col-span-3",
      options: [
        { label: "Vazio", value: "Vazio" },
        { label: "1/4", value: "1/4" },
        { label: "1/2", value: "1/2" },
        { label: "3/4", value: "3/4" },
        { label: "Cheio", value: "Cheio" },
      ],
    },
    arquivos: {
      label: "Imagens",
      placeholder: "",
      description: "Anexe imagens do veículo",
      inputType: "file",
      registerName: "imagens",
      fieldClassName: "col-span-full sm:col-span-6 hidden",
      className: "hidden",
    },
  };
  return ChecklistConfig;
}
function GetSimplifiedChecklistConfig(datas: any[], chave: string[]) {
  // Obtém a configuração completa
  const fullConfig = GetChecklistConfig(datas, chave);

  // Retorna apenas os campos necessários para o formulário simplificado
  return {
    veiculoId: fullConfig.veiculoId,
    condutorId: fullConfig.condutorId,
    credenciadoId: fullConfig.credenciadoId,
    osId: fullConfig.osId,
    tipo: fullConfig.tipo,
    data: fullConfig.data,
    previsao_entrega: fullConfig.previsao_entrega,
    odometro_atual: fullConfig.odometro_atual,
    // Oculta o campo nivel_do_combustivel mas mantém no schema
    nivel_do_combustivel: {
      ...fullConfig.nivel_do_combustivel,
      className: "hidden",
      fieldClassName: "hidden",
      hidden: true,
    },
    arquivos: fullConfig.arquivos,
    // Não incluímos situacao_do_veiculo aqui
  };
}

function GetAbastecimentoConfig(datas: any[], chave: string[]) {
  const AbastecimentoConfig: Record<
    keyof z.infer<typeof abastecimentoSchema>,
    FieldConfig<typeof abastecimentoSchema>
  > = {
    veiculoId: {
      label: "Escolha o veículo *",
      placeholder: " Ford Ka",
      description: "Escolha um veículo",
      inputType: "combobox",
      datas: datas[0].filter(isValidVeiculo).map((veiculo: any) => ({
        ...veiculo,
        display: formatVeiculoDisplay(veiculo),
      })),
      chave: "display",
    },
    data_e_hora: {
      label: "Data e Hora *",
      placeholder: " 01/01/2023 12:00",
      description: "Informe a data e hora do abastecimento",
      inputType: "datetime-local",
      registerName: "data_e_hora",
    },
    odometro: {
      label: "Odômetro *",
      placeholder: "0 KM",
      description: "Informe o valor do odômetro",
      inputType: "number",
      registerName: "odometro",
    },
    combustivel: {
      label: "Combustível *",
      placeholder: "Selecione o combustível",
      description: "Escolha o tipo de combustível",
      inputType: "multiple-select",
      options: tiposDeCombustiveis.map((combustivel) => ({
        label: combustivel,
        value: combustivel,
      })),
      registerName: "combustivel",
    },
    preco_l: {
      label: "Preço por Litro *",
      placeholder: " 5.50",
      description: "Informe o preço por litro",
      inputType: "number",
      registerName: "preco_l",
    },
    valor_total: {
      label: "Valor Total *",
      placeholder: " 200.00",
      description: "Informe o valor total do abastecimento",
      inputType: "number",
      registerName: "valor_total",
    },
    litros: {
      label: "Litros *",
      placeholder: " 40",
      description: "Informe a quantidade de litros",
      inputType: "number",
      registerName: "litros",
    },
    condutorId: {
      label: "Condutor *",
      placeholder: "Selecione um condutor",
      description: "Escolha o condutor relacionado",
      inputType: "combobox",
      referenceId: "condutorId",
      chave: chave[1],
      datas: datas[1],
      registerName: "condutorId",
    },
    credenciadoId: {
      label: "Selecione o Posto de Combustivel *",
      placeholder: " Posto Shell LTDA",
      description: "Escolha um Credenciado",
      inputType: "combobox",
      datas: datas[2].map((credenciado: any) => ({
        ...credenciado,
        display: credenciado.informacoes[0]?.razao_social || "N/A", // Exibe a razão social
      })),
      chave: "display", // Exibe a razão social no combobox
      referenceId: "id", // Valor real a ser enviado
    },
    observacoes: {
      label: "Observações",
      placeholder: " Observações sobre o abastecimento",
      description: "Informe observações adicionais",
      inputType: "textarea",
      registerName: "observacoes",
    },
    arquivos: {
      label: "Arquivos",
      placeholder: "",
      description: "Anexe arquivos relacionados",
      inputType: "file",
      registerName: "arquivos",
    },
    completando_tanque: {
      label: "Completando o tanque",
      placeholder: "",
      description: "Marque a caixa se você está completando o tanque",
      inputType: "checkbox",
      fieldClassName: "col-span-2",
    },
  };
  return AbastecimentoConfig;
}

function GetDespesaConfig(datas: any[], chave: string[]) {
  const DespesaConfig: Record<
    keyof z.infer<typeof despesaSchema>,
    FieldConfig<typeof despesaSchema>
  > = {
    veiculoId: {
      label: "Escolha o veículo",
      placeholder: " Ford Ka",
      description: "Escolha um veículo",
      inputType: "combobox",
      datas: datas[0].filter(isValidVeiculo).map((veiculo: any) => ({
        ...veiculo,
        display: formatVeiculoDisplay(veiculo),
      })),
      chave: "display",
    },
    condutorId: {
      label: "Condutor",
      placeholder: " João Silva",
      description: "Selecione o condutor",
      inputType: "combobox",
      datas: datas[2],
      chave: chave[2],
    },
    data_e_hora: {
      label: "Data e Hora",
      placeholder: " 2023-01-01 12:00",
      description: "Data e hora da despesa",
      inputType: "datetime-local",
    },
    odometro: {
      label: "Odômetro",
      placeholder: "0 KM",
      description: "Quilometragem atual do veículo",
      inputType: "number",
    },
    tipo_de_despesaId: {
      label: "Tipo de Despesa",
      placeholder: " Combustível",
      description: "Selecione o tipo de despesa",
      inputType: "combobox",
      datas: datas[1],
      chave: chave[1],
    },
    valor: {
      label: "Valor",
      placeholder: "R$ 100.00",
      description: "Valor da despesa",
      inputType: "number",
    },
    local: {
      label: "Local",
      placeholder: " Posto XYZ",
      description: "Local onde a despesa foi realizada",
      inputType: "text",
    },
    observacoes: {
      label: "Observações",
      placeholder: " Observações sobre a despesa",
      description: "Escreva observações sobre a despesa",
      inputType: "textarea",
    },
    arquivos: {
      label: "Arquivos",
      placeholder: "",
      description: "Anexe arquivos relacionados à despesa",
      inputType: "file",
      registerName: "arquivos",
    },
  };
  return DespesaConfig;
}
const CidadeConfig: Record<
  keyof z.infer<typeof cidadeSchema>,
  FieldConfig<typeof cidadeSchema>
> = {
  estado: {
    label: "Estado",
    placeholder: " São Paulo",
    description: "Informe o estado",
    inputType: "text",
    registerName: "estado",
  },
  cidade: {
    label: "Cidade",
    placeholder: " São Paulo",
    description: "Informe a cidade",
    inputType: "text",
    registerName: "cidade",
  },
};
function GetCidadeConfig() {
  return CidadeConfig;
}
const PoloRegionalConfig: Record<
  keyof z.infer<typeof poloRegionalSchema>,
  FieldConfig<typeof poloRegionalSchema>
> = {
  descricao: {
    label: "Descrição",
    placeholder: " Polo Regional Norte",
    description: "Descrição do polo regional",
    inputType: "text",
  },
};
const PrazoDePagamentoConfig: Record<
  keyof z.infer<typeof prazoDePagamentoSchema>,
  FieldConfig<typeof prazoDePagamentoSchema>
> = {
  descricao: {
    label: "Descrição",
    placeholder: " Prazo de 30 dias",
    description: "Descrição do prazo de pagamento",
    inputType: "text",
  },
};

function GetPercursoConfig(datas: any[], chave: string[]) {
  const PercursoConfig: Record<
    keyof z.infer<typeof percursoSchema>,
    FieldConfig<typeof percursoSchema>
  > = {
    veiculoId: {
      label: "Veículo",
      placeholder: " Ford Ka",
      description: "Selecione um veículo",
      inputType: "combobox",
      datas: datas[0],
      chave: chave[0],
    },
    condutorId: {
      label: "Condutor",
      placeholder: " João Silva",
      description: "Selecione um condutor",
      inputType: "combobox",
      datas: datas[1],
      chave: chave[1],
    },
    motivo: {
      label: "Motivo",
      placeholder: " Viagem a trabalho",
      description: "Motivo do percurso",
      inputType: "text",
    },
    arquivos: {
      label: "Arquivos",
      placeholder: "",
      description: "Anexe arquivos relacionados ao percurso",
      inputType: "file",
      registerName: "arquivos",
    },
    origem: {
      label: "Origem",
      placeholder: "",
      description: "Informações sobre a origem",
      inputType: "object",
      fields: {
        local: {
          label: "Local de Origem",
          placeholder: " São Paulo",
          description: "Local de origem do percurso",
          inputType: "text",
          registerName: "origem.local",
        },
        data: {
          label: "Data de Origem",
          placeholder: " 2023-01-01",
          description: "Data de origem do percurso",
          inputType: "date",
          registerName: "origem.data",
        },
        odometro: {
          label: "Odômetro de Origem",
          placeholder: "0 KM",
          description: "Odômetro no local de origem",
          inputType: "number",
          registerName: "origem.odometro",
        },
      },
    },
    destino: {
      label: "Destino",
      placeholder: "",
      description: "Informações sobre o destino",
      inputType: "object",
      fields: {
        local: {
          label: "Local de Destino",
          placeholder: " Rio de Janeiro",
          description: "Local de destino do percurso",
          inputType: "text",
          registerName: "destino.local",
        },
        data: {
          label: "Data de Destino",
          placeholder: " 2023-01-01",
          description: "Data de destino do percurso",
          inputType: "date",
          registerName: "destino.data",
        },
        odometro: {
          label: "Odômetro de Destino",
          placeholder: "0 KM",
          description: "Odômetro no local de destino",
          inputType: "number",
          registerName: "destino.odometro",
        },
      },
    },

    observacoes: {
      label: "Observações",
      placeholder: " Observações sobre o percurso",
      description: "Escreva observações sobre o percurso",
      inputType: "textarea",
    },
  };
  return PercursoConfig;
}

function GetLembreteConfig(datas: any[], chave: string[]) {
  const LembreteConfig: Record<
    keyof z.infer<typeof lembreteSchema>,
    FieldConfig<typeof lembreteSchema>
  > = {
    veiculoId: {
      label: "Veículo",
      placeholder: " Ford Ka",
      description: "Selecione um veículo",
      inputType: "combobox",
      datas: datas[0],
      chave: chave[0],
    },
    tipo: {
      label: "Tipo",
      placeholder: " DESPESA",
      description: "Selecione o tipo de lembrete",
      inputType: "select",
      options: [
        { label: "Despesa", value: "DESPESA" },
        { label: "Serviço", value: "SERVICO" },
      ],
    },
    referencialId: {
      label: "Referência",
      placeholder: " Despesa 123",
      description: "Selecione uma referência",
      inputType: "combobox",
      datas: datas[0],
      chave: chave[0],
    },
    odometro: {
      label: "Odômetro",
      placeholder: "0 KM",
      description: "Quilometragem atual do veículo",
      inputType: "number",
    },
    repeticao: {
      label: "Repetição",
      placeholder: "",
      description: "Configuração de repetição do lembrete",
      inputType: "object",
      fields: {
        tipo: {
          label: "Tipo de Repetição",
          placeholder: " Único",
          description: "Selecione o tipo de repetição",
          inputType: "select",
          options: [
            { label: "Único", value: "UNICO" },
            { label: "Repetir a cada", value: "REPETIR_A_CADA" },
          ],
        },
        intervalo: {
          label: "Intervalo",
          placeholder: " 7",
          description: "Intervalo de repetição (em dias, semanas, etc.)",
          inputType: "number",
        },
      },
    },
    data: {
      label: "Data",
      placeholder: " 2023-01-01",
      description: "Data do lembrete",
      inputType: "date",
    },
    dataDesejada: {
      label: "Data Desejada",
      placeholder: " 2023-01-01",
      description: "Data desejada para o lembrete",
      inputType: "date",
    },
    quantidade: {
      label: "Quantidade",
      placeholder: " 3",
      description: "Quantidade de repetições",
      inputType: "number",
    },
    periodo: {
      label: "Período",
      placeholder: " Dias",
      description: "Período de repetição",
      inputType: "select",
      options: [
        { label: "Dias", value: "DIAS" },
        { label: "Semanas", value: "SEMANAS" },
        { label: "Meses", value: "MESES" },
      ],
    },
    observacoes: {
      label: "Observações",
      placeholder: " Observações sobre o lembrete",
      description: "Escreva observações sobre o lembrete",
      inputType: "textarea",
    },
  };
  return LembreteConfig;
}
const VersaoConfig: Record<
  keyof z.infer<typeof versaoSchema>,
  FieldConfig<typeof usuarioSchema>
> = {
  descricao: {
    label: "Descrição",
    placeholder: " Descrição da versão",
    description: "Escreva observações sobre o percurso",
    inputType: "text",
  },
};

export function getFinanceiroContratoConfig(centroDeCustosOptions: any[]) {
  const financeiroContratoConfig: Record<
    keyof z.infer<typeof fincanceiroContratoSchema>,
    FieldConfig<typeof fincanceiroContratoSchema>
  > = {
    send_nf_to: {
      label: "Enviar NF para",
      placeholder: "Selecione",
      description: "Selecione para quem enviar a NF",
      inputType: "select",
      options: [
        {
          label: "Carletto",
          value: "carletto",
        },
        ...centroDeCustosOptions?.map(
          ({ descricao }: { descricao: string }) => ({
            label: descricao,
            value: descricao,
          })
        ),
      ],
      registerName: "send_nf_to",
      className: "col-span-2",
    },
    cnpj_financial: {
      label: "CNPJ",
      placeholder: "00.000.000/0000-00",
      description: "Digite o CNPJ da empresa",
      inputType: "cnpj-lookup",
      registerName: "cnpj_financial",
    },
    numero_contrato_financial: {
      label: "Número Contrato",
      placeholder: "12456",
      description: "Informe o número do contrato",
      inputType: "text",
      registerName: "numero_contrato_financial",
    },
    endereco_financial: {
      label: "Endereço",
      placeholder: "",
      description: "Informe o endereço",
      inputType: "object",
      fields: {
        cep: {
          label: "CEP",
          placeholder: "Digite apenas números",
          description: "Informe o CEP",
          inputType: "cep",
          registerName: "endereco_financial.cep",
          cepAddressMapping: {
            street: "endereco_financial.logradouro",
            neighborhood: "endereco_financial.bairro",
            city: "endereco_financial.cidade",
            state: "endereco_financial.estado",
          },
        },
        logradouro: {
          label: "Logradouro",
          placeholder: " Rua das Flores",
          description: "Informe o logradouro",
          inputType: "text",
          registerName: "endereco_financial.logradouro",
        },
        bairro: {
          label: "Bairro",
          placeholder: " Centro",
          description: "Informe o bairro",
          inputType: "text",
          registerName: "endereco_financial.bairro",
        },
        estado: {
          label: "Estado",
          placeholder: " São Paulo",
          description: "Informe o estado",
          inputType: "text",
          registerName: "endereco_financial.estado",
        },
        cidade: {
          label: "Cidade",
          placeholder: " São Paulo",
          description: "Informe a cidade",
          inputType: "text",
          registerName: "endereco_financial.cidade",
        },
      },
    },
    validacao_nf: {
      label: "Validação de NF",
      placeholder: "",
      description: "Configure a validação de NF",
      inputType: "object",
      fields: {
        placa_do_veiculo: {
          label: "Placa do Veículo",
          placeholder: " true",
          description: "Incluir placa do veículo na NF",
          inputType: "checkbox",
          registerName: "validacao_nf.placa_do_veiculo",
        },
        numero_da_os: {
          label: "Número da OS",
          placeholder: " true",
          description: "Incluir número da OS na NF",
          inputType: "checkbox",
          registerName: "validacao_nf.numero_da_os",
        },
        modelo_do_veiculo: {
          label: "Modelo do Veículo",
          placeholder: " true",
          description: "Incluir modelo do veículo na NF",
          inputType: "checkbox",
          registerName: "validacao_nf.modelo_do_veiculo",
        },
        numero_do_contrato: {
          label: "Número do Contrato",
          placeholder: " true",
          description: "Incluir número do contrato na NF",
          inputType: "checkbox",
          registerName: "validacao_nf.numero_do_contrato",
        },
      },
    },
  };

  return financeiroContratoConfig;
}

function GetContratoConfig() {
  const ContratoConfig: Record<
    keyof z.infer<typeof contratoSchema>,
    FieldConfig<typeof contratoSchema>
  > = {
    nome: {
      label: "Nome",
      placeholder: " Contrato de Manutenção",
      description: "Informe o nome do contrato",
      inputType: "text",
      registerName: "nome",
    },
    numero: {
      label: "Número do Contrato",
      placeholder: " 123456",
      description: "Informe o número do contrato",
      inputType: "text",
      registerName: "numero",
    },
    data_inicial: {
      label: "Data Inicial",
      placeholder: " 01/01/2023",
      description: "Informe a data inicial do contrato",
      inputType: "date",
      registerName: "data_inicial",
    },
    data_final: {
      label: "Data Final",
      placeholder: " 31/12/2023",
      description: "Informe a data final do contrato",
      inputType: "date",
      registerName: "data_final",
    },
    valor_do_contrato: {
      label: "Valor do Contrato",
      placeholder: " R$ 100000",
      description: "Informe o valor do contrato",
      inputType: "currency",
      registerName: "valor_do_contrato",
    },
    taxa_administrativa: {
      label: "Taxa Administrativa em (%)",
      placeholder: "5%",
      description: "Informe a taxa administrativa",
      inputType: "number",
      registerName: "taxa_administrativa",
    },
    cnpj: {
      label: "CNPJ",
      placeholder: "00.000.000/0000-00",
      description: "Digite o CNPJ da empresa",
      inputType: "cnpj-lookup",
      registerName: "cnpj",
    },
    razao_social: {
      label: "Razão Social",
      placeholder: " Empresa XYZ Ltda.",
      description: "Informe a razão social",
      inputType: "text",
      registerName: "razao_social",
    },
    logotipo: {
      label: "Logotipo",
      placeholder: "",
      description: "Carregue o logotipo",
      inputType: "file",
      registerName: "logotipo",
      className: "col-span-1/2",
    },
    limite_gastos_percent: {
      label: "Limite de Gastos por veiculo em 12 meses (em %)",
      placeholder: "10",
      description: "Informe o limite de gastos em %",
      inputType: "number",
      registerName: "limite_gastos_percent",
      className: "col-span-1/2",
    },
    contato: {
      label: "Contato",
      placeholder: "",
      description: "Informe os dados de contato",
      inputType: "object",
      fields: {
        responsavel: {
          label: "Responsável",
          placeholder: " João Silva",
          description: "Informe o nome do responsável",
          inputType: "text",
          registerName: "contato.responsavel",
        },
        telefone: {
          label: "Telefone",
          placeholder: " 11 1234-5678",
          description: "Informe o telefone do responsável",
          inputType: "telefone",
          registerName: "contato.telefone",
        },
        email: {
          label: "E-mail",
          placeholder: " <EMAIL>",
          description: "Informe o e-mail",
          inputType: "email",
          registerName: "contato.email",
        },
      },
    },
    endereco: {
      label: "Endereço",
      placeholder: "",
      description: "Informe o endereço",
      inputType: "object",
      fields: {
        cep: {
          label: "CEP",
          placeholder: "Digite apenas números",
          description: "Informe o CEP",
          inputType: "cep",
          registerName: "endereco.cep",
          cepAddressMapping: {
            street: "endereco.logradouro",
            neighborhood: "endereco.bairro",
            city: "endereco.cidade",
            state: "endereco.estado",
          },
        },
        logradouro: {
          label: "Logradouro",
          placeholder: " Rua das Flores",
          description: "Informe o logradouro",
          inputType: "text",
          registerName: "endereco.logradouro",
        },
        bairro: {
          label: "Bairro",
          placeholder: " Centro",
          description: "Informe o bairro",
          inputType: "text",
          registerName: "endereco.bairro",
        },
        estado: {
          label: "Estado",
          placeholder: " São Paulo",
          description: "Informe o estado",
          inputType: "text",
          registerName: "endereco.estado",
        },
        cidade: {
          label: "Cidade",
          placeholder: " São Paulo",
          description: "Informe a cidade",
          inputType: "text",
          registerName: "endereco.cidade",
        },
      },
    },
    configuracao: {
      label: "Configuração",
      placeholder: "",
      description: "Configure as opções do contrato",
      inputType: "object",
      fields: {
        ativo: {
          label: "Ativo",
          placeholder: " true",
          description: "Contrato ativo",
          inputType: "checkbox",
          registerName: "configuracao.ativo",
        },
        abertura_os_credenciado: {
          label: "Abertura de OS por Credenciado",
          placeholder: " true",
          description: "Permitir abertura de OS por credenciado",
          inputType: "checkbox",
          registerName: "configuracao.abertura_os_credenciado",
        },
        envio_de_emails: {
          label: "Bloquear Envio de E-mails",
          placeholder: " true",
          description: "Bloquear envio de e-mails",
          inputType: "checkbox",
          registerName: "configuracao.envio_de_emails",
        },
        veiculos_com_rfid: {
          label: "Veículos com RFID",
          placeholder: " true",
          description: "Habilitar veículos com TAG",
          inputType: "checkbox",
          registerName: "configuracao.veiculos_com_rfid",
        },
        parametrizacao_obrigatoria: {
          label: "Parametrização Obrigatória",
          placeholder: " true",
          description: "Tornar parametrização obrigatória",
          inputType: "checkbox",
          registerName: "configuracao.parametrizacao_obrigatoria",
        },
        checklist_simplificado_pecas: {
          label: "Checklist Simplificado de Peças",
          placeholder: " true",
          description: "Habilitar checklist simplificado de peças",
          inputType: "checkbox",
          registerName: "configuracao.checklist_simplificado_pecas",
        },
        aprovacao_gestor: {
          label: "Necessário Aprovação do Gestor",
          placeholder: " true",
          description: "Aprovação do gestor para conclusão de OS",
          inputType: "checkbox",
          registerName: "configuracao.aprovacao_gestor",
        },
        ajuste_gestor: {
          label: "Gestor pode solicitar ajuste",
          placeholder: " true",
          description: "Gestor pode solicitar ajuste na OS",
          inputType: "checkbox",
          registerName: "configuracao.ajuste_gestor",
        },
        restringir_veiculos: {
          label: "Restringir veículos em manutenção",
          placeholder: "true",
          description: "Restringir veículos que estão com OS em execução",
          inputType: "checkbox",
          registerName: "configuracao.restringir_veiculos",
        },
      },
    },
  };
  return ContratoConfig;
}
function GetAcessoConfig(datas: any[], chave: string[]) {
  const AcessoConfig: Record<
    keyof z.infer<typeof acessoSchema>,
    FieldConfig<typeof acessoSchema>
  > = {
    permissao: {
      label: "Permissões",
      placeholder: "Selecione as permissões",
      description: "Escolha as permissões para o acesso",
      inputType: "multiple-select",
      options: permissoesValidas.map((permissao) => ({
        label: permissao,
        value: permissao,
      })),
      registerName: "permissao",
    },
    contratoId: {
      label: "Contrato",
      placeholder: "Selecione um contrato",
      description: "Escolha o contrato relacionado",
      inputType: "combobox",
      referenceId: "contratoId",
      chave: chave[0],
      datas: datas[0],
      registerName: "contratoId",
    },
    centro_de_custoId: {
      label: "Centro de Custo",
      placeholder: "Selecione um centro de custo",
      description: "Escolha o centro de custo relacionado",
      inputType: "combobox",
      referenceId: "centro_de_custoId",
      chave: chave[1],
      datas: datas[1],
      registerName: "centro_de_custoId",
    },
  };
  return AcessoConfig;
}

type Role = { label: string; value: string };

function GetUserConfig(datas: any[], chave: string[]) {
  const setSelectedContratoId = datas[3];
  const setSelectedCentroCustoId = datas[4];

  interface FormWatcher {
    watch: (field: string) => any;
  }

  interface FormSetter extends FormWatcher {
    setValue: (field: string, value: any) => void;
  }

  interface CentroDeCusto {
    id: string;
    descricao: string;
  }

  interface Contrato {
    id: string;
    [key: string]: any;
  }

  interface Unidade {
    id: string;
    descricao: string;
  }

  const UsuarioConfig: Record<
    keyof z.infer<typeof usuarioSchema>,
    FieldConfig<typeof usuarioSchema>
  > = {
    nome: {
      label: "Nome",
      placeholder: " João Silva",
      description: "Informe o nome do usuário",
      inputType: "text",
      registerName: "nome",
      fieldClassName: "col-span-1",
    },
    matricula: {
      label: "Matrícula",
      placeholder: " 123456",
      description: "Informe a matrícula do usuário",
      inputType: "text",
      registerName: "matricula",
      fieldClassName: "col-span-1",
    },
    email: {
      label: "E-mail",
      placeholder: " <EMAIL>",
      description: "Informe o e-mail do usuário",
      inputType: "email",
      registerName: "email",
    },
    senha: {
      label: "Senha",
      placeholder: " abc123",
      description: "Informe a senha para acesso do usuário",
      inputType: "password",
      registerName: "senha",
    },
    telefone: {
      label: "Telefone Fixo",
      placeholder: " 11 1234-5678",
      description: "Informe o telefone do usuário",
      inputType: "telefone",
      registerName: "telefone",
    },
    celular: {
      label: "Celular",
      placeholder: " 11 98765-4321",
      description: "Informe o celular do usuário",
      inputType: "celular",
      registerName: "celular",
    },
    contratos: {
      label: "Contrato",
      placeholder: "ABC LTDA",
      description: "Informe o contrato de vínculo do usuário",
      inputType: "multiple-select",
      options: datas[0].map(({ nome_contrato, id }: any) => ({
        label: nome_contrato,
        value: id,
      })),
      registerName: "contratos",
      onChange: (value) => {
        if (!value || value.length === 0) return;
        const valueLength = value.length;
        setSelectedContratoId(value[valueLength - 1].value);
      },
    },
    roles: {
      label: "Funções",
      placeholder: "Selecione as roles",
      description: "Selecione as roles do usuário",
      inputType: "multiple-select",
      options: [
        { label: "Acesso geral ao sistema", value: "ADMIN" },
        { label: "Operacional", value: "OPERACIONAL" },
        { label: "Orçamentista Oficina", value: "ORCAMENTISTA_OFICINA" },
        { label: "Gestor da Frota", value: "GESTOR_FROTA" },
        { label: "Gestor Geral", value: "GESTOR_GERAL" },
        { label: "Aprovação de Os", value: "APROVADOR" },
        { label: "Abertura de Os", value: "ABERTURA_OS" },
        { label: "Credenciamento", value: "CREDENCIAMENTO" },
        { label: "Financeiro", value: "FINANCEIRO" },
      ] as Role[],
      registerName: "roles",
      onChange: (value: Role[], form: FormSetter) => {
        const roles = value.map((role: Role) => role.value);
        const isGestor =
          roles.includes("GESTOR") ||
          roles.includes("GESTOR_FROTA") ||
          roles.includes("GESTOR_GERAL");

        // Limpa os campos se não for gestor
        if (!isGestor) {
          form.setValue("centro_de_custoId", "");
          form.setValue("unidade_filha_id", "");
          form.setValue("nivel_aprovacao", undefined);
        }
      },
    },
    centro_de_custoId: {
      label: "Centro de Custo",
      placeholder: "Selecione o centro de custo",
      description: "Selecione o centro de custo do usuário",
      inputType: "combobox",
      referenceId: "centro_de_custoId",
      chave: "descricao",
      datas: datas[1] as CentroDeCusto[],
      registerName: "centro_de_custoId",
      onChange: (value: string, setValue: any) => {
        setSelectedCentroCustoId(value);
        setValue("unidade_filha_id", []);
      },
      showIf: (form: FormWatcher) => {
        const contratos = form.watch("contratos");
        return contratos?.length > 0;
      },
    },
    unidade_filha_id: {
      label: "Unidade",
      placeholder: "Selecione as unidades",
      description: "Selecione as unidades específicas",
      inputType: "multiple-select",
      options: datas[2].map((unidade: any) => ({
        label: unidade.descricao || "Sem descrição",
        value: unidade.id,
      })),
      registerName: "unidade_filha_id",
      showIf: (form: FormWatcher) => {
        const centroDeCustoId = form.watch("centro_de_custoId");

        return Boolean(centroDeCustoId);
      },
    },
    unidades_filha_ids: {
      label: "Unidades",
      placeholder: "",
      description: "",
      inputType: "hidden", // Hide this field as it's handled programmatically
      className: "hidden",
      fieldClassName: "hidden",
      hidden: true,
      registerName: "unidades_filha_ids",
    },
    nivel_aprovacao: {
      label: "Nível de Aprovação Máxima",
      placeholder: "Inserir o valor máximo de aprovação",
      description: "Insira o valor máximo de aprovação do gestor",
      inputType: "currency",
      registerName: "nivel_aprovacao",
      hidden: true,
      showIf: (form: FormWatcher) => {
        const roles = form.watch("roles") || [];
        return roles.some((role: Role) => role.value === "GESTOR_FROTA");
      },
    },
    ativo: {
      label: "Ativo",
      placeholder: " true",
      description: "Indique se o usuário está ativo",
      inputType: "checkbox",
      registerName: "ativo",
    },
  };
  return UsuarioConfig;
}
const TipoDeOsConfig: Record<
  keyof z.infer<typeof tipoDeOsSchema>,
  FieldConfig<typeof usuarioSchema>
> = {
  descricao: {
    label: "Descrição",
    placeholder: " Carro",
    description: "Escreva a descrição do tipo de ordem de serviço",
    inputType: "text",
  },
  obs: {
    label: "Observações",
    placeholder: " Sem observações",
    description: "Escreva observações sobre o tipo de OS",
    inputType: "textarea",
  },
  prazo_para_execucao: {
    label: "Prazo para Execução",
    placeholder: " 30 dias",
    description: "Escreva o prazo para execução do tipo de OS",
    inputType: "text",
  },
};

function GetCentroDeCustoConfig(datas: any[], chave: string[]) {
  const CentroDeCustoConfig: Record<
    keyof z.infer<typeof centroDeCustoSchema>,
    FieldConfig<typeof centroDeCustoSchema>
  > = {
    descricao: {
      label: "Descrição *",
      placeholder: " Descrição do centro de custo",
      description: "Descreva o centro de custo",
      inputType: "textarea",
      registerName: "descricao",
    },
    centro_de_custo_ascendenteId: {
      label: "Centro de Custo Ascendente",
      placeholder: "Selecione um centro de custo ascendente",
      description: "Escolha o centro de custo ascendente",
      inputType: "combobox",
      referenceId: "centro_de_custo_ascendenteId",
      chave: chave[0],
      datas: datas[0],
      registerName: "centro_de_custo_ascendenteId",
    },
    dotacao_orcamentista: {
      label: "Dotação Orçamentária *",
      placeholder: " Dotação orçamentária",
      description: "Informe a dotação orçamentária",
      inputType: "text",
      registerName: "dotacao_orcamentista",
    },
    valor_dotacao: {
      label: "Valor da Dotação *",
      placeholder: " R$ 1000",
      description: "Informe o valor da dotação",
      inputType: "currency",
      registerName: "valor_dotacao",
    },
    nome_responsavel: {
      label: "Nome do Responsável *",
      placeholder: " João Silva",
      description: "Informe o nome do responsável",
      inputType: "text",
      registerName: "nome_responsavel",
    },
    contato: {
      label: "Contato *",
      placeholder: "Somente números",
      description: "Informe o contato do responsável (apenas números)",
      inputType: "celular",
      registerName: "contato",
    },
    email: {
      label: "E-mail *",
      placeholder: " <EMAIL>",
      description: "Informe o e-mail do responsável",
      inputType: "email",
      registerName: "email",
    },
    cnpj: {
      label: "CNPJ *",
      placeholder: "Digite apenas números",
      description: "Informe o CNPJ do centro de custo",
      inputType: "cnpj",
      registerName: "cnpj",
    },
    razao_social: {
      label: "Razão Social *",
      placeholder: " Empresa XYZ Ltda.",
      description: "Informe a razão social",
      inputType: "text",
      registerName: "razao_social",
    },
    endereco: {
      label: "Endereço",
      placeholder: "",
      description: "Informe o endereço completo",
      inputType: "object",
      fields: {
        cep: {
          label: "CEP",
          placeholder: "Digite apenas números",
          description: "Informe o CEP",
          inputType: "cep",
          registerName: "endereco.cep",
          cepAddressMapping: {
            street: "endereco.logradouro",
            neighborhood: "endereco.bairro",
            city: "endereco.cidade",
            state: "endereco.estado",
          },
        },
        logradouro: {
          label: "Logradouro",
          placeholder: " Rua das Flores",
          description: "Informe o logradouro",
          inputType: "text",
          registerName: "endereco.logradouro",
        },
        bairro: {
          label: "Bairro",
          placeholder: " Centro",
          description: "Informe o bairro",
          inputType: "text",
          registerName: "endereco.bairro",
        },
        estado: {
          label: "Estado",
          placeholder: " São Paulo",
          description: "Informe o estado",
          inputType: "text",
          registerName: "endereco.estado",
        },
        cidade: {
          label: "Cidade",
          placeholder: " São Paulo",
          description: "Informe a cidade",
          inputType: "text",
          registerName: "endereco.cidade",
        },
      },
    },
    centro_de_custo_tomadorId: {
      label: "Centro de Custo Tomador",
      placeholder: "Selecione um centro de custo tomador",
      description: "Escolha o centro de custo tomador",
      inputType: "combobox",
      referenceId: "centro_de_custo_tomadorId",
      chave: chave[1],
      datas: datas[1],
      registerName: "centro_de_custo_tomadorId",
    },
    active: {
      label: "Ativo",
      placeholder: " true",
      description: "Indique se o centro de custo está ativo",
      inputType: "checkbox",
      registerName: "active",
    },
    veiculos_com_rfid: {
      label: "Veículos com RFID",
      placeholder: " true",
      description: "Habilitar veículos com TAG",
      inputType: "checkbox",
      registerName: "veiculos_com_rfid",
    },
    abertura_os: {
      label: "Abertura de OS",
      placeholder: " true",
      description: "Permitir abertura de OS",
      inputType: "checkbox",
      registerName: "abertura_os_",
    },
  };
  return CentroDeCustoConfig;
}

function GetEmpenhoConfig(datas: any[], chave: string[]) {
  const EmpenhoConfig: Record<
    keyof z.infer<typeof empenhoSchema>,
    FieldConfig<typeof empenhoSchema>
  > = {
    centro_de_custoId: {
      label: "Centro de Custo *",
      placeholder: "Selecione um centro de custo",
      description: "Escolha o centro de custo relacionado",
      inputType: "combobox",
      referenceId: "centro_de_custoId",
      chave: chave[0],
      datas: datas[0],
      registerName: "centro_de_custoId",
    },
    nota_empenho: {
      label: "Nota de Empenho *",
      placeholder: " 123456",
      description: "Informe a nota de empenho",
      inputType: "text",
      registerName: "nota_empenho",
    },
    ano_de_competencia: {
      label: "Ano de Competência *",
      placeholder: " 2023",
      description: "Informe o ano de competência",
      inputType: "text",
      registerName: "ano_de_competencia",
    },
    data_inicial: {
      label: "Data Inicial *",
      placeholder: " 01/01/2023",
      description: "Informe a data inicial",
      inputType: "date",
      registerName: "data_inicial",
    },
    data_final: {
      label: "Data Final *",
      placeholder: " 31/12/2023",
      description: "Informe a data final",
      inputType: "date",
      registerName: "data_final",
    },
    dotacao_orcamentada: {
      label: "Dotação Orçamentada *",
      placeholder: "",
      description: "Informe a dotação orçamentada",
      inputType: "text",
      registerName: "dotacao_orcamentada",
    },
    valor_destinado_as_pecas: {
      label: "Valor Destinado às Peças *",
      placeholder: "R$ 50000",
      description: "Informe o valor destinado às peças",
      inputType: "currency",
      registerName: "valor_destinado_as_pecas",
    },
    valor_destinado_aos_servicos: {
      label: "Valor Destinado aos Serviços *",
      placeholder: "R$ 50000",
      description: "Informe o valor destinado aos serviços",
      inputType: "currency",
      registerName: "valor_destinado_aos_servicos",
    },
    bloqueado: {
      label: "Bloqueado",
      placeholder: " false",
      description: "Indique se o empenho está bloqueado",
      inputType: "checkbox",
      registerName: "bloqueado",
    },
    ativo: {
      label: "Ativo",
      placeholder: " true",
      description: "Indique se o empenho está ativo",
      inputType: "checkbox",
      registerName: "ativo",
    },
  };
  return EmpenhoConfig;
}
const MarcaConfig: Record<
  keyof z.infer<typeof marcaSchema>,
  FieldConfig<typeof usuarioSchema>
> = {
  marca: {
    label: "Marca",
    placeholder: " Ford, Toyota, BMW",
    description: "Nome da marca do veículo",
    inputType: "text",
  },
  codigo_fipe: {
    label: "Código FIPE",
    placeholder: " 123456",
    description: "Código FIPE associado à marca",
    inputType: "text",
  },
  codigo_suiv: {
    label: "Código SUIV",
    placeholder: " ABC123",
    description: "Código SUIV da marca, se aplicável",
    inputType: "text",
  },
};
function GetModeloVeiculoConfig(datas: any[], chave: string[]) {
  const ModeloConfig: Record<
    keyof z.infer<typeof modeloSchema>,
    FieldConfig<typeof usuarioSchema>
  > = {
    marca: {
      label: "Marca",
      placeholder: " Ford",
      description: "Identificador único da marca",
      inputType: "combobox",
      referenceId: "marcaId",
      chave: chave[0],
      datas: datas[0],
      registerName: "marcaId",
    },
    descricao: {
      label: "Descrição do modelo",
      placeholder: " Ranger 3.2 4x4 Automatica",
      description: "Modelo do veículo",
      inputType: "text",
    },
    codigo_fipe: {
      label: "Código FIPE",
      placeholder: " 123456",
      description: "Código FIPE associado à marca",
      inputType: "text",
    },
    codigo_suiv: {
      label: "Código SUIV",
      placeholder: " ABC123",
      description: "Código SUIV da marca, se aplicável",
      inputType: "text",
    },
  };
  return ModeloConfig;
}
const ServicoCredenciadoConfig: Record<
  keyof z.infer<typeof servicoCredenciadoSchema>,
  FieldConfig<typeof servicoCredenciadoSchema>
> = {
  description: {
    label: "Descrição",
    placeholder: " Serviço de Manutenção",
    description: "Descrição do serviço credenciado",
    inputType: "text",
  },
};
function GetTipoDeVeiculoConfig() {
  const TipoDeVeiculoConfig: Record<
    keyof z.infer<typeof tipoDeVeiculoSchema>,
    FieldConfig<typeof tipoDeVeiculoSchema>
  > = {
    descricao: {
      label: "Descrição",
      placeholder: " Caminhão, Carro, Moto",
      description: "Descreva o tipo de veículo",
      inputType: "text",
      registerName: "descricao",
    },
  };
  return TipoDeVeiculoConfig;
}
const TipoDeFrotaConfig: Record<
  keyof z.infer<typeof tipoDeFrotaSchema>,
  FieldConfig<typeof tipoDeFrotaSchema>
> = {
  descricao: {
    label: "Descrição",
    placeholder: " Frota Leve, Frota Pesada",
    description: "Descreva o tipo de frota",
    inputType: "text",
    registerName: "descricao",
  },
};
export {
  CondutorConfig,
  TipoDeOsConfig,
  TiposDeDeDespesaConfig,
  VersaoConfig,
  MarcaConfig,
  PoloRegionalConfig,
  CidadeConfig,
  ServicoCredenciadoConfig,
  PrazoDePagamentoConfig,
  TipoDeFrotaConfig,
  GetVeiculoConfig,
  GetCentroDeCustoConfig,
  GetLembreteConfig,
  GetPercursoConfig,
  GetCredenciadoConfig,
  GetItensDaVistoria,
  GetChecklistConfig,
  GetSimplifiedChecklistConfig,
  GetOsConfig,
  GetVistoriaConfig,
  GetDespesaConfig,
  GetEmpenhoConfig,
  GetTipoDeVeiculoConfig,
  GetContratoConfig,
  GetAcessoConfig,
  GetCidadeConfig,
  GetAbastecimentoConfig,
  GetUserConfig,
  GetModeloVeiculoConfig,
};
