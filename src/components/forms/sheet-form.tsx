import { ReactNode } from "react";
import { useForm, DefaultValues } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
  SheetDescription,
  SheetTitle,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { ZodSchema, z } from "zod";

type SheetFormProps<T extends ZodSchema<any>> = {
  title: string;
  schema: T;
  children: ReactNode;
  onSubmit: (values: z.infer<T>) => void;
  triggerLabel: ReactNode | string;
  defaultValues?: DefaultValues<z.infer<T>>;
  onClick?: () => void;
};

export function SheetForm<T extends ZodSchema<any>>({
  title,
  schema,
  children,
  onSubmit,
  triggerLabel,
  defaultValues,
  onClick,
}: SheetFormProps<T>) {
  const form = useForm<z.infer<T>>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(schema),
    defaultValues,
  });

  return (
    <Sheet>
      {onClick ? (
        <Button onClick={onClick}>{triggerLabel}</Button>
      ) : (
        <SheetTrigger asChild>
          <Button>{triggerLabel}</Button>
        </SheetTrigger>
      )}
      <SheetContent className="overflow-y-auto">
        <SheetTitle>{title}</SheetTitle>
        <SheetDescription>Preêncha o formulário abaixo</SheetDescription>

        <Form {...form}>
          <form className="space-y-4 py-4">
            {children}
            <Button
              className="w-full"
              type="button"
              onClick={async () => {
                const values = form.getValues();
                await onSubmit(values);
              }}>
              Criar
            </Button>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
}
