"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { GripVertical, ArrowUp, ArrowDown, Settings, Eye, EyeOff } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

export interface OSFormField {
  id: string;
  label: string;
  section: 'basic' | 'service' | 'location' | 'details' | 'files';
  required?: boolean;
  description?: string;
}

interface OSFieldCustomizerProps {
  availableFields: OSFormField[];
  selectedFields: string[];
  fieldOrder: string[];
  onFieldToggle: (fieldId: string) => void;
  onFieldReorder: (newOrder: string[]) => void;
  onSelectAll: () => void;
  onDeselectAll: () => void;
}

const sectionLabels = {
  basic: 'Informações Básicas',
  service: 'Tipo de Serviço',
  location: 'Localização',
  details: 'Detalhes e Configurações',
  files: 'Arquivos'
};

const sectionColors = {
  basic: 'bg-blue-100 text-blue-800',
  service: 'bg-green-100 text-green-800',
  location: 'bg-purple-100 text-purple-800',
  details: 'bg-orange-100 text-orange-800',
  files: 'bg-gray-100 text-gray-800'
};

export function OSFieldCustomizer({
  availableFields,
  selectedFields,
  fieldOrder,
  onFieldToggle,
  onFieldReorder,
  onSelectAll,
  onDeselectAll
}: OSFieldCustomizerProps) {
  const [open, setOpen] = useState(false);
  const [expandedSections, setExpandedSections] = useState<string[]>(['basic']);

  const moveField = (fieldId: string, direction: 'up' | 'down') => {
    const currentIndex = fieldOrder.indexOf(fieldId);
    if (currentIndex === -1) return;

    const newOrder = [...fieldOrder];
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;

    if (targetIndex < 0 || targetIndex >= newOrder.length) return;

    [newOrder[currentIndex], newOrder[targetIndex]] = [newOrder[targetIndex], newOrder[currentIndex]];
    onFieldReorder(newOrder);
  };

  const toggleSection = (section: string) => {
    setExpandedSections(prev => 
      prev.includes(section) 
        ? prev.filter(s => s !== section)
        : [...prev, section]
    );
  };

  const selectedFieldsInOrder = fieldOrder.filter(fieldId => selectedFields.includes(fieldId))
    .map(fieldId => availableFields.find(f => f.id === fieldId))
    .filter(Boolean) as OSFormField[];

  // Group fields by section
  const fieldsBySection = availableFields.reduce((acc, field) => {
    if (!acc[field.section]) {
      acc[field.section] = [];
    }
    acc[field.section].push(field);
    return acc;
  }, {} as Record<string, OSFormField[]>);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="flex items-center gap-2">
          <Settings className="h-4 w-4" />
          Personalizar Campos
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Personalizar Ordem dos Campos do Formulário</DialogTitle>
        </DialogHeader>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Campos Disponíveis */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Campos Disponíveis</span>
                <div className="flex gap-2">
                  <Button size="sm" variant="outline" onClick={onSelectAll}>
                    Selecionar Todos
                  </Button>
                  <Button size="sm" variant="outline" onClick={onDeselectAll}>
                    Desmarcar Todos
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {Object.entries(fieldsBySection).map(([section, fields]) => (
                <div key={section} className="border rounded-lg">
                  <button
                    onClick={() => toggleSection(section)}
                    className="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-t-lg"
                  >
                    <div className="flex items-center gap-2">
                      <Badge className={sectionColors[section as keyof typeof sectionColors]}>
                        {sectionLabels[section as keyof typeof sectionLabels]}
                      </Badge>
                      <span className="text-sm text-gray-600">
                        {fields.filter(f => selectedFields.includes(f.id)).length}/{fields.length}
                      </span>
                    </div>
                    <span className="text-sm">
                      {expandedSections.includes(section) ? '−' : '+'}
                    </span>
                  </button>
                  
                  {expandedSections.includes(section) && (
                    <div className="p-3 space-y-2 border-t">
                      {fields.map(field => (
                        <div key={field.id} className="flex items-start gap-3 p-2 hover:bg-gray-50 rounded">
                          <Checkbox
                            checked={selectedFields.includes(field.id)}
                            onCheckedChange={() => onFieldToggle(field.id)}
                            disabled={field.required}
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{field.label}</span>
                              {field.required && (
                                <Badge variant="secondary" className="text-xs">Obrigatório</Badge>
                              )}
                            </div>
                            {field.description && (
                              <p className="text-sm text-gray-500 mt-1">{field.description}</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Campos Selecionados e Ordem */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Ordem dos Campos
                <Badge variant="secondary">{selectedFieldsInOrder.length} campos</Badge>
              </CardTitle>
              <p className="text-sm text-gray-600">
                Use os botões para reorganizar a ordem dos campos no formulário
              </p>
            </CardHeader>
            <CardContent>
              {selectedFieldsInOrder.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <EyeOff className="mx-auto h-12 w-12 mb-2" />
                  <p>Nenhum campo selecionado</p>
                  <p className="text-sm">Selecione campos na lista ao lado</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {selectedFieldsInOrder.map((field, index) => (
                    <div
                      key={field.id}
                      className="flex items-center gap-3 p-3 border rounded-lg bg-white hover:shadow-sm"
                    >
                      <GripVertical className="h-4 w-4 text-gray-400" />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{field.label}</span>
                          <Badge className={sectionColors[field.section]}>
                            {sectionLabels[field.section]}
                          </Badge>
                          {field.required && (
                            <Badge variant="secondary" className="text-xs">Obrigatório</Badge>
                          )}
                        </div>
                      </div>
                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => moveField(field.id, 'up')}
                          disabled={index === 0}
                        >
                          <ArrowUp className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => moveField(field.id, 'down')}
                          disabled={index === selectedFieldsInOrder.length - 1}
                        >
                          <ArrowDown className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancelar
          </Button>
          <Button onClick={() => setOpen(false)}>
            Aplicar Configuração
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
