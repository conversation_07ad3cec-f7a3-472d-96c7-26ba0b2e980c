"use client";
import { use<PERSON><PERSON>, FormProvider } from "react-hook-form";
import { useState, useCallback, useEffect } from "react";
import { toast } from "sonner";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "../ui/card";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import { Checkbox } from "../ui/checkbox";
import { Label } from "../ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import {
  CriarCredenciadoData,
  atualizarCredenciado,
} from "@/serverActions/credenciadoAction";
import { useCredenciado } from "@/context/credenciado-context";
import { usePoloRegional } from "@/context/polo-regional-context";
import { useTipoDeVeiculo } from "@/context/tipo-de-veiculo-context";
import { usePrazoDePagamento } from "@/context/prazo-de-pagamento-context";
import { useServicoCredenciado } from "@/context/servico-credenciado-context";
import {
  CNPJInput,
  TelefoneInput,
  CelularInput,
} from "../inputs/numeric-input";
import { consultCnpj } from "@/serverActions/cnpjAction";
import { CnpjData } from "@/interfaces/cnpj.interface";
import { DateInput } from "../inputs/date-input";
import { CnpjDetailsModal } from "../modal/cnpj-details-modal";
import { useContrato } from "@/context/contrato-context";
import { CepInput } from "../inputs/cep-input";

// Definindo os passos de navegação do formulário
const formSteps = [
  "Informações",
  "Contatos",
  "Endereço",
  "Estrutura",
  "Serviços",
  "Configurações",
];

interface EditCredenciadoProps {
  credenciadoData: any; // Receive existing data from props
}

export function EditCredenciadoForm({ credenciadoData }: EditCredenciadoProps) {
  if (!credenciadoData) {
    return (
      <div className="p-6 bg-yellow-50 border border-yellow-200 rounded-md">
        <p className="text-yellow-700">Dados do credenciado não disponíveis.</p>
      </div>
    );
  }

  // Estados
  const [currentStep, setCurrentStep] = useState(0);
  const [isLoadingCNPJ, setIsLoadingCNPJ] = useState(false);
  const [selectedServicos, setSelectedServicos] = useState<string[]>([]);
  const [selectedTiposVeiculo, setSelectedTiposVeiculo] = useState<string[]>(
    []
  );
  const [selectedContratos, setSelectedContratos] = useState<string[]>([]);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [estruturaFiles, setEstruturaFiles] = useState<File[]>([]);
  const [documentacaoFiles, setDocumentacaoFiles] = useState<File[]>([]);
  const [cnpjModalData, setCnpjModalData] = useState<CnpjData | null>(null);
  const [isCnpjModalOpen, setIsCnpjModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [existingLogoUrl, setExistingLogoUrl] = useState<string | null>(null);
  const [existingEstruturaUrls, setExistingEstruturaUrls] = useState<string[]>(
    []
  );

  const { polosRegionais } = usePoloRegional();
  const { tiposDeVeiculo } = useTipoDeVeiculo();
  const { prazosDePagamento } = usePrazoDePagamento();
  const { servicosCredenciados } = useServicoCredenciado();
  const { setCredenciados } = useCredenciado();
  const { contratos } = useContrato();

  // Initialize form with existing data
  const formMethods = useForm({
    defaultValues: {
      informacoes: {
        cnpj: credenciadoData.informacoes?.[0]?.cnpj || "",
        razao_social: credenciadoData.informacoes?.[0]?.razao_social || "",
        inscri_estadual:
          credenciadoData.informacoes?.[0]?.inscri_estadual || "",
        inscri_municipal:
          credenciadoData.informacoes?.[0]?.inscri_municipal || "",
        nome_fantasia: credenciadoData.informacoes?.[0]?.nome_fantasia || "",
        atividade_principal:
          credenciadoData.informacoes?.[0]?.atividade_principal || "",
        horario_funcionamento:
          credenciadoData.informacoes?.[0]?.horario_funcionamento || "",
        data_abertura: credenciadoData.informacoes?.[0]?.data_abertura
          ? new Date(credenciadoData.informacoes[0].data_abertura)
              .toISOString()
              .split("T")[0]
          : "",
        porte_empresarial:
          typeof credenciadoData.informacoes?.[0]?.porte_empresarial ===
          "string"
            ? JSON.parse(credenciadoData.informacoes[0].porte_empresarial)
            : credenciadoData.informacoes?.[0]?.porte_empresarial || "",
        capital_social: credenciadoData.informacoes?.[0]?.capital_social || "",
        patrimonio_liquido:
          credenciadoData.informacoes?.[0]?.patrimonio_liquido || "",
        observacoes_gerais:
          credenciadoData.informacoes?.[0]?.observacoes_gerais || "",
      },
      estrutura: {
        capacidade_atendimento:
          credenciadoData.estrutura_credenciado?.capacidade_atendimento || 0,
        box_veiculos_leves:
          credenciadoData.estrutura_credenciado?.box_veiculos_leves || 0,
        box_veiculos_pesados:
          credenciadoData.estrutura_credenciado?.box_veiculos_pesados || 0,
        elevadores_veiculos:
          credenciadoData.estrutura_credenciado?.elevadores_veiculos || 0,
        estufas_pintura:
          credenciadoData.estrutura_credenciado?.estufas_pintura || 0,
        orcamentista: credenciadoData.estrutura_credenciado?.orcamentista || "",
        atende_placa_verde:
          credenciadoData.estrutura_credenciado?.atende_placa_verde || false,
        concessionaria:
          credenciadoData.estrutura_credenciado?.concessionaria || false,
        orcamentacao:
          credenciadoData.estrutura_credenciado?.orcamentacao || false,
      },
      endereco: {
        cep: credenciadoData.endereco?.cep || "",
        logradouro: credenciadoData.endereco?.logradouro || "",
        bairro: credenciadoData.endereco?.bairro || "",
        estado: credenciadoData.endereco?.estado || "",
        cidade: credenciadoData.endereco?.cidade || "",
        latitude: credenciadoData.endereco?.latitude || "",
        longitude: credenciadoData.endereco?.longitude || "",
      },
      contatos: {
        telefone: credenciadoData.contato?.telefone || "",
        celular: credenciadoData.contato?.celular || "",
        email: credenciadoData.contato?.email || "",
        nome_gerente: credenciadoData.contato?.nome_gerente || "",
        telefone_gerente: credenciadoData.contato?.telefone_gerente || "",
        nome_proprietario: credenciadoData.contato?.nome_proprietario || "",
        telefone_proprietario:
          credenciadoData.contato?.telefone_proprietario || "",
      },
      configuracoes: {
        polo_regionalId: credenciadoData.polo_regionalId || "",
        prazoId: credenciadoData.prazoId || "",
        optante: credenciadoData.optante || false,
        responsavelPrazo: credenciadoData.responsavelPrazo || "",
      },
    },
  });

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = formMethods;

  // Initialize existing data
  useEffect(() => {
    // Set existing logo and structure images
    if (credenciadoData.informacoes?.[0]?.logotipo_empresa) {
      setExistingLogoUrl(credenciadoData.informacoes[0].logotipo_empresa);
    }

    // Parse existing structure images
    if (credenciadoData.estrutura_credenciado?.images) {
      try {
        const parsedImages =
          typeof credenciadoData.estrutura_credenciado.images === "string"
            ? JSON.parse(credenciadoData.estrutura_credenciado.images)
            : credenciadoData.estrutura_credenciado.images;

        setExistingEstruturaUrls(
          Array.isArray(parsedImages) ? parsedImages : []
        );
      } catch (e) {
        console.error("Error parsing structure images:", e);
      }
    }

    // Set selected services
    if (credenciadoData.servicos && Array.isArray(credenciadoData.servicos)) {
      setSelectedServicos(
        credenciadoData.servicos.map((servico: any) => servico.id)
      );
    }

    // Set selected vehicle types
    if (
      credenciadoData.informacoes?.[0]?.tipos_veiculo &&
      Array.isArray(credenciadoData.informacoes[0].tipos_veiculo)
    ) {
      setSelectedTiposVeiculo(
        credenciadoData.informacoes[0].tipos_veiculo.map((tipo: any) => tipo.id)
      );
    }

    // Set selected contracts
    if (credenciadoData.contratos && Array.isArray(credenciadoData.contratos)) {
      setSelectedContratos(
        credenciadoData.contratos.map((contrato: any) => contrato.contratoId)
      );
    }
  }, [credenciadoData]);

  // Valor atual do CNPJ para consulta
  const cnpjValue = watch("informacoes.cnpj");

  console.log("Credenciado Data", credenciadoData);

  const handleCnpjLookup = async () => {
    if (!cnpjValue) {
      toast.error("Digite um CNPJ para consultar");
      return;
    }

    try {
      setIsLoadingCNPJ(true);
      const cleanCnpj = cnpjValue.replace(/\D/g, "");
      const response = await consultCnpj(cleanCnpj);

      if (!response) {
        throw new Error("CNPJ não encontrado");
      }

      setCnpjModalData(response);
      setIsCnpjModalOpen(true);
    } catch (error) {
      toast.error("Erro ao consultar CNPJ");
      console.error(error);
    } finally {
      setIsLoadingCNPJ(false);
    }
  };

  const handleCnpjConfirm = () => {
    if (!cnpjModalData) return;

    setValue("informacoes.razao_social", cnpjModalData.company.name);
    setValue("informacoes.nome_fantasia", cnpjModalData.alias || "");

    if (cnpjModalData.founded) {
      const dataFormatada = cnpjModalData.founded.split("T")[0];
      setValue("informacoes.data_abertura", dataFormatada);
    }

    setValue(
      "informacoes.capital_social",
      cnpjModalData.company.equity?.toString() || ""
    );

    const porteMap: Record<string, string> = {
      ME: "Microempresa",
      EPP: "Empresa de pequeno porte",
      DEMAIS: "Empresa Limitada",
      Demais: "Empresa Limitada",
      "Micro Empresa": "Microempresa",
      Microempresa: "Microempresa",
      MEI: "Microempreendedor Individual",
      EI: "Empresário Individual",
    };

    const porteOriginal = cnpjModalData.company.size.text;
    const porteMapeado = porteMap[porteOriginal] || "Empresa Limitada";

    setValue("informacoes.porte_empresarial", porteMapeado);
    setValue(
      "informacoes.atividade_principal",
      `${cnpjModalData.mainActivity.id} - ${cnpjModalData.mainActivity.text}`
    );

    setValue("endereco.cep", cnpjModalData.address.zip);
    setValue("endereco.logradouro", cnpjModalData.address.street);
    setValue("endereco.bairro", cnpjModalData.address.district);
    setValue("endereco.cidade", cnpjModalData.address.city);
    setValue("endereco.estado", cnpjModalData.address.state);

    if (cnpjModalData.phones && cnpjModalData.phones.length > 0) {
      setValue(
        "contatos.telefone",
        `(${cnpjModalData.phones[0].area}) ${cnpjModalData.phones[0].number}`
      );

      if (cnpjModalData.phones.length > 1) {
        setValue(
          "contatos.celular",
          `(${cnpjModalData.phones[1].area}) ${cnpjModalData.phones[1].number}`
        );
      }
    }

    if (cnpjModalData.emails && cnpjModalData.emails.length > 0) {
      setValue("contatos.email", cnpjModalData.emails[0].address);
    }

    setIsCnpjModalOpen(false);
    toast.success("Dados do CNPJ preenchidos com sucesso!");
  };

  // Funções de navegação
  const handleNext = () => {
    if (currentStep < formSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Função para tratar serviços selecionados
  const handleServicoToggle = (servicoId: string) => {
    setSelectedServicos((prevServicos) => {
      if (prevServicos.includes(servicoId)) {
        return prevServicos.filter((id) => id !== servicoId);
      } else {
        return [...prevServicos, servicoId];
      }
    });
  };

  // Função para tratar tipos de veículo selecionados
  const handleTipoVeiculoToggle = (tipoId: string) => {
    setSelectedTiposVeiculo((prevTipos) => {
      if (prevTipos.includes(tipoId)) {
        return prevTipos.filter((id) => id !== tipoId);
      } else {
        return [...prevTipos, tipoId];
      }
    });
  };

  // Função para tratar contratos selecionados
  const handleContratoToggle = (contratoId: string) => {
    setSelectedContratos((prevContratos) => {
      if (prevContratos.includes(contratoId)) {
        return prevContratos.filter((id) => id !== contratoId);
      } else {
        return [...prevContratos, contratoId];
      }
    });
  };

  // Upload de arquivos
  const uploadFile = async (file: File): Promise<string | null> => {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Erro ao fazer upload do arquivo");
      }

      const data = await response.json();
      return data.file.path;
    } catch (error) {
      console.error("Erro no upload:", error);
      toast.error("Falha ao enviar arquivo");
      return null;
    }
  };

  const uploadMultipleFiles = async (files: File[]): Promise<string[]> => {
    try {
      const formData = new FormData();
      files.forEach((file) => {
        formData.append("files", file);
      });

      const response = await fetch("/api/uploads", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Erro ao fazer upload dos arquivos");
      }

      const data = await response.json();
      return data.files.map((file: { path: string }) => file.path);
    } catch (error) {
      console.error("Erro no upload múltiplo:", error);
      toast.error("Falha ao enviar arquivos");
      return [];
    }
  };

  // Enviar formulário
  const onSubmit = async (formData: any) => {
    try {
      setIsSubmitting(true);

      // Upload do logotipo
      let logoUrl = existingLogoUrl;
      if (logoFile) {
        const uploadedLogoUrl = await uploadFile(logoFile);
        if (uploadedLogoUrl) {
          logoUrl = uploadedLogoUrl;
        }
      }

      // Upload das imagens da estrutura
      let estruturaUrls: string[] = existingEstruturaUrls || [];
      if (estruturaFiles.length > 0) {
        const uploadedEstruturaUrls = await uploadMultipleFiles(estruturaFiles);
        if (uploadedEstruturaUrls.length > 0) {
          estruturaUrls = uploadedEstruturaUrls;
        }
      }

      // Upload da documentação
      let documentacaoUrls: string[] = [];
      if (documentacaoFiles.length > 0) {
        documentacaoUrls = await uploadMultipleFiles(documentacaoFiles);
      }

      // Preparar dados para submissão
      const submissionData: CriarCredenciadoData = {
        informacoes: {
          cnpj: formData.informacoes.cnpj,
          razao_social: formData.informacoes.razao_social,
          inscri_estadual: formData.informacoes.inscri_estadual || undefined,
          inscri_municipal: formData.informacoes.inscri_municipal || undefined,
          nome_fantasia: formData.informacoes.nome_fantasia || undefined,
          atividade_principal:
            formData.informacoes.atividade_principal || undefined,
          horario_funcionamento:
            formData.informacoes.horario_funcionamento || undefined,
          data_abertura: formData.informacoes.data_abertura || undefined,
          porte_empresarial:
            formData.informacoes.porte_empresarial || undefined,
          capital_social: formData.informacoes.capital_social || null,
          patrimonio_liquido: formData.informacoes.patrimonio_liquido || null,
          observacoes_gerais: formData.informacoes.observacoes_gerais || null,
          logotipo_empresa: logoUrl || undefined,
        },
        estrutura: {
          capacidade_atendimento:
            Number(formData.estrutura.capacidade_atendimento) || undefined,
          box_veiculos_leves:
            Number(formData.estrutura.box_veiculos_leves) || undefined,
          box_veiculos_pesados:
            Number(formData.estrutura.box_veiculos_pesados) || undefined,
          elevadores_veiculos:
            Number(formData.estrutura.elevadores_veiculos) || undefined,
          estufas_pintura:
            Number(formData.estrutura.estufas_pintura) || undefined,
          images: estruturaUrls.length > 0 ? estruturaUrls : undefined,
          tipos_veiculo:
            selectedTiposVeiculo.length > 0 ? selectedTiposVeiculo : undefined,
          orcamentista: formData.estrutura.orcamentista || undefined,
          atende_placa_verde:
            formData.estrutura.atende_placa_verde || undefined,
          concessionaria: formData.estrutura.concessionaria || undefined,
          orcamentacao: formData.estrutura.orcamentacao || undefined,
        },
        endereco: {
          cep: formData.endereco.cep || undefined,
          logradouro: formData.endereco.logradouro,
          bairro: formData.endereco.bairro,
          estado: formData.endereco.estado,
          cidade: formData.endereco.cidade,
          latitude: formData.endereco.latitude || undefined,
          longitude: formData.endereco.longitude || undefined,
        },
        contatos: {
          telefone: formData.contatos.telefone,
          celular: formData.contatos.celular || undefined,
          email: formData.contatos.email,
          nome_gerente: formData.contatos.nome_gerente || undefined,
          telefone_gerente: formData.contatos.telefone_gerente || undefined,
          nome_proprietario: formData.contatos.nome_proprietario || undefined,
          telefone_proprietario:
            formData.contatos.telefone_proprietario || undefined,
        },
        documentacao:
          documentacaoUrls.length > 0 ? documentacaoUrls : undefined,
        servicos: selectedServicos.length > 0 ? selectedServicos : undefined,
        polo_regionalId: formData.configuracoes.polo_regionalId,
        prazoId: formData.configuracoes.prazoId || undefined,
        url_logo: logoUrl || undefined,
        url_estrutura: estruturaUrls.length > 0 ? estruturaUrls[0] : undefined,
        optante: formData.configuracoes.optante || false,
        responsavelPrazo: formData.configuracoes.responsavelPrazo || undefined,
        contratosIds:
          selectedContratos.length > 0 ? selectedContratos : undefined,
      };

      // Enviar para API
      const response = await atualizarCredenciado(
        credenciadoData.id,
        submissionData
      );

      if (!response.success) {
        throw new Error(response.error || "Erro ao atualizar o credenciado");
      }

      toast.success("Credenciado atualizado com sucesso!");

      // Update the list of credenciados in context
      setCredenciados((prev: any[]) => {
        const index = prev.findIndex((c) => c.id === credenciadoData.id);
        if (index >= 0) {
          const updated = [...prev];
          updated[index] = response.data;
          return updated;
        }
        return prev;
      });

      // Redirect back to the credenciados list
      window.location.href =
        "/dashboard/rede-credenciada/consultar-credenciados";
    } catch (error) {
      console.error("Erro ao atualizar o credenciado:", error);
      toast.error(
        `Erro ao atualizar o credenciado: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">Editar Credenciado</CardTitle>
          <CardDescription>Atualize os dados do credenciado.</CardDescription>
        </CardHeader>
        <CardContent>
          <FormProvider {...formMethods}>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <Tabs
                defaultValue={formSteps[0]}
                value={formSteps[currentStep]}
                onValueChange={(value) => {
                  const index = formSteps.indexOf(value);
                  if (index >= 0) setCurrentStep(index);
                }}
                className="w-full"
              >
                <TabsList className="flex flex-wrap gap-2 p-2 h-34 items-center justify-start">
                  {formSteps.map((step) => (
                    <TabsTrigger
                      key={step}
                      className="flex-1 sm:flex-none px-4 py-2 text-sm"
                      value={step}
                    >
                      {step}
                    </TabsTrigger>
                  ))}
                </TabsList>

                {/* Tab 1: Informações */}
                <TabsContent value={formSteps[0]} className="space-y-4 pt-4">
                  <div className="flex gap-2 items-end mb-4">
                    <div className="flex-1">
                      <Label htmlFor="cnpj">CNPJ</Label>
                      <CNPJInput
                        value={cnpjValue}
                        onChange={(value) =>
                          setValue("informacoes.cnpj", value)
                        }
                        placeholder="12.345.678/0001-99"
                        className="w-full"
                      />
                    </div>
                    <Button
                      type="button"
                      onClick={handleCnpjLookup}
                      disabled={isLoadingCNPJ}
                      variant="outline"
                    >
                      {isLoadingCNPJ ? "Consultando..." : "Consultar"}
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="razao_social">Razão Social</Label>
                      <Input
                        {...register("informacoes.razao_social")}
                        placeholder="Nome Empresarial"
                      />
                    </div>

                    <div>
                      <Label htmlFor="nome_fantasia">Nome Fantasia</Label>
                      <Input
                        {...register("informacoes.nome_fantasia")}
                        placeholder="Nome de Mercado"
                      />
                    </div>

                    <div>
                      <Label htmlFor="inscri_estadual">
                        Inscrição Estadual
                      </Label>
                      <Input
                        {...register("informacoes.inscri_estadual")}
                        placeholder="Inscrição Estadual"
                      />
                    </div>

                    <div>
                      <Label htmlFor="inscri_municipal">
                        Inscrição Municipal
                      </Label>
                      <Input
                        {...register("informacoes.inscri_municipal")}
                        placeholder="Inscrição Municipal"
                      />
                    </div>

                    <div>
                      <Label htmlFor="data_abertura">Data de Abertura</Label>
                      <DateInput
                        value={watch("informacoes.data_abertura")}
                        onChange={(value) =>
                          setValue("informacoes.data_abertura", value)
                        }
                      />
                    </div>

                    <div>
                      <Label htmlFor="porte_empresarial">
                        Porte Empresarial
                      </Label>
                      <Select
                        onValueChange={(value) =>
                          setValue("informacoes.porte_empresarial", value)
                        }
                        value={watch("informacoes.porte_empresarial")}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o porte" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Microempreendedor Individual">
                            Microempreendedor Individual
                          </SelectItem>
                          <SelectItem value="Microempresa">
                            Microempresa
                          </SelectItem>
                          <SelectItem value="Empresário Individual">
                            Empresário Individual
                          </SelectItem>
                          <SelectItem value="Empresa de pequeno porte">
                            Empresa de pequeno porte
                          </SelectItem>
                          <SelectItem value="Empresa Individual de Responsabilidade Limitada">
                            Empresa Individual de Responsabilidade Limitada
                          </SelectItem>
                          <SelectItem value="Sociedade Anônima">
                            Sociedade Anônima
                          </SelectItem>
                          <SelectItem value="Empresa Limitada">
                            Empresa Limitada
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="atividade_principal">
                        Atividade Principal
                      </Label>
                      <Input
                        {...register("informacoes.atividade_principal")}
                        placeholder="CNAE - Atividade"
                      />
                    </div>

                    <div>
                      <Label htmlFor="horario_funcionamento">
                        Horário de Funcionamento
                      </Label>
                      <Input
                        {...register("informacoes.horario_funcionamento")}
                        placeholder="Ex: 08:00 às 18:00"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <Label htmlFor="observacoes_gerais">
                        Observações Gerais
                      </Label>
                      <Textarea
                        {...register("informacoes.observacoes_gerais")}
                        placeholder="Observações sobre o credenciado..."
                        rows={4}
                      />
                    </div>

                    <div className="md:col-span-2">
                      {existingLogoUrl && (
                        <div className="mb-2">
                          <p className="text-sm mb-1">Logotipo atual:</p>
                          <img
                            src={existingLogoUrl}
                            alt="Logotipo atual"
                            className="h-20 object-contain mb-2"
                          />
                        </div>
                      )}
                      <Label htmlFor="logotipo">
                        Atualizar Logotipo da Empresa
                      </Label>
                      <Input
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          if (e.target.files && e.target.files[0]) {
                            setLogoFile(e.target.files[0]);
                          }
                        }}
                        className="mt-1"
                      />
                    </div>
                  </div>
                </TabsContent>

                {/* Tab 2: Contatos */}
                <TabsContent value={formSteps[1]} className="space-y-4 pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="telefone">Telefone</Label>
                      <TelefoneInput
                        value={watch("contatos.telefone")}
                        onChange={(value) =>
                          setValue("contatos.telefone", value)
                        }
                        placeholder="(00) 0000-0000"
                      />
                    </div>

                    <div>
                      <Label htmlFor="celular">Celular</Label>
                      <CelularInput
                        value={watch("contatos.celular")}
                        onChange={(value) =>
                          setValue("contatos.celular", value)
                        }
                        placeholder="(00) 00000-0000"
                      />
                    </div>

                    <div>
                      <Label htmlFor="email">Email</Label>
                      <Input
                        {...register("contatos.email")}
                        placeholder="<EMAIL>"
                        type="email"
                      />
                    </div>

                    <div>
                      <Label htmlFor="nome_gerente">Nome do Gerente</Label>
                      <Input
                        {...register("contatos.nome_gerente")}
                        placeholder="Nome completo do gerente"
                      />
                    </div>

                    <div>
                      <Label htmlFor="telefone_gerente">
                        Telefone do Gerente
                      </Label>
                      <TelefoneInput
                        value={watch("contatos.telefone_gerente")}
                        onChange={(value) =>
                          setValue("contatos.telefone_gerente", value)
                        }
                        placeholder="(00) 0000-0000"
                      />
                    </div>

                    <div>
                      <Label htmlFor="nome_proprietario">
                        Nome do Proprietário
                      </Label>
                      <Input
                        {...register("contatos.nome_proprietario")}
                        placeholder="Nome completo do proprietário"
                      />
                    </div>

                    <div>
                      <Label htmlFor="telefone_proprietario">
                        Telefone do Proprietário
                      </Label>
                      <TelefoneInput
                        value={watch("contatos.telefone_proprietario")}
                        onChange={(value) =>
                          setValue("contatos.telefone_proprietario", value)
                        }
                        placeholder="(00) 0000-0000"
                      />
                    </div>
                  </div>
                </TabsContent>

                {/* Tab 3: Endereço */}
                <TabsContent value={formSteps[2]} className="space-y-4 pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex gap-2">
                      <div className="flex-1">
                        <Label htmlFor="cep">CEP</Label>
                        <CepInput
                          value={watch("endereco.cep")}
                          onChange={(value) => {
                            if (typeof value === "string") {
                              setValue("endereco.cep", value);
                            } else {
                              setValue("endereco.cep", value.target.value);
                            }
                          }}
                          placeholder="00000-000"
                          addressMapping={{
                            street: "endereco.logradouro",
                            neighborhood: "endereco.bairro",
                            city: "endereco.cidade",
                            state: "endereco.estado",
                          }}
                          onCepFound={(data) => {
                            toast.success("CEP encontrado", {
                              description:
                                "Endereço preenchido automaticamente",
                            });
                          }}
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="logradouro">Logradouro</Label>
                      <Input
                        {...register("endereco.logradouro")}
                        placeholder="Rua, Avenida, etc."
                      />
                    </div>

                    <div>
                      <Label htmlFor="bairro">Bairro</Label>
                      <Input
                        {...register("endereco.bairro")}
                        placeholder="Bairro"
                      />
                    </div>

                    <div>
                      <Label htmlFor="estado">Estado</Label>
                      <Input
                        {...register("endereco.estado")}
                        placeholder="UF"
                      />
                    </div>

                    <div>
                      <Label htmlFor="cidade">Cidade</Label>
                      <Input
                        {...register("endereco.cidade")}
                        placeholder="Cidade"
                      />
                    </div>

                    <div>
                      <Label htmlFor="latitude">Latitude</Label>
                      <Input
                        {...register("endereco.latitude")}
                        placeholder="Ex: -23.5505"
                      />
                    </div>

                    <div>
                      <Label htmlFor="longitude">Longitude</Label>
                      <Input
                        {...register("endereco.longitude")}
                        placeholder="Ex: -46.6333"
                      />
                    </div>
                  </div>
                </TabsContent>

                {/* Tab 4: Estrutura */}
                <TabsContent value={formSteps[3]} className="space-y-4 pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="capacidade_atendimento">
                        Capacidade de Atendimento
                      </Label>
                      <Input
                        type="number"
                        {...register("estrutura.capacidade_atendimento")}
                        placeholder="0"
                      />
                    </div>

                    <div>
                      <Label htmlFor="box_veiculos_leves">
                        Box para Veículos Leves
                      </Label>
                      <Input
                        type="number"
                        {...register("estrutura.box_veiculos_leves")}
                        placeholder="0"
                      />
                    </div>

                    <div>
                      <Label htmlFor="box_veiculos_pesados">
                        Box para Veículos Pesados
                      </Label>
                      <Input
                        type="number"
                        {...register("estrutura.box_veiculos_pesados")}
                        placeholder="0"
                      />
                    </div>

                    <div>
                      <Label htmlFor="elevadores_veiculos">
                        Elevadores para Veículos
                      </Label>
                      <Input
                        type="number"
                        {...register("estrutura.elevadores_veiculos")}
                        placeholder="0"
                      />
                    </div>

                    <div>
                      <Label htmlFor="estufas_pintura">
                        Estufas de Pintura
                      </Label>
                      <Input
                        type="number"
                        {...register("estrutura.estufas_pintura")}
                        placeholder="0"
                      />
                    </div>

                    <div>
                      <Label htmlFor="orcamentista">Orçamentista</Label>
                      <Input
                        {...register("estrutura.orcamentista")}
                        placeholder="Nome do orçamentista"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <Label className="mb-3 block text-lg font-bold">
                        Tipos de Veículos Atendidos
                      </Label>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                        {tiposDeVeiculo &&
                          tiposDeVeiculo.map((tipo) => (
                            <div
                              key={tipo.id}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={`tipo-${tipo.id}`}
                                checked={selectedTiposVeiculo.includes(tipo.id)}
                                onCheckedChange={() =>
                                  handleTipoVeiculoToggle(tipo.id)
                                }
                              />
                              <Label
                                htmlFor={`tipo-${tipo.id}`}
                                className="text-sm"
                              >
                                {tipo.descricao}
                              </Label>
                            </div>
                          ))}
                      </div>
                    </div>

                    <div className="md:col-span-2">
                      <div className="flex flex-col space-y-2">
                        <p className="mb-3 block text-lg font-bold">
                          Outros Parâmetros
                        </p>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="atende_placa_verde"
                            {...register("estrutura.atende_placa_verde")}
                          />
                          <Label htmlFor="atende_placa_verde">
                            Atende Placa Verde
                          </Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="concessionaria"
                            {...register("estrutura.concessionaria")}
                          />
                          <Label htmlFor="concessionaria">
                            É Concessionária
                          </Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="orcamentacao"
                            {...register("estrutura.orcamentacao")}
                          />
                          <Label htmlFor="orcamentacao">
                            Possui Orçamentação
                          </Label>
                        </div>
                      </div>
                    </div>

                    <div className="md:col-span-2">
                      {existingEstruturaUrls.length > 0 && (
                        <div className="mb-4">
                          <p className="text-sm mb-2">
                            Imagens da estrutura atuais:
                          </p>
                          <div className="flex flex-wrap gap-2">
                            {existingEstruturaUrls.map((url, index) => (
                              <img
                                key={index}
                                src={url}
                                alt={`Estrutura ${index + 1}`}
                                className="h-24 w-24 object-cover rounded"
                              />
                            ))}
                          </div>
                        </div>
                      )}
                      <Label htmlFor="imagens">
                        Atualizar Imagens da Estrutura
                      </Label>
                      <Input
                        type="file"
                        multiple
                        accept="image/*"
                        onChange={(e) => {
                          if (e.target.files) {
                            setEstruturaFiles(Array.from(e.target.files));
                          }
                        }}
                        className="mt-1"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Obs: Ao enviar novas imagens, as anteriores serão
                        substituídas
                      </p>
                    </div>
                  </div>
                </TabsContent>

                {/* Tab 5: Serviços */}
                <TabsContent value={formSteps[4]} className="space-y-4 pt-4">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Serviços Oferecidos</h3>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                      {servicosCredenciados &&
                        servicosCredenciados.map((servico) => (
                          <div
                            key={servico.id}
                            className="flex items-center space-x-2"
                          >
                            <Checkbox
                              id={`servico-${servico.id}`}
                              checked={selectedServicos.includes(servico.id)}
                              onCheckedChange={() =>
                                handleServicoToggle(servico.id)
                              }
                            />
                            <Label
                              htmlFor={`servico-${servico.id}`}
                              className="text-sm"
                            >
                              {servico.descricao}
                            </Label>
                          </div>
                        ))}
                    </div>

                    <div>
                      <Label htmlFor="documentacao">
                        Documentação Adicional
                      </Label>
                      <Input
                        type="file"
                        multiple
                        onChange={(e) => {
                          if (e.target.files) {
                            setDocumentacaoFiles(Array.from(e.target.files));
                          }
                        }}
                        className="mt-1"
                      />
                      {credenciadoData.documentacao?.arquivos && (
                        <div className="mt-2 text-sm text-gray-600">
                          Arquivos:{" "}
                          <a
                            href={credenciadoData.documentacao.arquivos}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 underline"
                          >
                            {credenciadoData.documentacao?.arquivos
                              .split("/")
                              .pop()}
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                </TabsContent>

                {/* Tab 6: Configurações */}
                <TabsContent value={formSteps[5]} className="space-y-4 pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="polo_regionalId">Polo Regional</Label>
                      <Select
                        onValueChange={(value) =>
                          setValue("configuracoes.polo_regionalId", value)
                        }
                        value={watch("configuracoes.polo_regionalId")}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione um polo regional" />
                        </SelectTrigger>
                        <SelectContent>
                          {polosRegionais &&
                            polosRegionais.map((polo) => (
                              <SelectItem key={polo.id} value={polo.id}>
                                {polo.descricao}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="prazoId">Prazo de Pagamento</Label>
                      <Select
                        onValueChange={(value) =>
                          setValue("configuracoes.prazoId", value)
                        }
                        value={watch("configuracoes.prazoId")}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione um prazo" />
                        </SelectTrigger>
                        <SelectContent>
                          {prazosDePagamento &&
                            prazosDePagamento.map((prazo) => (
                              <SelectItem key={prazo.id} value={prazo.id}>
                                {prazo.descricao}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="responsavelPrazo">
                        Responsável pelo Prazo
                      </Label>
                      <Input
                        {...register("configuracoes.responsavelPrazo")}
                        placeholder="Nome do responsável"
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="optante"
                        {...register("configuracoes.optante")}
                      />
                      <Label htmlFor="optante">Optante Simples</Label>
                    </div>

                    {/* Vincular contratos */}
                    <div className="md:col-span-2 mt-6">
                      <Label className="mb-3 block text-lg font-bold">
                        Vincular Contratos
                      </Label>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 border p-4 rounded-md max-h-60 overflow-y-auto">
                        {contratos && contratos.length > 0 ? (
                          contratos.map((contrato) => (
                            <div
                              key={contrato.id}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={`contrato-${contrato.id}`}
                                checked={selectedContratos.includes(
                                  contrato.id
                                )}
                                onCheckedChange={() =>
                                  handleContratoToggle(contrato.id)
                                }
                              />
                              <Label
                                htmlFor={`contrato-${contrato.id}`}
                                className="text-sm"
                              >
                                {contrato.nome_contrato}
                              </Label>
                            </div>
                          ))
                        ) : (
                          <p className="text-sm text-muted-foreground col-span-full">
                            Nenhum contrato disponível para vinculação.
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>

              {/* Botões de navegação */}
              <div className="w-full flex items-center justify-between">
                <Button
                  type="button"
                  onClick={handlePrevious}
                  className="rounded"
                  variant={"secondary"}
                  disabled={currentStep === 0}
                >
                  Anterior
                </Button>

                {currentStep < formSteps.length - 1 ? (
                  <Button
                    type="button"
                    onClick={handleNext}
                    className="rounded"
                  >
                    Próximo
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    className="rounded"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <span className="animate-spin mr-2">⟳</span>
                        Atualizando...
                      </>
                    ) : (
                      "Atualizar"
                    )}
                  </Button>
                )}
              </div>
            </form>
          </FormProvider>
        </CardContent>
      </Card>
      {cnpjModalData && (
        <CnpjDetailsModal
          isOpen={isCnpjModalOpen}
          onClose={() => setIsCnpjModalOpen(false)}
          onConfirm={handleCnpjConfirm}
          data={cnpjModalData}
        />
      )}
    </>
  );
}
