import { useEffect, useState } from "react";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { DefaultValues, useForm } from "react-hook-form";
import { z, ZodSchema } from "zod";
import { Form } from "../ui/form";
import { Button } from "../ui/button";
import { Loader2 } from "lucide-react";

type PageFormType<T extends ZodSchema<any>> = {
  schema: T;
  children: React.ReactNode;
  onSubmit: (values: z.infer<T>) => Promise<void> | void;
  defaultValues?: DefaultValues<z.infer<T>>;
  className?: string;
  submitButtonText?: string;
};

export function PageForm<T extends ZodSchema<any>>({
  children,
  onSubmit,
  schema,
  defaultValues,
  className,
  submitButtonText = "Criar",
}: PageFormType<T>) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const form = useForm<z.infer<T>>({
    resolver: zod<PERSON>esolver(schema),
    defaultValues,
  });

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      const values = form.getValues();
      await onSubmit(values);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full">
      <div className="w-full"></div>
      <Form {...form}>
        <form className={`space-y-4 py-4 ${className}`}>
          {children}
          <div className="w-full flex justify-end">
            <Button
              type="button"
              disabled={isSubmitting}
              onClick={handleSubmit}>
              {isSubmitting && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              {submitButtonText}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}