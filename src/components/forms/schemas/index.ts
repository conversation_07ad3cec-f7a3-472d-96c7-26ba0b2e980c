import { z } from "zod";
import {
  celularValidator,
  cepValidator,
  cleanNonNumeric,
  cnpjValidator,
  cpfValidator,
  numbersOnlyValidator,
  telefoneEcelularValidator,
  telefoneValidator,
} from "@/lib/validators";

const osSchema = z.object({
  veiculoId: z.string().min(1, { message: "Selecione um veículo" }),
  condutorId: z.string().min(1, { message: "Selecione um condutor" }),
  tipo_de_osId: z.string().min(1, { message: "Selecione um tipo de serviço" }),
  credenciadoId: z.string().min(1, { message: "Selecione um credenciado" }),
  tipo_de_manutencao: z.enum([
    "Corretiva",
    "Preventiva",
    "Preditiva",
    "Sinistro",
  ]),
  estado_de_localizacao: z
    .string()
    .min(1, { message: "Selecione o estado de localização" }),
  cidade_de_localizacao: z
    .string()
    .min(1, { message: "Selecione a cidade de localização" }),
  odometro_atual: z.number().optional(),
  mobilizado: z.boolean(),
  descricao: z.string().min(1, { message: "Descreva o serviço ou problema" }),
  orcamento_individual: z.boolean(),
  minimun_orcamento: z.number().min(1),
  OSId: z.string().optional(),
  quoteExpiration: z.number(),
  // Campo derivado do veículo selecionado (read-only)
  tipo_de_frota_display: z.string().optional(),
});

const itemDaVistoria = z.object({
  tipo_de_veiculosId: z
    .string()
    .min(1, { message: "Selecione um tipo de veículo" }),
  descricao: z
    .string()
    .min(1, { message: "Você precisa escrever uma descricao" }),
});
const tipoDeOsSchema = z.object({
  descricao: z.string(),
  obs: z.string().optional(),
  prazo_para_execucao: z.string(),
});

const fipeDoVeiculoSchema = z.object({
  codigo_fipe: z.string().optional(),
  valor_venal: z.string().optional(),
});

const lotacaoDoVeiculoSchema = z.object({
  centro_de_custoId: z
    .string()
    .min(1, { message: "Selecione um centro de custo" }),
  estado: z.string().min(1, { message: "O estado é obrigatório" }),
  cidade: z.string().min(1, { message: "A cidade é obrigatória" }),
});

const combustiveisDoVeiculoSchema = z.object({
  capacidade_do_tanque: z.string().optional(),
  tipos_de_combustiveis: z.array(
    z.enum(["Gasolina", "Etanol / Alcool", "Diesel", "Querosene"])
  ),
});

const definicoesDoVeiculoSchema = z.object({
  numero_de_cambio: z.string().optional(),
  cilindradas: z.number().optional(),
  potencia: z.string().optional(),
  segmento: z.string().optional(),
  carroceria: z.string().optional(),
  transmissao: z.string().optional(),
  quantidade_de_portas: z.number().optional(),
  quantidade_de_assentos: z.number().optional(),
  quantidade_de_eixos: z.number().optional(),
  numero_de_valvulas: z.string().optional(),
  litragem: z.number().optional(),
  combustivel: z.string().optional(),
  origem_do_veiculo: z.enum(["nacional", "importado"]),
});

const faturamentoVeiculoSchema = z.object({
  centro_custoId: z
    .string()
    .min(1, { message: "Selecione um centro de custo" }),
  empenhoId: z.string().min(1, { message: "Selecione um empenho" }),
});

const veiculoSchema = z.object({
  marcaId: z.string().min(1, { message: "Selecione uma marca" }),
  modeloId: z.string().min(1, { message: "Selecione um modelo" }),
  versaoId: z.string().optional(),
  tipo_de_veiculoId: z
    .string()
    .min(1, { message: "Selecione um tipo de veículo" }),
  tipo_de_frotaId: z.string().optional(), // Made optional
  renovam: z.string().optional(),
  vin: z.string().optional(),
  numero_do_motor: z.string().optional(),
  ano_de_fabricacao: z
    .string()
    .min(1, { message: "Informe o ano de fabricação" }), // Made required
  ano_do_modelo: z.string().min(1, { message: "Informe o ano do modelo" }), // Made required
  cor: z.string().optional(),
  odometro_atual: z.number().optional(),
  matricula: z.string().optional(),
  tag_rfid: z.string().optional(),
  placa: z.string().min(1, { message: "A placa é obrigatória" }),
  data_compra: z.string().optional(), // Nova data de compra (não obrigatória)
  data_cedencia: z.string().optional(), // Nova data de cedência (não obrigatória)

  // Make these objects optional with default values
  combustivel: combustiveisDoVeiculoSchema.optional().default({
    capacidade_do_tanque: "",
    tipos_de_combustiveis: ["Gasolina"],
  }),

  definicoes: definicoesDoVeiculoSchema.optional().default({
    numero_de_cambio: "",
    cilindradas: undefined,
    potencia: "",
    segmento: "",
    carroceria: "",
    transmissao: "",
    quantidade_de_portas: undefined,
    quantidade_de_assentos: undefined,
    quantidade_de_eixos: undefined,
    numero_de_valvulas: "",
    litragem: undefined,
    combustivel: "",
    origem_do_veiculo: "nacional",
  }),

  lotacao: lotacaoDoVeiculoSchema.optional().default({
    centro_de_custoId: "",
    estado: "",
    cidade: "",
  }),

  faturamento: faturamentoVeiculoSchema.optional().default({
    centro_custoId: "",
    empenhoId: "",
  }),

  fipe: fipeDoVeiculoSchema.optional(),
  status: z
    .enum(["Ativo", "Inativo", "Em Manuntenção", "Sem condições de uso"])
    .default("Ativo"),
});

const condutorEnderecoSchema = z.object({
  cep: z.string().optional(),
  logradouro: z.string().optional(),
  bairro: z.string().optional(),
  estado: z.string().optional(),
  cidade: z.string().optional(),
});

const cnhSchema = z.object({
  numero: z.string().min(1, { message: "O número da CNH é obrigatório" }),
  categoria: z.string().min(1, { message: "A categoria da CNH é obrigatória" }),
  estado: z.string().min(1, { message: "O estado da CNH é obrigatório" }),
  data_emissao: z.date({ message: "A data de emissão da CNH é obrigatória" }),
  data_vencimento: z.date({
    message: "A data de vencimento da CNH é obrigatória",
  }),
});

const moppSchema = z.object({
  numero: z.string().min(1, { message: "O número do MOPP é obrigatório" }),
  emissor: z.string().min(1, { message: "O emissor do MOPP é obrigatório" }),
  data_emissao: z.date({ message: "A data de emissão do MOPP é obrigatória" }),
  data_vencimento: z.date({
    message: "A data de vencimento do MOPP é obrigatória",
  }),
});
const condutorSchema = z.object({
  nome: z.string().min(1, { message: "O nome é obrigatório" }),
  matricula: z.string().min(1, { message: "A matrícula é obrigatória" }),
  cpf: z.string().optional(),
  contato: z.string().min(1, { message: "O contato é obrigatório" }),
  email: z.string().email({ message: "E-mail inválido" }),
  endereco: condutorEnderecoSchema,
  cnh: cnhSchema,
  mopp: moppSchema.optional(),
  lotacao_condutorId: z.string().optional(),
  centro_de_custoId: z.string().optional(),
  status: z.boolean().default(true),
  ativo: z.boolean(),
  createdAt: z.date().optional(),
});
const servicoCredenciadoSchema = z.object({
  description: z.string().min(1, { message: "A descrição é obrigatória" }),
});
const informacoesDoCredenciadoSchema = z.object({
  cnpj: cnpjValidator(),
  inscricao_estadual: z.string().optional(),
  inscricao_municipal: z.string().optional(),
  razao_social: z.string().min(1, "Razão social é obrigatória"),
  nome_fantasia: z.string().min(1, "Nome fantasia é obrigatório"),
  atividade_principal: z.string().optional(),
  horario_funcionamento: z.string().optional(),
  data_abertura: z.string().min(1, "Data de abertura é obrigatória"),
  porte_empresarial: z.string().optional(),
  capital_social: z.null(),
  patrimonio_liquido: z.null(),
  observacoes_gerais: z.string().optional(),
  logotipo_empresa: z.union([z.instanceof(File), z.string()]).optional(),
});

const contatosDoCredenciadoSchema = z.object({
  telefone: telefoneValidator(),
  celular: celularValidator(),
  email: z.string().email({ message: "E-mail inválido" }),
  nome_do_gerente: z.string().optional(),
  telefone_do_gerente: telefoneValidator().optional(),
  nome_do_proprietario: z
    .string()
    .min(1, { message: "Nome do proprietário é obrigatório" }),
  telefone_do_proprietario: telefoneValidator(),
});

const enderecoDoCredenciadoSchema = z.object({
  cep: cepValidator(),
  logradouro: z.string().min(1, { message: "Logradouro é obrigatório" }),
  bairro: z.string().min(1, { message: "Bairro é obrigatório" }),
  estado: z.string().min(1, { message: "Estado é obrigatório" }),
  cidade: z.string().min(1, { message: "Cidade é obrigatória" }),
  latitude: z.string().optional(),
  longitude: z.string().optional(),
});

const estruturaDoCredenciadoSchema = z.object({
  capacidade_total_de_atendimento: z
    .number()
    .min(1, { message: "Capacidade total é obrigatória" }),
  box_para_veiculos_leves: z
    .number()
    .min(0, { message: "Número de boxes para veículos leves é obrigatório" }),
  box_para_veiculos_pesados: z
    .number()
    .min(0, { message: "Número de boxes para veículos pesados é obrigatório" }),
  elevadores_para_veiculos: z
    .number()
    .min(0, { message: "Número de elevadores é obrigatório" }),
  estufas_para_pintura: z
    .number()
    .min(0, { message: "Número de estufas é obrigatório" }),
  imagens: z.array(z.union([z.instanceof(File), z.string()])).optional(),
});

const configuracoesSchema = z.object({
  orcamentista: z.boolean(),
  atende_placa_verde: z.boolean(),
  concessionaria: z.boolean(),
  orcamentacao: z.boolean(),
  prazo_contratualId: z
    .string()
    .min(1, { message: "Prazo contratual é obrigatório" }),
  polo_regionalId: z
    .string()
    .min(1, { message: "Polo regional é obrigatório" }),
});

const documentacaoSchema = z.object({
  files: z.array(z.union([z.instanceof(File), z.string()])).optional(),
});

const credenciadoSchema = z.object({
  informacoes: informacoesDoCredenciadoSchema,
  contatos: contatosDoCredenciadoSchema,
  endereco: enderecoDoCredenciadoSchema,
  estrutura: estruturaDoCredenciadoSchema,
  documentacao: z.array(z.union([z.instanceof(File), z.string()])).optional(),
  configuracoes: configuracoesSchema,
  servicos: z.array(servicoCredenciadoSchema).optional(),
});

const vistoriaSchema = z.object({
  veiculoId: z.string().min(1, { message: "Selecione um veículo" }),
  tipo_de_avaliacao: z.enum(["Inicial", "Periódica"]),
  data: z.date(),
  odometro_atual: z.string(),
  previsao_troca_de_oleo: z.object({
    km: z.string(),
    data: z.date(),
  }),
  observacao: z.string(),
});

const checklistSchema = z.object({
  veiculoId: z.string().min(1, { message: "Selecione um veículo" }),
  condutorId: z.string().min(1, { message: "Selecione um condutor" }),
  credenciadoId: z.string().min(1, { message: "Selecione um credenciado" }),
  osId: z.string().min(1, { message: "Selecione um credenciado" }),
  situacao_do_veiculo: z.object({
    pneus: z.enum(["Conforme", "Não Conforme"]),
    rodas: z.enum(["Conforme", "Não Conforme"]),
    estepe: z.enum(["Conforme", "Não Conforme"]),
    freio_de_mão: z.enum(["Conforme", "Não Conforme"]),
    freio_de_pé: z.enum(["Conforme", "Não Conforme"]),
    embreagem: z.enum(["Conforme", "Não Conforme"]),
    luzes_do_painel: z.enum(["Conforme", "Não Conforme"]),
    buzina: z.enum(["Conforme", "Não Conforme"]),
    lanternas_dianteiras: z.enum(["Conforme", "Não Conforme"]),
    lanternas_traseiras: z.enum(["Conforme", "Não Conforme"]),
    farol_baixo: z.enum(["Conforme", "Não Conforme"]),
    farol_alto: z.enum(["Conforme", "Não Conforme"]),
    setas_dianteiras: z.enum(["Conforme", "Não Conforme"]),
    setas_traseiras: z.enum(["Conforme", "Não Conforme"]),
    luzes_de_emergência: z.enum(["Conforme", "Não Conforme"]),
    luzes_de_freio: z.enum(["Conforme", "Não Conforme"]),
    luzes_de_ré: z.enum(["Conforme", "Não Conforme"]),
    espelhos_retrovisores: z.enum(["Conforme", "Não Conforme"]),
    estado_geral_da_carroceria: z.enum(["Conforme", "Não Conforme"]),
    nível_de_água: z.enum(["Conforme", "Não Conforme"]),
    limpador_para_brisa: z.enum(["Conforme", "Não Conforme"]),
    nível_de_fluído: z.enum(["Conforme", "Não Conforme"]),
    óleo_do_motor: z.enum(["Conforme", "Não Conforme"]),
    observacoes: z.string().optional(),
    observacoes_rodas: z.string().optional(),
    observacoes_luzes_externas: z.string().optional(),
    observacoes_luz_do_painel: z.string().optional(),
    observacoes_limpeza: z.string().optional(),
    observacoes_estado_geral: z.string().optional(),
  }),
  tipo: z.enum(["Entrada", "Saída"]),
  data: z.date().optional(),
  previsao_entrega: z.date(),
  odometro_atual: z.string(),
  nivel_do_combustivel: z.enum(["Vazio", "1/4", "1/2", "3/4", "Cheio"]),
  arquivos: z.any(),
});

export const tiposDeCombustiveis = [
  "Gasolina",
  "Etanol / Alcool",
  "Diesel",
  "Querosene",
] as const;

const abastecimentoSchema = z.object({
  veiculoId: z.string().min(1, { message: "Selecione um veículo" }),
  data_e_hora: z.date({ message: "A data e hora são obrigatórias" }),
  odometro: z.string().min(1, { message: "O odômetro é obrigatório" }),
  combustivel: z.enum(tiposDeCombustiveis, {
    message: "Selecione um tipo de combustível válido",
  }),
  preco_l: z.string().min(1, { message: "O preço por litro é obrigatório" }),
  valor_total: z.string().min(1, { message: "O valor total é obrigatório" }),
  litros: z
    .string()
    .min(1, { message: "A quantidade de litros é obrigatória" }),
  condutorId: z.string().min(1, { message: "Selecione um condutor" }),
  credenciadoId: z.string().min(1, { message: "Selecione um credenciado" }),
  observacoes: z.string().optional(),
  arquivos: z.array(z.string()).optional(),
  completando_tanque: z.boolean().optional(),
});

const despesaSchema = z.object({
  veiculoId: z.string().min(1, { message: "Selecione um veículo" }),
  data_e_hora: z.date().optional(),
  odometro: z.string().min(1, { message: "O odômetro é obrigatório" }),
  tipo_de_despesaId: z
    .string()
    .min(1, { message: "Selecione um tipo de despesa" }),
  valor: z.string().min(1, { message: "O valor é obrigatório" }),
  local: z.string().min(1, { message: "O local é obrigatório" }),
  condutorId: z.string().min(1, { message: "Selecione um condutor" }),
  observacoes: z.string().optional(),
  arquivos: z.array(z.any()).optional(),
});

const poloRegionalSchema = z.object({
  descricao: z.string().min(1, { message: "A descrição é obrigatória" }),
});

const timePercursoSchema = z.object({
  local: z.string().min(1, { message: "O local é obrigatório" }),
  data: z.date().optional(), // Data é opcional
  odometro: z.number().min(1, { message: "O odômetro é obrigatório" }),
});

const percursoSchema = z.object({
  veiculoId: z.string().min(1, { message: "Selecione um veículo" }),
  condutorId: z.string().min(1, { message: "Selecione um condutor" }),
  motivo: z.string().min(1, { message: "O motivo é obrigatório" }),
  origem: timePercursoSchema,
  destino: timePercursoSchema,
  arquivos: z.array(z.any()).optional(),
  observacoes: z.string().optional(),
});

const repeticaoConfigSchema = z.object({
  tipo: z.enum(["UNICO", "REPETIR_A_CADA"]),
  intervalo: z.number().optional(),
});

const lembreteSchema = z.object({
  veiculoId: z.string().min(1, { message: "Selecione um veículo" }),
  tipo: z.enum(["DESPESA", "SERVICO"]),
  referencialId: z.string().min(1, { message: "Selecione uma referência" }),
  odometro: z.string().min(1, { message: "O odômetro é obrigatório" }),
  repeticao: repeticaoConfigSchema,
  data: z.date(),
  dataDesejada: z.date().optional(),
  quantidade: z.number().optional(),
  periodo: z.enum(["DIAS", "SEMANAS", "MESES"]).optional(),
  observacoes: z.string().optional(),
});

const contatoContratoSchema = z.object({
  responsavel: z.string().min(1, { message: "O responsável é obrigatório" }),
  telefone: z.string(),
  email: z.string().email({ message: "E-mail inválido" }),
});

const enderecoContratoSchema = z.object({
  cep: z.string(),
  logradouro: z.string().min(1, { message: "O logradouro é obrigatório" }),
  bairro: z.string().min(1, { message: "O bairro é obrigatório" }),
  estado: z.string().min(1, { message: "O estado é obrigatório" }),
  cidade: z.string().min(1, { message: "A cidade é obrigatória" }),
});

const nfContratoSchema = z.object({
  placa_do_veiculo: z.boolean(),
  numero_da_os: z.boolean(),
  modelo_do_veiculo: z.boolean(),
  numero_do_contrato: z.boolean(),
});

const configContratoSchema = z.object({
  ativo: z.boolean(),
  abertura_os_credenciado: z.boolean(),
  envio_de_emails: z.boolean(),
  veiculos_com_rfid: z.boolean(),
  parametrizacao_obrigatoria: z.boolean(),
  checklist_simplificado_pecas: z.boolean().optional(),
  aprovacao_gestor: z.boolean().optional(),
  ajuste_gestor: z.boolean().optional(),
});

const contratoSchema = z.object({
  nome: z.string().min(1, { message: "O nome é obrigatório" }),
  numero: z.string().min(1, { message: "O número do contrato é obrigatório" }),
  data_inicial: z.string({ message: "A data inicial é obrigatória" }),
  data_final: z.string({ message: "A data final é obrigatória" }),
  valor_do_contrato: z
    .number()
    .min(1, { message: "O valor do contrato é obrigatório" }),
  taxa_administrativa: z
    .number()
    .min(0, { message: "A taxa administrativa é obrigatória" }),
  cnpj: z.string(),
  razao_social: z.string().min(1, { message: "A razão social é obrigatória" }),
  logotipo: z.instanceof(File).optional(),
  limite_gastos_percent: z
    .number()
    .min(0, { message: "O limite de gastos é obrigatório" }),
  contato: contatoContratoSchema,
  endereco: enderecoContratoSchema,
  configuracao: configContratoSchema,
});

export const fincanceiroContratoSchema = z.object({
  validacao_nf: nfContratoSchema,
  send_nf_to: z.string().optional(),
  cnpj_financial: z.string(),
  numero_contrato_financial: z.string(),
  endereco_financial: enderecoContratoSchema,
});

const tiposDeDespesaSchema = z.object({
  descricao: z.string(),
});

const enderecoSchema = z.object({
  rua: z.string(),
  numero: z.string(),
  complemento: z.string().optional(),
  bairro: z.string(),
  cidade: z.string(),
  estado: z.string(),
  cep: cepValidator(),
});

const versaoSchema = z.object({
  descricao: z.string(),
});
const centroDeCustoEnderecoSchema = z.object({
  cep: cepValidator().optional(),
  logradouro: z.string().optional(),
  bairro: z.string().optional(),
  estado: z.string().optional(),
  cidade: z.string().optional(),
});

const centroDeCustoSchema = z.object({
  descricao: z.string().min(1, { message: "A descrição é obrigatória" }),
  centro_de_custo_ascendenteId: z
    .string()
    .min(0, { message: "Selecione um centro de custo ascendente" })
    .optional(),
  dotacao_orcamentista: z.string().optional(),
  valor_dotacao: z
    .number()
    .min(1, { message: "O valor da dotação é obrigatório" }),
  nome_responsavel: z.string().optional(),
  contato: z.string(),
  email: z.string().email({ message: "E-mail inválido" }).optional(),
  cnpj: z.string().min(14, { message: "CNPJ inválido" }),
  razao_social: z.string().min(1, { message: "A razão social é obrigatória" }),
  endereco: centroDeCustoEnderecoSchema,
  centro_de_custo_tomadorId: z
    .string()
    .min(0, { message: "Selecione um centro de custo tomador" })
    .optional(),
  active: z.boolean(),
  veiculos_com_rfid: z.boolean(),
  abertura_os: z.boolean(),
});

const empenhoSchema = z.object({
  centro_de_custoId: z
    .string()
    .min(1, { message: "Selecione um centro de custo" }),
  nota_empenho: z
    .string()
    .min(1, { message: "A nota de empenho é obrigatória" }),
  ano_de_competencia: z
    .string()
    .min(1, { message: "O ano de competência é obrigatório" }),
  data_inicial: z.date({ message: "A data inicial é obrigatória" }),
  data_final: z.date({ message: "A data final é obrigatória" }),
  dotacao_orcamentada: z
    .string()
    .min(1, { message: "A dotação orçamentada é obrigatória" }),
  valor_destinado_as_pecas: z
    .number()
    .min(1, { message: "O valor destinado às peças é obrigatório" }),
  valor_destinado_aos_servicos: z
    .number()
    .min(1, { message: "O valor destinado aos serviços é obrigatório" }),
  bloqueado: z.boolean(),
  ativo: z.boolean(),
});

const marcaSchema = z.object({
  marca: z.string().min(1, "O nome da marca é obrigatória"),
  codigo_fipe: z.string().optional(),
  codigo_suiv: z.string().optional(),
});

const modeloSchema = z.object({
  marca: z.string().min(1, "O Marca é obrigatório"),
  descricao: z.string().min(1, "A descrição é obrigatória"),
  codigo_fipe: z.string().optional(),
  codigo_suiv: z.string().optional(),
});

const prazoDePagamentoSchema = z.object({
  descricao: z.string().min(1, { message: "A descrição é obrigatória" }),
});
const tipoDeVeiculoSchema = z.object({
  descricao: z.string().min(1, { message: "A descrição é obrigatória" }),
});
const tipoDeFrotaSchema = z.object({
  descricao: z.string().min(1, { message: "A descrição é obrigatória" }),
});
const permissoesValidas = [
  "admin operacional",
  "supervisor de frota",
  "gestor de frota",
  "gestor financeiro",
  "gestor orçamento",
  "abertura de os",
  "credenciado",
] as const;

const acessoSchema = z.object({
  permissao: z
    .array(z.enum(permissoesValidas))
    .min(1, { message: "Selecione pelo menos uma permissão" }),
  contratoId: z.string().min(1, { message: "Selecione um contrato" }),
  centro_de_custoId: z
    .string()
    .min(1, { message: "Selecione um centro de custo" }),
});
const cidadeSchema = z.object({
  estado: z.string().min(1, { message: "O estado é obrigatório" }),
  cidade: z.string().min(1, { message: "A cidade é obrigatória" }),
});

const usuarioSchema = z
  .object({
    nome: z.string().min(1, { message: "O nome é obrigatório" }),
    matricula: z.string().optional(),
    email: z.string().email({ message: "E-mail inválido" }),
    telefone: z.string().optional(),
    celular: z.string().optional(),
    senha: z
      .string()
      .min(6, { message: "A senha deve conter no mínimo 6 caracteres" }),
    roles: z
      .array(z.object({ value: z.string(), label: z.string() }))
      .min(1, "Selecione ao menos uma função"),
    contratos: z
      .array(z.object({ value: z.string(), label: z.string() }))
      .optional(),
    centro_de_custoId: z.string().optional(),
    // Changed definition here to match the multi-select component output
    unidade_filha_id: z
      .array(z.object({ value: z.string(), label: z.string() }))
      .optional(),
    unidades_filha_ids: z.array(z.string()).optional(),
    ativo: z.boolean().optional(),
    nivel_aprovacao: z.number().optional(),
  })
  .refine(
    (data) => {
      // Verifica se o usuário tem a role ADMIN
      const isAdmin = data.roles.some((role) => role.value === "ADMIN");

      // Se for admin, não requer centro_de_custo nem unidade_filha mesmo que seja gestor
      if (isAdmin) {
        return true;
      }

      // Caso não seja admin, aplica a regra normal para gestores
      const isGestor = data.roles.some(
        (role) => role.value === "GESTOR" || role.value === "GESTOR_FROTA"
      );

      if (isGestor) {
        // Verifica se os campos obrigatórios estão preenchidos
        if (
          !data.centro_de_custoId ||
          !data.unidade_filha_id ||
          data.unidade_filha_id.length === 0
        )
          return false;
      }

      return true;
    },
    {
      message: "Centro de Custo e Unidade são obrigatórios para Gestores",
      path: ["centro_de_custoId"],
    }
  );

export {
  abastecimentoSchema,
  checklistSchema,
  condutorSchema,
  contratoSchema,
  credenciadoSchema,
  despesaSchema,
  lembreteSchema,
  osSchema,
  percursoSchema,
  tipoDeOsSchema,
  veiculoSchema,
  vistoriaSchema,
  itemDaVistoria,
  tiposDeDespesaSchema,
  centroDeCustoSchema,
  empenhoSchema,
  versaoSchema,
  marcaSchema,
  modeloSchema,
  poloRegionalSchema,
  servicoCredenciadoSchema,
  prazoDePagamentoSchema,
  tipoDeVeiculoSchema,
  tipoDeFrotaSchema,
  acessoSchema,
  permissoesValidas,
  cidadeSchema,
  usuarioSchema,
};
