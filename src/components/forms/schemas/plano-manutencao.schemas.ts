import { z } from "zod";

export const planoManutencaoSchema = z.object({
  id: z.string().optional(),
  nome: z.string().min(1, { message: "Nome do plano é obrigatório" }),
  descricao: z.string(),
  clienteId: z.string().min(1, { message: "Cliente é obrigatório" }),
  tipo_veiculoId: z.string().min(1, { message: "Tipo de veículo é obrigatório" }),
  modelo_veiculoId: z.string().min(1, { message: "Modelo de veículo é obrigatório" }),
  veiculoId: z.string().min(1, { message: "Veículo é obrigatório" }),
  intervalo_km: z.number().min(0),
  intervalo_tempo: z.number().min(0),
  itens: z.array(
    z.object({
      descricao: z.string().min(1, { message: "Descrição do item é obrigatória" }),
      tipo: z.enum(["PECAS", "SERVICOS"]),
      periodicidade_km: z.number().min(0),
      periodicidade_tempo: z.number().min(0),
      observacoes: z.string().optional(),
    })
  ),
  ativo: z.boolean(),
});
