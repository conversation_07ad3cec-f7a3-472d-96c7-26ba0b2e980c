"use client";

import { z } from "zod";
import { cnpjValidator, telefoneValidator, celularValidator, cepValidator } from "@/lib/validators";
const MAX_FILE_SIZE = 5000000;
const ACCEPTED_IMAGE_TYPES = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/webp",
];

export const configurationSchema = z.object({
  isBudgeter: z
    .boolean()
    .describe(
      "Orçamentistas são os credenciados que aceitam o veículo para criar o orçamento inicial."
    ),
  servesGreenPlate: z
    .boolean()
    .describe(
      "Placas verdes são utilizadas por oficinas e concessionárias nos veículos em teste ou test-drive."
    ),
  activeForBudgeting: z
    .boolean()
    .describe(
      "Se desabilitado, o credenciado não poderá ser escolhido para receber orçamentos a serem cotados."
    ),
  isDealership: z
    .boolean()
    .describe(
      "Permite controlar a solução de peças e serviços específicos durante a criação do orçamento pelo credenciado."
    ),
  contractualTerm: z
    .string()
    .describe("Prazo para pagamento ao credenciado após a entrega do veículo."),
  servedRegion: z
    .string()
    .describe(
      "Utilizado para seleção do credenciado por localização, na distribuição de cotações."
    ),
});

export const informationSchema = z.object({
  cnpj: cnpjValidator(),
  stateRegistration: z.string().optional(),
  municipalRegistration: z.string().optional(),
  corporateName: z.string().min(1, "Razão social é obrigatória"),
  tradingName: z.string().min(1, "Nome fantasia é obrigatório"),
  mainActivity: z.string().optional(),
  businessHours: z.string().optional(),
  openingDate: z.string().min(1, "Data de abertura é obrigatória"),
  companySize: z.string().optional(),
  shareCapital: z.string().optional(),
  netWorth: z.string().optional(),
  generalObservations: z.string().optional(),
  logo: z
    .instanceof(File)
    .refine(
      (file) => file?.size <= MAX_FILE_SIZE,
      `O tamanho máximo da imagem é 5MB.`
    )
    .refine(
      (file) => ACCEPTED_IMAGE_TYPES.includes(file?.type),
      "Apenas .jpg, .jpeg, .png e .webp formatos são suportados."
    ),
});

export const addressSchema = z.object({
  cep: cepValidator(),
  state: z.string().min(1, { message: "O estado é obrigatório" }).max(50, {
    message: "Nenhum nome de estado brasileiro é tão grande assim",
  }),
  city: z.string().min(1, { message: "A cidade é obrigatória" }).max(100, {
    message: "Nenhum nome de cidade brasileira é tão grande assim",
  }),
  neighborhood: z
    .string()
    .min(1, { message: "O bairro é obrigatório" })
    .max(100, {
      message: "Nenhum nome de bairro brasileiro é tão grande assim",
    }),
  street: z
    .string()
    .min(1, { message: "O logradouro é obrigatório" })
    .max(200, { message: "O nome do logradouro é muito grande" }),
  houseNumber: z
    .string()
    .min(1, { message: "O número da casa é obrigatório" })
    .max(20, { message: "O número da casa é muito grande" }),
});

export const contactsSchema = z.object({
  phone: telefoneValidator().optional(),
  mobile: celularValidator(),
  email: z.string().email("Invalid email"),
  manager: z.string().optional(),
  owner: z.string().optional(),
});

export const structureSchema = z.object({
  serviceCapacity: z.number().min(1, "Capacidade é obrigatória"),
  lightVehicleBays: z.number().min(0),
  heavyVehicleBays: z.number().min(0),
  elevators: z.number().min(0),
  ovens: z.number().min(0),
});

export const documentationSchema = z.object({
  files: z.array(z.any()),
});

// export const settingsSchema = z.object({
//   estimator: z.boolean(),
//   dealership: z.boolean(),
//   greenPlate: z.boolean(),
//   active: z.boolean(),
//   contractTerm: z.string().optional(),
//   regionalHub: z.string().optional(),
// });

// export const accreditorSchema = z.object({
//   information: informationSchema,
//   contacts: contactsSchema,
//   addres: addressSchema,
//   structure: structureSchema,

//   configuration: configurationSchema,
// });
export const accreditorSchema = z.object({
  cnpj: cnpjValidator(),
  stateRegistration: z.string().optional(),
  municipalRegistration: z.string().optional(),
  corporateName: z.string().min(1, "Razão social é obrigatória"),
  tradingName: z.string().min(1, "Nome fantasia é obrigatório"),
  mainActivity: z.string().optional(),
  businessHours: z.string().optional(),
  openingDate: z.string().min(1, "Data de abertura é obrigatória"),
  companySize: z.string().optional(),
  shareCapital: z.string().optional(),
  netWorth: z.string().optional(),
  generalObservations: z.string().optional(),
  logo: z.instanceof(FileList).optional(),
  phone: telefoneValidator().optional(),
  mobile: celularValidator(),
  email: z.string().email("Invalid email"),
  manager: z.string().optional(),
  owner: z.string().optional(),
  cep: cepValidator(),
  state: z.string().min(1, { message: "O estado é obrigatório" }).max(50, {
    message: "Nenhum nome de estado brasileiro é tão grande assim",
  }),
  city: z.string().min(1, { message: "A cidade é obrigatória" }).max(100, {
    message: "Nenhum nome de cidade brasileira é tão grande assim",
  }),
  neighborhood: z
    .string()
    .min(1, { message: "O bairro é obrigatório" })
    .max(100, {
      message: "Nenhum nome de bairro brasileiro é tão grande assim",
    }),
  street: z
    .string()
    .min(1, { message: "O logradouro é obrigatório" })
    .max(200, { message: "O nome do logradouro é muito grande" }),
  houseNumber: z
    .string()
    .min(1, { message: "O número da casa é obrigatório" })
    .max(20, { message: "O número da casa é muito grande" }),
  serviceCapacity: z.string().min(1, "Capacidade é obrigatória"),
  lightVehicleBays: z.string().min(0),
  heavyVehicleBays: z.string().min(0),
  elevators: z.string().min(0),
  ovens: z.string().min(0),
  isBudgeter: z
    .boolean()
    .describe(
      "Orçamentistas são os credenciados que aceitam o veículo para criar o orçamento inicial."
    ),
  servesGreenPlate: z
    .boolean()
    .describe(
      "Placas verdes são utilizadas por oficinas e concessionárias nos veículos em teste ou test-drive."
    ),
  activeForBudgeting: z
    .boolean()
    .describe(
      "Se desabilitado, o credenciado não poderá ser escolhido para receber orçamentos a serem cotados."
    ),
  isDealership: z
    .boolean()
    .describe(
      "Permite controlar a solução de peças e serviços específicos durante a criação do orçamento pelo credenciado."
    ),
  contractualTerm: z
    .string()
    .describe("Prazo para pagamento ao credenciado após a entrega do veículo."),
  servedRegion: z
    .string()
    .describe(
      "Utilizado para seleção do credenciado por localização, na distribuição de cotações."
    ),
});
