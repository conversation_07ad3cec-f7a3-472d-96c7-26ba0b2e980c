"use client";
import { useF<PERSON>, FormProvider } from "react-hook-form";
import { useState, useCallback } from "react";
import { toast } from "sonner";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "../ui/card";
import { <PERSON><PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import { Checkbox } from "../ui/checkbox";
import { Label } from "../ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import {
  CriarCredenciadoData,
  criarCredenciado,
  existCredenciadoByCnpj,
  existCredenciadoByEmail,
} from "@/serverActions/credenciadoAction";
import { useCredenciado } from "@/context/credenciado-context";
import { usePoloRegional } from "@/context/polo-regional-context";
import { useTipoDeVeiculo } from "@/context/tipo-de-veiculo-context";
import { usePrazoDePagamento } from "@/context/prazo-de-pagamento-context";
import { useServicoCredenciado } from "@/context/servico-credenciado-context";
import {
  CNPJInput,
  TelefoneInput,
  CelularInput,
} from "../inputs/numeric-input";
import { consultCnpj } from "@/serverActions/cnpjAction";
import { CnpjData } from "@/interfaces/cnpj.interface";
import { DateInput } from "../inputs/date-input";
import { CnpjDetailsModal } from "../modal/cnpj-details-modal";
import { useContrato } from "@/context/contrato-context";
import { CepInput } from "../inputs/cep-input";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { formatCNPJ } from "@/lib/utils";

// Definindo os passos de navegação do formulário
const formSteps = [
  "Informações",
  "Contatos",
  "Endereço",
  "Estrutura",
  "Serviços",
  "Configurações",
];

const normalizedFormStepsDict = [
  "informacoes",
  "contatos",
  "endereco",
  "estrutura",
  "servicos",
  "configuracoes",
];

const schema = z.object({
  informacoes: z.object({
    cnpj: z.string().min(1, "CNPJ é obrigatório"),
    razao_social: z.string().min(1, "Razão social é obrigatória"),
    inscri_estadual: z.string().optional(),
    inscri_municipal: z.string().optional(),
    nome_fantasia: z.string().optional(),
    atividade_principal: z.string().min(1, "Atividade principal é obrigatória"),
    horario_funcionamento: z.string().optional(),
    data_abertura: z.string().min(1, "Data de abertura é obrigatória"),
    porte_empresarial: z.string().min(1, "Porte empresarial é obrigatório"),
    capital_social: z.string().nullable().optional(),
    patrimonio_liquido: z.string().nullable().optional(),
    observacoes_gerais: z.string().optional(),
    logotipo_empresa: z.string().url().optional(),
  }),
  estrutura: z.object({
    capacidade_atendimento: z.string(),
    box_veiculos_leves: z.string(),
    box_veiculos_pesados: z.string(),
    elevadores_veiculos: z.string(),
    estufas_pintura: z.string(),
    images: z.array(z.string().url()).optional(),
    tipos_veiculo: z.array(z.string()).optional(),
    orcamentista: z.string().optional(),
    atende_placa_verde: z.boolean().optional(),
    concessionaria: z.boolean().optional(),
    orcamentacao: z.boolean().optional(),
  }),
  endereco: z.object({
    cep: z.string().min(1, "CEP é obrigatório"),
    logradouro: z.string().min(1, "Logradouro é obrigatório"),
    bairro: z.string().min(1, "Bairro é obrigatório"),
    estado: z.string().min(1, "Estado é obrigatório"),
    cidade: z.string().min(1, "Cidade é obrigatória"),
    latitude: z.string().optional(),
    longitude: z.string().optional(),
  }),
  contatos: z.object({
    telefone: z.string().optional(),
    celular: z.string().optional(),
    email: z.string().email("Email inválido"),
    nome_gerente: z.string().optional(),
    telefone_gerente: z.string().optional(),
    nome_proprietario: z.string().optional(),
    telefone_proprietario: z.string().optional(),
  }),
  servicos: z.array(z.string()).optional(),
  prazoId: z.string().optional(),
  url_logo: z.string().url().optional(),
  url_estrutura: z.string().url().optional(),
  optante: z.boolean().optional(),
  responsavelPrazo: z.string().optional(),
  contratosIds: z.array(z.string()).optional(),
  configuracoes: z.object({
    polo_regionalId: z.string().min(1, "Polo regional é obrigatório"),
    prazoId: z.string().optional(),
    optante: z.boolean().optional(),
    responsavelPrazo: z.string().optional(),
  }),
});

export function SimpleCredenciadoForm() {
  // Estados
  const [currentStep, setCurrentStep] = useState(0);
  const [isLoadingCNPJ, setIsLoadingCNPJ] = useState(false);
  const [selectedServicos, setSelectedServicos] = useState<string[]>([]);
  const [selectedTiposVeiculo, setSelectedTiposVeiculo] = useState<string[]>(
    []
  );
  const [selectedContratos, setSelectedContratos] = useState<string[]>([]);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [estruturaFiles, setEstruturaFiles] = useState<File[]>([]);
  const [documentacaoFiles, setDocumentacaoFiles] = useState<File[]>([]);
  const [cnpjModalData, setCnpjModalData] = useState<CnpjData | null>(null);
  const [isCnpjModalOpen, setIsCnpjModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [filtroServico, setFiltroServico] = useState("");

  const { polosRegionais } = usePoloRegional();
  const { tiposDeVeiculo } = useTipoDeVeiculo();
  const { prazosDePagamento } = usePrazoDePagamento();
  const { servicosCredenciados } = useServicoCredenciado();
  const { setCredenciados } = useCredenciado();
  const { contratos } = useContrato();

  // Formulário
  const formMethods = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      informacoes: {
        cnpj: "",
        razao_social: "",
        inscri_estadual: "",
        inscri_municipal: "",
        nome_fantasia: "",
        atividade_principal: "",
        horario_funcionamento: "",
        data_abertura: "",
        porte_empresarial: "",
        capital_social: "",
        patrimonio_liquido: "",
        observacoes_gerais: "",
      },
      estrutura: {
        capacidade_atendimento: "0",
        box_veiculos_leves: "0",
        box_veiculos_pesados: "0",
        elevadores_veiculos: "0",
        estufas_pintura: "0",
        orcamentista: "",
        atende_placa_verde: false,
        concessionaria: false,
        orcamentacao: false,
      },
      endereco: {
        cep: "",
        logradouro: "",
        bairro: "",
        estado: "",
        cidade: "",
        latitude: "",
        longitude: "",
      },
      contatos: {
        telefone: "",
        celular: "",
        email: "",
        nome_gerente: "",
        telefone_gerente: "",
        nome_proprietario: "",
        telefone_proprietario: "",
      },
      configuracoes: {
        polo_regionalId: "",
        prazoId: "",
        optante: false,
        responsavelPrazo: "",
      },
    },
  });
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = formMethods;
  // Valor atual do CNPJ para consulta
  const cnpjValue = watch("informacoes.cnpj");

  const handleCnpjLookup = async () => {
    if (!cnpjValue) {
      toast.error("Digite um CNPJ para consultar");
      return;
    }

    try {
      setIsLoadingCNPJ(true);
      const cleanCnpj = cnpjValue.replace(/\D/g, "");
      const response = await consultCnpj(cleanCnpj);

      if (!response) {
        throw new Error("CNPJ não encontrado");
      }

      // Em vez de preencher diretamente, guarde os dados e abra o modal
      setCnpjModalData(response);
      setIsCnpjModalOpen(true);
    } catch (error) {
      toast.error("Erro ao consultar CNPJ");
      console.error(error);
    } finally {
      setIsLoadingCNPJ(false);
    }
  };

  // Adicione a função para preencher os campos quando o usuário confirmar
  const handleCnpjConfirm = () => {
    if (!cnpjModalData) return;

    // Preencher os campos com os dados retornados
    setValue("informacoes.razao_social", cnpjModalData.company.name);
    setValue("informacoes.nome_fantasia", cnpjModalData.alias || "");

    // Formatação especial para a data
    if (cnpjModalData.founded) {
      const dataFormatada = cnpjModalData.founded.split("T")[0]; // Formato YYYY-MM-DD
      setValue("informacoes.data_abertura", dataFormatada);
    }

    setValue(
      "informacoes.capital_social",
      cnpjModalData.company.equity?.toString() || ""
    );

    // Mapear porte empresarial para valores aceitos
    const porteMap: Record<string, string> = {
      ME: "Microempresa",
      EPP: "Empresa de pequeno porte",
      DEMAIS: "Empresa Limitada",
      Demais: "Empresa Limitada",
      "Micro Empresa": "Microempresa",
      Microempresa: "Microempresa",
      MEI: "Microempreendedor Individual",
      EI: "Empresário Individual",
    };

    const porteOriginal = cnpjModalData.company.size.text;
    const porteMapeado = porteMap[porteOriginal] || "Empresa Limitada";

    setValue("informacoes.porte_empresarial", porteMapeado);
    setValue(
      "informacoes.atividade_principal",
      `${cnpjModalData.mainActivity.id} - ${cnpjModalData.mainActivity.text}`
    );

    // Endereço
    setValue("endereco.cep", cnpjModalData.address.zip);
    setValue("endereco.logradouro", cnpjModalData.address.street);
    setValue("endereco.bairro", cnpjModalData.address.district);
    setValue("endereco.cidade", cnpjModalData.address.city);
    setValue("endereco.estado", cnpjModalData.address.state);

    // Contatos
    if (cnpjModalData.phones && cnpjModalData.phones.length > 0) {
      setValue(
        "contatos.telefone",
        `(${cnpjModalData.phones[0].area}) ${cnpjModalData.phones[0].number}`
      );

      if (cnpjModalData.phones.length > 1) {
        setValue(
          "contatos.celular",
          `(${cnpjModalData.phones[1].area}) ${cnpjModalData.phones[1].number}`
        );
      }
    }

    if (cnpjModalData.emails && cnpjModalData.emails.length > 0) {
      setValue("contatos.email", cnpjModalData.emails[0].address);
    }

    setIsCnpjModalOpen(false);
    toast.success("Dados do CNPJ preenchidos com sucesso!");
  };

  // Funções de navegação
  const handleNext = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const sectionsValidationsByStep: any = [
      "informacoes",
      "contatos",
      "endereco",
      "estrutura",
      "servicos",
    ];

    const isStepValid = await formMethods.trigger(
      sectionsValidationsByStep[currentStep]
    );

    let shouldBlockNextStep = false;

    // Validação de CNPJ no passo 0
    if (currentStep === 0) {
      const cnpj = formatCNPJ(formMethods.getValues("informacoes.cnpj"));

      try {
        const response = await existCredenciadoByCnpj(cnpj);
        const { exist } = response.data;

        if (exist) {
          formMethods.setError("informacoes.cnpj", {
            type: "manual",
            message: "Esse CNPJ já está sendo utilizado.",
          });
          shouldBlockNextStep = true;
        }
      } catch (error) {
        console.error("Erro ao verificar CNPJ:", error);
        return;
      }
    }

    if (currentStep === 1) {
      const email = formMethods.getValues("contatos.email");

      try {
        const response = await existCredenciadoByEmail(email);
        const { exist } = response.data;

        if (exist) {
          formMethods.setError("contatos.email", {
            type: "manual",
            message: "Este e-mail já está sendo utilizado.",
          });
          shouldBlockNextStep = true;
        }
      } catch (error) {
        console.error("Erro ao verificar e-mail:", error);
        return;
      }
    }

    if (
      isStepValid &&
      currentStep < formSteps.length - 1 &&
      !shouldBlockNextStep
    ) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = (e: React.MouseEvent) => {
    e.preventDefault();
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Função para tratar serviços selecionados
  const handleServicoToggle = (servicoId: string) => {
    setSelectedServicos((prevServicos) => {
      if (prevServicos.includes(servicoId)) {
        return prevServicos.filter((id) => id !== servicoId);
      } else {
        return [...prevServicos, servicoId];
      }
    });
  };

  // Função para tratar tipos de veículo selecionados
  const handleTipoVeiculoToggle = (tipoId: string) => {
    setSelectedTiposVeiculo((prevTipos) => {
      if (prevTipos.includes(tipoId)) {
        return prevTipos.filter((id) => id !== tipoId);
      } else {
        return [...prevTipos, tipoId];
      }
    });
  };

  // Função para tratar contratos selecionados
  const handleContratoToggle = (contratoId: string) => {
    setSelectedContratos((prevContratos) => {
      if (prevContratos.includes(contratoId)) {
        return prevContratos.filter((id) => id !== contratoId);
      } else {
        return [...prevContratos, contratoId];
      }
    });
  };

  // Upload de arquivos
  const uploadFile = async (file: File): Promise<string | null> => {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Erro ao fazer upload do arquivo");
      }

      const data = await response.json();
      return data.file.path;
    } catch (error) {
      console.error("Erro no upload:", error);
      toast.error("Falha ao enviar arquivo");
      return null;
    }
  };

  const uploadMultipleFiles = async (files: File[]): Promise<string[]> => {
    try {
      const formData = new FormData();
      files.forEach((file) => {
        formData.append("files", file);
      });

      const response = await fetch("/api/uploads", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Erro ao fazer upload dos arquivos");
      }

      const data = await response.json();
      return data.files.map((file: { path: string }) => file.path);
    } catch (error) {
      console.error("Erro no upload múltiplo:", error);
      toast.error("Falha ao enviar arquivos");
      return [];
    }
  };

  // Enviar formulário
  const onSubmit = async (formData: any) => {
    try {
      setIsSubmitting(true);

      // Upload do logotipo
      let logoUrl = null;
      if (logoFile) {
        logoUrl = await uploadFile(logoFile);
      }

      // Upload das imagens da estrutura
      let estruturaUrls: string[] = [];
      if (estruturaFiles.length > 0) {
        estruturaUrls = await uploadMultipleFiles(estruturaFiles);
      }

      // Upload da documentação
      let documentacaoUrls: string[] = [];
      if (documentacaoFiles.length > 0) {
        documentacaoUrls = await uploadMultipleFiles(documentacaoFiles);
      }

      // Preparar dados para submissão
      const submissionData: CriarCredenciadoData = {
        informacoes: {
          cnpj: formData.informacoes.cnpj,
          razao_social: formData.informacoes.razao_social,
          inscri_estadual: formData.informacoes.inscri_estadual || "Isento",
          inscri_municipal: formData.informacoes.inscri_municipal || "Isento",
          nome_fantasia: formData.informacoes.nome_fantasia || undefined,
          atividade_principal: formData.informacoes.atividade_principal,
          horario_funcionamento:
            formData.informacoes.horario_funcionamento || undefined,
          data_abertura: formData.informacoes.data_abertura,
          porte_empresarial: formData.informacoes.porte_empresarial,
          capital_social: formData.informacoes.capital_social || null,
          patrimonio_liquido: formData.informacoes.patrimonio_liquido || null,
          observacoes_gerais:
            formData.informacoes.observacoes_gerais || undefined,
          logotipo_empresa: logoUrl || undefined,
        },
        estrutura: {
          capacidade_atendimento: Number(
            formData.estrutura.capacidade_atendimento
          ),
          box_veiculos_leves: Number(formData.estrutura.box_veiculos_leves),
          box_veiculos_pesados: Number(formData.estrutura.box_veiculos_pesados),
          elevadores_veiculos: Number(formData.estrutura.elevadores_veiculos),
          estufas_pintura: Number(formData.estrutura.estufas_pintura),
          images: estruturaUrls.length > 0 ? estruturaUrls : undefined,
          tipos_veiculo:
            selectedTiposVeiculo.length > 0 ? selectedTiposVeiculo : undefined,
          orcamentista: formData.estrutura.orcamentista || undefined,
          atende_placa_verde: formData.estrutura.atende_placa_verde || false,
          concessionaria: formData.estrutura.concessionaria || false,
          orcamentacao: formData.estrutura.orcamentacao || false,
        },
        endereco: {
          cep: formData.endereco.cep,
          logradouro: formData.endereco.logradouro,
          bairro: formData.endereco.bairro,
          estado: formData.endereco.estado,
          cidade: formData.endereco.cidade,
          latitude: formData.endereco.latitude || undefined,
          longitude: formData.endereco.longitude || undefined,
        },
        contatos: {
          telefone: formData.contatos.telefone || "",
          celular: formData.contatos.celular || "",
          email: formData.contatos.email,
          nome_gerente: formData.contatos.nome_gerente || undefined,
          telefone_gerente: formData.contatos.telefone_gerente || undefined,
          nome_proprietario: formData.contatos.nome_proprietario || undefined,
          telefone_proprietario:
            formData.contatos.telefone_proprietario || undefined,
        },
        documentacao: documentacaoUrls,
        servicos: selectedServicos.length > 0 ? selectedServicos : undefined,
        polo_regionalId: formData.configuracoes.polo_regionalId,
        prazoId: formData.configuracoes.prazoId || undefined,
        url_logo: logoUrl || undefined,
        url_estrutura: estruturaUrls.length > 0 ? estruturaUrls[0] : undefined,
        optante: formData.configuracoes.optante || false,
        responsavelPrazo: formData.configuracoes.responsavelPrazo || undefined,
        contratosIds:
          selectedContratos.length > 0 ? selectedContratos : undefined,
      };

      // Enviar para API
      const response = await criarCredenciado(submissionData);

      if (!response.success) {
        throw new Error(response.error || "Erro ao salvar o credenciado");
      }

      setCredenciados((prev: any[]) => [...prev, response.data]);
      toast.success("Credenciado cadastrado com sucesso!");
      window.location.href =
        "/dashboard/rede-credenciada/consultar-credenciados";
    } catch (error) {
      console.error("Erro ao salvar o credenciado:", error);
      toast.error(
        `Erro ao salvar o credenciado: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">
            Cadastro de Novo Credenciado
          </CardTitle>
          <CardDescription>
            Preencha os dados do novo credenciado.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <FormProvider {...formMethods}>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <Tabs
                defaultValue={formSteps[0]}
                value={formSteps[currentStep]}
                onValueChange={(value) => {
                  const index = formSteps.indexOf(value);
                  if (index >= 0) setCurrentStep(index);
                }}
                className="w-full"
              >
                <TabsList className="flex flex-wrap gap-2 p-2 h-34 items-center justify-start">
                  {formSteps.map((step, index) => {
                    const stepKey = normalizedFormStepsDict[
                      index
                    ] as keyof typeof errors;
                    const errorsByStep = errors[stepKey] ?? {};
                    const errorsByStepLength = Object.keys(errorsByStep).length;
                    return (
                      <TabsTrigger
                        key={step}
                        className="flex-1 sm:flex-none px-4 py-2 text-sm  relative"
                        value={step}
                      >
                        {step}
                        {errorsByStepLength > 0 && (
                          <div className="absolute top-0 right-0 -mt-1 -mr-2 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                            {errorsByStepLength}
                          </div>
                        )}
                      </TabsTrigger>
                    );
                  })}
                </TabsList>

                {/* Tab 1: Informações */}
                <TabsContent value={formSteps[0]} className="space-y-4 pt-4">
                  <div className="flex gap-2 items-end mb-4">
                    <div className="flex-1">
                      <Label htmlFor="cnpj">CNPJ *</Label>
                      <CNPJInput
                        value={cnpjValue}
                        onChange={(value) =>
                          setValue("informacoes.cnpj", value)
                        }
                        placeholder="12.345.678/0001-99"
                        className="w-full"
                      />
                      {errors.informacoes?.cnpj && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.informacoes.cnpj.message}
                        </p>
                      )}
                    </div>
                    <Button
                      type="button"
                      onClick={handleCnpjLookup}
                      disabled={isLoadingCNPJ}
                      variant="outline"
                    >
                      {isLoadingCNPJ ? "Consultando..." : "Consultar"}
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="razao_social">Razão Social *</Label>
                      <Input
                        {...register("informacoes.razao_social")}
                        placeholder="Nome Empresarial"
                      />
                      {errors.informacoes?.razao_social && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.informacoes.razao_social.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="nome_fantasia">Nome Fantasia</Label>
                      <Input
                        {...register("informacoes.nome_fantasia")}
                        placeholder="Nome de Mercado"
                      />
                    </div>

                    <div>
                      <Label htmlFor="inscri_estadual">
                        Inscrição Estadual
                      </Label>
                      <Input
                        {...register("informacoes.inscri_estadual")}
                        placeholder="Inscrição Estadual"
                      />
                    </div>

                    <div>
                      <Label htmlFor="inscri_municipal">
                        Inscrição Municipal
                      </Label>
                      <Input
                        {...register("informacoes.inscri_municipal")}
                        placeholder="Inscrição Municipal"
                      />
                    </div>

                    <div>
                      <Label htmlFor="data_abertura">Data de Abertura *</Label>
                      <DateInput
                        value={watch("informacoes.data_abertura")}
                        onChange={(value) =>
                          setValue("informacoes.data_abertura", value)
                        }
                      />
                      {errors.informacoes?.data_abertura && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.informacoes.data_abertura.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="porte_empresarial">
                        Porte Empresarial *
                      </Label>
                      <Select
                        onValueChange={(value) =>
                          setValue("informacoes.porte_empresarial", value)
                        }
                        value={watch("informacoes.porte_empresarial")}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o porte" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Microempreendedor Individual">
                            Microempreendedor Individual
                          </SelectItem>
                          <SelectItem value="Microempresa">
                            Microempresa
                          </SelectItem>
                          <SelectItem value="Empresário Individual">
                            Empresário Individual
                          </SelectItem>
                          <SelectItem value="Empresa de pequeno porte">
                            Empresa de pequeno porte
                          </SelectItem>
                          <SelectItem value="Empresa Individual de Responsabilidade Limitada">
                            Empresa Individual de Responsabilidade Limitada
                          </SelectItem>
                          <SelectItem value="Sociedade Anônima">
                            Sociedade Anônima
                          </SelectItem>
                          <SelectItem value="Empresa Limitada">
                            Empresa Limitada
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      {errors.informacoes?.porte_empresarial && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.informacoes.porte_empresarial.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="atividade_principal">
                        Atividade Principal *
                      </Label>
                      <Input
                        {...register("informacoes.atividade_principal")}
                        placeholder="CNAE - Atividade"
                      />
                      {errors.informacoes?.atividade_principal && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.informacoes.atividade_principal.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="horario_funcionamento">
                        Horário de Funcionamento
                      </Label>
                      <Input
                        {...register("informacoes.horario_funcionamento")}
                        placeholder="Ex: 08:00 às 18:00"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <Label htmlFor="observacoes_gerais">
                        Observações Gerais
                      </Label>
                      <Textarea
                        {...register("informacoes.observacoes_gerais")}
                        placeholder="Observações sobre o credenciado..."
                        rows={4}
                      />
                    </div>
                  </div>
                </TabsContent>

                {/* Tab 2: Contatos */}

                <TabsContent value={formSteps[1]} className="space-y-4 pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="telefone">Telefone</Label>
                      <TelefoneInput
                        value={watch("contatos.telefone")}
                        onChange={(value) =>
                          setValue("contatos.telefone", value)
                        }
                        placeholder="(00) 0000-0000"
                      />
                    </div>

                    <div>
                      <Label htmlFor="celular">Celular *</Label>
                      <CelularInput
                        value={watch("contatos.celular")}
                        onChange={(value) =>
                          setValue("contatos.celular", value)
                        }
                        placeholder="(00) 00000-0000"
                      />
                      {errors.contatos?.celular && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.contatos.celular.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="email">Email *</Label>
                      <Input
                        {...register("contatos.email")}
                        placeholder="<EMAIL>"
                        type="email"
                      />
                      {errors.contatos?.email && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.contatos.email.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="nome_gerente">Nome do Gerente</Label>
                      <Input
                        {...register("contatos.nome_gerente")}
                        placeholder="Nome completo do gerente"
                      />
                    </div>

                    <div>
                      <Label htmlFor="telefone_gerente">
                        Telefone do Gerente
                      </Label>
                      <TelefoneInput
                        value={watch("contatos.telefone_gerente")}
                        onChange={(value) =>
                          setValue("contatos.telefone_gerente", value)
                        }
                        placeholder="(00) 0000-0000"
                      />
                    </div>

                    <div>
                      <Label htmlFor="nome_proprietario">
                        Nome do Proprietário
                      </Label>
                      <Input
                        {...register("contatos.nome_proprietario")}
                        placeholder="Nome completo do proprietário"
                      />
                    </div>

                    <div>
                      <Label htmlFor="telefone_proprietario">
                        Telefone do Proprietário
                      </Label>
                      <TelefoneInput
                        value={watch("contatos.telefone_proprietario")}
                        onChange={(value) =>
                          setValue("contatos.telefone_proprietario", value)
                        }
                        placeholder="(00) 0000-0000"
                      />
                    </div>
                  </div>
                </TabsContent>

                {/* Tab 3: Endereço */}
                <TabsContent value={formSteps[2]} className="space-y-4 pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex gap-2">
                      <div className="flex-1">
                        <Label htmlFor="cep">CEP *</Label>
                        <CepInput
                          value={watch("endereco.cep")}
                          onChange={(value) => {
                            if (typeof value === "string") {
                              setValue("endereco.cep", value);
                            } else {
                              setValue("endereco.cep", value.target.value);
                            }
                          }}
                          placeholder="00000-000"
                          addressMapping={{
                            street: "endereco.logradouro",
                            neighborhood: "endereco.bairro",
                            city: "endereco.cidade",
                            state: "endereco.estado",
                          }}
                          onCepFound={(data) => {
                            toast.success("CEP encontrado", {
                              description:
                                "Endereço preenchido automaticamente",
                            });
                          }}
                        />
                        {errors.endereco?.cep && (
                          <p className="text-red-500 text-sm mt-1">
                            {errors.endereco.cep.message}
                          </p>
                        )}
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="logradouro">Logradouro *</Label>
                      <Input
                        {...register("endereco.logradouro")}
                        placeholder="Rua, Avenida, etc."
                      />
                      {errors.endereco?.logradouro && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.endereco.logradouro.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="bairro">Bairro *</Label>
                      <Input
                        {...register("endereco.bairro")}
                        placeholder="Bairro"
                      />
                      {errors.endereco?.bairro && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.endereco.bairro.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="estado">Estado *</Label>
                      <Input
                        {...register("endereco.estado")}
                        placeholder="UF"
                      />
                      {errors.endereco?.estado && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.endereco.estado.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="cidade">Cidade *</Label>
                      <Input
                        {...register("endereco.cidade")}
                        placeholder="Cidade"
                      />
                      {errors.endereco?.cidade && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.endereco.cidade.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="latitude">Latitude</Label>
                      <Input
                        {...register("endereco.latitude")}
                        placeholder="Ex: -23.5505"
                      />
                    </div>

                    <div>
                      <Label htmlFor="longitude">Longitude</Label>
                      <Input
                        {...register("endereco.longitude")}
                        placeholder="Ex: -46.6333"
                      />
                    </div>
                  </div>
                </TabsContent>

                {/* Tab 4: Estrutura */}
                <TabsContent value={formSteps[3]} className="space-y-4 pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="capacidade_atendimento">
                        Capacidade de Atendimento *
                      </Label>
                      <Input
                        type="number"
                        {...register("estrutura.capacidade_atendimento")}
                        placeholder="0"
                      />
                      {errors.estrutura?.capacidade_atendimento && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.estrutura.capacidade_atendimento.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="box_veiculos_leves">
                        Box para Veículos Leves *
                      </Label>
                      <Input
                        type="number"
                        {...register("estrutura.box_veiculos_leves")}
                        placeholder="0"
                      />
                      {errors.estrutura?.box_veiculos_leves && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.estrutura.box_veiculos_leves.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="box_veiculos_pesados">
                        Box para Veículos Pesados *
                      </Label>
                      <Input
                        type="number"
                        {...register("estrutura.box_veiculos_pesados")}
                        placeholder="0"
                      />
                      {errors.estrutura?.box_veiculos_pesados && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.estrutura.box_veiculos_pesados.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="elevadores_veiculos">
                        Elevadores para Veículos *
                      </Label>
                      <Input
                        type="number"
                        {...register("estrutura.elevadores_veiculos")}
                        placeholder="0"
                      />
                      {errors.estrutura?.elevadores_veiculos && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.estrutura.elevadores_veiculos.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="estufas_pintura">
                        Estufas de Pintura *
                      </Label>
                      <Input
                        type="number"
                        {...register("estrutura.estufas_pintura")}
                        placeholder="0"
                      />
                      {errors.estrutura?.estufas_pintura && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.estrutura.estufas_pintura.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="orcamentista">Orçamentista</Label>
                      <Input
                        {...register("estrutura.orcamentista")}
                        placeholder="Nome do orçamentista"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <Label className="mb-3 block text-lg font-bold">
                        Tipos de Veículos Atendidos
                      </Label>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                        {tiposDeVeiculo &&
                          tiposDeVeiculo.map((tipo) => (
                            <div
                              key={tipo.id}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={`tipo-${tipo.id}`}
                                checked={selectedTiposVeiculo.includes(tipo.id)}
                                onCheckedChange={() =>
                                  handleTipoVeiculoToggle(tipo.id)
                                }
                              />
                              <Label
                                htmlFor={`tipo-${tipo.id}`}
                                className="text-sm"
                              >
                                {tipo.descricao}
                              </Label>
                            </div>
                          ))}
                      </div>
                    </div>

                    <div className="md:col-span-2">
                      <div className="flex flex-col space-y-2">
                        <p className="mb-3 block text-lg font-bold">
                          Outros Parâmetros
                        </p>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="atende_placa_verde"
                            {...register("estrutura.atende_placa_verde")}
                          />
                          <Label htmlFor="atende_placa_verde">
                            Atende Placa Verde
                          </Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="concessionaria"
                            {...register("estrutura.concessionaria")}
                          />
                          <Label htmlFor="concessionaria">
                            É Concessionária
                          </Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="orcamentacao"
                            {...register("estrutura.orcamentacao")}
                          />
                          <Label htmlFor="orcamentacao">
                            Possui Orçamentação
                          </Label>
                        </div>
                      </div>
                    </div>

                    <div className="md:col-span-2">
                      <Label htmlFor="imagens">Imagens da Estrutura</Label>
                      <Input
                        type="file"
                        multiple
                        accept="image/*"
                        onChange={(e) => {
                          if (e.target.files) {
                            setEstruturaFiles(Array.from(e.target.files));
                          }
                        }}
                        className="mt-1"
                      />
                      {estruturaFiles.length > 0 && (
                        <div className="mt-2">
                          <p className="text-sm font-medium">
                            Imagens selecionadas ({estruturaFiles.length}):
                          </p>
                          <ul className="text-sm text-muted-foreground ml-2">
                            {estruturaFiles.map((file, index) => (
                              <li key={index}>{file.name}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                </TabsContent>

                {/* Tab 5: Serviços */}
                <TabsContent value={formSteps[4]} className="space-y-4 pt-4">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Serviços Oferecidos</h3>
                    <Input
                      type="text"
                      placeholder="Filtrar serviços..."
                      value={filtroServico}
                      onChange={(e) => setFiltroServico(e.target.value)}
                      className="border border-gray-300 rounded px-2 py-1 w-full"
                    />
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                      {servicosCredenciados &&
                        servicosCredenciados
                          .filter((servico) =>
                            servico.descricao
                              .toLowerCase()
                              .normalize("NFD")
                              .replace(/[\u0300-\u036f]/g, "")
                              .includes(
                                filtroServico
                                  .toLowerCase()
                                  .normalize("NFD")
                                  .replace(/[\u0300-\u036f]/g, "")
                              )
                          )
                          .sort((a, b) =>
                            a.descricao.localeCompare(b.descricao)
                          )
                          .map((servico) => (
                            <div
                              key={servico.id}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={`servico-${servico.id}`}
                                checked={selectedServicos.includes(servico.id)}
                                onCheckedChange={() =>
                                  handleServicoToggle(servico.id)
                                }
                              />
                              <Label
                                htmlFor={`servico-${servico.id}`}
                                className="text-sm"
                              >
                                {servico.descricao}
                              </Label>
                            </div>
                          ))}
                    </div>

                    <div>
                      <Label htmlFor="documentacao">Documentação *</Label>
                      <Input
                        type="file"
                        multiple
                        onChange={(e) => {
                          if (e.target.files) {
                            setDocumentacaoFiles(Array.from(e.target.files));
                          }
                        }}
                        className="mt-1"
                      />
                      {documentacaoFiles.length > 0 && (
                        <div className="mt-2">
                          <p className="text-sm font-medium">
                            Arquivos selecionados ({documentacaoFiles.length}):
                          </p>
                          <ul className="text-sm text-muted-foreground ml-2">
                            {documentacaoFiles.map((file, index) => (
                              <li key={index}>{file.name}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                </TabsContent>

                {/* Tab 6: Configurações */}
                <TabsContent value={formSteps[5]} className="space-y-4 pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="polo_regionalId">Polo Regional *</Label>
                      <Select
                        onValueChange={(value) =>
                          setValue("configuracoes.polo_regionalId", value)
                        }
                        value={watch("configuracoes.polo_regionalId")}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione um polo regional" />
                        </SelectTrigger>
                        <SelectContent>
                          {polosRegionais &&
                            polosRegionais.map((polo) => (
                              <SelectItem key={polo.id} value={polo.id}>
                                {polo.descricao}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                      {errors.configuracoes?.polo_regionalId && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.configuracoes.polo_regionalId.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="prazoId">Prazo de Pagamento</Label>
                      <Select
                        onValueChange={(value) =>
                          setValue("configuracoes.prazoId", value)
                        }
                        value={watch("configuracoes.prazoId")}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione um prazo" />
                        </SelectTrigger>
                        <SelectContent>
                          {prazosDePagamento &&
                            prazosDePagamento.map((prazo) => (
                              <SelectItem key={prazo.id} value={prazo.id}>
                                {prazo.descricao}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="responsavelPrazo">
                        Responsável pelo Prazo
                      </Label>
                      <Input
                        {...register("configuracoes.responsavelPrazo")}
                        placeholder="Nome do responsável"
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="optante"
                        {...register("configuracoes.optante")}
                      />
                      <Label htmlFor="optante">Optante Simples</Label>
                    </div>

                    {/* Novo campo para vincular contratos */}
                    <div className="md:col-span-2 mt-6">
                      <Label className="mb-3 block text-lg font-bold">
                        Vincular Contratos
                      </Label>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 border p-4 rounded-md max-h-60 overflow-y-auto">
                        {contratos && contratos.length > 0 ? (
                          contratos.map((contrato) => (
                            <div
                              key={contrato.id}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={`contrato-${contrato.id}`}
                                checked={selectedContratos.includes(
                                  contrato.id
                                )}
                                onCheckedChange={() =>
                                  handleContratoToggle(contrato.id)
                                }
                              />
                              <Label
                                htmlFor={`contrato-${contrato.id}`}
                                className="text-sm"
                              >
                                {contrato.nome_contrato}
                              </Label>
                            </div>
                          ))
                        ) : (
                          <p className="text-sm text-muted-foreground col-span-full">
                            Nenhum contrato disponível para vinculação.
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>

              {/* Botões de navegação */}
              <div className="w-full flex items-center justify-between">
                <Button
                  type="button"
                  onClick={handlePrevious}
                  className="rounded"
                  variant={"secondary"}
                  disabled={currentStep === 0}
                >
                  Anterior
                </Button>

                {currentStep < formSteps.length - 1 ? (
                  <Button
                    type="button"
                    onClick={handleNext}
                    className="rounded"
                    disabled={
                      documentacaoFiles.length === 0 && currentStep === 4
                    }
                  >
                    Próximo
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    className="rounded"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <span className="animate-spin mr-2">⟳</span>
                        Cadastrando...
                      </>
                    ) : (
                      "Cadastrar"
                    )}
                  </Button>
                )}
              </div>
            </form>
          </FormProvider>
        </CardContent>
      </Card>
      {cnpjModalData && (
        <CnpjDetailsModal
          isOpen={isCnpjModalOpen}
          onClose={() => setIsCnpjModalOpen(false)}
          onConfirm={handleCnpjConfirm}
          data={cnpjModalData}
        />
      )}
    </>
  );
}
