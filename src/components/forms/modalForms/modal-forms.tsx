"use client";

import React, { ReactNode } from "react";
import { Plus } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import { zodResolver } from "@hookform/resolvers/zod";
import { DefaultValues, useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { AnyZodObject, z, ZodSchema } from "zod";
import { Button } from "@/components/ui/button";

type SheetFormProps<T extends ZodSchema<any>> = {
  title: string;
  schema: T;
  children: ReactNode;
  onSubmit: (values: z.infer<T>) => void;
  triggerLabel: ReactNode | string;
  defaultValues?: DefaultValues<z.infer<T>>;
  onClick?: () => void;
  description: string;
  modalContentClassName?: string;
  modalFormClassName?: string;
};

export function ModalForm<T extends ZodSchema<any>>({
  title,
  schema,
  children,
  onSubmit,
  triggerLabel,
  defaultValues,
  onClick,
  description,
  modalContentClassName,
  modalFormClassName,
}: SheetFormProps<T>) {
  const form = useForm<z.infer<T>>({
    resolver: zodResolver(schema),
    defaultValues,
  });

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button onClick={onClick} variant="outline">
          {triggerLabel || (
            <>
              <Plus className="mr-2 h-4 w-4" />
              Adicionar Item
            </>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className={`${modalContentClassName}`}>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>
        <Form {...form}>
          <form className={`space-y-4 py-4 ${modalFormClassName}`}>
            {children}
            <div className="flex justify-end space-x-2 pt-4 col-span-2">
              <DialogClose asChild>
                <Button variant="outline" type="button">
                  Cancelar
                </Button>
              </DialogClose>
              <Button
                className="w-full"
                type="button"
                onClick={async () => {
                  const values = form.getValues();
                  await onSubmit(values);
                }}>
                Criar
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
