"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FieldCustomizer, ReportField } from './FieldCustomizer';
import { useCentroCustoCRUD } from '@/hooks/useCentroCustoActions';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { Download, FileText, Filter, Settings, MapPin } from 'lucide-react';

interface CentroCustoLocationFilters {
  status?: 'all' | 'active' | 'inactive';
  hasLocation?: 'all' | 'yes' | 'no';
  estado?: string;
  cidade?: string;
  searchTerm?: string;
}

const availableFields: ReportField[] = [
  // Campos Básicos
  { id: 'descricao', label: 'Descrição', type: 'string', category: 'basic', required: true },
  { id: 'cnpj', label: 'CNPJ', type: 'string', category: 'basic', required: true },
  { id: 'razao_social', label: 'Razão Social', type: 'string', category: 'basic' },
  { id: 'nome_responsavel', label: 'Responsável', type: 'string', category: 'basic' },
  { id: 'contato', label: 'Contato', type: 'string', category: 'basic' },
  { id: 'email', label: 'E-mail', type: 'string', category: 'basic' },
  { id: 'ativo', label: 'Status', type: 'boolean', category: 'basic' },
  
  // Localização
  { id: 'cep', label: 'CEP', type: 'string', category: 'location' },
  { id: 'logradouro', label: 'Logradouro', type: 'string', category: 'location' },
  { id: 'numero', label: 'Número', type: 'string', category: 'location' },
  { id: 'complemento', label: 'Complemento', type: 'string', category: 'location' },
  { id: 'bairro', label: 'Bairro', type: 'string', category: 'location' },
  { id: 'cidade', label: 'Cidade', type: 'string', category: 'location' },
  { id: 'estado', label: 'Estado', type: 'string', category: 'location' },
  { id: 'pais', label: 'País', type: 'string', category: 'location' },
  { id: 'localidade_completa', label: 'Endereço Completo', type: 'string', category: 'location' },
  { id: 'coordenadas_latitude', label: 'Latitude', type: 'number', category: 'location' },
  { id: 'coordenadas_longitude', label: 'Longitude', type: 'number', category: 'location' },
  
  // Financeiro
  { id: 'dotacao_orcamentista', label: 'Dotação Orçamentária', type: 'string', category: 'financial' },
  { id: 'valor_dotacao', label: 'Valor da Dotação', type: 'currency', category: 'financial' },
  
  // Operacional
  { id: 'centro_custo_pai.descricao', label: 'Centro de Custo Pai', type: 'string', category: 'operational' },
  { id: 'centro_custos_filhos.length', label: 'Qtd. Centros Filhos', type: 'number', category: 'operational' },
  { id: 'lotacao_veiculos.length', label: 'Qtd. Veículos', type: 'number', category: 'operational' },
  { id: 'empenhos.length', label: 'Qtd. Empenhos', type: 'number', category: 'operational' },
  
  // Datas
  { id: 'createdAt', label: 'Data de Criação', type: 'date', category: 'basic' },
  { id: 'updatedAt', label: 'Última Atualização', type: 'date', category: 'basic' },
];

export function CentroCustoLocationReport() {
  const { centrosCusto, loading } = useCentroCustoCRUD();

  const [filters, setFilters] = useState<CentroCustoLocationFilters>({});
  const [selectedFields, setSelectedFields] = useState<string[]>([
    'descricao', 'cnpj', 'razao_social', 'cidade', 'estado', 'ativo'
  ]);
  const [fieldOrder, setFieldOrder] = useState<string[]>([
    'descricao', 'cnpj', 'razao_social', 'cidade', 'estado', 'ativo'
  ]);
  const [showFieldCustomizer, setShowFieldCustomizer] = useState(false);
  const [filteredData, setFilteredData] = useState<any[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [uniqueEstados, setUniqueEstados] = useState<string[]>([]);
  const [uniqueCidades, setUniqueCidades] = useState<string[]>([]);

  useEffect(() => {
    if (!centrosCusto) return;

    // Extrair estados e cidades únicos
    const estados = [...new Set(centrosCusto.map(c => c.endereco?.[0]?.estado).filter(Boolean))] as string[];
    const cidades = [...new Set(centrosCusto.map(c => c.endereco?.[0]?.cidade).filter(Boolean))] as string[];
    setUniqueEstados(estados);
    setUniqueCidades(cidades);

    // Aplicar filtros
    const filtered = centrosCusto.filter((centro) => {
      // Filtro por status
      if (filters.status === 'active' && !centro.ativo) return false;
      if (filters.status === 'inactive' && centro.ativo) return false;
      
      // Filtro por localização
      const endereco = centro.endereco?.[0];
      const hasLocation = endereco?.cidade || endereco?.estado || endereco?.cep || endereco?.logradouro;
      if (filters.hasLocation === 'yes' && !hasLocation) return false;
      if (filters.hasLocation === 'no' && hasLocation) return false;

      // Filtro por estado
      if (filters.estado && endereco?.estado !== filters.estado) return false;

      // Filtro por cidade
      if (filters.cidade && endereco?.cidade !== filters.cidade) return false;
      
      // Filtro por termo de busca
      if (filters.searchTerm) {
        const searchTerm = filters.searchTerm.toLowerCase();
        const endereco = centro.endereco?.[0];
        const searchableFields = [
          centro.descricao,
          centro.razao_social,
          centro.cnpj,
          centro.nome_responsavel,
          endereco?.cidade,
          endereco?.estado,
          endereco?.logradouro,
          endereco?.bairro
        ];
        
        const matches = searchableFields.some(field => 
          field?.toLowerCase().includes(searchTerm)
        );
        
        if (!matches) return false;
      }
      
      return true;
    });

    setFilteredData(filtered);
  }, [centrosCusto, filters]);

  const handleFieldToggle = (fieldId: string) => {
    setSelectedFields(prev => {
      const field = availableFields.find(f => f.id === fieldId);
      if (field?.required) return prev;
      
      if (prev.includes(fieldId)) {
        setFieldOrder(order => order.filter(id => id !== fieldId));
        return prev.filter(id => id !== fieldId);
      } else {
        setFieldOrder(order => [...order, fieldId]);
        return [...prev, fieldId];
      }
    });
  };

  const handleSelectAll = () => {
    const allFieldIds = availableFields.map(f => f.id);
    setSelectedFields(allFieldIds);
    setFieldOrder(allFieldIds);
  };

  const handleDeselectAll = () => {
    const requiredFields = availableFields.filter(f => f.required).map(f => f.id);
    setSelectedFields(requiredFields);
    setFieldOrder(requiredFields);
  };

  const getFieldValue = (centro: any, fieldPath: string): any => {
    try {
      if (fieldPath.includes('.length')) {
        const arrayPath = fieldPath.replace('.length', '');
        const arrayValue = arrayPath.split('.').reduce((obj: any, key: string) => obj?.[key], centro);
        return Array.isArray(arrayValue) ? arrayValue.length : 0;
      }
      
      return fieldPath.split('.').reduce((obj: any, key: string) => obj?.[key], centro);
    } catch {
      return '';
    }
  };

  const formatFieldValue = (value: any, field: ReportField): string => {
    if (value === null || value === undefined) return '';
    
    switch (field.type) {
      case 'date':
        return format(new Date(value), 'dd/MM/yyyy HH:mm');
      case 'currency':
        return new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL'
        }).format(Number(value) || 0);
      case 'boolean':
        return value ? 'Ativo' : 'Inativo';
      case 'number':
        return Number(value).toLocaleString('pt-BR');
      default:
        return String(value);
    }
  };

  const generatePDF = () => {
    if (filteredData.length === 0) {
      toast.error('Nenhum dado encontrado para gerar o relatório');
      return;
    }

    setIsGenerating(true);
    try {
      const doc = new jsPDF('l', 'mm', 'a4');
      
      // Cabeçalho
      doc.setFontSize(16);
      doc.text('Relatório de Centros de Custo com Localização', 20, 20);
      doc.setFontSize(10);
      doc.text(`Gerado em: ${format(new Date(), 'dd/MM/yyyy HH:mm')}`, 20, 30);
      doc.text(`Total de registros: ${filteredData.length}`, 20, 35);

      // Estatísticas
      const comLocalizacao = filteredData.filter(c => c.endereco?.[0]?.cidade || c.endereco?.[0]?.estado).length;
      const semLocalizacao = filteredData.length - comLocalizacao;
      doc.text(`Com localização: ${comLocalizacao} | Sem localização: ${semLocalizacao}`, 20, 40);

      // Preparar dados da tabela
      const selectedFieldsInOrder = fieldOrder.filter(fieldId => selectedFields.includes(fieldId));
      const headers = selectedFieldsInOrder.map(fieldId => {
        const field = availableFields.find(f => f.id === fieldId);
        return field?.label || fieldId;
      });

      const tableData = filteredData.map(centro => {
        return selectedFieldsInOrder.map(fieldId => {
          const field = availableFields.find(f => f.id === fieldId);
          const value = getFieldValue(centro, fieldId);
          return field ? formatFieldValue(value, field) : String(value || '');
        });
      });

      // Adicionar tabela
      (doc as any).autoTable({
        head: [headers],
        body: tableData,
        startY: 50,
        styles: { fontSize: 8 },
        headStyles: { fillColor: [41, 128, 185], textColor: 255 },
        columnStyles: headers.reduce((acc, _, index) => {
          acc[index] = { cellWidth: 'auto' };
          return acc;
        }, {} as any),
      });

      doc.save(`relatorio-centros-custo-localizacao-${format(new Date(), 'dd-MM-yyyy-HH-mm-ss')}.pdf`);
      toast.success('Relatório PDF gerado com sucesso!');
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      toast.error('Erro ao gerar relatório PDF');
    } finally {
      setIsGenerating(false);
    }
  };

  const generateExcel = () => {
    if (filteredData.length === 0) {
      toast.error('Nenhum dado encontrado para gerar o relatório');
      return;
    }

    setIsGenerating(true);
    try {
      const wb = XLSX.utils.book_new();
      
      // Preparar dados principais
      const selectedFieldsInOrder = fieldOrder.filter(fieldId => selectedFields.includes(fieldId));
      const excelData = filteredData.map(centro => {
        const row: any = {};
        selectedFieldsInOrder.forEach(fieldId => {
          const field = availableFields.find(f => f.id === fieldId);
          const value = getFieldValue(centro, fieldId);
          row[field?.label || fieldId] = field ? formatFieldValue(value, field) : String(value || '');
        });
        return row;
      });

      const ws = XLSX.utils.json_to_sheet(excelData);
      XLSX.utils.book_append_sheet(wb, ws, 'Centros de Custo');

      // Aba de estatísticas
      const comLocalizacao = filteredData.filter(c => c.endereco?.[0]?.cidade || c.endereco?.[0]?.estado).length;
      const semLocalizacao = filteredData.length - comLocalizacao;
      
      const statsData = [
        { Métrica: 'Total de Centros', Valor: filteredData.length },
        { Métrica: 'Com Localização', Valor: comLocalizacao },
        { Métrica: 'Sem Localização', Valor: semLocalizacao },
        { Métrica: 'Centros Ativos', Valor: filteredData.filter(c => c.ativo).length },
        { Métrica: 'Centros Inativos', Valor: filteredData.filter(c => !c.ativo).length },
        { Métrica: 'Estados Únicos', Valor: uniqueEstados.length },
        { Métrica: 'Cidades Únicas', Valor: uniqueCidades.length },
        { Métrica: 'Data de Geração', Valor: format(new Date(), 'dd/MM/yyyy HH:mm') },
      ];
      
      const wsStats = XLSX.utils.json_to_sheet(statsData);
      XLSX.utils.book_append_sheet(wb, wsStats, 'Estatísticas');

      XLSX.writeFile(wb, `relatorio-centros-custo-localizacao-${format(new Date(), 'dd-MM-yyyy-HH-mm-ss')}.xlsx`);
      toast.success('Relatório Excel gerado com sucesso!');
    } catch (error) {
      console.error('Erro ao gerar Excel:', error);
      toast.error('Erro ao gerar relatório Excel');
    } finally {
      setIsGenerating(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6 px-4">
        <div className="text-center">
          <p>Carregando dados dos centros de custo...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-4 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <MapPin className="h-8 w-8" />
            Relatório de Centros de Custo - Localização
          </h1>
          <p className="text-gray-600">
            Relatório detalhado com informações de localização dos centros de custo
          </p>
        </div>
        <Button
          onClick={() => setShowFieldCustomizer(!showFieldCustomizer)}
          variant="outline"
          className="flex items-center gap-2"
        >
          <Settings className="h-4 w-4" />
          {showFieldCustomizer ? 'Ocultar' : 'Personalizar'} Campos
        </Button>
      </div>

      {/* Customizador de Campos */}
      {showFieldCustomizer && (
        <Card>
          <CardHeader>
            <CardTitle>Personalização de Campos e Ordem</CardTitle>
          </CardHeader>
          <CardContent>
            <FieldCustomizer
              availableFields={availableFields}
              selectedFields={selectedFields}
              fieldOrder={fieldOrder}
              onFieldToggle={handleFieldToggle}
              onFieldReorder={setFieldOrder}
              onSelectAll={handleSelectAll}
              onDeselectAll={handleDeselectAll}
            />
          </CardContent>
        </Card>
      )}

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros de Localização
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <Label>Status</Label>
              <Select
                value={filters.status || 'all'}
                onValueChange={(value) => setFilters(prev => ({ 
                  ...prev, 
                  status: value === 'all' ? undefined : value as any 
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="active">Apenas Ativos</SelectItem>
                  <SelectItem value="inactive">Apenas Inativos</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Possui Localização</Label>
              <Select
                value={filters.hasLocation || 'all'}
                onValueChange={(value) => setFilters(prev => ({ 
                  ...prev, 
                  hasLocation: value === 'all' ? undefined : value as any 
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="yes">Com Localização</SelectItem>
                  <SelectItem value="no">Sem Localização</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Estado</Label>
              <Select
                value={filters.estado || ''}
                onValueChange={(value) => setFilters(prev => ({ 
                  ...prev, 
                  estado: value || undefined 
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Todos os estados" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Todos os estados</SelectItem>
                  {uniqueEstados.map(estado => (
                    <SelectItem key={estado} value={estado}>
                      {estado}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Cidade</Label>
              <Select
                value={filters.cidade || ''}
                onValueChange={(value) => setFilters(prev => ({ 
                  ...prev, 
                  cidade: value || undefined 
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Todas as cidades" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Todas as cidades</SelectItem>
                  {uniqueCidades.map(cidade => (
                    <SelectItem key={cidade} value={cidade}>
                      {cidade}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="md:col-span-2">
              <Label>Buscar</Label>
              <Input
                placeholder="Digite para buscar por descrição, razão social, CNPJ, etc."
                value={filters.searchTerm || ''}
                onChange={(e) => setFilters(prev => ({ 
                  ...prev, 
                  searchTerm: e.target.value || undefined 
                }))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Resultados e Ações */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Resultados ({filteredData.length} registros)</span>
            <div className="flex gap-2">
              <Button
                onClick={generatePDF}
                disabled={isGenerating || filteredData.length === 0}
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                {isGenerating ? 'Gerando...' : 'PDF'}
              </Button>
              <Button
                onClick={generateExcel}
                disabled={isGenerating || filteredData.length === 0}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                {isGenerating ? 'Gerando...' : 'Excel'}
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredData.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <MapPin className="mx-auto h-12 w-12 mb-2" />
              <p>Nenhum centro de custo encontrado com os filtros aplicados</p>
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center p-3 bg-blue-50 rounded">
                <div className="text-2xl font-bold text-blue-600">{filteredData.length}</div>
                <div className="text-blue-600">Total</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded">
                <div className="text-2xl font-bold text-green-600">
                  {filteredData.filter(c => c.endereco?.[0]?.cidade || c.endereco?.[0]?.estado).length}
                </div>
                <div className="text-green-600">Com Localização</div>
              </div>
              <div className="text-center p-3 bg-orange-50 rounded">
                <div className="text-2xl font-bold text-orange-600">
                  {filteredData.filter(c => !(c.endereco?.[0]?.cidade || c.endereco?.[0]?.estado)).length}
                </div>
                <div className="text-orange-600">Sem Localização</div>
              </div>
              <div className="text-center p-3 bg-purple-50 rounded">
                <div className="text-2xl font-bold text-purple-600">{selectedFields.length}</div>
                <div className="text-purple-600">Campos Selecionados</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
