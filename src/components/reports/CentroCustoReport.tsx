"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { format } from "date-fns";
import { jsPDF } from "jspdf";
import "jspdf-autotable";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { toast } from "sonner";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { FileText, Download, Filter } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface CentroCustoReportFilters {
  status: string;
  hasChildren: string;
  searchTerm: string;
}

interface CentroCustoReportColumns {
  descricao: boolean;
  cnpj: boolean;
  razaoSocial: boolean;
  responsavel: boolean;
  contato: boolean;
  email: boolean;
  endereco: boolean;
  dotacaoOrcamentaria: boolean;
  valorDotacao: boolean;
  centroAscendente: boolean;
  centroTomador: boolean;
  status: boolean;
  quantidadeFilhos: boolean;
  veiculosVinculados: boolean;
  empenhosVinculados: boolean;
}

export function CentroCustoReport() {
  const { centrosDeCusto, loading } = useCentroDeCusto();
  const [filters, setFilters] = useState<CentroCustoReportFilters>({
    status: "all",
    hasChildren: "all",
    searchTerm: "",
  });

  const [columns, setColumns] = useState<CentroCustoReportColumns>({
    descricao: true,
    cnpj: true,
    razaoSocial: true,
    responsavel: true,
    contato: true,
    email: true,
    endereco: false,
    dotacaoOrcamentaria: true,
    valorDotacao: true,
    centroAscendente: false,
    centroTomador: false,
    status: true,
    quantidadeFilhos: true,
    veiculosVinculados: false,
    empenhosVinculados: false,
  });

  const [isGenerating, setIsGenerating] = useState(false);

  // Função para filtrar os dados
  const getFilteredData = () => {
    let filtered = [...centrosDeCusto];

    // Filtro por status
    if (filters.status !== "all") {
      filtered = filtered.filter(centro => 
        filters.status === "active" ? centro.active : !centro.active
      );
    }

    // Filtro por ter filhos
    if (filters.hasChildren !== "all") {
      filtered = filtered.filter(centro => {
        const hasChildren = centro.centro_custos_filhos && centro.centro_custos_filhos.length > 0;
        return filters.hasChildren === "yes" ? hasChildren : !hasChildren;
      });
    }

    // Filtro por termo de busca
    if (filters.searchTerm) {
      const term = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(centro =>
        centro.descricao?.toLowerCase().includes(term) ||
        centro.razao_social?.toLowerCase().includes(term) ||
        centro.cnpj?.includes(term) ||
        centro.nome_responsavel?.toLowerCase().includes(term)
      );
    }

    return filtered;
  };

  const filteredData = getFilteredData();

  // Função para gerar relatório em PDF
  const generatePDF = async () => {
    setIsGenerating(true);
    try {
      const doc = new jsPDF();
      
      // Cabeçalho
      doc.setFontSize(18);
      doc.text("Relatório de Centros de Custo", 14, 22);
      
      doc.setFontSize(11);
      doc.text(`Gerado em: ${format(new Date(), 'dd/MM/yyyy HH:mm')}`, 14, 30);
      doc.text(`Total de registros: ${filteredData.length}`, 14, 36);

      // Preparar cabeçalhos da tabela
      const headers: string[] = [];
      if (columns.descricao) headers.push("Descrição");
      if (columns.cnpj) headers.push("CNPJ");
      if (columns.razaoSocial) headers.push("Razão Social");
      if (columns.responsavel) headers.push("Responsável");
      if (columns.contato) headers.push("Contato");
      if (columns.email) headers.push("E-mail");
      if (columns.endereco) headers.push("Endereço");
      if (columns.dotacaoOrcamentaria) headers.push("Dotação Orçamentária");
      if (columns.valorDotacao) headers.push("Valor Dotação");
      if (columns.centroAscendente) headers.push("Centro Ascendente");
      if (columns.status) headers.push("Status");
      if (columns.quantidadeFilhos) headers.push("Qtd. Filhos");

      // Preparar dados da tabela
      const tableData = filteredData.map(centro => {
        const row: string[] = [];
        if (columns.descricao) row.push(centro.descricao || "");
        if (columns.cnpj) row.push(centro.cnpj || "");
        if (columns.razaoSocial) row.push(centro.razao_social || "");
        if (columns.responsavel) row.push(centro.nome_responsavel || "");
        if (columns.contato) row.push(centro.contato || "");
        if (columns.email) row.push(centro.email || "");
        if (columns.endereco) {
          const endereco = centro.endereco;
          const enderecoStr = endereco ? 
            `${endereco.logradouro || ""}, ${endereco.cidade || ""} - ${endereco.estado || ""}` : "";
          row.push(enderecoStr);
        }
        if (columns.dotacaoOrcamentaria) row.push(centro.dotacao_orcamentista || "");
        if (columns.valorDotacao) {
          const valor = centro.valor_dotacao ? 
            centro.valor_dotacao.toLocaleString("pt-BR", { style: "currency", currency: "BRL" }) : "R$ 0,00";
          row.push(valor);
        }
        if (columns.centroAscendente) {
          row.push(centro.centro_custo_pai?.descricao || "");
        }
        if (columns.status) row.push(centro.active ? "Ativo" : "Inativo");
        if (columns.quantidadeFilhos) {
          row.push((centro.centro_custos_filhos?.length || 0).toString());
        }
        return row;
      });

      // Adicionar tabela ao PDF
      (doc as any).autoTable({
        head: [headers],
        body: tableData,
        startY: 45,
        styles: { fontSize: 8 },
        headStyles: { fillColor: [41, 128, 185], textColor: 255 },
        columnStyles: {
          0: { cellWidth: 'auto' },
        },
        margin: { top: 45 },
      });

      // Salvar PDF
      doc.save(`relatorio-centros-custo-${format(new Date(), "dd-MM-yyyy-HH-mm-ss")}.pdf`);
      toast.success("Relatório PDF gerado com sucesso!");
    } catch (error) {
      console.error("Erro ao gerar PDF:", error);
      toast.error("Erro ao gerar relatório PDF");
    } finally {
      setIsGenerating(false);
    }
  };

  // Função para gerar relatório em Excel
  const generateExcel = async () => {
    setIsGenerating(true);
    try {
      // Preparar dados para Excel
      const excelData = filteredData.map(centro => {
        const row: any = {};
        if (columns.descricao) row["Descrição"] = centro.descricao || "";
        if (columns.cnpj) row["CNPJ"] = centro.cnpj || "";
        if (columns.razaoSocial) row["Razão Social"] = centro.razao_social || "";
        if (columns.responsavel) row["Responsável"] = centro.nome_responsavel || "";
        if (columns.contato) row["Contato"] = centro.contato || "";
        if (columns.email) row["E-mail"] = centro.email || "";
        if (columns.endereco) {
          const endereco = centro.endereco;
          row["Endereço"] = endereco ? 
            `${endereco.logradouro || ""}, ${endereco.cidade || ""} - ${endereco.estado || ""}` : "";
        }
        if (columns.dotacaoOrcamentaria) row["Dotação Orçamentária"] = centro.dotacao_orcamentista || "";
        if (columns.valorDotacao) row["Valor Dotação"] = centro.valor_dotacao || 0;
        if (columns.centroAscendente) row["Centro Ascendente"] = centro.centro_custo_pai?.descricao || "";
        if (columns.status) row["Status"] = centro.active ? "Ativo" : "Inativo";
        if (columns.quantidadeFilhos) row["Quantidade de Filhos"] = centro.centro_custos_filhos?.length || 0;
        
        // Campos adicionais para Excel (mais detalhados)
        if (columns.veiculosVinculados) {
          row["Veículos Lotação"] = centro.lotacao_dos_veiculos?.length || 0;
          row["Veículos Faturamento"] = centro.faturamento_dos_veiculos?.length || 0;
        }
        if (columns.empenhosVinculados) {
          row["Empenhos Vinculados"] = centro.empenhos?.length || 0;
        }
        
        return row;
      });

      // Criar planilha
      const ws = XLSX.utils.json_to_sheet(excelData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Centros de Custo");

      // Adicionar informações do relatório
      const infoData = [
        { Campo: "Data de Geração", Valor: format(new Date(), 'dd/MM/yyyy HH:mm') },
        { Campo: "Total de Registros", Valor: filteredData.length },
        { Campo: "Filtros Aplicados", Valor: `Status: ${filters.status}, Tem Filhos: ${filters.hasChildren}` },
      ];
      const infoWs = XLSX.utils.json_to_sheet(infoData);
      XLSX.utils.book_append_sheet(wb, infoWs, "Informações");

      // Salvar arquivo
      XLSX.writeFile(wb, `relatorio-centros-custo-${format(new Date(), "dd-MM-yyyy-HH-mm-ss")}.xlsx`);
      toast.success("Relatório Excel gerado com sucesso!");
    } catch (error) {
      console.error("Erro ao gerar Excel:", error);
      toast.error("Erro ao gerar relatório Excel");
    } finally {
      setIsGenerating(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p>Carregando dados dos centros de custo...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Relatório de Centros de Custo</h2>
          <p className="text-gray-600">
            Gere relatórios detalhados dos centros de custo cadastrados no sistema
          </p>
        </div>
        <Badge variant="outline" className="text-lg px-3 py-1">
          {filteredData.length} registros
        </Badge>
      </div>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Status</Label>
              <Select value={filters.status} onValueChange={(value) => 
                setFilters(prev => ({ ...prev, status: value }))
              }>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="active">Ativos</SelectItem>
                  <SelectItem value="inactive">Inativos</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Possui Centros Filhos</Label>
              <Select value={filters.hasChildren} onValueChange={(value) => 
                setFilters(prev => ({ ...prev, hasChildren: value }))
              }>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="yes">Sim</SelectItem>
                  <SelectItem value="no">Não</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Buscar</Label>
              <Input
                placeholder="Descrição, CNPJ, Razão Social..."
                value={filters.searchTerm}
                onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Seleção de Colunas */}
      <Card>
        <CardHeader>
          <CardTitle>Colunas do Relatório</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="descricao"
                checked={columns.descricao}
                onCheckedChange={(checked) =>
                  setColumns(prev => ({ ...prev, descricao: checked as boolean }))
                }
              />
              <Label htmlFor="descricao">Descrição</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="cnpj"
                checked={columns.cnpj}
                onCheckedChange={(checked) =>
                  setColumns(prev => ({ ...prev, cnpj: checked as boolean }))
                }
              />
              <Label htmlFor="cnpj">CNPJ</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="razaoSocial"
                checked={columns.razaoSocial}
                onCheckedChange={(checked) =>
                  setColumns(prev => ({ ...prev, razaoSocial: checked as boolean }))
                }
              />
              <Label htmlFor="razaoSocial">Razão Social</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="responsavel"
                checked={columns.responsavel}
                onCheckedChange={(checked) =>
                  setColumns(prev => ({ ...prev, responsavel: checked as boolean }))
                }
              />
              <Label htmlFor="responsavel">Responsável</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="contato"
                checked={columns.contato}
                onCheckedChange={(checked) =>
                  setColumns(prev => ({ ...prev, contato: checked as boolean }))
                }
              />
              <Label htmlFor="contato">Contato</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="email"
                checked={columns.email}
                onCheckedChange={(checked) =>
                  setColumns(prev => ({ ...prev, email: checked as boolean }))
                }
              />
              <Label htmlFor="email">E-mail</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="endereco"
                checked={columns.endereco}
                onCheckedChange={(checked) =>
                  setColumns(prev => ({ ...prev, endereco: checked as boolean }))
                }
              />
              <Label htmlFor="endereco">Endereço</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="dotacaoOrcamentaria"
                checked={columns.dotacaoOrcamentaria}
                onCheckedChange={(checked) =>
                  setColumns(prev => ({ ...prev, dotacaoOrcamentaria: checked as boolean }))
                }
              />
              <Label htmlFor="dotacaoOrcamentaria">Dotação Orçamentária</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="valorDotacao"
                checked={columns.valorDotacao}
                onCheckedChange={(checked) =>
                  setColumns(prev => ({ ...prev, valorDotacao: checked as boolean }))
                }
              />
              <Label htmlFor="valorDotacao">Valor Dotação</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="centroAscendente"
                checked={columns.centroAscendente}
                onCheckedChange={(checked) =>
                  setColumns(prev => ({ ...prev, centroAscendente: checked as boolean }))
                }
              />
              <Label htmlFor="centroAscendente">Centro Ascendente</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="status"
                checked={columns.status}
                onCheckedChange={(checked) =>
                  setColumns(prev => ({ ...prev, status: checked as boolean }))
                }
              />
              <Label htmlFor="status">Status</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="quantidadeFilhos"
                checked={columns.quantidadeFilhos}
                onCheckedChange={(checked) =>
                  setColumns(prev => ({ ...prev, quantidadeFilhos: checked as boolean }))
                }
              />
              <Label htmlFor="quantidadeFilhos">Qtd. Filhos</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="veiculosVinculados"
                checked={columns.veiculosVinculados}
                onCheckedChange={(checked) =>
                  setColumns(prev => ({ ...prev, veiculosVinculados: checked as boolean }))
                }
              />
              <Label htmlFor="veiculosVinculados">Veículos Vinculados</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="empenhosVinculados"
                checked={columns.empenhosVinculados}
                onCheckedChange={(checked) =>
                  setColumns(prev => ({ ...prev, empenhosVinculados: checked as boolean }))
                }
              />
              <Label htmlFor="empenhosVinculados">Empenhos Vinculados</Label>
            </div>
          </div>

          <div className="flex gap-2 mt-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setColumns({
                descricao: true,
                cnpj: true,
                razaoSocial: true,
                responsavel: true,
                contato: true,
                email: true,
                endereco: true,
                dotacaoOrcamentaria: true,
                valorDotacao: true,
                centroAscendente: true,
                centroTomador: true,
                status: true,
                quantidadeFilhos: true,
                veiculosVinculados: true,
                empenhosVinculados: true,
              })}
            >
              Selecionar Todos
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setColumns({
                descricao: false,
                cnpj: false,
                razaoSocial: false,
                responsavel: false,
                contato: false,
                email: false,
                endereco: false,
                dotacaoOrcamentaria: false,
                valorDotacao: false,
                centroAscendente: false,
                centroTomador: false,
                status: false,
                quantidadeFilhos: false,
                veiculosVinculados: false,
                empenhosVinculados: false,
              })}
            >
              Desmarcar Todos
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Ações */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Gerar Relatório
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              onClick={generatePDF}
              disabled={isGenerating || filteredData.length === 0}
              className="flex items-center gap-2"
            >
              {isGenerating ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <FileText className="h-4 w-4" />
              )}
              Gerar PDF
            </Button>

            <Button
              onClick={generateExcel}
              disabled={isGenerating || filteredData.length === 0}
              variant="outline"
              className="flex items-center gap-2"
            >
              {isGenerating ? (
                <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin" />
              ) : (
                <Download className="h-4 w-4" />
              )}
              Gerar Excel
            </Button>
          </div>

          {filteredData.length === 0 && (
            <p className="text-sm text-gray-500 mt-2">
              Nenhum registro encontrado com os filtros aplicados.
            </p>
          )}
        </CardContent>
      </Card>

      {/* Preview dos dados */}
      <Card>
        <CardHeader>
          <CardTitle>Preview dos Dados</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  {columns.descricao && <th className="text-left p-2">Descrição</th>}
                  {columns.cnpj && <th className="text-left p-2">CNPJ</th>}
                  {columns.razaoSocial && <th className="text-left p-2">Razão Social</th>}
                  {columns.responsavel && <th className="text-left p-2">Responsável</th>}
                  {columns.status && <th className="text-left p-2">Status</th>}
                  {columns.quantidadeFilhos && <th className="text-left p-2">Filhos</th>}
                </tr>
              </thead>
              <tbody>
                {filteredData.slice(0, 5).map((centro) => (
                  <tr key={centro.id} className="border-b">
                    {columns.descricao && <td className="p-2">{centro.descricao}</td>}
                    {columns.cnpj && <td className="p-2">{centro.cnpj}</td>}
                    {columns.razaoSocial && <td className="p-2">{centro.razao_social}</td>}
                    {columns.responsavel && <td className="p-2">{centro.nome_responsavel || "N/A"}</td>}
                    {columns.status && (
                      <td className="p-2">
                        <Badge variant={centro.active ? "default" : "secondary"}>
                          {centro.active ? "Ativo" : "Inativo"}
                        </Badge>
                      </td>
                    )}
                    {columns.quantidadeFilhos && (
                      <td className="p-2">{centro.centro_custos_filhos?.length || 0}</td>
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
            {filteredData.length > 5 && (
              <p className="text-sm text-gray-500 mt-2">
                Mostrando 5 de {filteredData.length} registros no preview.
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
