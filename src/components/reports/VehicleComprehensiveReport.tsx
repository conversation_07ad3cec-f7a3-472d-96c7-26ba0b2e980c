"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DateInput } from '@/components/inputs/date-input';
import { Checkbox } from '@/components/ui/checkbox';
import { useVeiculos } from '@/context/veiculos-context';
import { useCondutor } from '@/context/condutor-context';
import { useCentroDeCusto } from '@/context/centro-de-custo-context';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { Download, FileText, Filter, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';

export interface ReportField {
  name: string;
  label: string;
  type: 'string' | 'number' | 'date' | 'boolean' | 'currency' | 'array';
}

interface VehicleReportFilters {
  startDateCompra?: string;
  endDateCompra?: string;
  startDateCedencia?: string;
  endDateCedencia?: string;
  centroCustoId?: string;
  tipoVeiculoId?: string;
  tipoFrotaId?: string;
  marcaId?: string;
  modeloId?: string;
  status?: string;
  anoFabricacaoInicio?: string;
  anoFabricacaoFim?: string;
  estabelecimento?: string;
}

const availableFields: ReportField[] = [
  // Campos principais do relatório
  { name: 'lotacao_veiculos.centro_custo.descricao', label: 'Centro de Custo/Órgão Contratante', type: 'string' },
  { name: 'ano_fab', label: 'Ano de Fabricação', type: 'string' },
  { name: 'tipo_de_veiculo.descricao', label: 'Tipo de Veículo', type: 'string' },
  { name: 'modelo.descricao', label: 'Modelo do Veículo', type: 'string' },
  { name: 'marca.descricao', label: 'Marca', type: 'string' },
  { name: 'placa', label: 'Placa', type: 'string' },
  { name: 'data_compra', label: 'Data de Compra', type: 'date' },
  { name: 'data_cedencia', label: 'Data de Cedência', type: 'date' },
  { name: 'valor_depreciacao', label: 'Depreciação', type: 'currency' },
  { name: 'valor_mercado', label: 'Valor de Mercado', type: 'currency' },
  { name: 'condutor_principal', label: 'Motorista Principal', type: 'string' },

  // Campos complementares de identificação
  { name: 'renovam', label: 'Renavam', type: 'string' },
  { name: 'vin', label: 'VIN/Chassi', type: 'string' },
  { name: 'numero_do_motor', label: 'Número do Motor', type: 'string' },
  { name: 'matricula', label: 'Matrícula', type: 'string' },
  { name: 'ano_modelo', label: 'Ano do Modelo', type: 'string' },
  { name: 'cor', label: 'Cor', type: 'string' },
  { name: 'tipo_de_frota.descricao', label: 'Tipo de Frota', type: 'string' },
  { name: 'odometro_atual', label: 'Odômetro Atual', type: 'number' },
  { name: 'status', label: 'Status', type: 'string' },
  { name: 'valor_venal', label: 'Valor Venal', type: 'currency' },
  { name: 'codigo_fipe.codigo_fipe', label: 'Código FIPE', type: 'string' },
  { name: 'combustivel.tipos_de_combustiveis', label: 'Combustível', type: 'array' },
  { name: 'definicoes.potencia', label: 'Potência', type: 'string' },
  { name: 'definicoes.cilindradas', label: 'Cilindradas', type: 'number' },
  { name: 'definicoes.transmissao', label: 'Transmissão', type: 'string' },
  { name: 'definicoes.quantidade_de_portas', label: 'Portas', type: 'number' },
  { name: 'definicoes.quantidade_de_assentos', label: 'Assentos', type: 'number' },
];

export function VehicleComprehensiveReport() {
  const { veiculos } = useVeiculos();
  const { condutores } = useCondutor();
  const { centrosDeCusto } = useCentroDeCusto();

  const [filters, setFilters] = useState<VehicleReportFilters>({});
  // Campos principais incluídos por padrão
  const defaultFields = [
    'lotacao_veiculos.centro_custo.descricao', // Centro de custo/órgão contratante
    'ano_fab', // Ano de fabricação
    'tipo_de_veiculo.descricao', // Tipo de veículo
    'modelo.descricao', // Modelo do veículo
    'marca.descricao', // Marca
    'placa', // Placas
    'data_compra', // Data de compra
    'data_cedencia', // Data de cedência
    'valor_depreciacao', // Depreciação
    'valor_mercado', // Valor de mercado
    'condutor_principal', // Motorista
    'renovam', // Renavam (dados de identificação)
    'vin', // VIN/Chassi (dados de identificação)
    'numero_do_motor', // Número do motor (dados de identificação)
    'matricula', // Matrícula (dados de identificação)
  ];

  const [selectedFields, setSelectedFields] = useState<string[]>(defaultFields);
  const [fieldOrder, setFieldOrder] = useState<string[]>(defaultFields);
  const [showFieldCustomizer, setShowFieldCustomizer] = useState(false);
  const [filteredData, setFilteredData] = useState<veiculo[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(50);

  // Aplicar filtros
  useEffect(() => {
    if (!veiculos) return;

    let filtered = [...veiculos];

    // Filtro por data de compra
    if (filters.startDateCompra) {
      filtered = filtered.filter(v => {
        if (!v.data_compra) return false;
        return new Date(v.data_compra) >= new Date(filters.startDateCompra!);
      });
    }

    if (filters.endDateCompra) {
      filtered = filtered.filter(v => {
        if (!v.data_compra) return false;
        return new Date(v.data_compra) <= new Date(filters.endDateCompra!);
      });
    }

    // Filtro por data de cedência
    if (filters.startDateCedencia) {
      filtered = filtered.filter(v => {
        if (!v.data_cedencia) return false;
        return new Date(v.data_cedencia) >= new Date(filters.startDateCedencia!);
      });
    }

    if (filters.endDateCedencia) {
      filtered = filtered.filter(v => {
        if (!v.data_cedencia) return false;
        return new Date(v.data_cedencia) <= new Date(filters.endDateCedencia!);
      });
    }

    // Filtro por centro de custo
    if (filters.centroCustoId) {
      filtered = filtered.filter(v => 
        v.lotacao_veiculos?.centro_custo?.id === filters.centroCustoId
      );
    }

    // Filtro por tipo de veículo
    if (filters.tipoVeiculoId) {
      filtered = filtered.filter(v => v.tipo_de_veiculoId === filters.tipoVeiculoId);
    }

    // Filtro por tipo de frota
    if (filters.tipoFrotaId) {
      filtered = filtered.filter(v => v.tipo_de_frotaId === filters.tipoFrotaId);
    }

    // Filtro por marca
    if (filters.marcaId) {
      filtered = filtered.filter(v => v.marcaId === filters.marcaId);
    }

    // Filtro por modelo
    if (filters.modeloId) {
      filtered = filtered.filter(v => v.modeloId === filters.modeloId);
    }

    // Filtro por status
    if (filters.status) {
      filtered = filtered.filter(v => v.status === filters.status);
    }

    // Filtro por ano de fabricação
    if (filters.anoFabricacaoInicio) {
      filtered = filtered.filter(v => 
        v.ano_fab && parseInt(v.ano_fab) >= parseInt(filters.anoFabricacaoInicio!)
      );
    }

    if (filters.anoFabricacaoFim) {
      filtered = filtered.filter(v => 
        v.ano_fab && parseInt(v.ano_fab) <= parseInt(filters.anoFabricacaoFim!)
      );
    }

    setFilteredData(filtered);
    setCurrentPage(1); // Reset para primeira página quando filtros mudarem
  }, [veiculos, filters]);

  // Função para obter valor aninhado do objeto
  const getNestedValue = (obj: any, path: string): any => {
    // Campos especiais que precisam de lógica customizada
    switch (path) {
      case 'valor_depreciacao':
        // TODO: Implementar cálculo de depreciação quando API estiver disponível
        // Por enquanto, retorna valor estimado baseado no ano
        if (obj.ano_fab && obj.valor_venal) {
          const anoAtual = new Date().getFullYear();
          const anoFab = parseInt(obj.ano_fab);
          const idadeVeiculo = anoAtual - anoFab;
          const valorVenal = parseFloat(obj.valor_venal) || 0;
          // Depreciação estimada de 10% ao ano
          const depreciacaoEstimada = valorVenal * (idadeVeiculo * 0.1);
          return depreciacaoEstimada.toString();
        }
        return 'A calcular';

      case 'valor_mercado':
        // TODO: Integrar com API FIPE quando disponível
        // Por enquanto, usa valor venal como base
        if (obj.valor_venal) {
          return obj.valor_venal;
        }
        return 'A consultar';

      case 'condutor_principal':
        // Busca o condutor principal através das OS mais recentes
        if (obj.os && obj.os.length > 0) {
          // Pega a OS mais recente que tem condutor
          const osComCondutor = obj.os
            .filter((os: any) => os.condutor?.nome)
            .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

          if (osComCondutor.length > 0) {
            return osComCondutor[0].condutor.nome;
          }
        }

        // Se não encontrou nas OS, busca nos condutores do mesmo centro de custo
        if (obj.lotacao_veiculos?.centro_custo?.id && condutores.length > 0) {
          const condutorMesmoCentro = condutores.find((condutor: any) =>
            condutor.lotacao_condutor?.centro_custoID === obj.lotacao_veiculos.centro_custo.id &&
            condutor.status === true
          );

          if (condutorMesmoCentro) {
            return condutorMesmoCentro.nome;
          }
        }

        return 'Não definido';

      default:
        // Lógica padrão para campos aninhados
        return path.split('.').reduce((current, key) => {
          if (current && typeof current === 'object') {
            return current[key];
          }
          return undefined;
        }, obj);
    }
  };

  // Função para formatar valor baseado no tipo
  const formatValue = (value: any, type: string): string => {
    if (value === null || value === undefined) return 'N/A';
    
    switch (type) {
      case 'date':
        if (typeof value === 'string' && value) {
          try {
            return format(new Date(value), 'dd/MM/yyyy');
          } catch {
            return value;
          }
        }
        return 'N/A';
      case 'currency':
        if (typeof value === 'string' && value) {
          const numValue = parseFloat(value);
          return isNaN(numValue) ? value : numValue.toLocaleString('pt-BR', {
            style: 'currency',
            currency: 'BRL'
          });
        }
        return 'N/A';
      case 'array':
        if (Array.isArray(value)) {
          return value.join(', ');
        }
        return value?.toString() || 'N/A';
      case 'number':
        return value?.toString() || 'N/A';
      default:
        return value?.toString() || 'N/A';
    }
  };

  const generatePDF = () => {
    if (filteredData.length === 0) {
      toast.error('Nenhum dado encontrado para gerar o relatório');
      return;
    }

    setIsGenerating(true);
    try {
      const doc = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4'
      });

      // Cabeçalho
      doc.setFontSize(16);
      doc.text('Relatório de Composição de Frota por Órgão/Unidade Contratante', 20, 20);

      doc.setFontSize(12);
      doc.text('Dados Completos de Identificação e Composição de Frota', 20, 28);

      doc.setFontSize(10);
      doc.text(`Gerado em: ${format(new Date(), 'dd/MM/yyyy HH:mm')}`, 20, 38);
      doc.text(`Total de registros: ${filteredData.length}`, 20, 43);

      // Preparar dados da tabela
      const headers = fieldOrder
        .filter(field => selectedFields.includes(field))
        .map(field => {
          const fieldConfig = availableFields.find(f => f.name === field);
          return fieldConfig?.label || field;
        });

      const tableData = filteredData.map(vehicle => 
        fieldOrder
          .filter(field => selectedFields.includes(field))
          .map(field => {
            const fieldConfig = availableFields.find(f => f.name === field);
            const value = getNestedValue(vehicle, field);
            return formatValue(value, fieldConfig?.type || 'string');
          })
      );

      // Adicionar tabela
      (doc as any).autoTable({
        head: [headers],
        body: tableData,
        startY: 50,
        styles: { fontSize: 7 },
        headStyles: { fillColor: [41, 128, 185], textColor: 255 },
        columnStyles: headers.reduce((acc, _, index) => {
          acc[index] = { cellWidth: 'auto' };
          return acc;
        }, {} as any),
      });

      doc.save(`relatorio-composicao-frota-${format(new Date(), 'dd-MM-yyyy-HH-mm-ss')}.pdf`);
      toast.success('Relatório PDF gerado com sucesso!');
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      toast.error('Erro ao gerar relatório PDF');
    } finally {
      setIsGenerating(false);
    }
  };

  const generateExcel = () => {
    if (filteredData.length === 0) {
      toast.error('Nenhum dado encontrado para gerar o relatório');
      return;
    }

    setIsGenerating(true);
    try {
      const headers = fieldOrder
        .filter(field => selectedFields.includes(field))
        .map(field => {
          const fieldConfig = availableFields.find(f => f.name === field);
          return fieldConfig?.label || field;
        });

      const data = filteredData.map(vehicle => {
        const row: any = {};
        fieldOrder
          .filter(field => selectedFields.includes(field))
          .forEach((field, index) => {
            const fieldConfig = availableFields.find(f => f.name === field);
            const value = getNestedValue(vehicle, field);
            row[headers[index]] = formatValue(value, fieldConfig?.type || 'string');
          });
        return row;
      });

      const ws = XLSX.utils.json_to_sheet(data);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Veículos');

      XLSX.writeFile(wb, `relatorio-composicao-frota-${format(new Date(), 'dd-MM-yyyy-HH-mm-ss')}.xlsx`);
      toast.success('Relatório Excel gerado com sucesso!');
    } catch (error) {
      console.error('Erro ao gerar Excel:', error);
      toast.error('Erro ao gerar relatório Excel');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="container mx-auto py-6 px-4 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Car className="h-8 w-8" />
            Composição de Frota por Órgão/Unidade Contratante
          </h1>
          <p className="text-gray-600">
            Relatório completo de composição de frota: ano de fabricação, tipo de veículo, modelo, marca, centro de custo, placas, datas de compra e cedência, depreciação, valor de mercado, motorista e dados de identificação
          </p>
        </div>
        <Button
          onClick={() => setShowFieldCustomizer(!showFieldCustomizer)}
          variant="outline"
          className="flex items-center gap-2"
        >
          <Settings className="h-4 w-4" />
          {showFieldCustomizer ? 'Ocultar' : 'Personalizar'} Campos
        </Button>
      </div>

      {showFieldCustomizer && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Personalizar Campos do Relatório
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex gap-2 mb-4">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setSelectedFields(availableFields.map(f => f.name))}
                >
                  Selecionar Todos
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setSelectedFields([])}
                >
                  Desmarcar Todos
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {availableFields.map(field => (
                  <div key={field.name} className="flex items-center space-x-2">
                    <Checkbox
                      id={field.name}
                      checked={selectedFields.includes(field.name)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedFields(prev => [...prev, field.name]);
                          setFieldOrder(prev => [...prev, field.name]);
                        } else {
                          setSelectedFields(prev => prev.filter(f => f !== field.name));
                          setFieldOrder(prev => prev.filter(f => f !== field.name));
                        }
                      }}
                    />
                    <label
                      htmlFor={field.name}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {field.label}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Informações Importantes */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">!</span>
              </div>
            </div>
            <div>
              <h3 className="font-semibold text-blue-900 mb-2">Informações sobre o Relatório</h3>
              <div className="text-sm text-blue-800 space-y-1">
                <p>• <strong>Campos principais:</strong> Este relatório inclui automaticamente os campos essenciais para composição de frota.</p>
                <p>• <strong>Depreciação:</strong> Calculada estimativamente (10% ao ano) até integração com API específica.</p>
                <p>• <strong>Valor de Mercado:</strong> Baseado no valor venal até integração com API FIPE em tempo real.</p>
                <p>• <strong>Motorista Principal:</strong> Campo preparado para integração com sistema de condutores.</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros do Relatório
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Filtros de Data de Compra */}
            <div className="space-y-2">
              <Label>Data de Compra - Início</Label>
              <DateInput
                value={filters.startDateCompra || ''}
                onChange={(value) => setFilters(prev => ({
                  ...prev,
                  startDateCompra: value || undefined
                }))}
              />
            </div>

            <div className="space-y-2">
              <Label>Data de Compra - Fim</Label>
              <DateInput
                value={filters.endDateCompra || ''}
                onChange={(value) => setFilters(prev => ({
                  ...prev,
                  endDateCompra: value || undefined
                }))}
              />
            </div>

            {/* Filtros de Data de Cedência */}
            <div className="space-y-2">
              <Label>Data de Cedência - Início</Label>
              <DateInput
                value={filters.startDateCedencia || ''}
                onChange={(value) => setFilters(prev => ({
                  ...prev,
                  startDateCedencia: value || undefined
                }))}
              />
            </div>

            <div className="space-y-2">
              <Label>Data de Cedência - Fim</Label>
              <DateInput
                value={filters.endDateCedencia || ''}
                onChange={(value) => setFilters(prev => ({
                  ...prev,
                  endDateCedencia: value || undefined
                }))}
              />
            </div>

            {/* Filtro por Centro de Custo */}
            <div className="space-y-2">
              <Label>Centro de Custo</Label>
              <Select
                value={filters.centroCustoId || ''}
                onValueChange={(value) => setFilters(prev => ({
                  ...prev,
                  centroCustoId: value || undefined
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Todos os centros de custo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Todos os centros de custo</SelectItem>
                  {centrosDeCusto.map(centro => (
                    <SelectItem key={centro.id} value={centro.id}>
                      {centro.descricao}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Filtro por Status */}
            <div className="space-y-2">
              <Label>Status</Label>
              <Select
                value={filters.status || ''}
                onValueChange={(value) => setFilters(prev => ({
                  ...prev,
                  status: value || undefined
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Todos os status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Todos os status</SelectItem>
                  <SelectItem value="Ativo">Ativo</SelectItem>
                  <SelectItem value="Inativo">Inativo</SelectItem>
                  <SelectItem value="Em Manuntenção">Em Manutenção</SelectItem>
                  <SelectItem value="Sem condições de uso">Sem condições de uso</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Filtros de Ano de Fabricação */}
            <div className="space-y-2">
              <Label>Ano de Fabricação - Início</Label>
              <Input
                type="number"
                placeholder="2020"
                value={filters.anoFabricacaoInicio || ''}
                onChange={(e) => setFilters(prev => ({
                  ...prev,
                  anoFabricacaoInicio: e.target.value || undefined
                }))}
              />
            </div>

            <div className="space-y-2">
              <Label>Ano de Fabricação - Fim</Label>
              <Input
                type="number"
                placeholder="2024"
                value={filters.anoFabricacaoFim || ''}
                onChange={(e) => setFilters(prev => ({
                  ...prev,
                  anoFabricacaoFim: e.target.value || undefined
                }))}
              />
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              onClick={() => setFilters({})}
              variant="outline"
              className="flex items-center gap-2"
            >
              Limpar Filtros
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Resumo dos Dados */}
      <Card>
        <CardHeader>
          <CardTitle>Resumo dos Dados</CardTitle>
          <p className="text-sm text-gray-600">
            Relatório de composição de frota por órgão/unidade contratante
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{filteredData.length}</div>
              <div className="text-sm text-gray-600">Total de Veículos</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {filteredData.filter(v => v.status === 'Ativo').length}
              </div>
              <div className="text-sm text-gray-600">Veículos Ativos</div>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">
                {filteredData.filter(v => v.status === 'Em Manuntenção').length}
              </div>
              <div className="text-sm text-gray-600">Em Manutenção</div>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">
                {filteredData.filter(v => v.status === 'Inativo' || v.status === 'Sem condições de uso').length}
              </div>
              <div className="text-sm text-gray-600">Inativos/Sem Condições</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Botões de Exportação */}
      <Card>
        <CardHeader>
          <CardTitle>Exportar Relatório - Composição de Frota</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Button
              onClick={generatePDF}
              disabled={isGenerating || filteredData.length === 0}
              className="flex items-center gap-2"
            >
              <FileText className="h-4 w-4" />
              {isGenerating ? 'Gerando...' : 'Exportar PDF Completo'}
            </Button>
            <Button
              onClick={generateExcel}
              disabled={isGenerating || filteredData.length === 0}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              {isGenerating ? 'Gerando...' : 'Exportar Excel'}
            </Button>
          </div>
          {filteredData.length === 0 && (
            <p className="text-sm text-gray-500 mt-2">
              Nenhum veículo encontrado com os filtros aplicados.
            </p>
          )}
        </CardContent>
      </Card>

      {/* Preview dos Dados */}
      {filteredData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Dados do Relatório ({filteredData.length} registros)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    {fieldOrder
                      .filter(field => selectedFields.includes(field))
                      .map(field => {
                        const fieldConfig = availableFields.find(f => f.name === field);
                        return (
                          <th key={field} className="border border-gray-300 px-2 py-1 text-left text-xs">
                            {fieldConfig?.label || field}
                          </th>
                        );
                      })}
                  </tr>
                </thead>
                <tbody>
                  {filteredData
                    .slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
                    .map((vehicle, index) => (
                    <tr key={vehicle.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      {fieldOrder
                        .filter(field => selectedFields.includes(field))
                        .map(field => {
                          const fieldConfig = availableFields.find(f => f.name === field);
                          const value = getNestedValue(vehicle, field);
                          return (
                            <td key={field} className="border border-gray-300 px-2 py-1 text-xs">
                              {formatValue(value, fieldConfig?.type || 'string')}
                            </td>
                          );
                        })}
                    </tr>
                  ))}
                </tbody>
              </table>

              {/* Controles de Paginação */}
              {filteredData.length > itemsPerPage && (
                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">
                      Mostrando {((currentPage - 1) * itemsPerPage) + 1} a {Math.min(currentPage * itemsPerPage, filteredData.length)} de {filteredData.length} registros
                    </span>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      Anterior
                    </Button>

                    <span className="text-sm text-gray-600">
                      Página {currentPage} de {Math.ceil(filteredData.length / itemsPerPage)}
                    </span>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(Math.ceil(filteredData.length / itemsPerPage), prev + 1))}
                      disabled={currentPage === Math.ceil(filteredData.length / itemsPerPage)}
                    >
                      Próxima
                    </Button>
                  </div>

                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">Itens por página:</span>
                    <select
                      value={itemsPerPage}
                      onChange={(e) => {
                        setItemsPerPage(Number(e.target.value));
                        setCurrentPage(1);
                      }}
                      className="border border-gray-300 rounded px-2 py-1 text-sm"
                    >
                      <option value={25}>25</option>
                      <option value={50}>50</option>
                      <option value={100}>100</option>
                      <option value={filteredData.length}>Todos ({filteredData.length})</option>
                    </select>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
