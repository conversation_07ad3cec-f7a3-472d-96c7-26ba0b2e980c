import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { format } from "date-fns";
import { jsPDF } from "jspdf";
import "jspdf-autotable";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { toast } from "sonner";
import { useOS } from "@/context/os-context";
import { useVeiculos } from "@/context/veiculos-context";
import { useCondutor } from "@/context/condutor-context";
import { useCredenciado } from "@/context/credenciado-context";
import { DateInput } from "../inputs/date-input";

interface FilterConfig {
  startDate: Date | null;
  endDate: Date | null;
  centroCusto: string;
  tipoServico: string;
  veiculo: string;
  condutor: string;
  credenciado: string;
  status: string;
}

interface ColumnConfig {
  osNumber: boolean;
  centroCusto: boolean;
  tipoServico: boolean;
  veiculo: boolean;
  condutor: boolean;
  credenciado: boolean;
  data: boolean;
  status: boolean;
  valor: boolean;
}

export function CustomReport() {
  const { ordensDeServico } = useOS();
  const { veiculos } = useVeiculos();
  const { condutores } = useCondutor();
  const { credenciados } = useCredenciado();

  const [filters, setFilters] = useState<FilterConfig>({
    startDate: null,
    endDate: null,
    centroCusto: "",
    tipoServico: "",
    veiculo: "",
    condutor: "",
    credenciado: "",
    status: "",
  });

  const [columns, setColumns] = useState<ColumnConfig>({
    osNumber: true,
    centroCusto: true,
    tipoServico: true,
    veiculo: true,
    condutor: true,
    credenciado: true,
    data: true,
    status: true,
    valor: true,
  });

  const [filteredData, setFilteredData] = useState<OS[]>([]);

  useEffect(() => {
    if (!ordensDeServico) return;

    const filtered = ordensDeServico.filter((ordem) => {
      const dateMatch =
        (!filters.startDate ||
          new Date(ordem.createdAt) >= filters.startDate) &&
        (!filters.endDate || new Date(ordem.createdAt) <= filters.endDate);

      const centroCustoMatch =
        !filters.centroCusto ||
        ordem.veiculo?.lotacao_veiculos?.centro_custo?.descricao
          ?.toLowerCase()
          .includes(filters.centroCusto.toLowerCase());

      const tipoServicoMatch =
        !filters.tipoServico ||
        ordem.TiposDeOs?.descricao
          ?.toLowerCase()
          .includes(filters.tipoServico.toLowerCase());

      const veiculoMatch =
        !filters.veiculo ||
        ordem.veiculo?.placa?.toLowerCase().includes(filters.veiculo.toLowerCase());

      const condutorMatch =
        !filters.condutor ||
        ordem.condutor?.nome?.toLowerCase().includes(filters.condutor.toLowerCase());

      const credenciadoMatch =
        !filters.credenciado ||
        ordem.credenciado?.informacoes[0].razao_social
          ?.toLowerCase()
          .includes(filters.credenciado.toLowerCase());

      const statusMatch =
        !filters.status ||
        ordem.status?.toLowerCase().includes(filters.status.toLowerCase());

      return (
        dateMatch &&
        centroCustoMatch &&
        tipoServicoMatch &&
        veiculoMatch &&
        condutorMatch &&
        credenciadoMatch &&
        statusMatch
      );
    });

    setFilteredData(filtered);
  }, [ordensDeServico, filters]);

  const exportToPDF = () => {
    try {
      const doc = new jsPDF("landscape");
      doc.setFontSize(16);
      doc.text("Relatório de Ordens de Serviço", 14, 20);
      doc.setFontSize(10);
      doc.text(
        `Período: ${
          filters.startDate ? format(filters.startDate, "dd/MM/yyyy") : "Início"
        } até ${
          filters.endDate ? format(filters.endDate, "dd/MM/yyyy") : "Atual"
        }`,
        14,
        30
      );

      const headers: string[] = [];
      const data: string[][] = [];

      if (columns.osNumber) headers.push("Nº OS");
      if (columns.centroCusto) headers.push("Centro de Custo");
      if (columns.tipoServico) headers.push("Tipo de Serviço");
      if (columns.veiculo) headers.push("Veículo");
      if (columns.condutor) headers.push("Condutor");
      if (columns.credenciado) headers.push("Credenciado");
      if (columns.data) headers.push("Data");
      if (columns.status) headers.push("Status");
      if (columns.valor) headers.push("Valor Total");

      filteredData.forEach((ordem) => {
        const row: string[] = [];
        if (columns.osNumber)
          row.push(`#${ordem.osNumber}/${new Date(ordem.createdAt).getFullYear()}`);
        if (columns.centroCusto)
          row.push(ordem.veiculo?.lotacao_veiculos?.centro_custo?.descricao || "N/A");
        if (columns.tipoServico) row.push(ordem.TiposDeOs?.descricao || "N/A");
        if (columns.veiculo)
          row.push(
            `${ordem.veiculo?.placa} | ${ordem.veiculo?.marca?.descricao} - ${ordem.veiculo?.modelo?.descricao}`
          );
        if (columns.condutor) row.push(ordem.condutor?.nome || "N/A");
        if (columns.credenciado)
          row.push(ordem.credenciado?.informacoes[0].razao_social || "N/A");
        if (columns.data)
          row.push(format(new Date(ordem.createdAt), "dd/MM/yyyy HH:mm"));
        if (columns.status) row.push(ordem.status || "N/A");
        // if (columns.valor)
        //   row.push(
        //     ordem.?.toLocaleString("pt-BR", {
        //       style: "currency",
        //       currency: "BRL",
        //     }) || "R$ 0,00"
        //   );
        data.push(row);
      });

      (doc as any).autoTable({
        head: [headers],
        body: data,
        startY: 40,
        styles: { fontSize: 8 },
        headStyles: { fillColor: [41, 128, 185], textColor: 255 },
      });

      doc.save(
        `relatorio-os-${format(new Date(), "dd-MM-yyyy-HH-mm-ss")}.pdf`
      );
      toast.success("Relatório PDF gerado com sucesso!");
    } catch (error) {
      console.error("Erro ao gerar PDF:", error);
      toast.error("Erro ao gerar relatório PDF");
    }
  };

  const exportToExcel = () => {
    try {
      const headers: string[] = [];
      const data: Record<string, string>[] = [];

      filteredData.forEach((ordem) => {
        const row: Record<string, string> = {};
        if (columns.osNumber) {
          headers.push("Nº OS");
          row["Nº OS"] = `#${ordem.osNumber}/${new Date(ordem.createdAt).getFullYear()}`;
        }
        if (columns.centroCusto) {
          headers.push("Centro de Custo");
          row["Centro de Custo"] = ordem.veiculo?.lotacao_veiculos?.centro_custo?.descricao || "N/A";
        }
        if (columns.tipoServico) {
          headers.push("Tipo de Serviço");
          row["Tipo de Serviço"] = ordem.TiposDeOs?.descricao || "N/A";
        }
        if (columns.veiculo) {
          headers.push("Veículo");
          row["Veículo"] = `${ordem.veiculo?.placa} | ${ordem.veiculo?.marca?.descricao} - ${ordem.veiculo?.modelo?.descricao}`;
        }
        if (columns.condutor) {
          headers.push("Condutor");
          row["Condutor"] = ordem.condutor?.nome || "N/A";
        }
        if (columns.credenciado) {
          headers.push("Credenciado");
          row["Credenciado"] = ordem.credenciado?.informacoes[0].razao_social || "N/A";
        }
        if (columns.data) {
          headers.push("Data");
          row["Data"] = format(new Date(ordem.createdAt), "dd/MM/yyyy HH:mm");
        }
        if (columns.status) {
          headers.push("Status");
          row["Status"] = ordem.status || "N/A";
        }
        // if (columns.valor) {
        //   headers.push("Valor Total");
        //   row["Valor Total"] = ordem.valor_total?.toLocaleString("pt-BR", {
        //     style: "currency",
        //     currency: "BRL",
        //   }) || "R$ 0,00";
        // }
        data.push(row);
      });

      const ws = XLSX.utils.json_to_sheet(data);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Relatório OS");
      const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
      const dataBlob = new Blob([excelBuffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
      saveAs(
        dataBlob,
        `relatorio-os-${format(new Date(), "dd-MM-yyyy-HH-mm-ss")}.xlsx`
      );
      toast.success("Relatório Excel gerado com sucesso!");
    } catch (error) {
      console.error("Erro ao gerar Excel:", error);
      toast.error("Erro ao gerar relatório Excel");
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Relatório Customizado</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <div className="space-y-2">
            <Label>Período Inicial</Label>
            <DateInput
              value={filters.startDate ? format(filters.startDate, "yyyy-MM-dd") : null}
              onChange={(dateStr) =>
                setFilters((prev) => ({ ...prev, startDate: dateStr ? new Date(dateStr) : null }))
              }
            />
          </div>
          <div className="space-y-2">
            <Label>Período Final</Label>
            <DateInput
                value={filters.endDate ? format(filters.endDate, "yyyy-MM-dd") : null}
                onChange={(dateStr) =>
                  setFilters((prev) => ({
                    ...prev,
                    endDate: dateStr ? new Date(dateStr) : null,
                  }))
                }
              />
          </div>
          <div className="space-y-2">
            <Label>Centro de Custo</Label>
            <Input
              value={filters.centroCusto}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, centroCusto: e.target.value }))
              }
              placeholder="Filtrar por centro de custo"
            />
          </div>
          <div className="space-y-2">
            <Label>Tipo de Serviço</Label>
            <Input
              value={filters.tipoServico}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, tipoServico: e.target.value }))
              }
              placeholder="Filtrar por tipo de serviço"
            />
          </div>
          <div className="space-y-2">
            <Label>Veículo</Label>
            <Input
              value={filters.veiculo}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, veiculo: e.target.value }))
              }
              placeholder="Filtrar por veículo"
            />
          </div>
          <div className="space-y-2">
            <Label>Condutor</Label>
            <Input
              value={filters.condutor}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, condutor: e.target.value }))
              }
              placeholder="Filtrar por condutor"
            />
          </div>
          <div className="space-y-2">
            <Label>Credenciado</Label>
            <Input
              value={filters.credenciado}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, credenciado: e.target.value }))
              }
              placeholder="Filtrar por credenciado"
            />
          </div>
          <div className="space-y-2">
            <Label>Status</Label>
            <Select
              value={filters.status}
              onValueChange={(value) =>
                setFilters((prev) => ({ ...prev, status: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione o status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todos</SelectItem>
                <SelectItem value="LANCADA">Lançada</SelectItem>
                <SelectItem value="EM_ANALISE">Em Análise</SelectItem>
                <SelectItem value="AUTORIZADA">Autorizada</SelectItem>
                <SelectItem value="EM_EXECUCAO">Em Execução</SelectItem>
                <SelectItem value="FATURADA">Faturada</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="border rounded-lg p-4 mb-6">
          <h3 className="font-medium mb-4">Colunas do Relatório</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="osNumber"
                checked={columns.osNumber}
                onCheckedChange={(checked) =>
                  setColumns((prev) => ({ ...prev, osNumber: checked as boolean }))
                }
              />
              <Label htmlFor="osNumber">Nº OS</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="centroCusto"
                checked={columns.centroCusto}
                onCheckedChange={(checked) =>
                  setColumns((prev) => ({
                    ...prev,
                    centroCusto: checked as boolean,
                  }))
                }
              />
              <Label htmlFor="centroCusto">Centro de Custo</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="tipoServico"
                checked={columns.tipoServico}
                onCheckedChange={(checked) =>
                  setColumns((prev) => ({
                    ...prev,
                    tipoServico: checked as boolean,
                  }))
                }
              />
              <Label htmlFor="tipoServico">Tipo de Serviço</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="veiculo"
                checked={columns.veiculo}
                onCheckedChange={(checked) =>
                  setColumns((prev) => ({ ...prev, veiculo: checked as boolean }))
                }
              />
              <Label htmlFor="veiculo">Veículo</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="condutor"
                checked={columns.condutor}
                onCheckedChange={(checked) =>
                  setColumns((prev) => ({ ...prev, condutor: checked as boolean }))
                }
              />
              <Label htmlFor="condutor">Condutor</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="credenciado"
                checked={columns.credenciado}
                onCheckedChange={(checked) =>
                  setColumns((prev) => ({
                    ...prev,
                    credenciado: checked as boolean,
                  }))
                }
              />
              <Label htmlFor="credenciado">Credenciado</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="data"
                checked={columns.data}
                onCheckedChange={(checked) =>
                  setColumns((prev) => ({ ...prev, data: checked as boolean }))
                }
              />
              <Label htmlFor="data">Data</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="status"
                checked={columns.status}
                onCheckedChange={(checked) =>
                  setColumns((prev) => ({ ...prev, status: checked as boolean }))
                }
              />
              <Label htmlFor="status">Status</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="valor"
                checked={columns.valor}
                onCheckedChange={(checked) =>
                  setColumns((prev) => ({ ...prev, valor: checked as boolean }))
                }
              />
              <Label htmlFor="valor">Valor Total</Label>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <Button variant="outline" onClick={exportToExcel}>
            Exportar Excel
          </Button>
          <Button onClick={exportToPDF}>Exportar PDF</Button>
        </div>

        <div className="mt-4 text-sm text-muted-foreground">
          Total de registros encontrados: {filteredData.length}
        </div>
      </CardContent>
    </Card>
  );
} 