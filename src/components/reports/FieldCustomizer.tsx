"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { GripVertical, Eye, EyeOff, ArrowUp, ArrowDown } from 'lucide-react';

export interface ReportField {
  id: string;
  label: string;
  type: 'string' | 'number' | 'date' | 'boolean' | 'currency';
  category: 'basic' | 'financial' | 'operational' | 'location' | 'maintenance';
  required?: boolean;
  description?: string;
}

interface FieldCustomizerProps {
  availableFields: ReportField[];
  selectedFields: string[];
  fieldOrder: string[];
  onFieldToggle: (fieldId: string) => void;
  onFieldReorder: (newOrder: string[]) => void;
  onSelectAll: () => void;
  onDeselectAll: () => void;
}

const categoryLabels = {
  basic: 'Informações Básicas',
  financial: 'Financeiro',
  operational: 'Operacional',
  location: 'Localização',
  maintenance: 'Manutenção'
};

const categoryColors = {
  basic: 'bg-blue-100 text-blue-800',
  financial: 'bg-green-100 text-green-800',
  operational: 'bg-orange-100 text-orange-800',
  location: 'bg-purple-100 text-purple-800',
  maintenance: 'bg-red-100 text-red-800'
};

export function FieldCustomizer({
  availableFields,
  selectedFields,
  fieldOrder,
  onFieldToggle,
  onFieldReorder,
  onSelectAll,
  onDeselectAll
}: FieldCustomizerProps) {
  const [expandedCategories, setExpandedCategories] = useState<string[]>(['basic']);

  // Função para mover campo para cima ou para baixo

  const moveField = (fieldId: string, direction: 'up' | 'down') => {
    const currentIndex = fieldOrder.indexOf(fieldId);
    if (currentIndex === -1) return;

    const newOrder = [...fieldOrder];
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;

    if (targetIndex < 0 || targetIndex >= newOrder.length) return;

    [newOrder[currentIndex], newOrder[targetIndex]] = [newOrder[targetIndex], newOrder[currentIndex]];
    onFieldReorder(newOrder);
  };

  const toggleCategory = (category: string) => {
    setExpandedCategories(prev =>
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  const getFieldsByCategory = () => {
    const categories: Record<string, ReportField[]> = {};
    availableFields.forEach(field => {
      if (!categories[field.category]) {
        categories[field.category] = [];
      }
      categories[field.category].push(field);
    });
    return categories;
  };

  const getSelectedFieldsInOrder = () => {
    return fieldOrder
      .map(fieldId => availableFields.find(f => f.id === fieldId))
      .filter((field): field is ReportField => field !== undefined && selectedFields.includes(field.id));
  };

  const fieldsByCategory = getFieldsByCategory();
  const selectedFieldsInOrder = getSelectedFieldsInOrder();

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Campos Disponíveis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Campos Disponíveis
            <div className="flex gap-2">
              <Button size="sm" variant="outline" onClick={onSelectAll}>
                Selecionar Todos
              </Button>
              <Button size="sm" variant="outline" onClick={onDeselectAll}>
                Desmarcar Todos
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {Object.entries(fieldsByCategory).map(([category, fields]) => (
            <div key={category} className="border rounded-lg">
              <button
                onClick={() => toggleCategory(category)}
                className="w-full p-3 text-left font-medium bg-gray-50 hover:bg-gray-100 rounded-t-lg flex items-center justify-between"
              >
                <span className="flex items-center gap-2">
                  <Badge className={categoryColors[category as keyof typeof categoryColors]}>
                    {categoryLabels[category as keyof typeof categoryLabels]}
                  </Badge>
                  <span className="text-sm text-gray-500">
                    ({fields.filter(f => selectedFields.includes(f.id)).length}/{fields.length})
                  </span>
                </span>
                <span className={`transform transition-transform ${expandedCategories.includes(category) ? 'rotate-180' : ''}`}>
                  ▼
                </span>
              </button>
              
              {expandedCategories.includes(category) && (
                <div className="p-3 space-y-2">
                  {fields.map(field => (
                    <div key={field.id} className="flex items-start gap-3 p-2 hover:bg-gray-50 rounded">
                      <Checkbox
                        checked={selectedFields.includes(field.id)}
                        onCheckedChange={() => onFieldToggle(field.id)}
                        disabled={field.required}
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{field.label}</span>
                          {field.required && (
                            <Badge variant="secondary" className="text-xs">Obrigatório</Badge>
                          )}
                        </div>
                        {field.description && (
                          <p className="text-sm text-gray-500 mt-1">{field.description}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Campos Selecionados e Ordem */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Campos Selecionados e Ordem
            <Badge variant="secondary">{selectedFieldsInOrder.length} campos</Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Arraste os campos para reorganizar a ordem no relatório
          </p>
        </CardHeader>
        <CardContent>
          {selectedFieldsInOrder.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <EyeOff className="mx-auto h-12 w-12 mb-2" />
              <p>Nenhum campo selecionado</p>
              <p className="text-sm">Selecione campos na lista ao lado</p>
            </div>
          ) : (
            <div className="space-y-2">
              {selectedFieldsInOrder.map((field, index) => (
                <div
                  key={field.id}
                  className="flex items-center gap-3 p-3 border rounded-lg bg-white hover:shadow-sm"
                >
                  <div className="text-gray-400">
                    <GripVertical className="h-4 w-4" />
                  </div>
                            
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2">
                                <span className="font-medium">{field.label}</span>
                                <Badge className={categoryColors[field.category]}>
                                  {categoryLabels[field.category]}
                                </Badge>
                                {field.required && (
                                  <Badge variant="secondary" className="text-xs">Obrigatório</Badge>
                                )}
                              </div>
                            </div>

                            <div className="flex items-center gap-1">
                              <span className="text-sm text-gray-500 min-w-[2rem] text-center">
                                {index + 1}
                              </span>
                              <div className="flex flex-col gap-1">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => moveField(field.id, 'up')}
                                  disabled={index === 0}
                                  className="h-6 w-6 p-0"
                                >
                                  <ArrowUp className="h-3 w-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => moveField(field.id, 'down')}
                                  disabled={index === selectedFieldsInOrder.length - 1}
                                  className="h-6 w-6 p-0"
                                >
                                  <ArrowDown className="h-3 w-3" />
                                </Button>
                              </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
