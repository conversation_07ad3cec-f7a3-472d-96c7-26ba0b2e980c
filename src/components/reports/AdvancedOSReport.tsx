"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DateInput } from '@/components/inputs/date-input';
import { FieldCustomizer, ReportField } from './FieldCustomizer';
import { useOS } from '@/context/os-context';
import { useVeiculos } from '@/context/veiculos-context';
import { useCredenciado } from '@/context/credenciado-context';
import { useTiposDeOs } from '@/context/tipos-de-os-context';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { Download, FileText, Filter, Settings } from 'lucide-react';

interface OSReportFilters {
  startDate?: string;
  endDate?: string;
  veiculoId?: string;
  credenciadoId?: string;
  tipoServicoId?: string;
  status?: string;
  tipoFrota?: string;
  localidade?: string;
  estabelecimento?: string;
}

const availableFields: ReportField[] = [
  // Campos Básicos
  { id: 'osNumber', label: 'Número da OS', type: 'string', category: 'basic', required: true },
  { id: 'createdAt', label: 'Data de Criação', type: 'date', category: 'basic', required: true },
  { id: 'status', label: 'Status', type: 'string', category: 'basic' },
  { id: 'veiculo.placa', label: 'Placa do Veículo', type: 'string', category: 'basic' },
  { id: 'condutor.nome', label: 'Condutor', type: 'string', category: 'basic' },
  
  // Tipo de Serviço e Frota
  { id: 'TiposDeOs.descricao', label: 'Tipo de Serviço', type: 'string', category: 'operational', description: 'Tipo de serviço da OS' },
  { id: 'veiculo.tipo_de_frota.descricao', label: 'Tipo de Frota', type: 'string', category: 'operational', description: 'Leve, médio, pesado, máquina, moto' },
  { id: 'tipo_manutencao', label: 'Tipo de Manutenção', type: 'string', category: 'operational' },
  
  // Localização
  { id: 'estado_de_localizacao', label: 'Estado de Localização', type: 'string', category: 'location', description: 'Estado onde a OS foi criada' },
  { id: 'cidade_de_localizacao', label: 'Cidade de Localização', type: 'string', category: 'location', description: 'Cidade onde a OS foi criada' },
  { id: 'credenciado.informacoes[0].razao_social', label: 'Estabelecimento Credenciado', type: 'string', category: 'location' },
  
  // Financeiro
  { id: 'valor_autorizado', label: 'Valor Autorizado', type: 'currency', category: 'financial' },
  { id: 'orcamento_individual', label: 'Orçamento Individual', type: 'boolean', category: 'financial' },
  
  // Centro de Custo
  { id: 'veiculo.lotacao_veiculos.centro_custo.descricao', label: 'Centro de Custo', type: 'string', category: 'basic' },
  { id: 'veiculo.lotacao_veiculos.centro_custo.razao_social', label: 'Razão Social do Centro', type: 'string', category: 'basic' },
  
  // Manutenção
  { id: 'odometro_atual', label: 'Odômetro Atual', type: 'number', category: 'maintenance' },
  { id: 'mobilizado', label: 'Mobilizado', type: 'boolean', category: 'maintenance' },
  { id: 'descricao', label: 'Descrição do Serviço', type: 'string', category: 'maintenance' },
  
  // Veículo
  { id: 'veiculo.marca.descricao', label: 'Marca do Veículo', type: 'string', category: 'basic' },
  { id: 'veiculo.modelo.descricao', label: 'Modelo do Veículo', type: 'string', category: 'basic' },
  { id: 'veiculo.ano_fab', label: 'Ano de Fabricação', type: 'string', category: 'basic' },
  { id: 'veiculo.ano_modelo', label: 'Ano do Modelo', type: 'string', category: 'basic' },
  { id: 'veiculo.tipo_de_veiculo.descricao', label: 'Tipo de Veículo', type: 'string', category: 'basic' },
  { id: 'veiculo.vin', label: 'Chassi/VIN', type: 'string', category: 'basic' },
  { id: 'veiculo.cor', label: 'Cor do Veículo', type: 'string', category: 'basic' },

  // Campos Adicionais Financeiros
  { id: 'minimun_orcament', label: 'Mínimo de Orçamentos', type: 'string', category: 'financial' },
  { id: 'quoteExpiration', label: 'Prazo de Orçamento (horas)', type: 'number', category: 'financial' },
  { id: 'quoteExpirationDate', label: 'Data de Expiração do Orçamento', type: 'date', category: 'financial' },

  // Gestão e Controle
  { id: 'gestorCriador', label: 'Gestor Criador', type: 'string', category: 'basic' },
  { id: 'gestorAprovador', label: 'Gestor Aprovador', type: 'string', category: 'basic' },
  { id: 'upDateTimedAt', label: 'Última Atualização', type: 'date', category: 'basic' },

  // Centro de Custo Adicional
  { id: 'veiculo.lotacao_veiculos.centro_custo.cnpj', label: 'CNPJ do Centro de Custo', type: 'string', category: 'basic' },
  { id: 'veiculo.lotacao_veiculos.centro_custo.nome_responsavel', label: 'Responsável do Centro', type: 'string', category: 'basic' },

  // Credenciado Adicional
  { id: 'credenciado.informacoes[0].cnpj', label: 'CNPJ do Credenciado', type: 'string', category: 'location' },
  { id: 'credenciado.informacoes[0].telefone', label: 'Telefone do Credenciado', type: 'string', category: 'location' },
];

export function AdvancedOSReport() {
  const { ordensDeServico } = useOS();
  const { veiculos } = useVeiculos();
  const { credenciados } = useCredenciado();
  const { tiposDeOs } = useTiposDeOs();

  const [filters, setFilters] = useState<OSReportFilters>({});
  const [selectedFields, setSelectedFields] = useState<string[]>([
    'osNumber', 'createdAt', 'status', 'veiculo.placa', 'TiposDeOs.descricao',
    'veiculo.tipo_de_frota.descricao', 'estado_de_localizacao', 'cidade_de_localizacao',
    'veiculo.lotacao_veiculos.centro_custo.descricao'
  ]);
  const [fieldOrder, setFieldOrder] = useState<string[]>([
    'osNumber', 'createdAt', 'status', 'veiculo.placa', 'TiposDeOs.descricao',
    'veiculo.tipo_de_frota.descricao', 'estado_de_localizacao', 'cidade_de_localizacao',
    'veiculo.lotacao_veiculos.centro_custo.descricao'
  ]);
  const [showFieldCustomizer, setShowFieldCustomizer] = useState(false);
  const [filteredData, setFilteredData] = useState<OS[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  useEffect(() => {
    if (!ordensDeServico) return;

    const filtered = ordensDeServico.filter((os) => {
      // Filtro por data
      if (filters.startDate && new Date(os.createdAt) < new Date(filters.startDate)) return false;
      if (filters.endDate && new Date(os.createdAt) > new Date(filters.endDate)) return false;
      
      // Filtro por veículo
      if (filters.veiculoId && os.veiculoId !== filters.veiculoId) return false;
      
      // Filtro por credenciado
      if (filters.credenciadoId && os.credenciadoId !== filters.credenciadoId) return false;
      
      // Filtro por tipo de serviço
      if (filters.tipoServicoId && os.tipo_de_osId !== filters.tipoServicoId) return false;
      
      // Filtro por status
      if (filters.status && os.status !== filters.status) return false;
      
      // Filtro por tipo de frota
      if (filters.tipoFrota && os.veiculo?.tipo_de_frota?.descricao !== filters.tipoFrota) return false;
      
      // Filtro por localidade (estado ou cidade)
      if (filters.localidade) {
        const localidade = filters.localidade.toLowerCase();
        const estado = os.estado_de_localizacao?.toLowerCase() || '';
        const cidade = os.cidade_de_localizacao?.toLowerCase() || '';
        if (!estado.includes(localidade) && !cidade.includes(localidade)) return false;
      }
      
      return true;
    });

    setFilteredData(filtered);
  }, [ordensDeServico, filters]);

  const handleFieldToggle = (fieldId: string) => {
    setSelectedFields(prev => {
      const field = availableFields.find(f => f.id === fieldId);
      if (field?.required) return prev; // Não permite desmarcar campos obrigatórios
      
      if (prev.includes(fieldId)) {
        // Remove o campo
        setFieldOrder(order => order.filter(id => id !== fieldId));
        return prev.filter(id => id !== fieldId);
      } else {
        // Adiciona o campo
        setFieldOrder(order => [...order, fieldId]);
        return [...prev, fieldId];
      }
    });
  };

  const handleSelectAll = () => {
    const allFieldIds = availableFields.map(f => f.id);
    setSelectedFields(allFieldIds);
    setFieldOrder(allFieldIds);
  };

  const handleDeselectAll = () => {
    const requiredFields = availableFields.filter(f => f.required).map(f => f.id);
    setSelectedFields(requiredFields);
    setFieldOrder(requiredFields);
  };

  const getFieldValue = (os: OS, fieldPath: string): any => {
    try {
      return fieldPath.split('.').reduce((obj: any, key: string) => {
        // Handle array access like informacoes[0]
        if (key.includes('[') && key.includes(']')) {
          const [arrayKey, indexStr] = key.split('[');
          const index = parseInt(indexStr.replace(']', ''));
          return obj?.[arrayKey]?.[index];
        }
        return obj?.[key];
      }, os);
    } catch {
      return '';
    }
  };

  const formatFieldValue = (value: any, field: ReportField): string => {
    if (value === null || value === undefined) return '';
    
    switch (field.type) {
      case 'date':
        return format(new Date(value), 'dd/MM/yyyy HH:mm');
      case 'currency':
        return new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL'
        }).format(Number(value) || 0);
      case 'boolean':
        return value ? 'Sim' : 'Não';
      case 'number':
        return Number(value).toLocaleString('pt-BR');
      default:
        return String(value);
    }
  };

  const generatePDF = () => {
    if (filteredData.length === 0) {
      toast.error('Nenhum dado encontrado para gerar o relatório');
      return;
    }

    setIsGenerating(true);
    try {
      const doc = new jsPDF('l', 'mm', 'a4'); // Landscape para mais colunas
      
      // Cabeçalho
      doc.setFontSize(16);
      doc.text('Relatório Avançado de Ordens de Serviço', 20, 20);
      doc.setFontSize(10);
      doc.text(`Gerado em: ${format(new Date(), 'dd/MM/yyyy HH:mm')}`, 20, 30);
      doc.text(`Total de registros: ${filteredData.length}`, 20, 35);

      // Preparar dados da tabela
      const selectedFieldsInOrder = fieldOrder.filter(fieldId => selectedFields.includes(fieldId));
      const headers = selectedFieldsInOrder.map(fieldId => {
        const field = availableFields.find(f => f.id === fieldId);
        return field?.label || fieldId;
      });

      const tableData = filteredData.map(os => {
        return selectedFieldsInOrder.map(fieldId => {
          const field = availableFields.find(f => f.id === fieldId);
          const value = getFieldValue(os, fieldId);
          return field ? formatFieldValue(value, field) : String(value || '');
        });
      });

      // Adicionar tabela
      (doc as any).autoTable({
        head: [headers],
        body: tableData,
        startY: 45,
        styles: { fontSize: 8 },
        headStyles: { fillColor: [41, 128, 185], textColor: 255 },
        columnStyles: headers.reduce((acc, _, index) => {
          acc[index] = { cellWidth: 'auto' };
          return acc;
        }, {} as any),
      });

      doc.save(`relatorio-os-avancado-${format(new Date(), 'dd-MM-yyyy-HH-mm-ss')}.pdf`);
      toast.success('Relatório PDF gerado com sucesso!');
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      toast.error('Erro ao gerar relatório PDF');
    } finally {
      setIsGenerating(false);
    }
  };

  const generateExcel = () => {
    if (filteredData.length === 0) {
      toast.error('Nenhum dado encontrado para gerar o relatório');
      return;
    }

    setIsGenerating(true);
    try {
      const wb = XLSX.utils.book_new();
      
      // Preparar dados
      const selectedFieldsInOrder = fieldOrder.filter(fieldId => selectedFields.includes(fieldId));
      const excelData = filteredData.map(os => {
        const row: any = {};
        selectedFieldsInOrder.forEach(fieldId => {
          const field = availableFields.find(f => f.id === fieldId);
          const value = getFieldValue(os, fieldId);
          row[field?.label || fieldId] = field ? formatFieldValue(value, field) : String(value || '');
        });
        return row;
      });

      const ws = XLSX.utils.json_to_sheet(excelData);
      XLSX.utils.book_append_sheet(wb, ws, 'Ordens de Serviço');

      // Adicionar aba de informações
      const infoData = [
        { Campo: 'Total de Registros', Valor: filteredData.length },
        { Campo: 'Data de Geração', Valor: format(new Date(), 'dd/MM/yyyy HH:mm') },
        { Campo: 'Filtros Aplicados', Valor: Object.keys(filters).length },
      ];
      const wsInfo = XLSX.utils.json_to_sheet(infoData);
      XLSX.utils.book_append_sheet(wb, wsInfo, 'Informações');

      XLSX.writeFile(wb, `relatorio-os-avancado-${format(new Date(), 'dd-MM-yyyy-HH-mm-ss')}.xlsx`);
      toast.success('Relatório Excel gerado com sucesso!');
    } catch (error) {
      console.error('Erro ao gerar Excel:', error);
      toast.error('Erro ao gerar relatório Excel');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="container mx-auto py-6 px-4 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Relatório Avançado de OS</h1>
          <p className="text-gray-600">
            Relatório personalizado com filtros avançados e campos customizáveis
          </p>
        </div>
        <Button
          onClick={() => setShowFieldCustomizer(!showFieldCustomizer)}
          variant="outline"
          className="flex items-center gap-2"
        >
          <Settings className="h-4 w-4" />
          {showFieldCustomizer ? 'Ocultar' : 'Personalizar'} Campos
        </Button>
      </div>

      {/* Customizador de Campos */}
      {showFieldCustomizer && (
        <Card>
          <CardHeader>
            <CardTitle>Personalização de Campos e Ordem</CardTitle>
          </CardHeader>
          <CardContent>
            <FieldCustomizer
              availableFields={availableFields}
              selectedFields={selectedFields}
              fieldOrder={fieldOrder}
              onFieldToggle={handleFieldToggle}
              onFieldReorder={setFieldOrder}
              onSelectAll={handleSelectAll}
              onDeselectAll={handleDeselectAll}
            />
          </CardContent>
        </Card>
      )}

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros Avançados
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <Label>Data Inicial</Label>
              <DateInput
                value={filters.startDate}
                onChange={(date) => setFilters(prev => ({ ...prev, startDate: date }))}
              />
            </div>
            
            <div>
              <Label>Data Final</Label>
              <DateInput
                value={filters.endDate}
                onChange={(date) => setFilters(prev => ({ ...prev, endDate: date }))}
              />
            </div>

            <div>
              <Label>Veículo</Label>
              <Select
                value={filters.veiculoId || ''}
                onValueChange={(value) => setFilters(prev => ({ ...prev, veiculoId: value || undefined }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Todos os veículos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Todos os veículos</SelectItem>
                  {veiculos.map(veiculo => (
                    <SelectItem key={veiculo.id} value={veiculo.id}>
                      {veiculo.placa} - {veiculo.marca?.descricao} {veiculo.modelo?.descricao}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Tipo de Serviço</Label>
              <Select
                value={filters.tipoServicoId || ''}
                onValueChange={(value) => setFilters(prev => ({ ...prev, tipoServicoId: value || undefined }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Todos os tipos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Todos os tipos</SelectItem>
                  {tiposDeOs.map(tipo => (
                    <SelectItem key={tipo.id} value={tipo.id}>
                      {tipo.descricao}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Estabelecimento Credenciado</Label>
              <Select
                value={filters.credenciadoId || ''}
                onValueChange={(value) => setFilters(prev => ({ ...prev, credenciadoId: value || undefined }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Todos os credenciados" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Todos os credenciados</SelectItem>
                  {credenciados.map(credenciado => (
                    <SelectItem key={credenciado.id} value={credenciado.id}>
                      {credenciado.informacoes[0]?.razao_social}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Localidade (Estado/Cidade)</Label>
              <Input
                placeholder="Digite estado ou cidade"
                value={filters.localidade || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, localidade: e.target.value || undefined }))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Resultados e Ações */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Resultados ({filteredData.length} registros)</span>
            <div className="flex gap-2">
              <Button
                onClick={generatePDF}
                disabled={isGenerating || filteredData.length === 0}
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                {isGenerating ? 'Gerando...' : 'PDF'}
              </Button>
              <Button
                onClick={generateExcel}
                disabled={isGenerating || filteredData.length === 0}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                {isGenerating ? 'Gerando...' : 'Excel'}
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredData.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="mx-auto h-12 w-12 mb-2" />
              <p>Nenhum registro encontrado com os filtros aplicados</p>
            </div>
          ) : (
            <div className="text-sm text-gray-600">
              <p>Campos selecionados: {selectedFields.length}</p>
              <p>Registros encontrados: {filteredData.length}</p>
              <p>Use os botões acima para gerar o relatório nos formatos desejados</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
