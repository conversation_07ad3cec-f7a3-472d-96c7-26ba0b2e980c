"use client";

import * as React from "react";
import { useState } from "react";
import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { useOS } from "@/context/os-context";
import { useVeiculos } from "@/context/veiculos-context";
import { useCondutor } from "@/context/condutor-context";
import { useCredenciado } from "@/context/credenciado-context";

interface FilterOptions {
  startDate?: Date;
  endDate?: Date;
  veiculoId?: number;
  condutorId?: number;
  credenciadoId?: number;
  status?: string;
  placa?: string;
  marca?: string;
  modelo?: string;
  centroCusto?: string;
  nomeCondutor?: string;
  razaoSocial?: string;
  unidade?: string;
  subunidade?: string;
  localManutencao?: string;
  tipoManutencao?: string;
  componente?: string;
  periodicity?: 'daily' | 'weekly' | 'monthly' | 'yearly';
}

interface SelectedFields {
  os: {
    osNumber: boolean;
    createdAt: boolean;
    status: boolean;
    valor_total: boolean;
    veiculo: boolean;
    condutor: boolean;
    credenciado: boolean;
    tipo: boolean;
    unidade: boolean;
    subunidade: boolean;
    localManutencao: boolean;
    componentes: boolean;
    vidaUtil: boolean;
  };
  veiculo: {
    placa: boolean;
    marca: boolean;
    modelo: boolean;
    centroCusto: boolean;
    unidade: boolean;
    subunidade: boolean;
    historicoManutencao: boolean;
    vidaUtilRestante: boolean;
  };
  condutor: {
    nome: boolean;
    unidade: boolean;
    subunidade: boolean;
  };
  credenciado: {
    razaoSocial: boolean;
    localidade: boolean;
    servicosPrestados: boolean;
  };
  manutencao: {
    tipo: boolean;
    local: boolean;
    componentes: boolean;
    pecas: boolean;
    custos: boolean;
    dataRealizacao: boolean;
    proximaManutencao: boolean;
  };
}

interface ItemManutencao {
  descricao: string;
  estado: string;
  quantidade: number;
  valor_unitario: number;
}

interface ComponenteManutencao {
  nome: string;
  estado: string;
  vidaUtilRestante: number;
}

interface ReportConfig {
  includeOS: boolean;
  includeVeiculos: boolean;
  includeCondutores: boolean;
  includeCredenciados: boolean;
  showGraphs: boolean;
}


export function CustomReportBuilder() {
  const { ordensDeServico } = useOS();
  const { veiculos } = useVeiculos();
  const { condutores } = useCondutor();
  const { credenciados } = useCredenciado();

  const [filters, setFilters] = useState<FilterOptions>({});
  const [config, setConfig] = useState<ReportConfig>({
    includeOS: true,
    includeVeiculos: true,
    includeCondutores: true,
    includeCredenciados: true,
    showGraphs: true,
  });

  const [selectedFields, setSelectedFields] = useState<SelectedFields>({
    os: {
      osNumber: true,
      createdAt: true,
      status: true,
      valor_total: true,
      veiculo: true,
      condutor: true,
      credenciado: true,
      tipo: true,
      unidade: true,
      subunidade: true,
      localManutencao: true,
      componentes: true,
      vidaUtil: true
    },
    veiculo: {
      placa: true,
      marca: true,
      modelo: true,
      centroCusto: true,
      unidade: true,
      subunidade: true,
      historicoManutencao: true,
      vidaUtilRestante: true
    },
    condutor: {
      nome: true,
      unidade: true,
      subunidade: true
    },
    credenciado: {
      razaoSocial: true,
      localidade: true,
      servicosPrestados: true
    },
    manutencao: {
      tipo: true,
      local: true,
      componentes: true,
      pecas: true,
      custos: true,
      dataRealizacao: true,
      proximaManutencao: true
    }
  });

  const generateMaintenanceReport = (doc: jsPDF) => {
    doc.addPage();
    const headers: string[] = [];
    const fields: string[] = [];

    if (selectedFields.manutencao.tipo) { headers.push("Tipo"); fields.push("tipo"); }
    if (selectedFields.manutencao.local) { headers.push("Local"); fields.push("local"); }
    if (selectedFields.manutencao.dataRealizacao) { headers.push("Data Realização"); fields.push("dataRealizacao"); }
    if (selectedFields.manutencao.proximaManutencao) { headers.push("Próxima Manutenção"); fields.push("proximaManutencao"); }
    if (selectedFields.manutencao.custos) { headers.push("Custos Totais"); fields.push("custos.total"); }

    const manutencoes = ordensDeServico
      .filter((os) => os.TiposDeOs?.descricao.toLowerCase().includes("manutenção"))
      .map((os) => ({
        tipo: os.TiposDeOs?.descricao,
        local: os.credenciado?.informacoes[0]?.razao_social,
        dataRealizacao: os.createdAt,
        proximaManutencao: calcularProximaManutencao(os.createdAt.toString(), os.TiposDeOs?.descricao),
        // componentes: os.itens?.map(
        //   (item: ItemManutencao): ComponenteManutencao => ({
        //     nome: item.descricao,
        //     estado: item.estado,
        //     vidaUtilRestante: calcularVidaUtilRestante(item),
        //   })
        // ),
      }));

    autoTable(doc, {
      head: [headers],
      body: manutencoes.map(m => {
        return fields.map(field => {
          const value = field.split('.').reduce((obj: any, key: string) => obj?.[key], m);
          return value?.toString() || "";
        });
      }),
    });

    // Adiciona seção de componentes e peças se selecionado
    // if (selectedFields.manutencao.componentes || selectedFields.manutencao.pecas) {
    //   doc.addPage();
    //   const componentHeaders = ["Componente", "Estado", "Vida Útil Restante"];
      
    //   manutencoes.forEach(m => {
    //     if (m.componentes?.length) {
    //       doc.text(`Componentes da Manutenção - ${m.dataRealizacao}`, 14, 20);
    //       autoTable(doc, {
    //         head: [componentHeaders],
    //         body: m.componentes?.map((c: ComponenteManutencao) => [c.nome, c.estado, `${c.vidaUtilRestante}%`]) || [],
    //         startY: 30
    //       });
    //     }
    //   });
    // }
  };

  const generateLifecycleReport = (doc: jsPDF) => {
    doc.addPage();
    const headers = ["Modelo", "Vida Útil Média", "Custo Médio Manutenção", "Próxima Manutenção Prevista"];
    
    const modelosData = veiculos.reduce((acc: any, v) => {
      const modelo = v.modelo?.descricao || 'Não especificado';
      if (!acc[modelo]) {
        acc[modelo] = {
          manutencoes: [],
          custoTotal: 0,
          count: 0
        };
      }
      
      const manutencoes = ordensDeServico.filter(
        (os) =>
          os.veiculo?.id === v.id && os.TiposDeOs?.descricao.toLowerCase().includes("manutenção")
      );
      
      acc[modelo].manutencoes.push(...manutencoes);
      acc[modelo].custoTotal += manutencoes.reduce((sum, os) => {
        const orcamentos = Array.isArray(os.orcamentos) ? os.orcamentos : [];
        const totalOrcamentos = orcamentos.reduce((subSum, orcamento) => subSum + orcamento.valorTotal, 0);
        return sum + totalOrcamentos;
      }, 0);
      acc[modelo].count++;
      
      return acc;
    }, {});

    const lifecycleData = Object.entries(modelosData).map(([modelo, data]: [string, any]) => {
      const vidaUtilMedia = calcularVidaUtilMedia(data.manutencoes);
      const custoMedioManutencao = data.custoTotal / data.count;
      const proximaManutencao = preverProximaManutencao(data.manutencoes);
      
      return [
        modelo,
        `${vidaUtilMedia} meses`,
        `R$ ${custoMedioManutencao.toFixed(2)}`,
        proximaManutencao
      ];
    });

    autoTable(doc, {
      head: [headers],
      body: lifecycleData
    });
  };

  const generatePDF = () => {
    const doc = new jsPDF();
    
    if (config.includeOS) {
      const filteredOS = (ordensDeServico).filter((os) => {
        if (filters.startDate && new Date(os.createdAt) < filters.startDate) return false;
        if (filters.endDate && new Date(os.createdAt) > filters.endDate) return false;
        if (filters.veiculoId && Number(os.veiculo?.id) !== filters.veiculoId) return false;
        if (filters.condutorId && Number(os.condutor?.id) !== filters.condutorId) return false;
        if (filters.credenciadoId && Number(os.credenciado?.id) !== filters.credenciadoId) return false;
        if (filters.status && os.status !== filters.status) return false;
        if (filters.placa && os.veiculo?.placa !== filters.placa) return false;
        if (filters.marca && os.veiculo?.marca?.descricao !== filters.marca) return false;
        if (filters.modelo && os.veiculo?.modelo?.descricao !== filters.modelo) return false;
        if (filters.centroCusto && os.veiculo?.lotacao_veiculos?.centro_custo?.descricao !== filters.centroCusto) return false;
        if (filters.nomeCondutor && os.condutor?.nome !== filters.nomeCondutor) return false;
        if (filters.razaoSocial && os.credenciado?.informacoes[0]?.razao_social !== filters.razaoSocial) return false;
        return true;
      });

      const headers: string[] = [];
      const fields: string[] = [];

      if (selectedFields.os.osNumber) { headers.push("Nº OS"); fields.push("osNumber"); }
      if (selectedFields.os.createdAt) { headers.push("Data"); fields.push("createdAt"); }
      if (selectedFields.os.status) { headers.push("Status"); fields.push("status"); }
      if (selectedFields.os.valor_total) { headers.push("Valor Total"); fields.push("valor_total"); }
      if (selectedFields.os.veiculo) { headers.push("Veículo"); fields.push("veiculo.placa"); }
      if (selectedFields.os.condutor) { headers.push("Condutor"); fields.push("condutor.nome"); }
      if (selectedFields.os.credenciado) { headers.push("Credenciado"); fields.push("credenciado.informacoes[0].razao_social"); }
      if (selectedFields.os.tipo) { headers.push("Tipo"); fields.push("TiposDeOs.descricao"); }

      autoTable(doc, {
        head: [headers],
        body: filteredOS.map((os) => {
          return fields.map((field: string) => {
            const value = field.split('.').reduce((obj: any, key: string) => obj?.[key], os);
            return value?.toString() || "";
          });
        }),
      });

      if (config.showGraphs) {
        doc.addPage();
        // Aqui você pode adicionar gráficos usando uma biblioteca como Chart.js
        // e convertê-los para imagens para incluir no PDF
      }
    }

    if (config.includeVeiculos) {
      doc.addPage();
      const headers: string[] = [];
      const fields: string[] = [];

      if (selectedFields.veiculo.placa) { headers.push("Placa"); fields.push("placa"); }
      if (selectedFields.veiculo.marca) { headers.push("Marca"); fields.push("marca.descricao"); }
      if (selectedFields.veiculo.modelo) { headers.push("Modelo"); fields.push("modelo.descricao"); }
      if (selectedFields.veiculo.centroCusto) { headers.push("Centro de Custo"); fields.push("lotacao_veiculos.centro_custo.descricao"); }

      autoTable(doc, {
        head: [headers],
        body: veiculos.map((v) => {
          return fields.map((field: string) => {
            const value = field.split('.').reduce((obj: any, key: string) => obj?.[key], v);
            return value?.toString() || "";
          });
        }),
      });
    }

    // Adiciona relatório de manutenção
    generateMaintenanceReport(doc);

    // Adiciona relatório de ciclo de vida
    generateLifecycleReport(doc);

    if (config.includeCondutores) {
      doc.addPage();
      const headers = selectedFields.condutor.nome ? ["Nome"] : [];
      autoTable(doc, {
        head: [headers],
        body: condutores.map(c => selectedFields.condutor.nome ? [c.nome || ""] : []),
      });
    }

    if (config.includeCredenciados) {
      doc.addPage();
      const headers = selectedFields.credenciado.razaoSocial ? ["Razão Social"] : [];
      autoTable(doc, {
        head: [headers],
        body: credenciados.map(c => selectedFields.credenciado.razaoSocial ? [c.informacoes[0]?.razao_social || ""] : []),
      });
    }

    doc.save("relatorio-customizado.pdf");
  };

  const generateExcel = () => {
    const wb = XLSX.utils.book_new();

    if (config.includeOS) {
      const filteredOS = (ordensDeServico ).filter((os: OS) => {
        if (filters.startDate && new Date(os.createdAt) < filters.startDate) return false;
        if (filters.endDate && new Date(os.createdAt) > filters.endDate) return false;
        if (filters.veiculoId && Number(os.veiculo?.id) !== filters.veiculoId) return false;
        if (filters.condutorId && Number(os.condutor?.id) !== filters.condutorId) return false;
        if (filters.credenciadoId && Number(os.credenciado?.id) !== filters.credenciadoId) return false;
        if (filters.status && os.status !== filters.status) return false;
        if (filters.placa && os.veiculo?.placa !== filters.placa) return false;
        if (filters.marca && os.veiculo?.marca?.descricao !== filters.marca) return false;
        if (filters.modelo && os.veiculo?.modelo?.descricao !== filters.modelo) return false;
        if (
          filters.centroCusto &&
          os.veiculo?.lotacao_veiculos?.centro_custo?.descricao !== filters.centroCusto
        )
          return false;
        if (filters.nomeCondutor && os.condutor?.nome !== filters.nomeCondutor) return false;
        if (
          filters.razaoSocial &&
          os.credenciado?.informacoes[0]?.razao_social !== filters.razaoSocial
        )
          return false;
        return true;
      });

      const wsOS = XLSX.utils.json_to_sheet(
        filteredOS.map(os => {
          const row: any = {};
          if (selectedFields.os.osNumber) row["Nº OS"] = os.osNumber;
          if (selectedFields.os.createdAt) row["Data"] = os.createdAt;
          if (selectedFields.os.status) row["Status"] = os.status;
          // if (selectedFields.os.valor_total) row["Valor Total"] = os.valor_total;
          if (selectedFields.os.veiculo) row["Veículo"] = os.veiculo?.placa;
          if (selectedFields.os.condutor) row["Condutor"] = os.condutor?.nome;
          if (selectedFields.os.credenciado) row["Credenciado"] = os.credenciado?.informacoes[0]?.razao_social;
          if (selectedFields.os.tipo) row["Tipo"] = os.TiposDeOs?.descricao;
          return row;
        })
      );
      XLSX.utils.book_append_sheet(wb, wsOS, "Ordens de Serviço");
    }

    if (config.includeVeiculos) {
      const wsVeiculos = XLSX.utils.json_to_sheet(
        veiculos.map(v => {
          const row: any = {};
          if (selectedFields.veiculo.placa) row["Placa"] = v.placa;
          if (selectedFields.veiculo.marca) row["Marca"] = v.marca?.descricao;
          if (selectedFields.veiculo.modelo) row["Modelo"] = v.modelo?.descricao;
          if (selectedFields.veiculo.centroCusto) row["Centro de Custo"] = v.lotacao_veiculos?.centro_custo?.descricao;
          return row;
        })
      );
      XLSX.utils.book_append_sheet(wb, wsVeiculos, "Veículos");
    }

    if (config.includeCondutores) {
      const wsCondutores = XLSX.utils.json_to_sheet(
        condutores.map(c => ({
          ...(selectedFields.condutor.nome && { Nome: c.nome }),
        }))
      );
      XLSX.utils.book_append_sheet(wb, wsCondutores, "Condutores");
    }

    if (config.includeCredenciados) {
      const wsCredenciados = XLSX.utils.json_to_sheet(
        credenciados.map(c => ({
          ...(selectedFields.credenciado.razaoSocial && { "Razão Social": c.informacoes[0]?.razao_social }),
        }))
      );
      XLSX.utils.book_append_sheet(wb, wsCredenciados, "Credenciados");
    }

    const wbout = XLSX.write(wb, { bookType: "xlsx", type: "array" });
    const blob = new Blob([wbout], { type: "application/octet-stream" });
    saveAs(blob, "relatorio-customizado.xlsx");
  };

  // Funções auxiliares
  const calcularProximaManutencao = (dataUltima: string, tipo: string | undefined): string => {
    const data = new Date(dataUltima);
    switch(tipo?.toLowerCase()) {
      case 'manutenção preventiva':
        data.setMonth(data.getMonth() + 6);
        break;
      case 'revisão':
        data.setMonth(data.getMonth() + 3);
        break;
      default:
        data.setMonth(data.getMonth() + 12);
    }
    return data.toLocaleDateString();
  };

  const calcularVidaUtilRestante = (item: ItemManutencao): number => {
    // Implementar lógica específica para cada tipo de componente
    return 100; // Placeholder
  };

  const calcularVidaUtilMedia = (manutencoes: any[]): number => {
    if (!manutencoes.length) return 0;
    // Implementar cálculo baseado no histórico de manutenções
    return 60; // Placeholder: 60 meses
  };

  const preverProximaManutencao = (manutencoes: any[]): string => {
    if (!manutencoes.length) return 'Não há dados suficientes';
    const ultimaManutencao = manutencoes
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0];
    return calcularProximaManutencao(ultimaManutencao.createdAt, ultimaManutencao.TiposDeOs?.descricao);
  };

  return (
    <div className="p-4">
      <h2 className="text-2xl font-bold mb-4">Gerador de Relatórios Customizados</h2>
      
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="font-semibold mb-2">Filtros</h3>
            <div className="space-y-2">
              <input
                type="date"
                onChange={(e) => setFilters(f => ({ ...f, startDate: e.target.value ? new Date(e.target.value) : undefined }))}
                className="w-full p-2 border rounded"
                placeholder="Data Inicial"
              />
              <input
                type="date"
                onChange={(e) => setFilters(f => ({ ...f, endDate: e.target.value ? new Date(e.target.value) : undefined }))}
                className="w-full p-2 border rounded"
                placeholder="Data Final"
              />
              <input
                type="text"
                onChange={(e) => setFilters(f => ({ ...f, placa: e.target.value || undefined }))}
                className="w-full p-2 border rounded"
                placeholder="Placa"
              />
              <input
                type="text"
                onChange={(e) => setFilters(f => ({ ...f, marca: e.target.value || undefined }))}
                className="w-full p-2 border rounded"
                placeholder="Marca"
              />
              <input
                type="text"
                onChange={(e) => setFilters(f => ({ ...f, modelo: e.target.value || undefined }))}
                className="w-full p-2 border rounded"
                placeholder="Modelo"
              />
              <input
                type="text"
                onChange={(e) => setFilters(f => ({ ...f, centroCusto: e.target.value || undefined }))}
                className="w-full p-2 border rounded"
                placeholder="Centro de Custo"
              />
              <input
                type="text"
                onChange={(e) => setFilters(f => ({ ...f, nomeCondutor: e.target.value || undefined }))}
                className="w-full p-2 border rounded"
                placeholder="Nome do Condutor"
              />
              <input
                type="text"
                onChange={(e) => setFilters(f => ({ ...f, razaoSocial: e.target.value || undefined }))}
                className="w-full p-2 border rounded"
                placeholder="Razão Social"
              />
              <select
                onChange={(e) => setFilters(f => ({ ...f, status: e.target.value || undefined }))}
                className="w-full p-2 border rounded"
              >
                <option value="">Selecione o Status</option>
                <option value="ABERTA">Aberta</option>
                <option value="EM_ANDAMENTO">Em Andamento</option>
                <option value="CONCLUIDA">Concluída</option>
                <option value="CANCELADA">Cancelada</option>
              </select>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold mb-2">Campos do Relatório</h3>
            <div className="space-y-4">
              {config.includeOS && (
                <div>
                  <h4 className="font-medium mb-1">Ordens de Serviço</h4>
                  <div className="space-y-1">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedFields.os.osNumber}
                        onChange={(e) => setSelectedFields(f => ({ ...f, os: { ...f.os, osNumber: e.target.checked } }))}
                        className="mr-2"
                      />
                      Número da OS
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedFields.os.createdAt}
                        onChange={(e) => setSelectedFields(f => ({ ...f, os: { ...f.os, createdAt: e.target.checked } }))}
                        className="mr-2"
                      />
                      Data
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedFields.os.status}
                        onChange={(e) => setSelectedFields(f => ({ ...f, os: { ...f.os, status: e.target.checked } }))}
                        className="mr-2"
                      />
                      Status
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedFields.os.valor_total}
                        onChange={(e) => setSelectedFields(f => ({ ...f, os: { ...f.os, valor_total: e.target.checked } }))}
                        className="mr-2"
                      />
                      Valor Total
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedFields.os.veiculo}
                        onChange={(e) => setSelectedFields(f => ({ ...f, os: { ...f.os, veiculo: e.target.checked } }))}
                        className="mr-2"
                      />
                      Veículo
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedFields.os.condutor}
                        onChange={(e) => setSelectedFields(f => ({ ...f, os: { ...f.os, condutor: e.target.checked } }))}
                        className="mr-2"
                      />
                      Condutor
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedFields.os.credenciado}
                        onChange={(e) => setSelectedFields(f => ({ ...f, os: { ...f.os, credenciado: e.target.checked } }))}
                        className="mr-2"
                      />
                      Credenciado
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedFields.os.tipo}
                        onChange={(e) => setSelectedFields(f => ({ ...f, os: { ...f.os, tipo: e.target.checked } }))}
                        className="mr-2"
                      />
                      Tipo
                    </label>
                  </div>
                </div>
              )}

              {config.includeVeiculos && (
                <div>
                  <h4 className="font-medium mb-1">Veículos</h4>
                  <div className="space-y-1">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedFields.veiculo.placa}
                        onChange={(e) => setSelectedFields(f => ({ ...f, veiculo: { ...f.veiculo, placa: e.target.checked } }))}
                        className="mr-2"
                      />
                      Placa
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedFields.veiculo.marca}
                        onChange={(e) => setSelectedFields(f => ({ ...f, veiculo: { ...f.veiculo, marca: e.target.checked } }))}
                        className="mr-2"
                      />
                      Marca
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedFields.veiculo.modelo}
                        onChange={(e) => setSelectedFields(f => ({ ...f, veiculo: { ...f.veiculo, modelo: e.target.checked } }))}
                        className="mr-2"
                      />
                      Modelo
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedFields.veiculo.centroCusto}
                        onChange={(e) => setSelectedFields(f => ({ ...f, veiculo: { ...f.veiculo, centroCusto: e.target.checked } }))}
                        className="mr-2"
                      />
                      Centro de Custo
                    </label>
                  </div>
                </div>
              )}

              {config.includeCondutores && (
                <div>
                  <h4 className="font-medium mb-1">Condutores</h4>
                  <div className="space-y-1">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedFields.condutor.nome}
                        onChange={(e) => setSelectedFields(f => ({ ...f, condutor: { ...f.condutor, nome: e.target.checked } }))}
                        className="mr-2"
                      />
                      Nome
                    </label>
                  </div>
                </div>
              )}

              {config.includeCredenciados && (
                <div>
                  <h4 className="font-medium mb-1">Credenciados</h4>
                  <div className="space-y-1">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedFields.credenciado.razaoSocial}
                        onChange={(e) => setSelectedFields(f => ({ ...f, credenciado: { ...f.credenciado, razaoSocial: e.target.checked } }))}
                        className="mr-2"
                      />
                      Razão Social
                    </label>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        
        <div className="flex space-x-4">
          <button
            onClick={generatePDF}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Exportar PDF
          </button>
          <button
            onClick={generateExcel}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Exportar Excel
          </button>
        </div>
      </div>
    </div>
  );
} 