"use client";

import React, { useEffect, useRef } from "react";
import Chart from "chart.js/auto";

interface ReportChartProps {
  type: "os" | "veiculos" | "credenciados" | "condutores" | "centro-custo" | "resumo-financeiro";
  data: any[];
  containerId: string;
}

export function ReportChart({ type, data, containerId }: ReportChartProps) {
  const chartRef = useRef<HTMLCanvasElement>(null);
  const chartInstance = useRef<Chart | null>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    // Limpe o gráfico anterior se existir
    if (chartInstance.current) {
      chartInstance.current.destroy();
    }

    // Prepare os dados com base no tipo de relatório - adicionando tipos explícitos
    let chartData: {
      labels: string[];
      datasets: {
        data: number[];
        backgroundColor: string[];
      }[];
    } = {
      labels: [],
      datasets: [
        {
          data: [],
          backgroundColor: [
            "#FF6384",
            "#36A2EB",
            "#FFCE56",
            "#4BC0C0",
            "#9966FF",
            "#FF9F40",
          ],
        },
      ],
    };

    let chartTitle = "";

    switch (type) {
      case "os":
        chartTitle = "Distribuição de Ordens de Serviço";
        // Agrupa por status ou tipo de serviço
        const serviceTypes = data.reduce<Record<string, number>>(
          (acc, item) => {
            const type = item.tipo_servico || "Não especificado";
            acc[type] = (acc[type] || 0) + 1;
            return acc;
          },
          {}
        );

        chartData.labels = Object.keys(serviceTypes);
        chartData.datasets[0].data = Object.values(serviceTypes);
        break;

      case "veiculos":
        chartTitle = "Distribuição de Veículos por Marca";
        // Agrupa por marca
        const marcas = data.reduce<Record<string, number>>((acc, item) => {
          const marca = item.marca || "Não especificado";
          acc[marca] = (acc[marca] || 0) + 1;
          return acc;
        }, {});

        chartData.labels = Object.keys(marcas);
        chartData.datasets[0].data = Object.values(marcas);
        break;

      case "credenciados":
        chartTitle = "Distribuição de Credenciados por Tipo";
        // Agrupa por tipo
        const tipoCredenciados = data.reduce<Record<string, number>>(
          (acc, item) => {
            const tipo = item.tipo || "Não especificado";
            acc[tipo] = (acc[tipo] || 0) + 1;
            return acc;
          },
          {}
        );

        chartData.labels = Object.keys(tipoCredenciados);
        chartData.datasets[0].data = Object.values(tipoCredenciados);
        break;

      case "condutores":
        chartTitle = "Condutores por Status";
        // Agrupa por status
        const statusCondutores = data.reduce<Record<string, number>>(
          (acc, item) => {
            const status = item.status || "Não especificado";
            acc[status] = (acc[status] || 0) + 1;
            return acc;
          },
          {}
        );

        chartData.labels = Object.keys(statusCondutores);
        chartData.datasets[0].data = Object.values(statusCondutores);
        break;

      case "centro-custo":
        chartTitle = "Centros de Custo por Status";
        // Agrupa por status
        const statusCentros = data.reduce<Record<string, number>>(
          (acc, item) => {
            const status = item.active ? "Ativo" : "Inativo";
            acc[status] = (acc[status] || 0) + 1;
            return acc;
          },
          {}
        );

        chartData.labels = Object.keys(statusCentros);
        chartData.datasets[0].data = Object.values(statusCentros);
        break;
    }

    // Criar o gráfico
    chartInstance.current = new Chart(chartRef.current, {
      type: "pie",
      data: chartData,
      options: {
        responsive: true,
        plugins: {
          legend: {
            position: "bottom",
          },
          title: {
            display: true,
            text: chartTitle,
          },
        },
      },
    });

    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [data, type]);

  return (
    <div id={containerId} className="w-full max-h-80 p-4 bg-white rounded-md">
      <canvas ref={chartRef}></canvas>
    </div>
  );
}
