import { useState, useRef } from "react";
import {
  ChevronDown,
  ChevronUp,
  RefreshCw,
  Search,
  SlidersHorizontal,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn, formatDate } from "@/lib/utils";
import { ExportTo } from "@/components/buttons/export-to";
import { useReactToPrint } from "react-to-print";
import { saveAs } from "file-saver";
import { utils as XLSXUtils, writeFile as xlsxWriteFile } from "xlsx";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import html2canvas from "html2canvas";
import { toast } from "sonner";
import { Label } from "@radix-ui/react-label";

// Correção: Definição apropriada para jsPDF com autoTable
interface JsPDFWithAutoTable extends jsPDF {
  lastAutoTable?: {
    finalY: number;
  };
}

interface DetailData {
  id: string;
  numeroOrcamento: string;
  credenciado: string;
  localManutencao: string;
  codigo: string;
  descricao: string;
  marca: string;
  vencimento: string;
  quantidade: number;
  valorItem: number;
  valorDesconto: number;
  valorNegociado: number;
  valorTotal: number;
  percentualDesconto: string; // Novo campo
  tipoVeiculo: string; // Novo campo
  tipoFrota: string; // Novo campo
  porteVeiculo: string; // Novo campo
  gestorAprovador: string;
  condutor: string;
}

interface VeiculoTableRow {
  id: string;
  osNumber: string;
  centro_custo: string;
  placa: string;
  marca: string;
  tipoVeiculo: string; // Novo campo
  tipoFrota: string; // Novo campo
  porteVeiculo: string; // Novo campo
  dataOS: string;
  dataSaida?: string;
  tipoManutencao?: string;
}

interface ExpandableRowProps {
  rowData: VeiculoTableRow;
  detailsData: DetailData[];
  isExpanded: boolean;
  toggleExpand: () => void;
}

interface ExpandableTableProps {
  data: {
    vehicles: VeiculoTableRow[];
    details: Record<string | number, DetailData[]>;
  };
}

// Componente de linha expansível
const ExpandableRow = ({
  rowData,
  detailsData,
  isExpanded,
  toggleExpand,
}: ExpandableRowProps) => {
  return (
    <>
      <TableRow className="group border-b">
        <TableCell className="w-10">
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleExpand}
            aria-label={isExpanded ? "Recolher" : "Expandir"}
          >
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </TableCell>
        <TableCell>{rowData.osNumber}</TableCell>
        <TableCell>{rowData.centro_custo}</TableCell>
        <TableCell>{rowData.placa}</TableCell>
        <TableCell>{rowData.marca}</TableCell>
        <TableCell>{rowData.tipoVeiculo}</TableCell>
        <TableCell>{rowData.tipoFrota}</TableCell>
        <TableCell>{rowData.porteVeiculo}</TableCell>
        <TableCell>{rowData.tipoManutencao}</TableCell>
        <TableCell>{rowData.dataOS}</TableCell>
      </TableRow>

      {isExpanded && (
        <TableRow>
          <TableCell colSpan={9} className="p-0">
            <div className="p-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Orçamento</TableHead>
                    <TableHead>Credenciado</TableHead>
                    <TableHead>Local de Manutenção</TableHead>
                    <TableHead>Código</TableHead>
                    <TableHead>Descrição</TableHead>
                    <TableHead>Marca</TableHead>
                    <TableHead>Vencimento Garantia</TableHead>
                    <TableHead>Quantidade</TableHead>
                    <TableHead>Valor Item</TableHead>
                    <TableHead>Valor Desconto</TableHead>
                    <TableHead>% Desconto</TableHead>
                    <TableHead>Valor Negociado</TableHead>
                    <TableHead>Valor Total</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {detailsData.map((detail) => (
                    <TableRow key={detail.id}>
                      <TableCell>{detail.numeroOrcamento}</TableCell>
                      <TableCell>{detail.credenciado}</TableCell>
                      <TableCell>{detail.localManutencao}</TableCell>
                      <TableCell>{detail.codigo || "-"}</TableCell>
                      <TableCell>{detail.descricao}</TableCell>
                      <TableCell>{detail.marca}</TableCell>
                      <TableCell>{detail.vencimento}</TableCell>
                      <TableCell>{detail.quantidade}</TableCell>
                      <TableCell>
                        R$ {detail.valorItem.toFixed(2).replace(".", ",")}
                      </TableCell>
                      <TableCell>
                        R$ {detail.valorDesconto.toFixed(2).replace(".", ",")}
                      </TableCell>
                      <TableCell>
                        <span className="font-medium text-green-600">
                          {detail.percentualDesconto}%
                        </span>
                      </TableCell>
                      <TableCell>
                        R$ {detail.valorNegociado.toFixed(2).replace(".", ",")}
                      </TableCell>
                      <TableCell>
                        R$ {detail.valorTotal.toFixed(2).replace(".", ",")}
                      </TableCell>
                    </TableRow>
                  ))}
                  {/* Linha de total */}
                  <TableRow>
                    <TableCell
                      colSpan={12}
                      className="text-right font-semibold"
                    >
                      Total
                    </TableCell>
                    <TableCell className="font-semibold">
                      R${" "}
                      {detailsData
                        .reduce((sum, item) => sum + item.valorTotal, 0)
                        .toFixed(2)
                        .replace(".", ",")}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </TableCell>
        </TableRow>
      )}
    </>
  );
};

// Componente principal da tabela
export function ExpandableTable({ data }: ExpandableTableProps) {
  const [expandedRows, setExpandedRows] = useState<
    Record<string | number, boolean>
  >({});
  const [searchQuery, setSearchQuery] = useState("");
  const [allExpanded, setAllExpanded] = useState(false);
  const [tipo, setTipo] = useState("");

  // Correção: Tipo específico para o contentRef
  const contentRef = useRef<HTMLDivElement>(null);

  const reactToPrintFn = useReactToPrint({
    documentTitle: "Histórico de Manutenção",
    onPrintError: () => toast.error("Erro ao imprimir"),
  });

  // Função para alternar a expansão de uma linha
  const toggleExpand = (id: string | number) => {
    setExpandedRows((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  // Função para expandir ou recolher todas as linhas
  const toggleAllRows = () => {
    const newExpandedState = !allExpanded;
    setAllExpanded(newExpandedState);

    const newExpandedRows: Record<string | number, boolean> = {};
    data.vehicles.forEach((vehicle) => {
      newExpandedRows[vehicle.id] = newExpandedState;
    });

    setExpandedRows(newExpandedRows);
  };

  // Filtrar veículos baseado na busca
  const filteredVehicles = data.vehicles.filter((vehicle) => {
    const searchTerm = searchQuery.toLowerCase();

    const matchesSearch =
      vehicle.osNumber.toLowerCase().includes(searchTerm) ||
      vehicle.centro_custo.toLowerCase().includes(searchTerm) ||
      vehicle.placa.toLowerCase().includes(searchTerm) ||
      vehicle.marca.toLowerCase().includes(searchTerm) ||
      vehicle.modelo.toLowerCase().includes(searchTerm) ||
      vehicle.dataOS.toLowerCase().includes(searchTerm);

    const matchesTipo = tipo === "" || vehicle.tipoManutencao === tipo;

    return matchesSearch && matchesTipo;
  });

  // Função de exportação adaptada do data-table
  const handleExport = (format: "csv" | "excel" | "pdf" | "print") => {
    if (filteredVehicles.length === 0) {
      toast.error("Não há dados para exportar");
      return;
    }

    // Impressão direta usando o hook useReactToPrint
    if (format === "print") {
      reactToPrintFn();
      return;
    }

    try {
      // Preparar dados para exportação em formato plano
      const exportData: Record<string, any>[] = [];

      filteredVehicles.forEach((vehicle) => {
        const details = data.details[vehicle.id] || [];

        if (details.length === 0) {
          // Se não tem detalhes, adiciona apenas a linha principal
          exportData.push({
            "OS Número": vehicle.osNumber,
            "Centro de Custo": vehicle.centro_custo,
            Placa: vehicle.placa,
            Marca: vehicle.marca,
            "Tipo Manutenção": vehicle.tipoManutencao,
            "Data da OS": vehicle.dataOS,
            "Informações Adicionais": "Sem detalhes disponíveis",
            "Data de Aprovação": vehicle.dataAprovacao
              ? formatDate(new Date(vehicle?.dataAprovacao))
              : "-",
            "Data de encerramento": vehicle.dataSaida
              ? formatDate(new Date(vehicle?.dataSaida))
              : "-",
          });
        } else {
          // Para cada detalhe, inclui uma linha no exportData
          details.forEach((detail) => {
            // No trecho de exportação onde são definidos os dados para exportação
            exportData.push({
              "OS Número": vehicle.osNumber,
              "Centro de Custo": vehicle.centro_custo,
              Placa: vehicle.placa,
              Marca: vehicle.marca,
              "Tipo Veículo": vehicle.tipoVeiculo,
              "Tipo Frota": vehicle.tipoFrota,
              "Porte Veículo": vehicle.porteVeiculo,
              "Tipo Manutenção": vehicle.tipoManutencao,
              "Data da OS": vehicle.dataOS,
              Orçamento: detail.numeroOrcamento,
              Credenciado: detail.credenciado, // Novo campo
              "Local Manutenção": detail.localManutencao, // Novo campo
              Código: detail.codigo || "-",
              Descrição: detail.descricao,
              "Marca Item": detail.marca,
              Vencimento: detail.vencimento,
              Quantidade: detail.quantidade,
              "Valor Item": `R$ ${detail.valorItem
                .toFixed(2)
                .replace(".", ",")}`,
              "Valor Desconto": `R$ ${detail.valorDesconto
                .toFixed(2)
                .replace(".", ",")}`,
              "% Desconto": `${detail.percentualDesconto}%`,
              "Valor Negociado": `R$ ${detail.valorNegociado
                .toFixed(2)
                .replace(".", ",")}`,
              "Valor Total": `R$ ${detail.valorTotal
                .toFixed(2)
                .replace(".", ",")}`,
              "Gestor Aprovador": detail.gestorAprovador || "-",
              Condutor: detail.condutor,
              "Data de Aprovação": vehicle.dataAprovacao
                ? formatDate(new Date(vehicle?.dataAprovacao))
                : "-",
              "Data de encerramento": vehicle.dataSaida
                ? formatDate(new Date(vehicle?.dataSaida))
                : "-",
            });
          });
        }
      });
      // Processar de acordo com o formato solicitado
      if (format === "csv") {
        // Exportar para CSV
        const header = Object.keys(exportData[0]);
        const csvRows = [
          header.join(","),
          ...exportData.map((row) =>
            header.map((field) => JSON.stringify(row[field] ?? "")).join(",")
          ),
        ];

        const blob = new Blob([csvRows.join("\n")], {
          type: "text/csv;charset=utf-8;",
        });

        saveAs(blob, "historico-manutencao.csv");
      } else if (format === "excel") {
        // Exportar para Excel
        const worksheet = XLSXUtils.json_to_sheet(exportData);
        const workbook = XLSXUtils.book_new();
        XLSXUtils.book_append_sheet(
          workbook,
          worksheet,
          "Histórico Manutenção"
        );
        xlsxWriteFile(workbook, "historico-manutencao.xlsx");
      } else if (format === "pdf") {
        // Exportar para PDF
        const doc = new jsPDF({
          orientation: "landscape",
        }) as JsPDFWithAutoTable;

        // Título do documento
        doc.setFontSize(16);
        doc.text("Histórico de Manutenção de Veículos", 14, 15);

        // Data de geração
        doc.setFontSize(10);
        doc.text(
          `Gerado em: ${new Date().toLocaleDateString("pt-BR")}`,
          14,
          22
        );

        // Verifica se existe um chartDivMan para incluir no PDF
        const chartDiv = document.getElementById("chartDivMan");
        const yStartPosition = chartDiv ? 100 : 30;

        // Se houver um gráfico, adiciona-o ao PDF
        if (chartDiv) {
          html2canvas(chartDiv)
            .then((canvas) => {
              const chartDataURL = canvas.toDataURL("image/png", 1.0);
              doc.addImage(chartDataURL, "PNG", 14, 30, 260, 60);

              // Adiciona a tabela após o gráfico
              addTableToPdf(doc, exportData, yStartPosition);
              doc.save("historico-manutencao.pdf");
            })
            .catch((err) => {
              console.error("Erro ao capturar gráfico:", err);
              // Se falhar ao capturar o gráfico, só adiciona a tabela
              addTableToPdf(doc, exportData, 30);
              doc.save("historico-manutencao.pdf");
            });
        } else {
          // Sem gráfico, só adiciona a tabela
          addTableToPdf(doc, exportData, 30);
          doc.save("historico-manutencao.pdf");
        }
      }

      toast.success(`Exportação para ${format.toUpperCase()} concluída`);
    } catch (err) {
      console.error("Erro na exportação:", err);
      toast.error("Erro ao exportar dados");
    }
  };

  // Função auxiliar para adicionar tabela ao PDF
  const addTableToPdf = (
    doc: JsPDFWithAutoTable,
    data: Record<string, any>[],
    startY: number
  ) => {
    if (data.length === 0) return;

    const headers = Object.keys(data[0]);
    const body = data.map((row) => headers.map((header) => row[header] || ""));

    autoTable(doc, {
      head: [headers],
      body: body,
      startY: startY,
      styles: { fontSize: 8 },
      headStyles: { fillColor: [22, 160, 133] },
      margin: { horizontal: 14 },
    });
  };

  // Calcular o número total de registros
  const totalRegistros = filteredVehicles.length;

  return (
    <div className="w-full space-y-4">
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="relative w-full sm:w-72">
          <Input
            type="text"
            placeholder="Busca rápida"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pr-8"
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <Search className="h-4 w-4 opacity-50" />
          </div>
        </div>

        <div className="flex flex-wrap gap-2 justify-end">
          <div className="space-y-2">
            <Select value={tipo} onValueChange={setTipo}>
              <SelectTrigger>
                <SelectValue placeholder="Corretiva, preditiva..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Corretiva">Corretiva</SelectItem>
                <SelectItem value="Preventiva">Preventiva</SelectItem>
                <SelectItem value="Preditiva">Preditiva</SelectItem>
                <SelectItem value="Sinistro">Sinistro</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button variant="outline" size="sm">
            <SlidersHorizontal className="h-4 w-4 mr-2" />
            Colunas
          </Button>

          <ExportTo exportTo={handleExport} />

          <Button size="sm">
            <Search className="h-4 w-4 mr-2" />
            Pesquisar
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={toggleAllRows}
            className="whitespace-nowrap"
          >
            {allExpanded ? (
              <>
                <ChevronUp className="h-4 w-4 mr-2" />
                Recolher todos
              </>
            ) : (
              <>
                <ChevronDown className="h-4 w-4 mr-2" />
                Expandir todos
              </>
            )}
          </Button>

          <Button variant="outline" size="icon">
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div ref={contentRef} className="border rounded-md overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-10"></TableHead>
                <TableHead>Ordem de Serviço</TableHead>
                <TableHead>Centro de custo</TableHead>
                <TableHead>Placa</TableHead>
                <TableHead>Marca</TableHead>
                <TableHead>Tipo Veículo</TableHead>
                <TableHead>Tipo Frota</TableHead>
                <TableHead>Porte Veículo</TableHead>
                <TableHead>Tipo Manutenção</TableHead>
                <TableHead>Data da OS</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredVehicles.map((vehicle) => (
                <ExpandableRow
                  key={vehicle.id}
                  rowData={vehicle}
                  detailsData={data.details[vehicle.id] || []}
                  isExpanded={!!expandedRows[vehicle.id]}
                  toggleExpand={() => toggleExpand(vehicle.id)}
                />
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-sm">
          Exibindo de 1 a {totalRegistros} de {totalRegistros} registros
        </div>

        <div className="flex items-center gap-1">
          <Button variant="outline" size="icon" disabled>
            <span>«</span>
          </Button>
          <Button variant="outline" size="icon" disabled>
            <span>‹</span>
          </Button>
          <Button
            variant="outline"
            size="icon"
            className={cn("relative", "bg-primary/10")}
          >
            1
          </Button>
          <Button variant="outline" size="icon" disabled>
            <span>›</span>
          </Button>
          <Button variant="outline" size="icon" disabled>
            <span>»</span>
          </Button>

          <Select defaultValue="10">
            <SelectTrigger className="w-16">
              <SelectValue placeholder="10" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}
