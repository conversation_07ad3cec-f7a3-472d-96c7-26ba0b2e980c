"use client";

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
  SortingState,
  VisibilityState,
  getSortedRowModel,
  RowSelectionState,
  RowModel,
  Table as TanstackTable,
} from "@tanstack/react-table";
import Link from "next/link";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useReactToPrint } from "react-to-print";
import html2canvas from "html2canvas";
import { DataTablePagination } from "../navigation/table-pagination";
import { DataTableViewOptions } from "./data-table-column-options";
import { useState, forwardRef, useImperativeHandle } from "react";
import { Input } from "../ui/input";
import { ExportTo } from "../buttons/export-to";
import { AnyZodObject, ZodSchema, ZodType } from "zod";
import { FieldConfig, GenericFormsInput } from "../inputs/generic-forms-input";
import { MapPinX, Plus, Search } from "lucide-react";
import { toast } from "sonner";
import { Button } from "../ui/button";
import { ModalForm } from "../forms/modalForms/modal-forms";
import { utils as XLSXUtils, writeFile as xlsxWriteFile } from "xlsx";
import { saveAs } from "file-saver";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import React from "react";
import { ReportFilters, ReportModal } from "../modal/report-modal";
import { ReportChart } from "./report-cart";
import { formatData } from "@/utils/format";
import Chart from "chart.js/auto";
import { useEffect, useRef } from 'react';

export interface DataTableMethods<TData> {
  getSelectedRowModel: () => RowModel<TData>;
  getSelectedRows: () => TData[];
  resetRowSelection: () => void;
  getTable: () => TanstackTable<TData>;
}
export interface ServerSidePaginationProps {
  enabled: boolean;
  currentPage: number;
  totalPages: number;
  totalItems: number;
  perPage: number;
  onPageChange: (page: number) => void;
  onPerPageChange: (perPage: number) => void;
  onSearch: (searchTerm: string) => void;
}

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  isLoading?: boolean;
  total?: {
    recebido: number;
    gasto: number;
    saldo: number;
  };
  exportTo?: boolean;
  newItem?: {
    name?: string;
    schema: ZodSchema;
    defaultValues: Record<string, any>;
    fieldConfig: Record<string, FieldConfig<ZodSchema>>;
  };
  onNewItem?: (newItem: any) => void;
  handleRowClick?: (data: TData) => void;
  onRowContextMenu?: (event: React.MouseEvent, rowData: TData) => void;
  onClick?: () => void;
  renderSubComponent?: ({ row }: { row: any }) => React.ReactNode;
  disablePagination?: boolean;
  showToolbar?: boolean;
  showSearch?: boolean;
  className?: string;
  modalClassName?: string;
  modalFormClassName?: string;
  showMapButton?: boolean;
  reportType?:
    | "os"
    | "veiculos"
    | "credenciados"
    | "condutores"
    | "centro-custo"
    | "resumo-financeiro";
  showReportButton?: boolean;
  serverSidePagination?: ServerSidePaginationProps;
  showFilterButton?: boolean;
  setShowFilterDialog?: (show: boolean) => void;
}

export const DataTable = forwardRef<
  DataTableMethods<any>,
  DataTableProps<any, any>
>(function DataTable<TData, TValue>(
  {
    columns,
    data = [],
    total,
    isLoading = false,
    exportTo,
    newItem,
    onNewItem,
    handleRowClick,
    onRowContextMenu,
    onClick,
    renderSubComponent,
    disablePagination = false,
    showToolbar = true,
    showSearch = true,
    className = "",
    modalClassName,
    modalFormClassName,
    showMapButton = false,
    reportType,
    showReportButton = false,
    serverSidePagination,
    showFilterButton = false,
    setShowFilterDialog,
  }: DataTableProps<TData, TValue>,
  ref: React.ForwardedRef<DataTableMethods<TData>>
) {
  const contentRef = useRef<HTMLTableElement>(null);
  const reactToPrintFn = useReactToPrint({ contentRef });
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [globalFilter, setGlobalFilter] = useState<string>("");
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [reportData, setReportData] = useState<TData[]>([]);
  const [showReportPreview, setShowReportPreview] = useState(false);

  const getAvailableFields = () => {
    // Obter campos das colunas da tabela (código existente)
    const columnOptions = columns
      .filter((col) => "accessorKey" in col || "accessorFn" in col)
      .map((col) => {
        const key = "accessorKey" in col ? String(col.accessorKey) : "";
        const label =
          typeof col.header === "string"
            ? col.header
            : key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, " ");

        return { value: key, label };
      })
      .filter((item) => item.value);

    let availableFields: any = [];

    // Se encontrou opções nas colunas, use-as
    if (columnOptions.length > 0) {
      availableFields = [...columnOptions];
    } else if (data.length > 0) {
      // Caso contrário, extrai campos do primeiro item dos dados
      const firstItem = data[0] as Record<string, any>;
      availableFields = Object.keys(firstItem)
        .filter((key) => typeof firstItem[key] !== "object")
        .map((key) => ({
          value: key,
          label: key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, " "),
        }));
    } else {
      // Sem dados disponíveis
      availableFields = [];
    }
    interface FieldOption {
      value: string;
      label: string;
    }

    if (reportType === "credenciados") {
      const requiredFields = [
        { value: "razao_social", label: "Razão Social" },
        { value: "nome_fantasia", label: "Nome Fantasia" },
        { value: "cnpj", label: "CNPJ" },
        { value: "atividade_principal", label: "Atividade Principal" },
        { value: "servicos_oferecidos", label: "Serviços Oferecidos" },
        { value: "contratos", label: "Contratos" },
        { value: "telefone", label: "Telefone" },
        { value: "email", label: "Email" },
        { value: "endereco_cidade", label: "Endereço/Cidade" },
      ];

      // Adicionar campos necessários que ainda não existem
      requiredFields.forEach((field) => {
        if (
          !availableFields.some(
            (f: FieldOption) => f.value === (field as FieldOption).value
          )
        ) {
          availableFields.push(field as FieldOption);
        }
      });

      // Remover explicitamente o campo endereco.cidade se existir
      availableFields = availableFields.filter(
        (f: FieldOption) => f.value !== "endereco.cidade"
      );
    } else if (reportType === "veiculos") {
      // Campos obrigatórios do Requisito 47 - Composição de frota por órgão/unidade contratante
      const requiredFields = [
        { value: "centro_de_custo", label: "Centro de Custo/Órgão Contratante" },
        { value: "ano_fab", label: "Ano de Fabricação" },
        { value: "tipo_de_veiculo", label: "Tipo de Veículo" },
        { value: "modelo", label: "Modelo do Veículo" },
        { value: "marca", label: "Marca" },
        { value: "placa", label: "Placa" },
        { value: "data_compra", label: "Data de Compra" },
        { value: "data_cedencia", label: "Data de Cedência" },
        { value: "valor_depreciacao", label: "Depreciação" },
        { value: "valor_mercado", label: "Valor de Mercado" },
        { value: "condutor_principal", label: "Motorista Principal" },
        { value: "renovam", label: "Renavam" },
        { value: "vin", label: "VIN/Chassi" },
        { value: "numero_do_motor", label: "Número do Motor" },
        { value: "matricula", label: "Matrícula" },
        { value: "ano_modelo", label: "Ano do Modelo" },
        { value: "cor", label: "Cor" },
        { value: "tipo_de_frota", label: "Tipo de Frota" },
        { value: "odometro_atual", label: "Odômetro Atual" },
        { value: "status", label: "Status" },
        { value: "valor_venal", label: "Valor Venal" },
        { value: "codigo_fipe", label: "Código FIPE" },
        { value: "combustivel", label: "Combustível" },
        { value: "potencia", label: "Potência" },
        { value: "cilindradas", label: "Cilindradas" },
        { value: "transmissao", label: "Transmissão" },
        { value: "quantidade_de_portas", label: "Portas" },
        { value: "quantidade_de_assentos", label: "Assentos" },
      ];

      // Adicionar campos necessários que ainda não existem
      requiredFields.forEach((field) => {
        if (
          !availableFields.some(
            (f: FieldOption) => f.value === (field as FieldOption).value
          )
        ) {
          availableFields.push(field as FieldOption);
        }
      });
    }

    return availableFields;
  };

  const handleGenerateReport = (filters: ReportFilters) => {
    // Filtra os dados com base na data, se aplicável
    let filteredData = [...data] as any[];

    // Verificar diferentes campos de data possíveis
    if (data.length > 0 && data[0]) {
      filteredData = filteredData.filter((item: any) => {
        // Verificar qual campo de data está disponível
        let itemDate: Date | null = null;

        if ("createdAt" in item) {
          itemDate = new Date(item.createdAt);
        } else if ("data_criacao" in item) {
          itemDate = new Date(item.data_criacao);
        } else if ("data" in item) {
          itemDate = new Date(item.data);
        }

        // Se encontrou um campo de data válido, aplica o filtro
        if (itemDate) {
          return (
            itemDate >= filters.dateRange.from &&
            itemDate <= filters.dateRange.to
          );
        }

        return true; // Se não encontrou campo de data, mantém o item
      });
    }

    // Ordena os dados com base no campo selecionado
    if (filters.sortBy) {
      filteredData.sort((a: any, b: any) => {
        if (a[filters.sortBy] < b[filters.sortBy]) return -1;
        if (a[filters.sortBy] > b[filters.sortBy]) return 1;
        return 0;
      });
    }

    setReportData(filteredData);
    setShowReportPreview(true);

    // Gera o PDF depois que o componente de visualização for renderizado
    setTimeout(() => generateReportPDF(filteredData, filters), 500);
  };

  // Função para obter colunas do relatório com base nas colunas da tabela e campos selecionados
  const getColumnsForReport = (selectedFields?: string[]) => {
    // Se campos selecionados foram fornecidos, usa-os prioritariamente
    if (selectedFields && selectedFields.length > 0) {
      const availableFields = getAvailableFields();
      return selectedFields.map((fieldKey) => {
        // Tenta encontrar o label do campo nos campos disponíveis
        interface FieldOption {
          value: string;
          label: string;
        }

        const field: FieldOption | undefined = availableFields.find(
          (f: FieldOption) => f.value === fieldKey
        );
        const label =
          field?.label ||
          fieldKey.charAt(0).toUpperCase() +
            fieldKey.slice(1).replace(/_/g, " ");
        return { key: fieldKey, label };
      });
    }

    // Pegue todas as colunas visíveis da tabela atual
    const visibleColumns = table.getVisibleFlatColumns();

    // Mapeie apenas colunas com acessor (ignorando ações, select, etc)
    const tableColumns = visibleColumns
      .filter((col) => {
        const colDef = col.columnDef;
        return (
          ("accessorKey" in colDef || "accessorFn" in colDef) &&
          col.id !== "select" &&
          col.id !== "actions"
        );
      })
      .map((col) => {
        const colDef = col.columnDef;
        // Determina a chave baseada em accessorKey ou id da coluna
        const key =
          "accessorKey" in colDef ? String(colDef.accessorKey) : col.id;

        // Determina o rótulo baseado no header ou na key formatada
        let label = "";
        if (typeof colDef.header === "string") {
          label = colDef.header;
        } else if ("accessorKey" in colDef) {
          label =
            String(colDef.accessorKey).charAt(0).toUpperCase() +
            String(colDef.accessorKey).slice(1).replace(/_/g, " ");
        } else {
          label =
            col.id.charAt(0).toUpperCase() + col.id.slice(1).replace(/_/g, " ");
        }

        return { key, label };
      })
      .filter((col) => col.key);

    // Garanta pelo menos 8 colunas relevantes, limitando a um máximo razoável para o PDF
    let columnLimit = 8;

    if (reportType === "veiculos") {
      // Colunas específicas para relatório de veículos, incluindo odômetro
      return [
        { key: "placa", label: "Placa" },
        { key: "marca", label: "Marca" },
        { key: "modelo", label: "Modelo" },
        { key: "centro_de_custo", label: "Centro de Custo" },
        { key: "cidade", label: "Cidade" },
        { key: "odometro", label: "Odômetro" }, // Adicionado campo de odômetro
        { key: "valor_venal", label: "Valor Venal (R$)" },
        { key: "status", label: "Status" },
        { key: "ano_de_fabricacao", label: "Ano Fabricação" },
      ];
    } else if (reportType === "os") {
      // Colunas específicas para relatório de ordens de serviço - CAMPOS SOLICITADOS
      return [
        { key: "osNumber", label: "Número OS" },
        { key: "status", label: "Status" },
        { key: "data", label: "Data de Criação" },
        { key: "placa", label: "Placa" },
        { key: "marca", label: "Marca" },
        { key: "modelo", label: "Modelo" },
        { key: "tipo_servico", label: "Tipo de Serviço" },
        { key: "tipo_de_frota", label: "Tipo de Frota" },
        { key: "tipo_manutencao", label: "Tipo de Manutenção" },
        { key: "estado_localizacao", label: "Estado de Localização" },
        { key: "cidade_localizacao", label: "Cidade de Localização" },
        { key: "centro_de_custo", label: "Centro de Custo" },
        { key: "credenciado", label: "Credenciado" },
        { key: "condutor", label: "Condutor" },
        { key: "gestor_criador", label: "Gestor Criador" },
      ];
    } else if (reportType === "credenciados") {
      return [
        { key: "razao_social", label: "Razão Social" },
        { key: "nome_fantasia", label: "Nome Fantasia" },
        { key: "cnpj", label: "CNPJ" },
        { key: "atividade_principal", label: "Atividade Principal" },
        { key: "servicos_oferecidos", label: "Serviços Oferecidos" },
        { key: "telefone", label: "Telefone" },
        { key: "email", label: "Email" },
        { key: "endereco_cidade", label: "Endereço/Cidade" },
        { key: "celular", label: "Celular" },
      ];
    } else if (reportType === "condutores") {
      // Colunas específicas para relatório de condutores
      return [
        { key: "nome", label: "Nome" },
        { key: "matricula", label: "Matrícula" },
        { key: "cpf", label: "CPF" },
        { key: "contato", label: "Contato" },
        { key: "email", label: "Email" },
        { key: "centro_de_custo", label: "Centro de Custo" },
        { key: "cidade", label: "Cidade" },
        { key: "status", label: "Status" },
      ];
    } else if (reportType === "centro-custo") {
      // Colunas específicas para relatório de centros de custo
      return [
        { key: "descricao", label: "Descrição" },
        { key: "cnpj", label: "CNPJ" },
        { key: "razao_social", label: "Razão Social" },
        { key: "responsavel", label: "Responsável" },
        { key: "contato", label: "Contato" },
        { key: "email", label: "E-mail" },
        { key: "dotacao_orcamentaria", label: "Dotação Orçamentária" },
        { key: "valor_dotacao", label: "Valor Dotação" },
        { key: "status", label: "Status" },
      ];
    }

    // Se não for um tipo específico ou as colunas definidas acima não existirem, usa o fallback
    if (tableColumns.length === 0 && data.length > 0) {
      const firstItem = data[0] as Record<string, any>;
      return Object.keys(firstItem)
        .filter((key) => typeof firstItem[key] !== "object")
        .slice(0, columnLimit)
        .map((key) => ({
          key,
          label: key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, " "),
        }));
    }

    return tableColumns.slice(0, columnLimit);
  };

  // Função para adicionar tabela ao PDF
  const addTableToPdf = (
    doc: jsPDF,
    data: TData[],
    columns: { key: string; label: string }[],
    filters: ReportFilters
  ) => {
    // Cria uma função de acesso para obter os dados de forma segura
    const accessProperty = (obj: any, path: string): any => {
      // Tratamento especial para campos específicos de cada tipo de relatório
      if (reportType === "veiculos") {
        // Campos obrigatórios do Requisito 47
        if (path === "centro_de_custo")
          return obj.lotacao_veiculos?.centro_custo?.descricao || "";
        if (path === "ano_fab") return obj.ano_fab || "";
        if (path === "tipo_de_veiculo") return obj.tipo_de_veiculo?.descricao || "";
        if (path === "modelo") return obj.modelo?.descricao || "";
        if (path === "marca") return obj.marca?.descricao || "";
        if (path === "placa") return obj.placa || "";
        if (path === "data_compra") return obj.data_compra ? new Date(obj.data_compra).toLocaleDateString("pt-BR") : "N/A";
        if (path === "data_cedencia") return obj.data_cedencia ? new Date(obj.data_cedencia).toLocaleDateString("pt-BR") : "N/A";
        if (path === "valor_depreciacao") {
          // Cálculo estimado de depreciação (10% ao ano)
          if (obj.ano_fab && obj.valor_venal) {
            const anoAtual = new Date().getFullYear();
            const anoFab = parseInt(obj.ano_fab);
            const idadeVeiculo = anoAtual - anoFab;
            const valorVenal = parseFloat(obj.valor_venal) || 0;
            const depreciacaoEstimada = valorVenal * (idadeVeiculo * 0.1);
            return depreciacaoEstimada.toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            });
          }
          return "A calcular";
        }
        if (path === "valor_mercado") {
          // Usa valor venal como base para valor de mercado
          const valor = obj.valor_venal || obj.codigo_fipe?.valor_venal || 0;
          return typeof valor === "number"
            ? valor.toLocaleString("pt-BR", {
                style: "currency",
                currency: "BRL",
              })
            : "A consultar";
        }
        if (path === "condutor_principal") {
          // Busca condutor principal através das OS mais recentes
          if (obj.os && obj.os.length > 0) {
            const osComCondutor = obj.os
              .filter((os: any) => os.condutor?.nome)
              .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
            if (osComCondutor.length > 0) {
              return osComCondutor[0].condutor.nome;
            }
          }
          return "Não definido";
        }
        if (path === "renovam") return obj.renovam || "";
        if (path === "vin") return obj.vin || "";
        if (path === "numero_do_motor") return obj.numero_do_motor || "";
        if (path === "matricula") return obj.matricula || "";
        if (path === "ano_modelo") return obj.ano_modelo || "";
        if (path === "cor") return obj.cor || "";
        if (path === "tipo_de_frota") return obj.tipo_de_frota?.descricao || "";
        if (path === "odometro_atual") return obj.odometro_atual || obj.odometro || obj.km || "";
        if (path === "status") return obj.status || "";
        if (path === "valor_venal") {
          const valor = obj.valor_venal || obj.codigo_fipe?.valor_venal || 0;
          return typeof valor === "number"
            ? valor.toLocaleString("pt-BR", {
                style: "currency",
                currency: "BRL",
              })
            : valor;
        }
        if (path === "codigo_fipe") return obj.codigo_fipe?.codigo_fipe || "";
        if (path === "combustivel") {
          if (obj.combustivel?.tipos_de_combustiveis) {
            return Array.isArray(obj.combustivel.tipos_de_combustiveis)
              ? obj.combustivel.tipos_de_combustiveis.join(", ")
              : obj.combustivel.tipos_de_combustiveis;
          }
          return "";
        }
        if (path === "potencia") return obj.definicoes?.potencia || "";
        if (path === "cilindradas") return obj.definicoes?.cilindradas || "";
        if (path === "transmissao") return obj.definicoes?.transmissao || "";
        if (path === "quantidade_de_portas") return obj.definicoes?.quantidade_de_portas || "";
        if (path === "quantidade_de_assentos") return obj.definicoes?.quantidade_de_assentos || "";

        // Campos legados para compatibilidade
        if (path === "cidade") return obj.lotacao_veiculos?.cidade || "";
        if (path === "ano_de_fabricacao") return obj.ano_fab || "";
        if (path === "odometro") return obj.odometro || obj.km || "";
      } else if (reportType === "os") {
        // Mapeia os nomes dos campos disponíveis para as propriedades corretas - CAMPOS SOLICITADOS
        if (path === "osNumber") return obj.osNumber || obj.numero || "";
        if (path === "status") return obj.status || "";
        if (path === "data") return obj.createdAt || "";
        if (path === "placa") return obj.veiculo?.placa || "";
        if (path === "marca") return obj.veiculo?.marca?.descricao || "";
        if (path === "modelo") return obj.veiculo?.modelo?.descricao || "";
        if (path === "tipo_servico") return obj.TiposDeOs?.descricao || obj.tipos_de_os?.descricao || "";
        if (path === "tipo_de_frota") return obj.veiculo?.tipo_de_frota?.descricao || "";
        if (path === "tipo_manutencao") return obj.tipo_manutencao || "";
        if (path === "estado_localizacao") return obj.estado_de_localizacao || "";
        if (path === "cidade_localizacao") return obj.cidade_de_localizacao || "";
        if (path === "centro_de_custo") return obj.veiculo?.lotacao_veiculos?.centro_custo?.descricao || "";
        if (path === "credenciado") return obj.credenciado?.informacoes?.[0]?.razao_social || "";
        if (path === "condutor") return obj.condutor?.nome || "";
        if (path === "gestor_criador") return obj.gestorCriador || "";

        // Campos legados para compatibilidade
        if (path === "veículo") return obj.veiculo?.placa || "";
        if (path === "tipo_de_serviço") return obj.TiposDeOs?.descricao || obj.tipos_de_os?.descricao || obj.tipo_servico || "";
        if (path === "orçamentista") return obj.credenciado?.informacoes?.[0]?.razao_social || "";
        if (path === "cidade") return obj.cidade_loc || obj.cidade_de_localizacao || "";
        if (path === "cidade_loc") return obj.cidade_loc || obj.cidade_de_localizacao || "";
      } else if (reportType === "credenciados") {
        if (path === "razao_social")
          return obj.informacoes?.[0]?.razao_social || "";
        if (path === "nome_fantasia")
          return obj.informacoes?.[0]?.nome_fantasia || "";
        if (path === "atividade_principal")
          return obj.informacoes?.[0]?.atividade_principal || "";
        if (path === "cnpj") return obj.informacoes?.[0]?.cnpj || "";
        if (path === "telefone") return obj.contato?.telefone || "";
        if (path === "celular") return obj.contato?.celular || "";
        if (path === "email") return obj.contato?.email || "";
        if (path === "endereco_cidade") {
          const cidade = obj.endereco?.cidade || "";
          const estado = obj.endereco?.estado || "";
          return cidade && estado ? `${cidade}/${estado}` : cidade || "";
        }
        if (path === "servicos_oferecidos") {
          if (obj.servicos && obj.servicos.length > 0) {
            return obj.servicos.map((s: any) => s.descricao).join(", ");
          }
          return "Nenhum serviço registrado";
        }
        if (path === "contratos") {
          if (obj.contratos && obj.contratos.length > 0) {
            return obj.contratos
              .map(
                (c: any) =>
                  c.contrato?.razao_social || c.contrato?.nome_contrato || ""
              )
              .filter((nome: string) => nome !== "")
              .join(", ");
          }
          return "Nenhum contrato associado";
        }
        // Mantendo para compatibilidade
        if (path === "cidade") return obj.endereco?.cidade || "";
        if (path === "estado") return obj.endereco?.estado || "";
        if (path === "veiculos_atendidos") {
          const tiposVeiculo = obj.informacoes?.[0]?.tipos_veiculo || [];
          return tiposVeiculo.length === 0 ? "Todos" : tiposVeiculo;
        }
        if (path === "servicos_oferecidos") {
          return obj.servicos && obj.servicos.length > 0 ? obj.servicos : "-";
        }
        if (path === "tipo") {
          if (obj.servicos && obj.servicos.length > 0) {
            return obj.servicos.map((s: any) => s.descricao).join(", ");
          }
          return obj.informacoes?.[0]?.atividade_principal || "";
        }
        if (path === "status") return obj.ativo ? "Ativo" : "Inativo";
      } else if (reportType === "condutores") {
        if (path === "nome") return obj.nome || "";
        if (path === "matricula") return obj.matricula || "";
        if (path === "cpf") return obj.cpf || "";
        if (path === "contato") return obj.contato || "";
        if (path === "email") return obj.email || "";
        if (path === "lotacao_condutor")
          return obj.lotacao_condutor?.centro_custo?.descricao || "";
        if (path === "cidade") return obj.endereco?.cidade || "";
        if (path === "ativo") return obj.status ? "Ativo" : "Inativo";
        if (path === "cnh_categoria") return obj.cnh?.categoria || "";
        if (path === "cnh_validade") {
          if (obj.cnh?.validade) {
            return new Date(obj.cnh.validade).toLocaleDateString("pt-BR");
          }
          return "";
        }
      } else if (reportType === "centro-custo") {
        if (path === "descricao") return obj.descricao || "";
        if (path === "cnpj") return obj.cnpj || "";
        if (path === "razao_social") return obj.razao_social || "";
        if (path === "responsavel") return obj.nome_responsavel || "";
        if (path === "contato") return obj.contato || "";
        if (path === "email") return obj.email || "";
        if (path === "dotacao_orcamentaria") return obj.dotacao_orcamentista || "";
        if (path === "valor_dotacao") {
          const valor = obj.valor_dotacao || 0;
          return typeof valor === "number"
            ? valor.toLocaleString("pt-BR", {
                style: "currency",
                currency: "BRL",
              })
            : valor;
        }
        if (path === "status") return obj.active ? "Ativo" : "Inativo";
        if (path === "centro_ascendente") return obj.centro_custo_pai?.descricao || "";
        if (path === "quantidade_filhos") return obj.centro_custos_filhos?.length || 0;
      }

      // Para propriedades simples ou quando o tratamento especial não se aplica
      if (path in obj) {
        return obj[path];
      }

      // Para propriedades aninhadas (usando notação de ponto)
      const parts = path.split(".");
      let current = obj;

      for (const part of parts) {
        if (current === null || current === undefined) {
          return "";
        }

        if (typeof current === "object" && part in current) {
          current = current[part];
        } else {
          // Se não encontrou a propriedade no caminho, retorna vazio
          return "";
        }
      }

      return current !== null && current !== undefined ? current : "";
    };

    const formatValue = (value: any, key: string): string => {
      if (value === null || value === undefined) return "";

      if (key === "veiculos_atendidos") {
        if (value === "Todos") return "Todos";
        if (Array.isArray(value) && value.length > 0) {
          return value.map((v: any) => v.descricao || v).join(", ");
        }
        return "Todos";
      }

      if (key === "contratos") {
        if (typeof value === "string") {
          return value;
        }
        if (Array.isArray(value) && value.length > 0) {
          return value
            .map(
              (c: any) =>
                c.contrato?.razao_social || c.contrato?.nome_contrato || ""
            )
            .filter((nome: string) => nome !== "")
            .join(", ");
        }
        return "Nenhum contrato associado";
      }

      if (key === "servicos_oferecidos") {
        if (value === "-") return "-";
        if (
          typeof value === "string" &&
          value !== "Nenhum serviço registrado"
        ) {
          // Se já for uma string formatada, apenas retornar
          return value;
        }
        if (Array.isArray(value) && value.length > 0) {
          return value.map((s: any) => s.descricao || s).join(", ");
        }
        return "Nenhum serviço registrado";
      }

      if (typeof value === "object" && value !== null) {
        if ("descricao" in value) return value.descricao;
        if ("nome" in value) return value.nome;
        if ("valor" in value) return formatValue(value.valor, "valor");
        return JSON.stringify(value);
      }

      if (
        key.includes("valor") ||
        key.includes("preco") ||
        key === "valor_venal"
      ) {
        return typeof value === "number"
          ? value.toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })
          : value.toString();
      }

      if (
        key.includes("data") &&
        (value instanceof Date || !isNaN(Date.parse(value)))
      ) {
        return formatData(value.toString()).slice(0, 10);
      }

      if (typeof value === "boolean") {
        return value ? "Sim" : "Não";
      }

      return value.toString();
    };

    const headers = columns.map((col) => col.label);
    const body = data.map((row: any) =>
      columns.map((col) => formatValue(accessProperty(row, col.key), col.key))
    );
    if (data.length === 0) {
      autoTable(doc, {
        head: [headers],
        body: body,
        startY: 120,
        styles: {
          fontSize: 8,
          cellPadding: 3, // Aumentar o padding
          overflow: "linebreak", // Garantir que textos longos quebrem linha
          cellWidth: "auto", // Ajustar largura automaticamente
        },
        headStyles: {
          fillColor: [22, 160, 133],
          textColor: [255, 255, 255],
          fontStyle: "bold",
        },
        margin: { horizontal: 14 },
        alternateRowStyles: { fillColor: [240, 240, 240] },
        tableWidth: "auto",
        columnStyles: {}, // Você pode definir estilos específicos para colunas aqui
        theme: "grid",
        didParseCell: function (data) {
          // Ajustar automaticamente colunas com muito texto
          const col = data.column.index;
          // Se for coluna com textos potencialmente grandes (como contratos ou serviços)
          if (
            headers[col] === "Serviços Oferecidos" ||
            headers[col] === "Contratos"
          ) {
            data.cell.styles.cellWidth = "wrap";
            data.cell.styles.minCellWidth = 60; // Largura mínima maior
          }
        },
      });
      return;
    }

    autoTable(doc, {
      head: [headers],
      body: body,
      startY: 120,
      styles: { fontSize: 8, cellPadding: 2 },
      headStyles: {
        fillColor: [22, 160, 133],
        textColor: [255, 255, 255],
        fontStyle: "bold",
      },
      margin: { horizontal: 14 },
      alternateRowStyles: { fillColor: [240, 240, 240] },
      tableWidth: "auto",
      columnStyles: {},
      theme: "grid",
    });
  };

  const renderChartToCanvas = (
    canvas: HTMLCanvasElement,
    data: TData[],
    type: string
  ): Promise<boolean> => {
    return new Promise((resolve) => {
      try {
        const ctx = canvas.getContext("2d");
        if (!ctx) {
          resolve(false);
          return;
        }

        // Limpar qualquer gráfico anterior
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Prepara os dados com base no tipo de relatório
        let chartData: {
          labels: string[];
          datasets: {
            data: number[];
            backgroundColor: string[];
            borderColor: string[];
            borderWidth: number;
          }[];
        } = {
          labels: [],
          datasets: [
            {
              data: [],
              backgroundColor: [
                "#FF6384",
                "#36A2EB",
                "#FFCE56",
                "#4BC0C0",
                "#9966FF",
                "#FF9F40",
                "#00CC99",
                "#FF9999",
                "#66B2FF",
                "#FFCC66",
                "#99E699",
                "#C2C2F0",
              ],
              borderColor: Array(12).fill("#FFFFFF"),
              borderWidth: 1,
            },
          ],
        };

        let chartTitle = "";

        if (data.length === 0) {
          chartData.labels = ["Sem dados"];
          chartData.datasets[0].data = [1];
          chartTitle = "Sem dados disponíveis";
        } else {
          switch (type) {
            case "os":
              chartTitle = "Distribuição de Ordens de Serviço";

              // Tenta acessar os dados corretos com base na estrutura real dos objetos
              const serviceTypes = (data as any[]).reduce<
                Record<string, number>
              >((acc, item) => {
                // Determinar qual campo usar para a agregação
                let typeField = null;

                if (item.tipo_servico) {
                  typeField = item.tipo_servico;
                } else if (item.TiposDeOs?.descricao) {
                  typeField = item.TiposDeOs.descricao;
                } else if (item.status) {
                  typeField = item.status;
                } else if (item.tipo_manutencao) {
                  typeField = item.tipo_manutencao;
                } else {
                  typeField = "Não especificado";
                }

                acc[typeField] = (acc[typeField] || 0) + 1;
                return acc;
              }, {});

              chartData.labels = Object.keys(serviceTypes);
              chartData.datasets[0].data = Object.values(serviceTypes);
              break;

            case "veiculos":
              chartTitle = "Distribuição de Veículos";

              // Agrupar por marca
              const marcas = (data as any[]).reduce<Record<string, number>>(
                (acc, item) => {
                  // Extrair o nome da marca corretamente
                  const marca = item.marca?.descricao || "Não especificado";
                  acc[marca] = (acc[marca] || 0) + 1;
                  return acc;
                },
                {}
              );

              chartData.labels = Object.keys(marcas);
              chartData.datasets[0].data = Object.values(marcas);
              chartTitle = "Distribuição de Veículos por Marca";
              break;

            case "credenciados":
              chartTitle = "Distribuição de Credenciados";
              let credField = "tipo";

              // Tenta identificar o melhor campo para agrupar credenciados
              if ((data as any[]).some((item) => item.servicos?.length > 0)) {
                // Agrupar por tipo de serviço
                const tipos = (data as any[]).reduce<Record<string, number>>(
                  (acc, item) => {
                    const servicos = item.servicos || [];
                    if (servicos.length === 0) {
                      acc["Não especificado"] =
                        (acc["Não especificado"] || 0) + 1;
                      return acc;
                    }

                    // Para cada credenciado, contabilizar cada tipo de serviço
                    servicos.forEach((servico: any) => {
                      const tipo = servico.descricao || "Não especificado";
                      acc[tipo] = (acc[tipo] || 0) + 1;
                    });

                    return acc;
                  },
                  {}
                );

                chartData.labels = Object.keys(tipos);
                chartData.datasets[0].data = Object.values(tipos);
                chartTitle = "Distribuição de Credenciados por Serviço";
              } else if (
                (data as any[]).some((item) => item.polo_regional?.descricao)
              ) {
                // Agrupar por polo regional
                const polos = (data as any[]).reduce<Record<string, number>>(
                  (acc, item) => {
                    const polo =
                      item.polo_regional?.descricao || "Não especificado";
                    acc[polo] = (acc[polo] || 0) + 1;
                    return acc;
                  },
                  {}
                );

                chartData.labels = Object.keys(polos);
                chartData.datasets[0].data = Object.values(polos);
                chartTitle = "Distribuição de Credenciados por Polo Regional";
              } else if (
                (data as any[]).some((item) => item.endereco?.cidade)
              ) {
                // Agrupar por cidade
                const cidades = (data as any[]).reduce<Record<string, number>>(
                  (acc, item) => {
                    const cidade = item.endereco?.cidade || "Não especificado";
                    acc[cidade] = (acc[cidade] || 0) + 1;
                    return acc;
                  },
                  {}
                );

                chartData.labels = Object.keys(cidades);
                chartData.datasets[0].data = Object.values(cidades);
                chartTitle = "Distribuição de Credenciados por Cidade";
              } else {
                // Fallback
                chartData.labels = ["Dados insuficientes"];
                chartData.datasets[0].data = [data.length];
              }
              break;

            case "condutores":
              chartTitle = "Distribuição de Condutores";

              // Tenta identificar o melhor campo para agrupar condutores
              if ((data as any[]).some((item) => item.status !== undefined)) {
                // Agrupar por status
                const statusCondutores = (data as any[]).reduce<
                  Record<string, number>
                >((acc, item) => {
                  const status = item.status ? "Ativo" : "Inativo";
                  acc[status] = (acc[status] || 0) + 1;
                  return acc;
                }, {});

                chartData.labels = Object.keys(statusCondutores);
                chartData.datasets[0].data = Object.values(statusCondutores);
                chartTitle = "Condutores por Status";
              } else if (
                (data as any[]).some(
                  (item) => item.lotacao_condutor?.centro_custo?.descricao
                )
              ) {
                // Agrupar por centro de custo
                const centros = (data as any[]).reduce<Record<string, number>>(
                  (acc, item) => {
                    const centro =
                      item.lotacao_condutor?.centro_custo?.descricao ||
                      "Não especificado";
                    acc[centro] = (acc[centro] || 0) + 1;
                    return acc;
                  },
                  {}
                );

                chartData.labels = Object.keys(centros);
                chartData.datasets[0].data = Object.values(centros);
                chartTitle = "Condutores por Centro de Custo";
              } else if ((data as any[]).some((item) => item.cnh?.categoria)) {
                // Agrupar por categoria da CNH
                const categorias = (data as any[]).reduce<
                  Record<string, number>
                >((acc, item) => {
                  const categoria = item.cnh?.categoria || "Não especificado";
                  acc[categoria] = (acc[categoria] || 0) + 1;
                  return acc;
                }, {});

                chartData.labels = Object.keys(categorias);
                chartData.datasets[0].data = Object.values(categorias);
                chartTitle = "Condutores por Categoria de CNH";
              } else {
                // Fallback
                chartData.labels = ["Dados insuficientes"];
                chartData.datasets[0].data = [data.length];
              }
              break;

            default:
              chartTitle = "Distribuição de Dados";
              // Tenta encontrar algum campo comum para agrupar
              const firstItem = data[0] as Record<string, any>;
              const possibleFields = [
                "status",
                "tipo",
                "categoria",
                "marca",
                "cidade",
              ];

              let selectedField = possibleFields.find(
                (field) =>
                  typeof firstItem[field] === "string" ||
                  typeof firstItem[field] === "boolean" ||
                  typeof firstItem[field] === "number"
              );

              if (selectedField) {
                const agregados = (data as any[]).reduce<
                  Record<string, number>
                >((acc, item) => {
                  const valor = item[selectedField] || "Não especificado";
                  acc[valor] = (acc[valor] || 0) + 1;
                  return acc;
                }, {});

                chartData.labels = Object.keys(agregados);
                chartData.datasets[0].data = Object.values(agregados);
              } else {
                chartData.labels = ["Total de registros"];
                chartData.datasets[0].data = [data.length];
              }
          }
        }

        // Garante que sempre haja algo para mostrar
        if (chartData.labels.length === 0) {
          chartData.labels = ["Sem categorias"];
          chartData.datasets[0].data = [0];
        }

        // Criar uma instância Chart.js com configurações aprimoradas
        const chart = new Chart(ctx, {
          type: "pie",
          data: chartData,
          options: {
            responsive: false,
            maintainAspectRatio: false,
            animation: false,
            plugins: {
              legend: {
                position: "right",
                labels: {
                  boxWidth: 12,
                  padding: 15,
                  font: {
                    size: 14,
                  },
                },
              },
              title: {
                display: true,
                text: chartTitle,
                font: {
                  size: 18,
                  weight: "bold",
                },
                padding: {
                  top: 10,
                  bottom: 20,
                },
              },
              tooltip: {
                callbacks: {
                  label: function (context) {
                    const label = context.label || "";
                    const value = context.raw as number;
                    const total = (
                      context.chart.data.datasets[0].data as number[]
                    ).reduce((a, b) => a + b, 0);
                    const percentage = Math.round((value / total) * 100);
                    return `${label}: ${value} (${percentage}%)`;
                  },
                },
              },
            },
          },
        });

        // Espera para renderização
        setTimeout(() => {
          resolve(true);
        }, 500);
      } catch (error) {
        console.error("Erro ao renderizar gráfico:", error);
        resolve(false);
      }
    });
  };

  const generateReportPDF = (data: TData[], filters: ReportFilters) => {
    // Primeiro cria o documento PDF
    const doc = new jsPDF({ orientation: "landscape" }); // Modo paisagem/horizontal

    // Título do relatório
    let reportTitle = "Relatório";
    if (reportType) {
      switch (reportType) {
        case "os":
          reportTitle = "Relatório de Ordens de Serviço";
          break;
        case "veiculos":
          reportTitle = "Relatório de Veículos";
          break;
        case "credenciados":
          reportTitle = "Relatório de Credenciados";
          break;
        case "condutores":
          reportTitle = "Relatório de Condutores";
          break;
      }
    }

    // Adiciona título e informações do filtro
    doc.setFontSize(18);
    doc.text(reportTitle, 14, 15);

    doc.setFontSize(10);

    // Usa formatData do arquivo format.ts para exibir datas no formato brasileiro
    doc.text(
      `Período: ${formatData(filters.dateRange.from.toString()).slice(
        0,
        10
      )} a ${formatData(filters.dateRange.to.toString()).slice(0, 10)}`,
      14,
      25
    );

    if (filters.sortBy) {
      doc.text(`Ordenado por: ${filters.sortBy}`, 14, 30);
    }

    // Cria um canvas temporário para o gráfico - com tamanho maior
    const tempCanvas = document.createElement("canvas");
    tempCanvas.width = 1200; // Aumentado para melhor resolução
    tempCanvas.height = 600; // Aumentado para melhor resolução

    // Não adiciona ao DOM visível
    tempCanvas.style.position = "absolute";
    tempCanvas.style.top = "-9999px";
    tempCanvas.style.left = "-9999px";
    document.body.appendChild(tempCanvas);

    // Usa setTimeout para garantir que o DOM esteja pronto
    setTimeout(() => {
      try {
        // Renderiza o gráfico diretamente no canvas
        renderChartToCanvas(tempCanvas, data, reportType || "os")
          .then((success) => {
            try {
              if (success) {
                // Adiciona a imagem do gráfico usando o canvas com qualidade alta
                const chartDataURL = tempCanvas.toDataURL("image/jpeg", 1.0);
                doc.addImage(chartDataURL, "JPEG", 14, 35, 260, 80);
              }

              // Remove o canvas temporário com verificação de segurança
              if (document.body.contains(tempCanvas)) {
                document.body.removeChild(tempCanvas);
              }

              // Usar as colunas selecionadas pelo usuário para o relatório
              const tableColumns = getColumnsForReport(filters.selectedFields);

              // Adiciona a tabela ao PDF (posição Y após o gráfico)
              addTableToPdf(doc, data, tableColumns, filters);

              // Salva o PDF
              doc.save(`relatorio-${reportType || "dados"}.pdf`);
              setShowReportPreview(false);
            } catch (error) {
              console.error("Erro ao gerar PDF:", error);
              if (document.body.contains(tempCanvas)) {
                document.body.removeChild(tempCanvas);
              }
              generateReportWithoutChart(doc, data, filters);
              setShowReportPreview(false);
            }
          })
          .catch((error) => {
            console.error("Erro ao renderizar gráfico:", error);
            if (document.body.contains(tempCanvas)) {
              document.body.removeChild(tempCanvas);
            }
            generateReportWithoutChart(doc, data, filters);
            setShowReportPreview(false);
          });
      } catch (error) {
        console.error("Erro ao inicializar gráfico:", error);
        document.body.removeChild(tempCanvas);
        generateReportWithoutChart(doc, data, filters);
        setShowReportPreview(false);
      }
    }, 300); // Um curto delay para garantir que o DOM esteja pronto
  };

  // Função auxiliar para gerar relatório sem gráfico
  const generateReportWithoutChart = (
    doc: jsPDF,
    data: TData[],
    filters: ReportFilters
  ) => {
    if (data.length === 0) {
      autoTable(doc, {
        head: [["Sem dados"]],
        body: [["Não foram encontrados dados para o período selecionado"]],
        startY: 30,
        styles: { fontSize: 8 },
        headStyles: { fillColor: [22, 160, 133] },
        margin: { horizontal: 14 },
      });
    } else {
      // Obter as colunas selecionadas pelo usuário
      const tableColumns = getColumnsForReport(filters.selectedFields);

      // Função auxiliar para extrair valores formatados
      const extractValue = (obj: any, key: string): string => {
        // Tratamento especial para objetos conhecidos
        if (key === "marca" && obj.marca && obj.marca.descricao) {
          return obj.marca.descricao;
        }
        if (key === "modelo" && obj.modelo && obj.modelo.descricao) {
          return obj.modelo.descricao;
        }
        if (key === "odometro") {
          return obj.odometro || obj.km || "";
        }

        const value = obj[key];

        // Se for um objeto, tenta extrair uma propriedade útil
        if (value && typeof value === "object") {
          if ("descricao" in value) return value.descricao;
          if ("nome" in value) return value.nome;
          if ("valor" in value) {
            return typeof value.valor === "number"
              ? value.valor.toLocaleString("pt-BR", {
                  style: "currency",
                  currency: "BRL",
                })
              : value.valor;
          }
          return JSON.stringify(value);
        }

        // Formatação para valores específicos
        if (key.includes("valor") || key.includes("preco")) {
          return typeof value === "number"
            ? value.toLocaleString("pt-BR", {
                style: "currency",
                currency: "BRL",
              })
            : value?.toString() || "";
        }

        return value?.toString() || "";
      };

      const headers = tableColumns.map((col) => col.label);
      const body = data.map((row: any) =>
        tableColumns.map((col) => extractValue(row, col.key))
      );

      autoTable(doc, {
        head: [headers],
        body: body,
        startY: 30,
        styles: { fontSize: 8 },
        headStyles: { fillColor: [22, 160, 133] },
        margin: { horizontal: 14 },
      });
    }

    doc.save(`relatorio-${reportType || "dados"}.pdf`);
  };

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: (value) => {
      setGlobalFilter(value);
      // Aciona a busca no servidor se paginação no servidor estiver ativa
      if (serverSidePagination?.enabled) {
        serverSidePagination.onSearch(value);
      }
    },
    getCoreRowModel: getCoreRowModel(),
    onRowSelectionChange: setRowSelection,
    // Se servidor estiver habilitado, não usar paginação cliente
    getPaginationRowModel:
      serverSidePagination?.enabled || disablePagination
        ? undefined
        : getPaginationRowModel(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      globalFilter,
      rowSelection,
    },
  });
  useImperativeHandle(ref, () => ({
    getSelectedRowModel: () => table.getSelectedRowModel(),
    getSelectedRows: () =>
      table.getSelectedRowModel().flatRows.map((row) => row.original),
    resetRowSelection: () => table.resetRowSelection(),
    getTable: () => table,
  }));

  const handleExport = (
    format: "csv" | "excel" | "pdf" | "print",
    chartContainerId: string
  ) => {
    // Obtém as linhas selecionadas
    let selectedData = table
      .getSelectedRowModel()
      .rows.map((row) => row.original) as Record<string, any>[];

    // Se nenhuma linha foi selecionada, usa as linhas visíveis filtradas
    if (selectedData.length === 0) {
      selectedData = table
        .getFilteredRowModel()
        .rows.map((row) => row.original) as Record<string, any>[];

      if (selectedData.length === 0) {
        toast("Sem dados para exportação", {
          description: "Não há dados disponíveis para exportar.",
          duration: 3000,
        });
        return;
      }

      toast("Exportando todos os dados visíveis", {
        description:
          "Nenhum item selecionado. Exportando todos os dados visíveis na tabela.",
        duration: 3000,
      });
    }

    if (format === "print") {
      reactToPrintFn();
      return;
    }

    try {
      if (format === "csv") {
        // Mapeia as colunas visíveis da tabela para garantir consistência
        const visibleColumns = table
          .getVisibleFlatColumns()
          .filter((col) => col.id !== "select" && col.id !== "actions");

        const headers = visibleColumns.map((col) => {
          const header = col.columnDef.header;
          return typeof header === "string" ? header : col.id;
        });

        const rows = selectedData.map((row) =>
          visibleColumns.map((col) => {
            // Extrai o valor da célula de forma segura
            const key = col.id;
            const value = key.includes(".")
              ? key.split(".").reduce((obj, key) => obj?.[key] ?? "", row)
              : row[key] ?? "";
            return JSON.stringify(value);
          })
        );

        const csvContent = [
          headers.join(","),
          ...rows.map((row) => row.join(",")),
        ].join("\n");

        const blob = new Blob([csvContent], {
          type: "text/csv;charset=utf-8;",
        });
        saveAs(
          blob,
          `${reportType || "dados"}-export-${new Date()
            .toISOString()
            .slice(0, 10)}.csv`
        );
      } else if (format === "excel") {
        // Define colunas específicas para cada tipo de relatório
        let excelColumns: { key: string; label: string }[] = [];

        if (reportType === "credenciados") {
          excelColumns = [
            { key: "informacoes.0.razao_social", label: "Razão Social" },
            { key: "informacoes.0.cnpj", label: "CNPJ" },
            { key: "informacoes.0.nome_fantasia", label: "Nome Fantasia" },
            { key: "endereco.cidade", label: "Cidade" },
            { key: "endereco.estado", label: "Estado" },
            { key: "contato.telefone", label: "Telefone" },
            { key: "contato.celular", label: "Celular" },
            { key: "contato.email", label: "Email" },
            { key: "ativo", label: "Status" },
          ];
        } else if (reportType === "veiculos") {
          excelColumns = [
            { key: "placa", label: "Placa" },
            { key: "marca.descricao", label: "Marca" },
            { key: "modelo.descricao", label: "Modelo" },
            {
              key: "lotacao_veiculos.centro_custo.descricao",
              label: "Centro de Custo",
            },
            { key: "lotacao_veiculos.cidade", label: "Cidade" },
            { key: "odometro", label: "Odômetro" },
            { key: "codigo_fipe.valor_venal", label: "Valor Venal (R$)" },
            { key: "status", label: "Status" },
            { key: "ano_fab", label: "Ano Fabricação" },
          ];
        } else if (reportType === "resumo-financeiro") {
          excelColumns = [
            { key: "os", label: "OS" },
            { key: "veiculo.placa", label: "Placa" },
            { key: "veiculo.marca", label: "Marca" },
            { key: "veiculo.modelo", label: "Modelo" },
            { key: "centro_custo", label: "Centro de Custo" },
            { key: "servico.descricao", label: "Serviço" },
            { key: "servico.desconto", label: "Grupo Desconto" },
            { key: "servico.marca", label: "Marca da Peça" },
            { key: "quantidade", label: "Quantidade" },
            { key: "unidade", label: "Unidade" },
            { key: "empenho", label: "Empenho" },
            { key: "orcamento", label: "Orçamento" },
            { key: "credenciado.nome", label: "Credenciado" },
            { key: "credenciado.cnpj", label: "CNPJ" },
            { key: "valor_negociado", label: "Valor Negociado (R$)" },
            { key: "desconto_reais", label: "Desconto (R$)" },
            { key: "desconto_percentual", label: "Desconto (%)" },
            { key: "total", label: "Total (R$)" },
          ];
        } else {
          // Fallback: usar colunas visíveis da tabela
          excelColumns = table
            .getVisibleFlatColumns()
            .filter((col) => col.id !== "select" && col.id !== "actions")
            .map((col) => {
              const header = col.columnDef.header;
              const label = typeof header === "string" ? header : col.id;
              return { key: col.id, label };
            });
        }

        // Função auxiliar para acessar propriedades aninhadas
        const getNestedValue = (obj: any, path: string): any => {
          if (!path) return "";
          if (path.includes(".")) {
            const parts = path.split(".");
            let current = obj;
            for (const part of parts) {
              if (!current || typeof current !== "object") return "";
              // Se é um índice de array
              if (!isNaN(Number(part))) {
                current = current[Number(part)];
              } else {
                current = current[part];
              }
            }
            return current;
          }
          return obj[path] ?? "";
        };

        const formatResumoFinanceiroData = (data: any[]) => {
          return data.map((item) => ({
            OS: item.os || "",
            Placa: item.veiculo?.placa || "",
            Marca: item.veiculo?.marca || "",
            Modelo: item.veiculo?.modelo || "",
            "Centro de Custo": item.centro_custo || "",
            Serviço: item.servico?.descricao || "",
            "Marca da Peça": item.servico?.marca || "",
            Quantidade: item.quantidade || "",
            "Grupo Desconto": item.servico?.desconto,
            Unidade: item.unidade || "",
            Empenho: item.empenho || "",
            Orçamento: item.orcamento || "",
            Credenciado: item.credenciado?.nome || "",
            CNPJ:
              item.credenciado?.cnpj?.replace(
                /^(\d{2})\D?(\d{3})\D?(\d{3})\D?(\d{4})\D?(\d{2})$/,
                "$1.$2.$3/$4-$5"
              ) || "",
            "Valor Negociado (R$)":
              typeof item.valor_negociado === "number"
                ? item.valor_negociado.toLocaleString("pt-BR", {
                    style: "currency",
                    currency: "BRL",
                  })
                : "",
            "Desconto (R$)":
              typeof item.desconto_reais === "number"
                ? item.desconto_reais.toLocaleString("pt-BR", {
                    style: "currency",
                    currency: "BRL",
                  })
                : "",
            "Desconto (%)": item.desconto_percentual + "%",
            "Total (R$)":
              typeof item.total === "number"
                ? item.total.toLocaleString("pt-BR", {
                    style: "currency",
                    currency: "BRL",
                  })
                : "",
          }));
        };

        // Cria um array de objetos formatados para o Excel
        const formattedData =
          reportType === "resumo-financeiro"
            ? formatResumoFinanceiroData(selectedData)
            : selectedData.map((row) => {
                const newRow: Record<string, any> = {};
                excelColumns.forEach((col) => {
                  const value = getNestedValue(row, col.key);

                  if (col.key === "ativo") {
                    newRow[col.label] = value ? "Ativo" : "Inativo";
                  } else if (col.key.includes("cnpj")) {
                    newRow[col.label] = value
                      ? value.replace(
                          /^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/,
                          "$1.$2.$3/$4-$5"
                        )
                      : "";
                  } else if (
                    col.key.includes("valor") ||
                    col.key.includes("preco")
                  ) {
                    newRow[col.label] =
                      typeof value === "number"
                        ? value.toLocaleString("pt-BR", {
                            style: "currency",
                            currency: "BRL",
                          })
                        : value?.toString() || "";
                  } else {
                    newRow[col.label] = value?.toString() || "";
                  }
                });
                return newRow;
              });

        // Cria a planilha com os dados formatados
        const worksheet = XLSXUtils.json_to_sheet(formattedData);
        const workbook = XLSXUtils.book_new();
        XLSXUtils.book_append_sheet(workbook, worksheet, reportType || "Dados");
        xlsxWriteFile(
          workbook,
          `${reportType || "dados"}-export-${new Date()
            .toISOString()
            .slice(0, 10)}.xlsx`
        );
      } else if (format === "pdf") {
        // Cria um documento PDF em modo landscape (horizontal)
        const doc = new jsPDF({ orientation: "landscape" });

        // Define colunas específicas para credenciados
        let pdfColumns: { key: string; label: string }[] = [];

        if (reportType === "credenciados") {
          pdfColumns = [
            { key: "informacoes.0.razao_social", label: "Razão Social" },
            { key: "informacoes.0.cnpj", label: "CNPJ" },
            { key: "informacoes.0.nome_fantasia", label: "Nome Fantasia" },
            { key: "endereco.cidade", label: "Cidade" },
            { key: "endereco.estado", label: "Estado" },
            { key: "contato.telefone", label: "Telefone" },
            { key: "contato.celular", label: "Celular" },
            { key: "contato.email", label: "Email" },
            { key: "ativo", label: "Status" },
          ];
        } else if (
          chartContainerId === "chartDivOS" &&
          reportType !== "resumo-financeiro"
        ) {
          // Colunas para relatório OS (manter o código existente)
          pdfColumns = [
            { key: "centro_de_custo", label: "Centro de Custo" },
            { key: "placa", label: "Placa" },
            { key: "marca", label: "Marca" },
            { key: "modelo", label: "Modelo" },
            { key: "valor_pecas", label: "Valor Peças (R$)" },
            { key: "valor_servicos", label: "Valor Serviços (R$)" },
          ];
        } else if (reportType === "resumo-financeiro") {
          pdfColumns = [
            { key: "os", label: "OS" },
            { key: "veiculo.placa", label: "Placa" },
            { key: "veiculo.marca", label: "Marca" },
            { key: "veiculo.modelo", label: "Modelo" },
            { key: "centro_custo", label: "Centro de Custo" },
            { key: "servico.descricao", label: "Serviço" },
            { key: "servico.marca", label: "Marca da Peça" },
            { key: "quantidade", label: "Quantidade" },
            { key: "unidade", label: "Unidade" },
            { key: "valor_negociado", label: "Valor Negociado (R$)" },
            { key: "desconto_reais", label: "Desconto (R$)" },
            { key: "desconto_percentual", label: "Desconto (%)" },
            { key: "total", label: "Total (R$)" },
            { key: "empenho", label: "Empenho" },
            { key: "orcamento", label: "Orçamento" },
            { key: "credenciado.nome", label: "Credenciado" },
            { key: "credenciado.cnpj", label: "CNPJ" },
            { key: "data_integracao", label: "Data de Integração" },
          ];
        } else {
          // Fallback: tenta usar as colunas visíveis da tabela
          pdfColumns = table
            .getVisibleFlatColumns()
            .filter((col) => col.id !== "select" && col.id !== "actions")
            .map((col) => {
              const header = col.columnDef.header;
              const label = typeof header === "string" ? header : col.id;
              return { key: col.id, label };
            });

          // Se ainda não tiver colunas, tenta extrair do primeiro item
          if (pdfColumns.length === 0 && selectedData.length > 0) {
            pdfColumns = Object.keys(selectedData[0]).map((key) => ({
              key,
              label:
                key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, " "),
            }));
          }
        }

        // Função auxiliar para acessar propriedades aninhadas
        const getNestedValue = (obj: any, path: string): any => {
          if (!path) return "";
          if (path.includes(".")) {
            const parts = path.split(".");
            let current = obj;
            for (const part of parts) {
              if (!current || typeof current !== "object") return "";
              // Se é um índice de array
              if (!isNaN(Number(part))) {
                current = current[Number(part)];
              } else {
                current = current[part];
              }
            }
            return current;
          }
          return obj[path] ?? "";
        };

        // Monta os arrays para o autoTable
        const headers = pdfColumns.map((col) => col.label);
        const body = selectedData.map((row) =>
          pdfColumns.map((col) => {
            const value = getNestedValue(row, col.key);
            // Formata valores específicos
            if (col.key === "ativo") {
              return value ? "Ativo" : "Inativo";
            }
            if (col.key.includes("cnpj")) {
              return value
                ? value.replace(
                    /^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/,
                    "$1.$2.$3/$4-$5"
                  )
                : "";
            }
            return value?.toString() || "";
          })
        );

        // Adiciona título
        doc.setFontSize(16);
        doc.text(
          `Exportação de ${
            reportType === "credenciados" ? "Credenciados" : "Dados"
          }`,
          14,
          15
        );

        // Adiciona a tabela
        autoTable(doc, {
          head: [headers],
          body: body,
          startY: 25,
          styles: { fontSize: 8 },
          headStyles: { fillColor: [22, 160, 133] },
          margin: { horizontal: 14 },
          alternateRowStyles: { fillColor: [240, 240, 240] },
        });

        doc.save(
          `${reportType || "dados"}-export-${new Date()
            .toISOString()
            .slice(0, 10)}.pdf`
        );
      }

      toast("Exportação concluída", {
        description: `Dados exportados com sucesso para ${format.toUpperCase()}.`,
        duration: 3000,
      });
    } catch (err) {
      console.error("Erro na exportação:", err);
      toast("Erro na exportação", {
        description:
          "Não foi possível completar a exportação. Verifique o console para mais detalhes.",
        duration: 3000,
      });
    }
  };

  // Replace the direct document check with a state variable
  const [chartContainerId, setChartContainerId] = useState("chartDivOS");
  
  // Add useEffect to check DOM elements only on client-side
  useEffect(() => {
    // Check if chartDivOS exists, otherwise use chartDivMan
    if (
      !document.getElementById("chartDivOS") &&
      document.getElementById("chartDivMan")
    ) {
      setChartContainerId("chartDivMan");
    }
  }, []);

  return (
    <div className={`flex flex-col w-full ${className}`}>
      {showToolbar && (
        <div className="flex flex-col gap-4 py-2 px-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-2 flex-1">
              <ExportTo
                exportTo={(format) => handleExport(format, chartContainerId)}
              />
              {showReportButton && reportType && (
                <ReportModal
                  onGenerateReport={handleGenerateReport}
                  availableFields={getAvailableFields()}
                  reportType={reportType}
                />
              )}
              {newItem && (onNewItem || onClick) ? (
                <ModalForm
                  schema={newItem.schema}
                  triggerLabel={
                    <>
                      <Plus className="mr-2 h-4 w-4" />
                      Adicionar {newItem.name}
                    </>
                  }
                  onSubmit={onNewItem || (() => {})}
                  onClick={onClick}
                  title={
                    newItem.name
                      ? `Formulário para criar um(a) ${newItem.name}`
                      : "Formulário"
                  }
                  description="Preencha o formulário abaixo"
                  modalContentClassName={modalClassName}
                  modalFormClassName={modalFormClassName}
                  defaultValues={newItem.defaultValues}
                >
                  <GenericFormsInput
                    variants="default"
                    fieldConfig={newItem.fieldConfig}
                  />
                </ModalForm>
              ) : (
                onClick && (
                  <Button
                    onClick={onClick}
                    variant="outline"
                    className="flex items-center"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Adicionar Item
                  </Button>
                )
              )}
            </div>
            <DataTableViewOptions table={table} />
          </div>

          {showSearch && (
            <div className="relative w-full max-w-md">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Buscar..."
                value={globalFilter ?? ""}
                onChange={(e) => setGlobalFilter(e.target.value)}
                className="pl-8 w-left"
              />
            </div>
          )}
          <div className="flex gap-2">
            {showMapButton && (
              <Link href={`/dashboard/rede-credenciada/mapa`} passHref>
                <Button className=" w-right">
                  <MapPinX className="h-4 w-4" />
                  <span>Ver no Mapa</span>
                </Button>
              </Link>
            )}
            {showFilterButton && (
              <Button
                type="button"
                variant="outline"
                className="w-full sm:w-auto"
                onClick={() => setShowFilterDialog?.(true)}
              >
                Filtros
              </Button>
            )}
          </div>
        </div>
      )}

      <div className="overflow-x-auto w-full">
        <Table ref={contentRef} className="min-w-full border-collapse">
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="border-b">
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    className="py-3 px-4 text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.length > 0 ? (
              table.getRowModel().rows.map((row) => {
                const os = row.original as OS;
                const isExpired =
                  os.quoteExpirationDate &&
                  new Date(os.quoteExpirationDate) < new Date();

                return (
                  <React.Fragment key={row.id}>
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      onClick={(e) => {
                        if (
                          (e.target as HTMLElement).closest(
                            '[data-cell-type="checkbox"]'
                          ) === null &&
                          handleRowClick
                        ) {
                          handleRowClick(row.original);
                        }
                      }}
                      onContextMenu={(e) => {
                        e.preventDefault();
                        onRowContextMenu?.(e, row.original);
                      }}
                      style={{ cursor: handleRowClick ? "pointer" : "default" }}
                      className={`relative hover:bg-muted/50 transition-colors -z-0 ${
                        isExpired ? "bg-red-100 text-red-700" : ""
                      }`}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell
                          key={cell.id}
                          className="py-4 px-4"
                          data-cell-type={
                            cell.column.id === "select" ? "checkbox" : "default"
                          }
                        >
                          {flexRender(cell.column.columnDef.cell, {
                            ...cell.getContext(),
                            cell,
                          })}
                        </TableCell>
                      ))}
                    </TableRow>
                    {renderSubComponent && renderSubComponent({ row })}
                  </React.Fragment>
                );
              })
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center text-muted-foreground"
                >
                  {isLoading ? (
                    <div className="flex justify-center items-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                      <span className="ml-2">Carregando dados...</span>
                    </div>
                  ) : (
                    "Nenhum resultado encontrado."
                  )}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div>
        {total && (
          <div className="mt-6 text-sm text-right pr-4">
            <div>
              <span className="font-semibold">Total Recebido:</span>{" "}
              <span className="font-semibold text-green-500">
                {total.recebido.toLocaleString("pt-BR", {
                  style: "currency",
                  currency: "BRL",
                })}{" "}
                (+)
              </span>
            </div>
            <div>
              <span className="font-semibold">Total Gasto:</span>{" "}
              <span className="font-semibold text-red-500">
                {Math.abs(total.gasto).toLocaleString("pt-BR", {
                  style: "currency",
                  currency: "BRL",
                })}{" "}
                (-)
              </span>
            </div>
            <div>
              <span className="font-semibold">Saldo Final:</span>{" "}
              <span
                className={`font-semibold ${
                  total.saldo < 0 ? "text-red-500" : "text-emerald-500"
                }`}
              >
                {total.saldo.toLocaleString("pt-BR", {
                  style: "currency",
                  currency: "BRL",
                })}{" "}
                (=)
              </span>
            </div>
          </div>
        )}
      </div>
      {showReportPreview && (
        <div className="hidden">
          <ReportChart
            type={reportType || "os"}
            data={reportData}
            containerId={
              reportType
                ? `chartDiv${
                    reportType.charAt(0).toUpperCase() + reportType.slice(1)
                  }`
                : "chartDivReport"
            }
          />
        </div>
      )}
      {!disablePagination && (
        <div className="p-2">
          <DataTablePagination
            table={table}
            serverSidePagination={serverSidePagination}
          />
        </div>
      )}
    </div>
  );
});
