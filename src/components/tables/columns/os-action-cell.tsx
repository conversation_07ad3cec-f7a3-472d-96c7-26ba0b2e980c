import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { updateOrcamentoStatus, updateOsStatus } from "@/serverActions/orcamentoAction";
import { updateEmpenhoSaldosBloqueadosAction } from "@/serverActions/empenhoAction";
import { useSession } from "@/components/session/use-session";

const ActionCell = ({ row }: { row: OS }) => {
  const { session } = useSession();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [justificativa, setJustificativa] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleCancelClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsDialogOpen(true);
  };

  const handleCancelConfirm = async (e?: React.MouseEvent) => {
    e?.stopPropagation();

    if (!justificativa.trim()) {
      alert("Por favor, informe uma justificativa para o cancelamento");
      return;
    }

    setIsLoading(true);
    try {
      // 1. Atualizar status da OS para "cancelada"
      await updateOsStatus(
        row.id,
        "cancelada",
        row.orcamentos?.find((o: any) => o.status === "execucao")?.id,
        undefined,
        undefined,
        undefined,
        justificativa
      );

      // 2. Atualizar saldos bloqueados
      if (["orcamento", "analise", "autorizada", "execucao"].includes(row.status)) {
        const selectedOrcamento = row.orcamentos?.find((o: any) => o.status === "execucao");
        if (selectedOrcamento) {
          await updateOrcamentoStatus(selectedOrcamento.id, "cancelada");
        }

        if (selectedOrcamento && row.veiculo?.faturamentoVeiculo?.empenho?.id) {
          await updateEmpenhoSaldosBloqueadosAction(row.veiculo.faturamentoVeiculo.empenho.id, {
            bloqueado_pecas:
              selectedOrcamento.processedPecas?.reduce(
                (acc: number, pecas: any) => acc - (pecas.valorNegociado * pecas.quantidade || 0),
                0
              ) || 0,
            bloqueado_servicos:
              selectedOrcamento.processedServicos?.reduce(
                (acc: number, servicos: any) => acc - (servicos.valor || 0),
                0
              ) || 0,
          });
        }
      }

      setIsDialogOpen(false);
      setJustificativa("");
      window.location.reload();
    } catch (error) {
      console.error("Erro ao cancelar OS:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Função para prevenir propagação em todos os eventos do Dialog
  const preventPropagation = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
  };

  const preventFocusPropagation = (e: React.FocusEvent) => {
    e.stopPropagation();
  };

  return (
    <>
      {session?.roles.includes("GESTOR_FROTA") && (
        <Button
          variant="destructive"
          size="sm"
          onClick={handleCancelClick}
          disabled={["cancelada", "finalizada"].includes(row.status)}>
          Cancelar OS
        </Button>
      )}

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent
          onClick={preventPropagation}
          onMouseDown={preventPropagation}
          onMouseUp={preventPropagation}>
          <DialogHeader onClick={preventPropagation}>
            <DialogTitle>Cancelar Ordem de Serviço</DialogTitle>
            <DialogDescription>
              Informe a justificativa para o cancelamento desta OS. Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4" onClick={preventPropagation}>
            <Textarea
              id="justificativa"
              value={justificativa}
              onChange={(e) => setJustificativa(e.target.value)}
              className="col-span-3"
              placeholder="Descreva o motivo do cancelamento..."
              required
              onClick={preventPropagation}
              onFocus={preventFocusPropagation}
            />
          </div>

          <DialogFooter onClick={preventPropagation}>
            <Button
              variant="outline"
              onClick={(e) => {
                preventPropagation(e);
                setIsDialogOpen(false);
              }}
              disabled={isLoading}>
              Voltar
            </Button>
            <Button
              variant="destructive"
              onClick={(e) => {
                preventPropagation(e);
                handleCancelConfirm();
              }}
              disabled={isLoading || !justificativa.trim()}>
              {isLoading ? "Cancelando..." : "Confirmar Cancelamento"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ActionCell;
