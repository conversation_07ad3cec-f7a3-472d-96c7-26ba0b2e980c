import { Checkbox } from "@/components/ui/checkbox";
import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "../column-header";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { PlayCircle } from "lucide-react";
import { ConductorAuthModal } from "@/components/modal/condutor-auth-modal";
import { useState } from "react";

interface CredenciadoCellProps {
  row: any;
  filterStatusOptions?: string[];
  orgao?: {
    nome?: string;
  };
}

interface OrcamentoRow {
  status?: string;
  taxaAdministracao?: number;
  valorPecas?: number;
}

interface TableRow {
  orcamentos?: OrcamentoRow[];
  veiculo?: {
    lotacao_veiculos?: {
      orgao?: { descricao: string };
    };
  };
}

function CredenciadoCell({ row, filterStatusOptions }: CredenciadoCellProps) {
  const filterOrc = row.orcamentos?.find((orc: any) =>
    filterStatusOptions?.includes(orc.status)
  );

  const credenciadoId = filterOrc?.credenciadoId as string | undefined;
  const { data, isLoading, isError } = useCredenciadoById(credenciadoId);

  const nomeFantasia =
    data?.data?.credenciado?.informacoes?.[0]?.nome_fantasia ??
    data?.data?.credenciado?.informacoes?.[0]?.razao_social ??
    "N/A";

  if (isLoading) return <span>Carregando...</span>;
  if (isError || !data) return <span>N/A</span>;

  return <span>{nomeFantasia}</span>;
}
import { toast } from "sonner";
import {
  updateOrcamentoStatus,
  updateOsStatus,
} from "@/serverActions/orcamentoAction";
import { useContrato } from "@/context/contrato-context";
import { useSession } from "@/components/session/use-session";
import ActionCell from "./os-action-cell";

import { useCredenciadoById } from "@/hooks/useCredenciado";

export const osLancadosColumn: ColumnDef<OS>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "osNumber",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Nº OS" />
    ),
    accessorFn: (row) =>
      `#${row.osNumber}/${new Date(row.createdAt).getFullYear()}` || "N/A",
  },
  {
    accessorKey: "centro_de_custo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Centro de Custo" />
    ),
    accessorFn: (row) =>
      row.veiculo?.lotacao_veiculos.centro_custo?.descricao || "N/A",
  },
  {
    accessorKey: "tipo_de_serviço",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Tipo de Serviço" />
    ),
    accessorFn: (row) => {
      return row.TiposDeOs?.descricao || "N/A";
    },
  },
  {
    accessorKey: "veículo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Veículo" />
    ),
    accessorFn: (row) =>
      `${row.veiculo?.placa} | ${row.veiculo?.marca?.descricao} -${row.veiculo?.modelo?.descricao} ` ||
      "N/A",
  },
  // {
  //   accessorKey: "orçamentista",
  //   header: ({ column }) => <DataTableColumnHeader column={column} title="Orçamentista" />,
  //   accessorFn: (row) => `${row.credenciado?.informacoes[0].razao_social}` || "N/A",
  // },
  {
    accessorKey: "data",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Data" />
    ),
    accessorFn: (row) => format(row.createdAt, "dd/MM/yyyy") || "N/A",
  },
  {
    accessorKey: "cidade",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Cidade" />
    ),
    accessorFn: (row) => {
      return `${row.cidade_loc}/${row.estado_loc}` || "N/A";
    },
  },
  {
    accessorKey: "credenciado",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Credenciado" />
    ),
    accessorFn: (row) =>
      row.credenciado?.informacoes[0].nome_fantasia
        ? row.credenciado.informacoes[0].nome_fantasia
        : row.credenciado?.informacoes[0].razao_social || "N/A",
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    // accessorFn: (row) => {
    //   return row.arquivosNotaFiscal?.length
    //     ? row.status
    //     : "Aguardando Nota Fiscal";
    // },
  },
  {
    id: "actions",
    cell: ({ row }) => <ActionCell row={row.original} />,
    enableSorting: false,
    enableHiding: false,
  },
];
export const osOrcamentoColumn: ColumnDef<OS>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "osNumber",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Nº OS" />
    ),
    accessorFn: (row) =>
      `#${row.osNumber}/${new Date(row.createdAt).getFullYear()}` || "N/A",
  },
  {
    accessorKey: "centro_de_custo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Centro de Custo" />
    ),
    accessorFn: (row) =>
      row.veiculo?.lotacao_veiculos.centro_custo?.descricao || "N/A",
  },
  {
    accessorKey: "tipo_de_serviço",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Tipo de Serviço" />
    ),
    accessorFn: (row) => row.TiposDeOs?.descricao || "N/A",
  },
  {
    accessorKey: "veículo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Veículo" />
    ),
    accessorFn: (row) =>
      `${row.veiculo?.placa} |${row.veiculo?.marca?.descricao} | ${row.veiculo?.modelo?.descricao}` ||
      "N/A",
  },
  {
    accessorKey: "data",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Data" />
    ),
    accessorFn: (row) => format(row.createdAt, "dd/MM/yyyy") || "N/A",
  },
  {
    accessorKey: "cidade",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Cidade" />
    ),
    accessorFn: (row) => `${row.cidade_loc} / ${row.estado_loc}` || "N/A",
  },
  {
    accessorKey: "minimun_orcamento",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Mínimo de Orçamentos" />
    ),
    accessorFn: (row) => row.minimun_orcament || "N/A",
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    accessorFn: (row) => row.status,
  },
  {
    id: "actions",
    cell: ({ row }) => <ActionCell row={row.original} />,
    enableSorting: false,
    enableHiding: false,
  },
];
export const osAnaliseColumn: ColumnDef<OS>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "osNumber",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Nº OS" />
    ),
    accessorFn: (row) =>
      `#${row.osNumber}/${new Date(row.createdAt).getFullYear()}` || "N/A",
  },
  {
    accessorKey: "centro_de_custo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Centro de Custo" />
    ),
    accessorFn: (row) =>
      row.veiculo?.lotacao_veiculos.centro_custo?.descricao || "N/A",
  },
  {
    accessorKey: "tipo_de_serviço",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Tipo de Serviço" />
    ),
    accessorFn: (row) => row.TiposDeOs?.descricao || "N/A",
  },
  {
    accessorKey: "veículo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Veículo" />
    ),
    accessorFn: (row) =>
      `${row.veiculo?.placa} |${row.veiculo?.marca?.descricao} | ${row.veiculo?.modelo?.descricao}` ||
      "N/A",
  },
  {
    accessorKey: "data",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Data" />
    ),
    accessorFn: (row) => format(row.createdAt, "dd/MM/yyyy") || "N/A",
  },
  {
    accessorKey: "cidade",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Cidade" />
    ),
    accessorFn: (row) => `${row.cidade_loc} - ${row.estado_loc}` || "N/A",
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    accessorFn: () => "Em Análise",
  },
  {
    id: "actions",
    cell: ({ row }) => <ActionCell row={row.original} />,
    enableSorting: false,
    enableHiding: false,
  },
];
export const osAutorizadoColumn: ColumnDef<OS>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "osNumber",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Nº OS" />
    ),
    accessorFn: (row) =>
      `#${row.osNumber}/${new Date(row.createdAt).getFullYear()}` || "N/A",
  },
  {
    accessorKey: "centro_de_custo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Centro de Custo" />
    ),
    accessorFn: (row) =>
      row.veiculo?.lotacao_veiculos.centro_custo?.descricao || "N/A",
  },
  {
    accessorKey: "tipo_de_serviço",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Tipo de Serviço" />
    ),
    accessorFn: (row) => row.TiposDeOs?.descricao || "N/A",
  },
  {
    accessorKey: "veículo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Veículo" />
    ),
    accessorFn: (row) =>
      `${row.veiculo?.placa} |${row.veiculo?.marca?.descricao} | ${row.veiculo?.modelo?.descricao}` ||
      "N/A",
  },
  {
    accessorKey: "data",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Data" />
    ),
    accessorFn: (row) => format(row.createdAt, "dd/MM/yyyy") || "N/A",
  },
  {
    accessorKey: "cidade",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Cidade" />
    ),
    accessorFn: (row) => `${row.estado_loc} - ${row.cidade_loc}` || "N/A",
  },
  {
    accessorKey: "credenciado",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Credenciado" />
    ),
    accessorFn: (row) =>
      row.credenciado?.informacoes[0].nome_fantasia
        ? row.credenciado.informacoes[0].nome_fantasia
        : row.credenciado?.informacoes[0].razao_social || "N/A",
  },
  // {
  //   accessorKey: "valor_autorizado",
  //   header: ({ column }) => <DataTableColumnHeader column={column} title="Valor autorizado" />,
  //   accessorFn: (row) =>
  //     row.valor_autorizado?.toLocaleString("pt-BR", {
  //       style: "currency",
  //       currency: "BRL",
  //     }) || "N/A",
  // },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    accessorFn: () => "Autorizada",
  },
  {
    id: "actions",
    cell: ({ row }) => <ActionCell row={row.original} />,
    enableSorting: false,
    enableHiding: false,
  },
];
export const osExecucaoColumn: ColumnDef<OS>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "osNumber",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Nº OS" />
    ),
    accessorFn: (row) =>
      `#${row.osNumber}/${new Date(row.createdAt).getFullYear()}` || "N/A",
  },
  {
    accessorKey: "centro_de_custo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Centro de Custo" />
    ),
    accessorFn: (row) =>
      row.veiculo?.lotacao_veiculos.centro_custo?.descricao || "N/A",
  },
  {
    accessorKey: "tipo_de_serviço",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Tipo de Serviço" />
    ),
    accessorFn: (row) => row.TiposDeOs?.descricao || "N/A",
  },
  {
    accessorKey: "veículo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Veículo" />
    ),
    accessorFn: (row) =>
      `${row.veiculo?.placa} |${row.veiculo?.marca?.descricao} | ${row.veiculo?.modelo?.descricao}` ||
      "N/A",
  },
  {
    accessorKey: "data",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Data" />
    ),
    accessorFn: (row) => format(row.createdAt, "dd/MM/yyyy") || "N/A",
  },
  {
    accessorKey: "cidade",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Cidade" />
    ),
    accessorFn: (row) => `${row.cidade_loc}/${row.estado_loc}` || "N/A",
  },
  {
    accessorKey: "credenciado",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Credenciado" />
    ),
    cell: ({ row }) => (
      <CredenciadoCell
        row={row.original}
        filterStatusOptions={["execucao", "Aguardando Aprovação"]}
      />
    ),
  },
  {
    accessorKey: "valor_autorizado",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Valor autorizado" />
    ),
    accessorFn: (row) => row.minimun_orcament || "N/A",
  },
  {
    accessorKey: "prazo_de_entrega",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Prazo de entrega" />
    ),
    accessorFn: (row) => row.tipo_manutencao,
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    accessorFn: () => "Executando",
  },
  {
    id: "actions",
    cell: ({ row }) => <ActionCell row={row.original} />,
    enableSorting: false,
    enableHiding: false,
  },
];
export const osFinalizadasColumn: ColumnDef<OS>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "osNumber",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Nº OS" />
    ),
    accessorFn: (row) =>
      `#${row.osNumber}/${new Date(row.createdAt).getFullYear()}` || "N/A",
  },
  {
    accessorKey: "centro_de_custo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Centro de Custo" />
    ),
    accessorFn: (row) =>
      row.veiculo?.lotacao_veiculos.centro_custo?.descricao || "N/A",
  },
  {
    accessorKey: "tipo_de_serviço",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Tipo de Serviço" />
    ),
    accessorFn: (row) => row.TiposDeOs?.descricao || "N/A",
  },
  {
    accessorKey: "veículo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Veículo" />
    ),
    accessorFn: (row) =>
      `${row.veiculo?.placa} |${row.veiculo?.marca?.descricao} | ${row.veiculo?.modelo?.descricao}` ||
      "N/A",
  },
  {
    accessorKey: "data",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Data" />
    ),
    accessorFn: (row) => format(row.createdAt, "dd/MM/yyyy HH:mm:ss") || "N/A",
  },
  {
    accessorKey: "cidade",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Cidade" />
    ),
    accessorFn: (row) => `${row.cidade_loc} - ${row.estado_loc}` || "N/A",
  },
  {
    accessorKey: "credenciado",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Credenciado" />
    ),
    cell: ({ row }) => (
      <CredenciadoCell
        row={row.original}
        filterStatusOptions={["finalizada", "execucao"]}
      />
    ),
  },
  {
    accessorKey: "valor_total",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Valor total" />
    ),
    accessorFn: (row) => {
      const finalizadas = row.orcamentos?.filter(
        (orc) =>
          orc.status === "finalizada" ||
          orc.status === "Aguardando Aprovação" ||
          orc.status === "execucao"
      );
      if (!finalizadas || finalizadas.length === 0) return "N/A";
      const total = finalizadas.reduce(
        (acc, orc) => acc + (orc.valorTotal || 0),
        0
      );
      return (total / 100).toLocaleString("pt-BR", {
        style: "currency",
        currency: "BRL",
      });
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    accessorFn: (row) => {
      return row.arquivosNotaFiscal?.length && row.status === "finalizada"
        ? "Finalizada"
        : row.status === "Aguardando Aprovação"
        ? row.status
        : "Aguardando Nota Fiscal";
    },
  },
];
export const osFaturadasColumn: ColumnDef<OS>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "osNumber",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Nº OS" />
    ),
    accessorFn: (row) =>
      row?.osNumber && row?.createdAt
        ? `#${row.osNumber}/${new Date(row.createdAt).getFullYear()}`
        : "N/A",
  },
  {
    accessorKey: "centro_de_custo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Centro de Custo" />
    ),
    accessorFn: (row) =>
      row?.veiculo?.lotacao_veiculos?.centro_custo?.descricao || "N/A",
  },
  {
    accessorKey: "veículo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Veículo" />
    ),
    accessorFn: (row) => {
      const modelo = row?.veiculo?.modelo?.descricao || "";
      const placa = row?.veiculo?.placa || "";
      const marca = row?.veiculo?.marca?.descricao || "";
      const info = [modelo, placa, marca].filter(Boolean).join(" | ");
      return info || "N/A";
    },
  },
  {
    accessorKey: "data",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Data" />
    ),
    accessorFn: (row) => {
      try {
        return row?.createdAt
          ? format(new Date(row.createdAt), "dd/MM/yyyy HH:mm:ss")
          : "N/A";
      } catch {
        return "N/A";
      }
    },
  },
  {
    accessorKey: "cidade",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Cidade" />
    ),
    accessorFn: (row) => {
      const cidade = row?.cidade_loc || "";
      const estado = row?.estado_loc || "";
      const local = [cidade, estado].filter(Boolean).join(" - ");
      return local || "N/A";
    },
  },
  {
    accessorKey: "credenciado",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Credenciado" />
    ),
    cell: ({ row }) => (
      <CredenciadoCell
        row={row?.original}
        filterStatusOptions={[
          "faturada",
          "finalizada",
          "execucao",
          "Aguardando Aprovação",
        ]}
      />
    ),
  },
  {
    accessorKey: "valor_total",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Valor total" />
    ),
    accessorFn: (row) => {
      const finalizadas = row.orcamentos?.filter(
        (orc) =>
          orc.status === "finalizada" ||
          orc.status === "faturada" ||
          orc.status === "execucao"
      );
      if (!finalizadas || finalizadas.length === 0) return "N/A";
      const total = finalizadas.reduce(
        (acc, orc) => acc + (orc.valorTotal || 0),
        0
      );
      return (total / 100).toLocaleString("pt-BR", {
        style: "currency",
        currency: "BRL",
      });
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    accessorFn: (row) => row?.status || "N/A",
  },

  {
    accessorKey: "fornecedor",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Fornecedor" />
    ),
    accessorFn: (row) =>
      row.orcamentos?.[0]?.credenciado?.informacoes?.[0]?.nome_fantasia ||
      row.orcamentos?.[0]?.credenciado?.informacoes?.[0]?.razao_social ||
      "N/A",
  },
  {
    accessorKey: "valor_mao_de_obra",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Valor Mão de Obra" />
    ),
    accessorFn: (row) => {
      const valor_servicos =
        row?.veiculo?.faturamentoVeiculo?.empenho?.valor_servicos || 0;

      return (valor_servicos / 100).toLocaleString("pt-BR", {
        style: "currency",
        currency: "BRL",
      });
    },
  },
  {
    accessorKey: "valor_pecas",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Valor Peças" />
    ),
    accessorFn: (row) => {
      const valor_pecas =
        row?.veiculo?.faturamentoVeiculo?.empenho?.valor_pecas || 0;

      return (valor_pecas / 100).toLocaleString("pt-BR", {
        style: "currency",
        currency: "BRL",
      });
    },
  },

  {
    accessorKey: "nota_fiscal",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Nota Empenho" />
    ),
    accessorFn: (row) => {
      const orcamentos = row.orcamentos?.filter((orc) =>
        ["finalizada", "faturada", "execucao"].includes(orc?.status || "")
      );

      console.log("orcamentos ->", orcamentos);

      return row?.veiculo?.faturamentoVeiculo?.empenho?.nota_empenho || "";
    },
  },
];

export const osCanceladasColumn: ColumnDef<OS>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "osNumber",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Nº OS" />
    ),
    accessorFn: (row) =>
      `#${row.osNumber}/${new Date(row.createdAt).getFullYear()}` || "N/A",
  },
  {
    accessorKey: "centro_de_custo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Centro de Custo" />
    ),
    accessorFn: (row) =>
      row.veiculo?.lotacao_veiculos.centro_custo?.descricao || "N/A",
  },
  {
    accessorKey: "tipo_de_serviço",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Tipo de Serviço" />
    ),
    accessorFn: (row) => row.TiposDeOs?.descricao || "N/A",
  },
  {
    accessorKey: "veículo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Veículo" />
    ),
    accessorFn: (row) =>
      `${row.veiculo?.modelo?.descricao} | ${row.veiculo?.placa} |${row.veiculo?.marca?.descricao}` ||
      "N/A",
  },
  {
    accessorKey: "data",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Data" />
    ),
    accessorFn: (row) => format(row.createdAt, "dd/MM/yyyy HH:mm:ss") || "N/A",
  },
  {
    accessorKey: "cidade",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Cidade" />
    ),
    accessorFn: (row) => `${row.estado_loc} - ${row.cidade_loc}` || "N/A",
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    accessorFn: (row) => row.status || "N/A",
  },
];

export const osEnviadaColumn: ColumnDef<OS>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "osNumber",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Nº OS" />
    ),
    accessorFn: (row) =>
      `#${row.osNumber}/${new Date(row.createdAt).getFullYear()}` || "N/A",
  },
  {
    accessorKey: "centro_de_custo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Centro de Custo" />
    ),
    accessorFn: (row) =>
      row.veiculo?.lotacao_veiculos.centro_custo?.descricao || "N/A",
  },
  {
    accessorKey: "tipo_de_serviço",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Tipo de Serviço" />
    ),
    accessorFn: (row) => row.TiposDeOs?.descricao || "N/A",
  },
  {
    accessorKey: "veículo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Veículo" />
    ),
    accessorFn: (row) =>
      `${row.veiculo?.placa} |${row.veiculo?.marca?.descricao} | ${row.veiculo?.modelo?.descricao}` ||
      "N/A",
  },
  {
    accessorKey: "data",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Data" />
    ),
    accessorFn: (row) => format(row.createdAt, "dd/MM/yyyy") || "N/A",
  },
  {
    accessorKey: "cidade",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Cidade" />
    ),
    accessorFn: (row) => `${row.cidade_loc} / ${row.estado_loc}` || "N/A",
  },

  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    accessorFn: (row) => {
      const { session } = useSession();
      const filterOrcamento = row?.orcamentos?.find(
        (orcamento) => orcamento.credenciadoId === session?.credenciadoId
      );
      return filterOrcamento?.status || "N/A";
    },
  },
  // Na coluna actions
  {
    id: "actions",
    header: "Ações",
    cell: function ActionCell({ row }) {
      const { session } = useSession();
      const { contratos } = useContrato();

      const filterContrato = contratos?.find(
        (contrato) => contrato.id === session?.contratoId
      );

      const filterOrcamento = row?.original.orcamentos?.find(
        (orcamento) => orcamento.credenciadoId === session?.credenciadoId
      );

      const [showConductorModal, setShowConductorModal] = useState(false);

      const handleAuthSuccess = async () => {
        try {
          const orcamentoEnviado = row.original.orcamentos?.find(
            (orc: any) => orc.status === "autorizada"
          );

          if (!orcamentoEnviado) {
            toast.error("Não foi encontrado um orçamento enviado para esta OS");
            return;
          }

          await updateOrcamentoStatus(orcamentoEnviado.id, "execucao");
          await updateOsStatus(
            row.original.id,
            "execucao",
            orcamentoEnviado.id
          );

          toast.success("Serviço iniciado com sucesso!");
          row.original.orcamentos?.some(
            (orcamento) => orcamento.servico?.length === 0
          )
            ? window.location.reload()
            : (window.location.href = `/dashboard/checklists/novo-checklist/veiculo/${row.original?.veiculoId}?osId=${orcamentoEnviado?.osId}`);
        } catch (error) {
          toast.error("Erro ao iniciar execução: " + (error as Error).message);
        }
      };

      const centroCustoTagRfid =
        row.original.veiculo?.lotacao_veiculos?.centro_custo?.veiculos_com_rfid;

      return (
        <>
          {filterOrcamento?.status === "autorizada" &&
            !filterContrato?.veiculo_rfid && (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1 text-blue-600 border-blue-600 hover:border-blue-200"
                  onClick={(e) => {
                    e.stopPropagation();
                    if (centroCustoTagRfid) {
                      window.location.href = "/dashboard/orcamento/leitura-tag";
                      return;
                    }

                    session?.contrato.checklist_simplificado_pecas
                      ? handleAuthSuccess()
                      : setShowConductorModal(true);
                  }}
                >
                  <PlayCircle className="h-3.5 w-3.5" />
                  Executar Serviço
                </Button>
              </div>
            )}
          <ConductorAuthModal
            isOpen={showConductorModal}
            onClose={() => setShowConductorModal(false)}
            onSuccess={handleAuthSuccess}
            hasPassword={row.original.condutor?.password ? true : false}
            osCondutor={row.original.condutor}
            title="Credenciais do Condutor"
            description="Por favor, insira a matrícula e senha do condutor para iniciar a execução."
            buttonText="Iniciar Execução"
          />
        </>
      );
    },
  },
];

// Colunas específicas para orçamentos perdidos
export const osPerdidosColumn: ColumnDef<OS>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "osNumber",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Nº OS" />
    ),
    accessorFn: (row) =>
      `#${row.osNumber}/${new Date(row.createdAt).getFullYear()}` || "N/A",
  },
  {
    accessorKey: "centro_de_custo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Centro de Custo" />
    ),
    accessorFn: (row) =>
      row.veiculo?.lotacao_veiculos.centro_custo?.descricao || "N/A",
  },
  {
    accessorKey: "tipo_de_serviço",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Tipo de Serviço" />
    ),
    accessorFn: (row) => row.TiposDeOs?.descricao || "N/A",
  },
  {
    accessorKey: "veículo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Veículo" />
    ),
    accessorFn: (row) =>
      `${row.veiculo?.placa} | ${row.veiculo?.marca?.descricao} - ${row.veiculo?.modelo?.descricao}` ||
      "N/A",
  },
  {
    accessorKey: "data",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Data" />
    ),
    accessorFn: (row) => format(row.createdAt, "dd/MM/yyyy") || "N/A",
  },
  {
    accessorKey: "cidade",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Cidade" />
    ),
    accessorFn: (row) => `${row.cidade_loc}/${row.estado_loc}` || "N/A",
  },
  {
    accessorKey: "gestor_criador",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Gestor Criador" />
    ),
    accessorFn: (row) => row.gestorCriador || "N/A",
  },
  {
    accessorKey: "motivo_cancelamento",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Motivo do Cancelamento" />
    ),
    accessorFn: (row) => {
      // Para OS canceladas, mostrar informações relevantes
      if (row.status?.toLowerCase() === "cancelada") {
        return row.observacao || "OS Cancelada";
      }

      return "N/A";
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    accessorFn: (row) => {
      if (row.status?.toLowerCase() === "cancelada") {
        return "Cancelada";
      }
      return row.status || "N/A";
    },
  },
  {
    id: "actions",
    cell: ({ row }) => <ActionCell row={row.original} />,
    enableSorting: false,
    enableHiding: false,
  },
];
