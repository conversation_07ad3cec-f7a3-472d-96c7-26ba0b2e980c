import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { DataTableColumnHeader } from "@/components/tables/column-header";
import { ColumnDef } from "@tanstack/react-table";

export const empenhoColumns: ColumnDef<empenho>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Selecionar todos"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Selecionar linha"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "centro_custo.descricao",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Centro de custo" />,
  },
  {
    id: "empenhado",
    header: () => "Empenhado",
    cell: ({ row }) => {
      const { valor_pecas, valor_servicos } = row.original;
      const total = valor_pecas + valor_servicos;
      return (
        <div className="text-sm space-y-1">
          <div>
            Peças:{" "}
            {(valor_pecas / 100).toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}
          </div>
          <div>
            Serviço:{" "}
            {(valor_servicos / 100).toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}
          </div>
          <hr />
          <div className="font-semibold">
            Total:{" "}
            {(total / 100).toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}
          </div>
        </div>
      );
    },
  },
  {
    id: "bloqueado",
    header: () => "Bloqueado",
    cell: ({ row }) => {
      const { bloqueado_pecas, bloqueado_servicos } = row.original;
      const total = (bloqueado_pecas ?? 0) + (bloqueado_servicos ?? 0);
      return (
        <div className="text-sm space-y-1">
          <div>
            Peças:{" "}
            {((bloqueado_pecas ?? 0) / 100).toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}
          </div>
          <div>
            Serviços:{" "}
            {((bloqueado_servicos ?? 0) / 100).toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}
          </div>
          <hr />
          <div className="font-semibold">
            Total:{" "}
            {(total / 100).toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}
          </div>
        </div>
      );
    },
  },
  {
    id: "faturado",
    header: () => "Faturado",
    cell: ({ row }) => {
      const { faturado_pecas, faturado_servicos } = row.original;
      const total = (faturado_pecas ?? 0) + (faturado_servicos ?? 0);
      return (
        <div className="text-sm space-y-1">
          <div>
            Peças:{" "}
            {((faturado_pecas ?? 0) / 100).toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}
          </div>
          <div>
            Serviços:{" "}
            {((faturado_servicos ?? 0) / 100).toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}
          </div>
          <hr />
          <div className="font-semibold">
            Total:{" "}
            {(total / 100).toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}
          </div>
        </div>
      );
    },
  },
  {
    id: "contigenciado",
    header: () => "Contigenciado",
    cell: ({ row }) => {
      const { contigenciado_pecas, contigenciado_servicos } = row.original;
      const total = (contigenciado_pecas ?? 0) + (contigenciado_servicos ?? 0);
      return (
        <div className="text-sm space-y-1">
          <div>
            Peças:{" "}
            {((contigenciado_pecas ?? 0) / 100).toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}
          </div>
          <div>
            Serviços:{" "}
            {((contigenciado_servicos ?? 0) / 100).toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}
          </div>
          <hr />
          <div className="font-semibold">
            Total:{" "}
            {(total / 100).toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}
          </div>
        </div>
      );
    },
  },
  {
    id: "saldo",
    header: () => "Saldo",
    cell: ({ row }) => {
      const { saldo_pecas, saldo_servicos, saldo_total } = row.original;
      return (
        <div className="text-sm space-y-1">
          <div>
            Peças:{" "}
            {((saldo_pecas ?? 0) / 100).toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}
          </div>
          <div>
            Serviço:{" "}
            {((saldo_servicos ?? 0) / 100).toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}
          </div>
          <hr />
          <div className="font-semibold">
            Total:{" "}
            {((saldo_total ?? 0) / 100).toLocaleString("pt-BR", {
              style: "currency",
              currency: "BRL",
            })}
          </div>
        </div>
      );
    },
  },
  {
    id: "Status",
    header: () => "Status",
    cell: ({ row }) => {
      const { empenho_ativo } = row.original;
      return (
        <>
          <div className="mt-2">
            <Badge className={`${empenho_ativo ? "bg-green-500" : "bg-red-500"} text-white`}>
              {empenho_ativo ? "Desbloqueado" : "Bloqueado"}
            </Badge>
          </div>
        </>
      );
    },
  },
];
