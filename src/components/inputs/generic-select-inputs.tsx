"use client";

import { useFormContext } from "react-hook-form";
import { cva } from "class-variance-authority";
import { cn } from "@/lib/utils";
import { z } from "zod";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";

const inputGridVariants = cva("w-full grid gap-2", {
  variants: {
    variants: {
      default: "grid-cols-1 md:grid-cols-2",
      single: "grid-cols-1",
    },
  },
  defaultVariants: {
    variants: "default",
  },
});
/* eslint-disable @typescript-eslint/no-unused-vars */
export interface SelectFieldConfig<T extends z.ZodType<any, any>> {
  label: string;
  placeholder: string;
  description: string;
  options: { value: string; label: string }[];
}

interface GenericFormProps<T extends z.ZodType<any, any>> {
  fieldConfig: Record<keyof z.infer<T>, SelectFieldConfig<T>>;
  variants?: "default" | "single";
}

export function GenericSelectInput<T extends z.ZodType<any, any>>({
  fieldConfig,
  variants = "default",
}: GenericFormProps<T>) {
  const { control } = useFormContext();
  return (
    <div className={cn(inputGridVariants({ variants }))}>
      {Object.entries(fieldConfig).map(([name, config]) => (
        <FormField
          key={name}
          control={control}
          name={name}
          render={({ field }) => (
            <FormItem>
              <FormLabel>{config.label}</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={config.placeholder} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {config.options.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription>{config.description}</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      ))}
    </div>
  );
}
