import { Search } from "lucide-react";
import { useFormContext } from "react-hook-form";
import { Button } from "../ui/button";
import { CNPJInput } from "./numeric-input";
import { consultCnpj } from "@/serverActions/cnpjAction";
import { toast } from "sonner";
import { useState } from "react";
import { CnpjDetailsModal } from "../modal/cnpj-details-modal";
import { CnpjData } from "@/interfaces/cnpj.interface";

interface CnpjLookupInputProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export function CnpjLookupInput({ value, onChange, placeholder, className }: CnpjLookupInputProps) {
  const { setValue } = useFormContext();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [cnpjData, setCnpjData] = useState<CnpjData | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleLookup = async () => {
    try {
      if (!value) {
        toast("CNPJ vazio", {
          description: "Por favor, digite um CNPJ",
        });
        return;
      }

      setIsLoading(true);
      const cleanCnpj = value.replace(/\D/g, "");
      const response = await consultCnpj(cleanCnpj);
console.log(response)
      if (response) {
        setCnpjData(response);
        setIsModalOpen(true);
      }
    } catch (error) {
      toast("Erro na consulta", {
        description: "Não foi possível consultar o CNPJ",
      });
    } finally {
      setIsLoading(false);
    }
  };

    const handleConfirm = () => {
      if (cnpjData) {
        // Informações básicas
        setValue("informacoes.razao_social", cnpjData.company.name);
        setValue("informacoes.nome_fantasia", cnpjData.alias || "");
        setValue("informacoes.cnpj", cnpjData.taxId);
        setValue("informacoes.data_abertura", cnpjData.founded);
        setValue("informacoes.capital_social", cnpjData.company.equity);
        setValue("informacoes.porte_empresarial", cnpjData.company.size.text);
        setValue("informacoes.atividade_principal", `${cnpjData.mainActivity.id} - ${cnpjData.mainActivity.text}`);
        setValue("informacoes.capital_social", cnpjData.company.equity);
        
        // Endereço
        setValue("endereco.cep", cnpjData.address.zip);
        setValue("endereco.logradouro", cnpjData.address.street);
        setValue("endereco.numero", cnpjData.address.number);
        setValue("endereco.complemento", cnpjData.address.details || "");
        setValue("endereco.bairro", cnpjData.address.district);
        setValue("endereco.cidade", cnpjData.address.city);
        setValue("endereco.estado", cnpjData.address.state);
        
        // Contatos
        if (cnpjData.phones && cnpjData.phones.length > 0) {
          setValue("contatos.telefone", `(${cnpjData.phones[0].area}) ${cnpjData.phones[0].number}`);
          // Se houver um segundo telefone, preencher como celular
          if (cnpjData.phones.length > 1) {
            setValue("contatos.celular", `(${cnpjData.phones[1].area}) ${cnpjData.phones[1].number}`);
          }
        }
        
        if (cnpjData.emails && cnpjData.emails.length > 0) {
          setValue("contatos.email", cnpjData.emails[0].address);
        }
        
        toast("CNPJ encontrado", {
          description: "Dados preenchidos automaticamente",
        });
        setIsModalOpen(false);
      }
    };
  return (
    <>
      <div className="flex gap-2">
        <CNPJInput
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          className={className}
        />
        <Button type="button" disabled={isLoading} variant="outline" size="icon" onClick={handleLookup}>
          {isLoading ? (
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
          ) : (
            <Search className="h-4 w-4" />
          )}
        </Button>
      </div>

      {cnpjData && (
        <CnpjDetailsModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onConfirm={handleConfirm}
          data={cnpjData}
        />
      )}
    </>
  );
}
