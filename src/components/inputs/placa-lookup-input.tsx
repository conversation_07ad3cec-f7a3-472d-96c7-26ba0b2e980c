import { Search } from "lucide-react";
import { useFormContext } from "react-hook-form";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { toast } from "sonner";
import { useState } from "react";
import { VehicleDetailsModal } from "../modal/vehicle-details-modal";
import { useMarca } from "@/context/marca-context";
import { useModelo } from "@/context/modelo-context";
import { useVersao } from "@/context/versao-context";
import { getVehicleByPlateWithCache } from "@/service/suivCache.service";

interface PlacaLookupInputProps {
  name: string;
  placeholder?: string;
  className?: string;
}

export function PlacaLookupInput({ name, placeholder, className }: PlacaLookupInputProps) {
  const { setValue, register, trigger } = useFormContext();
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [vehicleData, setVehicleData] = useState<any | null>(null);

  const { marcas } = useMarca();
  const { modelos } = useModelo();
  const { versoes } = useVersao();

  const handleLookup = async () => {
    try {
      const fieldValue = (document.getElementsByName(name)[0] as HTMLInputElement)?.value;
      if (!fieldValue) {
        toast("Placa vazia", { description: "Por favor, digite uma placa" });
        return;
      }
      setIsLoading(true);
      const cleanPlate = fieldValue.replace(/[^a-zA-Z0-9]/g, "");
      const response = await getVehicleByPlateWithCache(cleanPlate);
      if (response) {
        setVehicleData(response);
        setIsModalOpen(true);
      }
    } catch (error) {
      toast("Erro na consulta", { description: "Não foi possível consultar a placa" });
    } finally {
      setIsLoading(false);
    }
  };

  const normalizeString = (str: string) => {
    return str?.toLowerCase().trim().replace(/\s+/g, " ") || "";
  };

  // Função de similaridade com base na interseção dos tokens
  const getStringSimilarity = (s1: string, s2: string) => {
    const tokens1 = s1.split(" ").filter((token) => token !== "");
    const tokens2 = s2.split(" ").filter((token) => token !== "");
    const intersection = tokens1.filter((token) => tokens2.includes(token)).length;
    const union = new Set([...tokens1, ...tokens2]).size;
    return union === 0 ? 0 : intersection / union;
  };

  // Função findBestMatch aprimorada para considerar similaridade por tokens
  const findBestMatch = (needle: string, haystack: any[], propertyName: string) => {
    const normalizedNeedle = normalizeString(needle);

    // Verifica correspondência exata
    const exactMatch = haystack.find(
      (item) => normalizeString(item[propertyName]) === normalizedNeedle
    );
    if (exactMatch) return exactMatch;

    // Verifica se há uma correspondência onde um contém o outro
    const containsMatch = haystack.find(
      (item) =>
        normalizeString(item[propertyName]).includes(normalizedNeedle) ||
        normalizedNeedle.includes(normalizeString(item[propertyName]))
    );
    if (containsMatch) return containsMatch;

    // Avalia similaridade baseada em interseção de tokens
    let bestMatch = null;
    let highestScore = 0;
    const threshold = 0.2; // Limiar mínimo para considerar um match

    haystack.forEach((item) => {
      const candidate = normalizeString(item[propertyName]);
      const score = getStringSimilarity(normalizedNeedle, candidate);
      if (score > highestScore) {
        highestScore = score;
        bestMatch = item;
      }
    });

    return highestScore >= threshold ? bestMatch : null;
  };

  const handleConfirm = () => {
    if (vehicleData) {
      setValue("placa", vehicleData.plate);
      setValue("ano_de_fabricacao", vehicleData.yearFab);
      setValue("ano_do_modelo", vehicleData.yearModel);
      setValue("cor", vehicleData.color);
      setValue("vin", vehicleData.vin);
      setValue("numero_do_motor", vehicleData.engineNumber);

      setValue("definicoes.numero_de_cambio", vehicleData.gearBoxNumber);
      setValue("definicoes.cilindradas", vehicleData.cubicCentimeters);
      setValue("definicoes.potencia", vehicleData.power);
      setValue("definicoes.carroceria", vehicleData.bodywork);
      setValue("definicoes.transmissao", vehicleData.transmission);
      setValue("definicoes.quantidade_de_assentos", vehicleData.seatCount);
      setValue("definicoes.quantidade_de_eixos", vehicleData.axisNumber);
      setValue("definicoes.numero_de_valvulas", vehicleData.engineValves);
      setValue("definicoes.combustivel", vehicleData.fuel);
      setValue("definicoes.origem_do_veiculo", vehicleData.isNational ? "nacional" : "importado");

      if (vehicleData.fipeDataCollection && vehicleData.fipeDataCollection.length > 0) {
        const fipeData = vehicleData.fipeDataCollection[0];
        setValue("fipe.codigo_fipe", fipeData.fipeId);
        setValue("fipe.valor_venal", fipeData.currentValue);
      }

      const findMarca = findBestMatch(vehicleData.maker, marcas, "descricao");
      if (findMarca) {
        setValue("marcaId", findMarca.id);
        const filteredModelos = modelos.filter((m: any) => m.marcaId === findMarca.id);
        const findModelo = findBestMatch(vehicleData.model, filteredModelos, "descricao");
        if (findModelo) {
          setValue("modeloId", findModelo.id);
          const filteredVersoes = versoes.filter((v: any) => v.modeloId === findModelo.id);
          const findVersao = findBestMatch(vehicleData.version, filteredVersoes, "descricao");
          // if (findVersao) {
          //   setValue("versaoId", findVersao.id);
          // }
          // if (findVersao) {
          //   setValue("versaoId", findVersao.id);
          // } else if (filteredVersoes.length > 0) {
          //   setValue("versaoId", filteredVersoes[0].id);
          // }
        }
        setTimeout(() => {
          trigger(["marcaId", "modeloId", "versaoId"]);
        }, 300);
      }
      toast("Veículo encontrado", { description: "Dados preenchidos automaticamente" });
      setIsModalOpen(false);
    }
  };

  return (
    <>
      <div className="flex gap-2">
        <Input {...register(name)} placeholder={placeholder} className={className} />
        <Button
          type="button"
          disabled={isLoading}
          variant="outline"
          size="icon"
          onClick={handleLookup}>
          {isLoading ? (
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
          ) : (
            <Search className="h-4 w-4" />
          )}
        </Button>
      </div>
      {vehicleData && (
        <VehicleDetailsModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onConfirm={handleConfirm}
          data={vehicleData}
        />
      )}
    </>
  );
}
