"use client";

import { MultipleSelector } from "@/components/ui/multiple-selector";
import { Controller, useFormContext } from "react-hook-form";

type MultipleSelectorType = {
  name: string;
  options: { value: string; label: string }[];
  placeHolder?: string;
  onChange?: (value: any, form: any) => void; // <- adiciona esse
};

function MultipleSelection({
  name,
  options,
  placeHolder,
  onChange, // <- recebe onChange
}: MultipleSelectorType) {
  const { control } = useFormContext();
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, formState }) => (
        <div className="space-y-4">
          <div>
            <MultipleSelector
              placeholder={placeHolder ? placeHolder : "Selecione uma opção"}
              defaultOptions={options}
              value={field.value || []}
              onChange={(selected) => {
                field.onChange(selected); // Atualiza react-hook-form
                if (onChange) {
                  onChange(selected, formState); // Executa seu custom onChange
                }
              }}
              className="w-full"
            />
          </div>
        </div>
      )}
    />
  );
}

export { MultipleSelection };
