"use client";

import React, { forwardRef } from "react";
import { Input } from "@/components/ui/input";
import { apenasNumerosValidator } from "@/lib/validators";
import { normalizeInputValue } from "@/lib/utils";


export const InputNumerico = forwardRef<
  HTMLInputElement, 
  React.InputHTMLAttributes<HTMLInputElement>
>(
  ({ className, onKeyDown, value, onChange, ...props }, ref) => {

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      
      if (
        /^\d$/.test(e.key) || 
        ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'Home', 'End',
        'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(e.key) ||
        ((e.ctrlKey || e.metaKey) && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase()))
      ) {
       
        if (onKeyDown) {
          onKeyDown(e);
        }
        return;
      }
      
      
      e.preventDefault();
    };
    
   
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      
      const numericValue = e.target.value.replace(/\D/g, '');
      
     
      if (onChange) {
        const syntheticEvent = Object.create(e);
        syntheticEvent.target = { ...e.target, value: numericValue };
        onChange(syntheticEvent);
      }
    };
    
    return (
      <Input
        {...props}
        className={className}
        type="text"
        inputMode="numeric"
        pattern="[0-9]*"
        onKeyDown={handleKeyDown}
        onChange={handleChange}
        ref={ref}
        value={value === null || value === undefined ? "" : value}
      />
    );
  }
);

InputNumerico.displayName = "InputNumerico"; 