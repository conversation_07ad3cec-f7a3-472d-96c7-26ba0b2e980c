import React, { forwardRef, useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { cleanNonNumeric } from "@/lib/validators";

export interface NumericInputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "onChange"> {
  onChange?: (value: string) => void;
  onValueChange?: (value: string) => void;
  maxLength?: number;
  allowLeadingZeros?: boolean;
}

/**
 * Componente de input que aceita apenas números
 * Use este componente para campos como CNJ, CPF, CNPJ e outros que exigem apenas dígitos
 */
export const NumericInput = forwardRef<HTMLInputElement, NumericInputProps>(
  (
    { value, onChange, onValueChange, maxLength, allowLeadingZeros = true, className, ...props },
    ref
  ) => {
    const [innerValue, setInnerValue] = useState<string>((value as string) || "");
    const [displayValue, setDisplayValue] = useState<string>("");

    // Sincronizar o valor interno com o valor externo, se for alterado
    useEffect(() => {
      if (value !== undefined && value !== innerValue) {
        setInnerValue(value as string);
      }
    }, [value]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      // Remove todos os caracteres não numéricos
      let newValue = cleanNonNumeric(e.target.value);

      // Opcional: remover zeros à esquerda (se não forem permitidos)
      if (!allowLeadingZeros && newValue.length > 0) {
        newValue = String(parseInt(newValue, 10));
        // Tratar o caso especial de valor zero
        if (newValue === "0" || newValue === "NaN") {
          newValue = "0";
        }
      }

      // Limitar comprimento se maxLength estiver definido
      if (maxLength && newValue.length > maxLength) {
        newValue = newValue.slice(0, maxLength);
      }

      // Atualizar o estado interno
      setInnerValue(newValue);

      // Chamar callbacks fornecidos pelos pais
      if (onChange) {
        onChange(newValue);
      }

      if (onValueChange) {
        onValueChange(newValue);
      }
    };

    return (
      <Input
        {...props}
        ref={ref}
        value={displayValue || innerValue}
        onChange={handleChange}
        maxLength={maxLength}
        className={className}
        inputMode="numeric"
        pattern="[0-9]*"
      />
    );
  }
);

NumericInput.displayName = "NumericInput";

/**
 * Aplica máscara de CPF (000.000.000-00)
 */
const applyCpfMask = (value: string): string => {
  if (!value) return "";

  return value
    .replace(/\D/g, "")
    .replace(/(\d{3})(\d)/, "$1.$2")
    .replace(/(\d{3})(\d)/, "$1.$2")
    .replace(/(\d{3})(\d{1,2})$/, "$1-$2");
};

/**
 * Aplica máscara de CNPJ (00.000.000/0000-00)
 */
const applyCnpjMask = (value: string): string => {
  if (!value) return "";

  return value
    .replace(/\D/g, "")
    .replace(/^(\d{2})(\d)/, "$1.$2")
    .replace(/^(\d{2})\.(\d{3})(\d)/, "$1.$2.$3")
    .replace(/\.(\d{3})(\d)/, ".$1/$2")
    .replace(/(\d{4})(\d)/, "$1-$2");
};

/**
 * Aplica máscara de telefone (00) 0000-0000 ou (00) 00000-0000
 */
const applyPhoneMask = (value: string): string => {
  if (!value) return "";

  value = value.replace(/\D/g, "");

  if (value.length > 10) {
    // Celular com 9º dígito
    return value.replace(/^(\d{2})(\d)/g, "($1) $2").replace(/(\d{5})(\d)/, "$1-$2");
  } else {
    // Telefone fixo
    return value.replace(/^(\d{2})(\d)/g, "($1) $2").replace(/(\d{4})(\d)/, "$1-$2");
  }
};

/**
 * Componente especializado para números CNJ
 */
export const CNJInput = forwardRef<HTMLInputElement, NumericInputProps>(
  ({ maxLength = 20, ...props }, ref) => {
    return (
      <NumericInput
        ref={ref}
        maxLength={maxLength}
        placeholder="Digite o número CNJ (apenas números)"
        {...props}
      />
    );
  }
);

CNJInput.displayName = "CNJInput";

/**
 * Componente especializado para CPF
 */
export const CPFInput = forwardRef<HTMLInputElement, NumericInputProps>(
  ({ value, onChange, ...props }, ref) => {
    const [displayValue, setDisplayValue] = useState("");

    useEffect(() => {
      setDisplayValue(applyCpfMask((value as string) || ""));
    }, [value]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const rawValue = cleanNonNumeric(e.target.value);
      setDisplayValue(applyCpfMask(rawValue));

      if (onChange) {
        onChange(rawValue.slice(0, 11));
      }
    };

    return (
      <Input
        ref={ref}
        value={displayValue}
        onChange={handleChange}
        maxLength={14} // Com máscara: 14 caracteres
        placeholder="000.000.000-00"
        inputMode="numeric"
        {...props}
      />
    );
  }
);

CPFInput.displayName = "CPFInput";

/**
 * Componente especializado para CNPJ
 */
export const CNPJInput = forwardRef<HTMLInputElement, NumericInputProps>(
  ({ value, onChange, ...props }, ref) => {
    const [displayValue, setDisplayValue] = useState("");

    useEffect(() => {
      setDisplayValue(applyCnpjMask((value as string) || ""));
    }, [value]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const rawValue = cleanNonNumeric(e.target.value);
      setDisplayValue(applyCnpjMask(rawValue));

      if (onChange) {
        onChange(rawValue.slice(0, 14));
      }
    };

    return (
      <Input
        ref={ref}
        value={displayValue}
        onChange={handleChange}
        maxLength={18} // Com máscara: 18 caracteres
        placeholder="00.000.000/0000-00"
        inputMode="numeric"
        {...props}
      />
    );
  }
);

CNPJInput.displayName = "CNPJInput";

/**
 * Componente especializado para telefone fixo
 * Formato esperado: (00) 0000-0000
 */
export const TelefoneInput = forwardRef<HTMLInputElement, NumericInputProps>(
  ({ value, onChange, ...props }, ref) => {
    const [displayValue, setDisplayValue] = useState("");

    useEffect(() => {
      setDisplayValue(applyPhoneMask((value as string) || ""));
    }, [value]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const rawValue = cleanNonNumeric(e.target.value);
      setDisplayValue(applyPhoneMask(rawValue));

      if (onChange) {
        onChange(rawValue.slice(0, 10));
      }
    };

    return (
      <Input
        ref={ref}
        value={displayValue}
        onChange={handleChange}
        maxLength={14} // Com máscara: (00) 0000-0000 = 14 caracteres
        placeholder="(00) 0000-0000"
        inputMode="tel"
        {...props}
      />
    );
  }
);

TelefoneInput.displayName = "TelefoneInput";

/**
 * Componente especializado para celular
 * Formato esperado: (00) 00000-0000
 */
export const CelularInput = forwardRef<HTMLInputElement, NumericInputProps>(
  ({ value, onChange, ...props }, ref) => {
    const [displayValue, setDisplayValue] = useState("");

    useEffect(() => {
      setDisplayValue(applyPhoneMask((value as string) || ""));
    }, [value]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const rawValue = cleanNonNumeric(e.target.value);
      setDisplayValue(applyPhoneMask(rawValue));

      if (onChange) {
        onChange(rawValue.slice(0, 11));
      }
    };

    return (
      <Input
        ref={ref}
        value={displayValue}
        onChange={handleChange}
        maxLength={15} // Com máscara: (00) 00000-0000 = 15 caracteres
        placeholder="(00) 00000-0000"
        inputMode="tel"
        {...props}
      />
    );
  }
);

CelularInput.displayName = "CelularInput";
