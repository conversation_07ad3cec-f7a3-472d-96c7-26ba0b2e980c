"use client";

import * as React from "react";
import { Check, ChevronsUpDown, Search } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import { useFormContext } from "react-hook-form";

type ComboboxType = {
  datas: any[] | { [key: string]: any };
  placeholder?: string;
  referenceId: string;
  description?: string;
  title: string;
  chave: string;
  name: string;
  filterFn?: (data: any) => boolean;
  fieldClassName?: string;
  multiple?: boolean;
  prefix?: string;
  onChange?: (value: any, form: any) => void;
};

const CLEAR_OPTION_ID = "___clear___";

function getSingleNestedValue(obj: any, path: string): string {
  if (!obj || !path) return "";

  const parts = path.split(/\.|\[|\]/).filter((part) => part !== "");
  let current = obj;

  for (const part of parts) {
    if (current === null || current === undefined) return "";
    if (!isNaN(Number(part))) {
      current = current[Number(part)];
    } else {
      current = current[part];
    }
    if (current === null || current === undefined) return "";
  }
  return current?.toString() || "";
}

function getNestedValue(obj: any, path: string, prefix?: string) {
  if (!obj || !path) return prefix || "";

  if (path.includes("+")) {
    const value = path
      .split("+")
      .map((part) => getSingleNestedValue(obj, part.trim()))
      .filter((val) => val)
      .join(" - ");
    return prefix ? `${prefix} ${value}` : value;
  }
  const value = getSingleNestedValue(obj, path);
  return prefix ? `${prefix} ${value}` : value;
}

export function Combobox({
  datas,
  title,
  placeholder,
  referenceId,
  description,
  chave,
  name,
  filterFn,
  fieldClassName,
  multiple = false,
  prefix,
  onChange,
}: ComboboxType) {
  const { control, setValue, watch } = useFormContext();
  const [isPopoverOpen, setIsPopoverOpen] = React.useState(false);
  const [searchTerm, setSearchTerm] = React.useState("");
  const currentValues = watch(name) || (multiple ? [] : "");
  const clearedManuallyRef = React.useRef(false);

  const dataArray = React.useMemo(() => {
    let result: any[] = [];
    if (Array.isArray(datas)) {
      result = datas;
    } else if (datas && typeof datas === "object") {
      result = datas.centrosCusto || datas.data || [];
    }

    if (filterFn && Array.isArray(result)) {
      result = result.filter(filterFn);
    }

    return result;
  }, [datas, filterFn]);

  const hasValidData = React.useMemo(() => {
    if (!Array.isArray(dataArray)) return false;
    if (dataArray.length === 0) return false;

    const hasUniqueIds = dataArray.every(
      (data, index, self) =>
        data &&
        data.id &&
        self.findIndex((d) => d && d.id === data.id) === index
    );

    return hasUniqueIds;
  }, [dataArray]);

  const displayItems = React.useMemo(() => {
    if (!hasValidData) return [];

    return dataArray.map((item) => ({
      id: item.id,
      displayValue: getNestedValue(item, chave, prefix),
    }));
  }, [dataArray, chave, prefix, hasValidData]);

  const displayItemsWithClear = React.useMemo(() => {
    if (!hasValidData || multiple) return displayItems;

    return [
      { id: CLEAR_OPTION_ID, displayValue: "Sem seleção" },
      ...displayItems,
    ];
  }, [displayItems, hasValidData, multiple]);

  const filteredItems = React.useMemo(() => {
    if (!hasValidData) return [];
    if (!searchTerm) return displayItemsWithClear;

    return displayItemsWithClear.filter((item) =>
      item.displayValue.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [displayItemsWithClear, searchTerm, hasValidData]);

  const displayValue = React.useMemo(() => {
    if (!hasValidData) return placeholder;

    if (multiple) {
      if (!currentValues || currentValues.length === 0) return placeholder;

      const selectedItems = currentValues
        .map((id: string) => {
          const item = displayItems.find((item) => item.id === id);
          return item ? item.displayValue : "";
        })
        .filter(Boolean);

      if (selectedItems.length === 0) return placeholder;
      if (selectedItems.length === 1) return selectedItems[0];

      const maxDisplayLength = 120;
      let displayText = selectedItems.join(", ");

      if (displayText.length > maxDisplayLength) {
        displayText = displayText.substring(0, maxDisplayLength) + "...";
      }

      return displayText;
    } else {
      if (
        currentValues === null ||
        currentValues === undefined ||
        currentValues === ""
      )
        return placeholder;

      const item = displayItems.find((item) => item.id === currentValues);
      return item ? item.displayValue : placeholder;
    }
  }, [currentValues, displayItems, placeholder, multiple, hasValidData]);

  React.useEffect(() => {
    if (
      !multiple &&
      hasValidData &&
      dataArray.length === 1 &&
      (currentValues === "" ||
        currentValues === null ||
        currentValues === undefined) &&
      !clearedManuallyRef.current
    ) {
      const singleId = dataArray[0].id;
      setValue(name, singleId);
      if (referenceId) setValue(referenceId, singleId);
    }
  }, [
    multiple,
    hasValidData,
    dataArray,
    currentValues,
    name,
    referenceId,
    setValue,
  ]);

  const handleSelect = (itemId: string) => {
    if (multiple) {
      const newValues = currentValues.includes(itemId)
        ? currentValues.filter((id: string) => id !== itemId)
        : [...currentValues, itemId];
      setValue(name, newValues);
      if (referenceId) setValue(referenceId, newValues);
    } else {
      if (itemId === CLEAR_OPTION_ID) {
        clearedManuallyRef.current = true;
        setValue(name, null);
        if (referenceId) setValue(referenceId, null);
      } else {
        clearedManuallyRef.current = false;
        setValue(name, itemId);
        if (referenceId) setValue(referenceId, itemId);
      }
      setIsPopoverOpen(false);
    }
  };

  return (
    <FormField
      control={control}
      name={name}
      render={({ field, formState }) => (
        <FormItem
          className={`${fieldClassName || ""} flex flex-col w-full py-2`}
        >
          <FormLabel>{title}</FormLabel>
          {!hasValidData ? (
            <FormControl>
              <Button
                variant="outline"
                disabled
                className="text-muted-foreground"
              >
                Sem dados disponíveis
              </Button>
            </FormControl>
          ) : (
            <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button
                    variant="outline"
                    role="combobox"
                    className={cn(
                      "justify-between text-ellipsis overflow-hidden whitespace-nowrap",
                      (!currentValues ||
                        (multiple && currentValues.length === 0)) &&
                        "text-muted-foreground"
                    )}
                  >
                    <span className="overflow-hidden text-ellipsis">
                      {displayValue}
                    </span>
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className="w-[320px] p-0">
                <div className="rounded-lg border shadow-md">
                  <div className="flex items-center border-b px-3">
                    <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                    <input
                      className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                      placeholder={`Pesquise por ${placeholder || title}`}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <div className="max-h-[200px] overflow-y-auto overflow-x-hidden">
                    {filteredItems.length === 0 ? (
                      <div className="py-6 text-center text-sm">Nada encontrado</div>
                    ) : (
                      <div className="overflow-hidden p-1 text-foreground">
                      {filteredItems.slice(0, 100).map((item) => (
                        <div
                          key={item.id}
                          className="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground"
                          onClick={() => {
                            handleSelect(item.id);
                            if (onChange) {
                              onChange(item.id, setValue);
                            }
                          }}
                        >
                          <Check
                            className={cn(
                              "mr-2 h-4 w-4",
                              multiple
                                ? currentValues.includes(item.id)
                                  ? "opacity-100"
                                  : "opacity-0"
                                : item.id === CLEAR_OPTION_ID
                                ? currentValues === null ||
                                  currentValues === undefined ||
                                  currentValues === ""
                                  ? "opacity-100"
                                  : "opacity-0"
                                : item.id === currentValues
                                ? "opacity-100"
                                : "opacity-0"
                            )}
                          />
                          {item.displayValue}
                        </div>
                      ))}
                      {filteredItems.length > 100 && (
                        <div className="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none opacity-50">
                          ...mais {filteredItems.length - 100} itens (refine sua
                          pesquisa)
                        </div>
                      )}
                      </div>
                    )}
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          )}
          <FormDescription>{description}</FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
