import React, { forwardRef, useState, useEffect } from "react";
import { NumericInput } from "./numeric-input";
import { useCep, type CepResponse } from "@/hooks/useCep";
import { useFormContext } from "react-hook-form";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

export interface CepInputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "onChange"> {
  name?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement> | string) => void;
  onValueChange?: (value: string) => void;
  onCepFound?: (data: CepResponse) => void;
  addressMapping?: {
    street?: string;
    neighborhood?: string;
    city?: string;
    state?: string;
    complement?: string;
  };
}

/**
 * Componente de input para CEP que busca automaticamente o endereço
 * quando o CEP é válido e preenche outros campos do formulário automaticamente
 */
export const CepInput = forwardRef<HTMLInputElement, CepInputProps>(
  ({ 
    value, 
    onChange, 
    onValueChange, 
    addressMapping,
    onCepFound,
    className,
    disabled,
    ...props 
  }, ref) => {
    const { loading, error, fetchAddressByCep } = useCep();
    const form = useFormContext();
    const [lastSearched, setLastSearched] = useState<string>("");
    const [inputValue, setInputValue] = useState<string>(value as string || "");

    // Sincronizar o valor interno com o valor externo
    useEffect(() => {
      if (value !== undefined && value !== inputValue) {
        setInputValue(value as string);
      }
    }, [value]);

    // Função para tratar valores de CEP e consultar a API
    const handleCepValue = async (value: string) => {
           setInputValue(value);
           if (onChange) {
             if (typeof onChange === "function") {
               const syntheticEvent = {
                 target: {
                   name: props.name,
                   value: value,
                 },
                 currentTarget: {
                   name: props.name,
                   value: value,
                 },
               } as React.ChangeEvent<HTMLInputElement>;

               onChange(syntheticEvent);
             }
           }
           if (onValueChange) {
             onValueChange(value);
           }

           const cep = value.replace(/\D/g, "");
      
      if (cep.length === 8 && cep !== lastSearched) {
        
        setLastSearched(cep);
        
        try {
          const data = await fetchAddressByCep(cep);
          
          if (data && form && addressMapping) {
            
            // Preencher os campos relacionados no formulário
            if (addressMapping.street && data.logradouro) {
              form.setValue(addressMapping.street, data.logradouro, { shouldValidate: true, shouldDirty: true });
            }
            
            if (addressMapping.neighborhood && data.bairro) {
              form.setValue(addressMapping.neighborhood, data.bairro, { shouldValidate: true, shouldDirty: true });
            }
            
            if (addressMapping.city && data.localidade) {
              form.setValue(addressMapping.city, data.localidade, { shouldValidate: true, shouldDirty: true });
            }
            
            if (addressMapping.state && data.uf) {
              form.setValue(addressMapping.state, data.uf, { shouldValidate: true, shouldDirty: true });
            }
            
            if (addressMapping.complement && data.complemento) {
              form.setValue(addressMapping.complement, data.complemento, { shouldValidate: true, shouldDirty: true });
            }
            
            // Gatilho para forçar a atualização do formulário
            form.trigger();
            
            // Mostrar uma notificação de sucesso
            toast.success("Endereço preenchido automaticamente", {
              description: "Os campos de endereço foram preenchidos com base no CEP informado."
            });
          } else {
            if (!data) console.log("Dados do CEP não encontrados");
            if (!form) console.log("Contexto do formulário não disponível");
            if (!addressMapping) console.log("Mapeamento de endereço não fornecido");
          }

          // Callback quando o CEP é encontrado
          if (data && onCepFound) {
            onCepFound(data);
          }
        } catch (error) {
          console.error("Erro ao processar CEP:", error);
          toast.error("Erro ao buscar o CEP", {
            description: "Não foi possível encontrar o endereço para o CEP informado."
          });
        }
      }
    };

    // Adaptador para compatibilidade com eventos React tradicionais
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      handleCepValue(e.target.value);
    };
    
    // Adaptador para o componente NumericInput
    const handleNumericInputChange = (value: string) => {
      handleCepValue(value);
    };

    // Efeito para processar o valor inicial, se houver
    useEffect(() => {
      const initialValue = value as string;
      if (initialValue && initialValue.length === 8 && initialValue !== lastSearched) {
        console.log("Processando valor inicial:", initialValue);
        handleCepValue(initialValue);
      }
    }, []);

    // Renderizar o loader quando estiver buscando o CEP
    const renderLoader = () => {
      if (loading) {
        return (
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          </div>
        );
      }
      return null;
    };
    
    return (
      <div className="relative">
        <NumericInput
          ref={ref}
          value={inputValue}
          onValueChange={handleNumericInputChange}
          maxLength={8}
          placeholder={props.placeholder || "Digite apenas números"}
          className={className}
          disabled={disabled || loading}
          {...props}
        />
        {renderLoader()}
        {error && (
          <p className="text-sm text-destructive mt-1">{error}</p>
        )}
      </div>
    );
  }
);

CepInput.displayName = "CepInput"; 