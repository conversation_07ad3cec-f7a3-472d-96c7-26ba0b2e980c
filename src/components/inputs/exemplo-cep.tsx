'use client';
import React, { useState } from 'react';
import { CepInput } from './cep-input';
import { Form, FormControl, FormField, FormItem, FormLabel } from '../ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { cepValidator } from '@/lib/validators';

// Esquema de validação
const formSchema = z.object({
  cep: cepValidator(),
  logradouro: z.string().optional(),
  bairro: z.string().optional(),
  cidade: z.string().optional(),
  estado: z.string().optional(),
});

type FormData = z.infer<typeof formSchema>;

export function ExemploCep() {
  const [resultadoConsulta, setResultadoConsulta] = useState<string>('');

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      cep: '',
      logradouro: '',
      bairro: '',
      cidade: '',
      estado: '',
    },
  });

  const onSubmit = (data: FormData) => {
    console.log('Formulário enviado:', data);
    setResultadoConsulta(JSON.stringify(data, null, 2));
  };

  return (
    <div className="p-6 max-w-2xl mx-auto bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-center">Exemplo de preenchimento automático de CEP</h2>
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="cep"
            render={({ field }) => (
              <FormItem>
                <FormLabel>CEP</FormLabel>
                <FormControl>
                  <CepInput
                    {...field}
                    addressMapping={{
                      street: 'logradouro',
                      neighborhood: 'bairro',
                      city: 'cidade',
                      state: 'estado',
                    }}
                    placeholder="Digite o CEP (apenas números)"
                  />
                </FormControl>
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="logradouro"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Logradouro</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Logradouro" />
                </FormControl>
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="bairro"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Bairro</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Bairro" />
                </FormControl>
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="cidade"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Cidade</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Cidade" />
                </FormControl>
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="estado"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Estado</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Estado" />
                </FormControl>
              </FormItem>
            )}
          />
          
          <Button type="submit" className="w-full">Enviar</Button>
        </form>
      </Form>
      
      {resultadoConsulta && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2">Dados do formulário:</h3>
          <pre className="bg-gray-100 p-4 rounded">{resultadoConsulta}</pre>
        </div>
      )}
    </div>
  );
} 