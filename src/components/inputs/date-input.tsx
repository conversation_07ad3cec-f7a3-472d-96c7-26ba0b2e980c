// components/inputs/date-input.tsx
"use client";

import React from "react";
import { Input, InputProps } from "@/components/ui/input";

interface DateInputProps extends Omit<InputProps, "onChange" | "value"> {
  onChange?: (value: string) => void; 
  value?: string | null; 
}

export const DateInput = React.forwardRef<HTMLInputElement, DateInputProps>(
  ({ onChange, value, ...props }, ref) => {
    const formatDate = (dateInput: string | Date | null | undefined): string => {
      if (!dateInput) return "";
    
      let dateStr: string;
      if (typeof dateInput !== "string") {
        if (dateInput instanceof Date) {
          dateStr = dateInput.toISOString().split("T")[0];
        } else {
          return "";
        }
      } else {
        dateStr = dateInput;
      }
    
      // Converte de YYYY-MM-DD para DD/MM/YYYY
      const [year, month, day] = dateStr.split("-");
      if (year && month && day) {
        return `${day}/${month}/${year}`;
      }
      return dateStr;
    };

    const parseDate = (formattedDate: string): string => {
      if (!formattedDate) return "";

      // Converte de DD/MM/YYYY para YYYY-MM-DD
      const [day, month, year] = formattedDate.split("/");
      if (day && month && year) {
        return `${year}-${month}-${day}`;
      }
      return formattedDate;
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const rawValue = e.target.value;

      // Remove caracteres não numéricos, exceto barras
      let cleanedValue = rawValue.replace(/[^\d/]/g, "");

      // Formata automaticamente enquanto digita
      if (cleanedValue.length > 2 && !cleanedValue.includes("/")) {
        cleanedValue = `${cleanedValue.slice(0, 2)}/${cleanedValue.slice(2)}`;
      }
      if (cleanedValue.length > 5 && cleanedValue.split("/").length < 3) {
        cleanedValue = `${cleanedValue.slice(0, 5)}/${cleanedValue.slice(5)}`;
      }

      // Limita o tamanho máximo
      if (cleanedValue.length > 10) {
        cleanedValue = cleanedValue.slice(0, 10);
      }

      // Atualiza o valor do input
      e.target.value = cleanedValue;

      // Envia o valor formatado para YYYY-MM-DD
      if (onChange) {
        onChange(parseDate(cleanedValue));
      }
    };

    return (
      <Input
        {...props}
        ref={ref}
        type="text"
        placeholder="dd/mm/aaaa"
        value={formatDate(value || "")}
        onChange={handleChange}
      />
    );
  }
);

DateInput.displayName = "DateInput";
