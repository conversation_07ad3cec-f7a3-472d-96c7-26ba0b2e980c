"use client";

import { useForm<PERSON>ontext, UseFormReturn } from "react-hook-form";
import { cva } from "class-variance-authority";
import { cn, normalizeInputValue } from "@/lib/utils";
import { z } from "zod";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Switch } from "../ui/switch";
import { Combobox } from "./combo-box";
import { Card, CardContent } from "../ui/card";
import { MultipleSelection } from "./multi-selector-input";
import {
  CNJInput,
  CNPJInput,
  CPFInput,
  NumericInput,
  TelefoneInput,
  CelularInput,
} from "./numeric-input";
import { CepInput } from "./cep-input";
import { CurrencyInput } from "./currency-input";
import { DateInput } from "./date-input";
import React from "react";
import { CnpjLookupInput } from "./cnpj-lookup";
import { PlacaLookupInput } from "./placa-lookup-input";

const inputGridVariants = cva("w-full grid gap-2", {
  variants: {
    variants: {
      default: "grid-cols-1 md:grid-cols-2",
      single: "grid-cols-1",
      auto: "grid grid-flow-col auto-cols-max gap-2",
      dynamic:
        "grid grid-cols-[repeat(auto-fill,minmax(150px,1fr))] gap-2 sm:grid-cols-[repeat(auto-fill,minmax(200px,1fr))] md:grid-cols-[repeat(auto-fill,minmax(250px,1fr))]",
    },
  },
  defaultVariants: {
    variants: "default",
  },
});

export type InputType =
  | "text"
  | "number"
  | "date"
  | "select"
  | "checkbox"
  | "textarea"
  | "file"
  | "combobox"
  | "multiple-select"
  | "numeric"
  | "cnj"
  | "cnpj"
  | "cpf"
  | "telefone"
  | "celular"
  | "cep"
  | "currency"
  | "email"
  | "datetime-local"
  | "password"
  | "object"
  | (string & {});

export type FieldOption = {
  value: string;
  label: string;
};
export interface FieldConfig<T extends z.ZodType<any, any>> {
  label: string;
  placeholder: string;
  description: string;
  inputType: InputType;
  options?: FieldOption[];
  registerName?: string;
  datas?: any[];
  referenceId?: string;
  chave?: string;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => boolean;
  fields?: Record<string, FieldConfig<z.AnyZodObject>>;
  name?: string;
  filterFn?: (data: any) => boolean;
  className?: string;
  fieldClassName?: string;
  cepAddressMapping?: Record<string, string>;
  [key: string]: any;
  onChange?: (value: any, form: any) => void;
  hidden?: boolean;
  showIf?: (form: any) => boolean;
}

interface GenericFormProps<T extends z.ZodType<any, any>> {
  fieldConfig: Partial<Record<keyof z.infer<T>, FieldConfig<T>>>;
  variants?: "default" | "single" | "auto" | "dynamic";
  className?: string;
  customComponents?: Record<
    string,
    React.ComponentType<{
      field: any;
      config: FieldConfig<T>;
    }>
  >;
}

export function GenericFormsInput<T extends z.ZodType<any, any>>({
  fieldConfig,
  variants = "default",
  className,
  customComponents = {},
}: GenericFormProps<T>) {
  const form = useFormContext<z.infer<T>>();
  const { control, register, watch } = form;

  const isNestedObject = (config: FieldConfig<T>) => {
    return config.inputType === "object" && config.fields !== undefined;
  };

  const renderGroupedInputs = (groupName: string, config: FieldConfig<T>) => {
    if (!config.fields) {
      return null;
    }

    return (
      <Card
        key={groupName}
        className={cn("border p-4 rounded-lg my-4", config.className)}
      >
        <CardContent className="m-0 p-0">
          <h4 className="font-semibold">{config.label}</h4>
          {config.description && (
            <FormDescription>{config.description}</FormDescription>
          )}
          <div className={cn(inputGridVariants({ variants }))}>
            {Object.entries(
              config.fields as Record<string, FieldConfig<T>>
            ).map(([name, nestedConfig]) =>
              nestedConfig.inputType === "combobox" ? (
                <Combobox
                  key={`${groupName}.${name}`}
                  datas={nestedConfig.datas || []}
                  title={nestedConfig.label || ""}
                  placeholder={nestedConfig.placeholder}
                  referenceId={nestedConfig.referenceId || ""}
                  description={nestedConfig.description}
                  chave={nestedConfig.chave || ""}
                  name={`${groupName}.${name}` as any}
                  fieldClassName={nestedConfig.fieldClassName}
                />
              ) : (
                <FormField
                  key={`${groupName}.${name}`}
                  control={control}
                  name={`${groupName}.${name}` as any}
                  render={({ field }) => (
                    <>
                      {renderInput(
                        { ...nestedConfig, className: config.fieldClassName },
                        field,
                        register,
                        `${groupName}.${name}` as any
                      )}
                    </>
                  )}
                />
              )
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderInput = (
    config: FieldConfig<T>,
    field: any,
    register?: any,
    fieldName?: string
  ) => {
    if (typeof fieldName !== "string") {
      fieldName = String(fieldName);
    }

    // Verifica se existe um componente customizado para este inputType
    if (config.inputType && customComponents[config.inputType]) {
      const CustomComponent = customComponents[config.inputType];
      return (
        <FormItem className={config.className}>
          {config.label && <FormLabel>{config.label}</FormLabel>}
          <FormControl>
            <CustomComponent field={field} config={config} />
          </FormControl>
          {config.description && (
            <FormDescription>{config.description}</FormDescription>
          )}
          <FormMessage />
        </FormItem>
      );
    }

    switch (config.inputType) {
      case "checkbox":
        return (
          <FormItem
            className={`flex flex-row items-center justify-between rounded-lg border p-4 max-h-[100px] ${config.fieldClassName}`}
          >
            <div className="space-y-0.5">
              {config.label && <FormLabel>{config.label}</FormLabel>}
              {config.description && (
                <FormDescription>{config.description}</FormDescription>
              )}
            </div>
            <FormControl>
              <Switch
                checked={field.value}
                onCheckedChange={field.onChange}
                aria-readonly
                {...(config.registerName
                  ? register(config.registerName)
                  : field)}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        );

      case "multiple-select":
        return (
          <FormItem className={config.className}>
            {config.label && <FormLabel>{config.label}</FormLabel>}
            <FormControl>
              <MultipleSelection
                name={field.name}
                options={config.options || []}
                placeHolder={config.placeholder ?? ""}
                onChange={config.onChange}
              />
            </FormControl>
            {config.description && (
              <FormDescription>{config.description}</FormDescription>
            )}
            <FormMessage />
          </FormItem>
        );

      case "numeric":
        return (
          <FormItem className={config.className}>
            {config.label && <FormLabel>{config.label}</FormLabel>}
            <FormControl>
              <NumericInput
                placeholder={config.placeholder}
                {...(config.registerName
                  ? register(config.registerName)
                  : field)}
                className={config.className}
              />
            </FormControl>
            {config.description && (
              <FormDescription>{config.description}</FormDescription>
            )}
            <FormMessage />
          </FormItem>
        );

      case "cnj":
        return (
          <FormItem className={config.className}>
            {config.label && <FormLabel>{config.label}</FormLabel>}
            <FormControl>
              <CNJInput
                placeholder={config.placeholder}
                {...(config.registerName
                  ? register(config.registerName)
                  : field)}
                className={config.className}
              />
            </FormControl>
            {config.description && (
              <FormDescription>{config.description}</FormDescription>
            )}
            <FormMessage />
          </FormItem>
        );

      case "cpf":
        return (
          <FormItem className={config.className}>
            {config.label && <FormLabel>{config.label}</FormLabel>}
            <FormControl>
              <CPFInput
                placeholder={config.placeholder}
                {...(config.registerName
                  ? register(config.registerName)
                  : field)}
                className={config.className}
              />
            </FormControl>
            {config.description && (
              <FormDescription>{config.description}</FormDescription>
            )}
            <FormMessage />
          </FormItem>
        );

      case "cnpj":
        return (
          <FormItem className={config.className}>
            {config.label && <FormLabel>{config.label}</FormLabel>}
            <FormControl>
              <CNPJInput
                placeholder={config.placeholder}
                {...(config.registerName
                  ? register(config.registerName)
                  : field)}
                className={config.className}
              />
            </FormControl>
            {config.description && (
              <FormDescription>{config.description}</FormDescription>
            )}
            <FormMessage />
          </FormItem>
        );

      case "telefone":
        return (
          <FormItem className={config.className}>
            {config.label && <FormLabel>{config.label}</FormLabel>}
            <FormControl>
              <TelefoneInput
                placeholder={config.placeholder}
                {...(config.registerName
                  ? register(config.registerName)
                  : field)}
                className={config.className}
              />
            </FormControl>
            {config.description && (
              <FormDescription>{config.description}</FormDescription>
            )}
            <FormMessage />
          </FormItem>
        );

      case "placa-lookup":
        return (
          <FormItem className={config.className}>
            {config.label && <FormLabel>{config.label}</FormLabel>}
            <FormControl>
              <PlacaLookupInput
                placeholder={config.placeholder}
                {...(config.registerName
                  ? register(config.registerName)
                  : field)}
                className={config.fieldClassName}
              />
            </FormControl>
            {config.description && (
              <FormDescription>{config.description}</FormDescription>
            )}
            <FormMessage />
          </FormItem>
        );

      case "cnpj-lookup":
        return (
          <FormItem className={config.className}>
            {config.label && <FormLabel>{config.label}</FormLabel>}
            <FormControl>
              <CnpjLookupInput
                value={field.value}
                onChange={field.onChange}
                placeholder={config.placeholder}
                className={config.className}
              />
            </FormControl>
            {config.description && (
              <FormDescription>{config.description}</FormDescription>
            )}
            <FormMessage />
          </FormItem>
        );
      case "celular":
        return (
          <FormItem className={config.className}>
            {config.label && <FormLabel>{config.label}</FormLabel>}
            <FormControl>
              <CelularInput
                placeholder={config.placeholder}
                {...(config.registerName
                  ? register(config.registerName)
                  : field)}
                className={config.className}
              />
            </FormControl>
            {config.description && (
              <FormDescription>{config.description}</FormDescription>
            )}
            <FormMessage />
          </FormItem>
        );

      case "cep":
        const addressMapping: any = {};
        if (config.cepAddressMapping) {
          Object.entries(config.cepAddressMapping).forEach(([key, value]) => {
            addressMapping[key] = value;
          });
        }

        return (
          <FormItem className={config.className}>
            {config.label && <FormLabel>{config.label}</FormLabel>}
            <FormControl>
              <CepInput
                placeholder={config.placeholder}
                {...(config.registerName
                  ? register(config.registerName)
                  : field)}
                className={config.className}
                addressMapping={addressMapping}
              />
            </FormControl>
            {config.description && (
              <FormDescription>{config.description}</FormDescription>
            )}
            <FormMessage />
          </FormItem>
        );

      case "input-numerico":
        return (
          <FormItem className={config.className}>
            {config.label && <FormLabel>{config.label}</FormLabel>}
            <FormControl>
              <Input
                type="text"
                inputMode="numeric"
                pattern="[0-9]*"
                placeholder={config.placeholder}
                className={config.className}
                value={
                  field.value === null || field.value === undefined
                    ? ""
                    : field.value
                }
                onChange={(e) => {
                  const numericValue = e.target.value.replace(/\D/g, "");
                  field.onChange(numericValue);
                }}
                onKeyDown={(e) => {
                  if (
                    /^\d$/.test(e.key) ||
                    [
                      "Backspace",
                      "Delete",
                      "Tab",
                      "Escape",
                      "Enter",
                      "Home",
                      "End",
                      "ArrowLeft",
                      "ArrowRight",
                      "ArrowUp",
                      "ArrowDown",
                    ].includes(e.key) ||
                    ((e.ctrlKey || e.metaKey) &&
                      ["a", "c", "v", "x"].includes(e.key.toLowerCase()))
                  ) {
                    return;
                  }
                  e.preventDefault();
                }}
              />
            </FormControl>
            {config.description && (
              <FormDescription>{config.description}</FormDescription>
            )}
            <FormMessage />
          </FormItem>
        );

      case "currency":
        return (
          <FormItem className={config.className}>
            {config.label && <FormLabel>{config.label}</FormLabel>}
            <FormControl>
              <CurrencyInput
                placeholder={config.placeholder}
                value={null}
                onChange={(value) => field.onChange(value)}
                className={config.className}
              />
            </FormControl>
            {config.description && (
              <FormDescription>{config.description}</FormDescription>
            )}
            <FormMessage />
          </FormItem>
        );

      case "date":
        return (
          <FormItem className={config.className}>
            {config.label && <FormLabel>{config.label}</FormLabel>}
            <FormControl>
              <DateInput
                placeholder={config.placeholder}
                value={field.value}
                onChange={(value) => field.onChange(value)}
                className={config.className}
              />
            </FormControl>
            {config.description && (
              <FormDescription>{config.description}</FormDescription>
            )}
            <FormMessage />
          </FormItem>
        );

      default:
        return (
          <FormItem className={config.className}>
            {config.label && <FormLabel>{config.label}</FormLabel>}
            <FormControl>
              {config.inputType === "select" ? (
                <Select
                  onValueChange={(value) => {
                    field.onChange(value);
                    field.onBlur();
                  }}
                  value={field.value ?? ""}
                  name={fieldName}
                >
                  <SelectTrigger className={config.className}>
                    <SelectValue placeholder={config.placeholder} />
                  </SelectTrigger>
                  <SelectContent>
                    {config.options?.map((option) => (
                      <SelectItem
                        key={`${fieldName}-${option.value}`}
                        value={option.value}
                      >
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : config.inputType === "file" ? (
                <Input
                  type="file"
                  placeholder={config.placeholder}
                  accept="image/*"
                  className={config.className}
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    field.onChange(file);
                  }}
                />
              ) : config.inputType === "textarea" ? (
                <textarea
                  placeholder={config.placeholder}
                  {...(config.registerName
                    ? register(config.registerName)
                    : field)}
                  className={cn("w-full p-2 border rounded", config.className)}
                />
              ) : (
                <Input
                  autoComplete="new-password"
                  aria-autocomplete="none"
                  type={config.inputType || "text"}
                  placeholder={config.placeholder}
                  className={config.fieldClassName}
                  {...(config.registerName
                    ? register(config.registerName, {
                        setValueAs: (value: any) => {
                          if (config.inputType === "number") {
                            return value === "" ? null : Number(value);
                          }
                          return value;
                        },
                      })
                    : {
                        ...field,
                        onChange: (e) => {
                          const value =
                            config.inputType === "number"
                              ? e.target.value === ""
                                ? null
                                : Number(e.target.value)
                              : e.target.value;
                          field.onChange(value);
                        },
                        value: normalizeInputValue(field.value),
                      })}
                  onKeyDown={config.onKeyDown}
                />
              )}
            </FormControl>
            {config.description && (
              <FormDescription>{config.description}</FormDescription>
            )}
            <FormMessage />
          </FormItem>
        );
    }
  };

  return (
    <div className={cn(inputGridVariants({ variants }), className)}>
      {Object.entries(fieldConfig).map(([name, config]) => {
        if (!config) return null;

        const shouldShow = config.showIf
          ? config.showIf({ watch })
          : !config.hidden;

        if (!shouldShow) {
          return null;
        }

        return isNestedObject(config as FieldConfig<T>) ? (
          renderGroupedInputs(name, config as FieldConfig<T>)
        ) : (
          <FormField
            key={name}
            control={control}
            name={name as any}
            render={({ field }) => (
              <>
                {config?.inputType === "combobox" ? (
                  <Combobox
                    datas={config.datas || []}
                    title={config.label || ""}
                    placeholder={config.placeholder}
                    referenceId={config.referenceId || ""}
                    description={config.description}
                    chave={config.chave || ""}
                    name={name}
                    fieldClassName={config.fieldClassName}
                    onChange={config.onChange}
                  />
                ) : config ? (
                  renderInput(config as FieldConfig<T>, field, register, name)
                ) : null}
              </>
            )}
          />
        );
      })}
    </div>
  );
}
