"use client";

import { useFormContext } from "react-hook-form";
import { cva } from "class-variance-authority";
import { cn } from "@/lib/utils";
import { z } from "zod";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

import { Switch } from "../ui/switch";

const inputGridVariants = cva("w-full grid gap-2", {
  variants: {
    variants: {
      default: "grid-cols-1 md:grid-cols-2",
      single: "grid-cols-1",
    },
  },
  defaultVariants: {
    variants: "default",
  },
});
/* eslint-disable @typescript-eslint/no-unused-vars */
interface FieldConfig<T extends z.ZodType<any, any>> {
  label: string;
  placeholder: string;
  description: string;
}

interface GenericFormProps<T extends z.ZodType<any, any>> {
  fieldConfig: Record<keyof z.infer<T>, FieldConfig<T>>;
  variants?: "default" | "single";
}

export function GenericSwitchInput<T extends z.ZodType<any, any>>({
  fieldConfig,
  variants = "default",
}: GenericFormProps<T>) {
  const { control } = useFormContext();

  return (
    <div className={cn(inputGridVariants({ variants }))}>
      {Object.entries(fieldConfig).map(([name, config]) => (
        <FormField
          key={name}
          control={control}
          name={name}
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel>{config.label}</FormLabel>
                <FormDescription>{config.description}</FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                  aria-readonly
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      ))}
    </div>
  );
}
