"use client";

import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuList,
} from "@/components/ui/navigation-menu";
import { ToggleTheme } from "../buttons/toggle-theme";
import { TooltipWrapper } from "../cards/tooltip";
import { ProfileCard } from "../cards/profile-card";
import { Fullscreen } from "../buttons/fullscreen";
import { Notifications } from "../buttons/notifications";
import { Card } from "../ui/card";
import CentroDeCustoCard from "../cards/centro-de-custo-card";
import { useEffect, useState } from "react";
import Modal from "../modal";
import { useSession } from "../session/use-session";

interface OrcamentoApiResponse {
  data: Orcamento[];
  success: boolean;
}

export default function MenuNav() {
  const { session } = useSession();
  const [showModal, setShowModal] = useState<boolean>(false);
  const [maintenances, setManutencoes] = useState([]);
 const [budgets, setBudgets] = useState<OrcamentoApiResponse | null>(null);

  useEffect(() => {
    const fetchProximasManutencoes = async () => {
      try {
        const response = await fetch("/api/manutencao", {
          method: "GET",
        });

        if (!response.ok) {
          throw new Error("Erro ao buscar manutenções");
        }

        const { data } = await response.json();
        setManutencoes(data.manutencoes);
      } catch (error) {
        console.error("Erro ao buscar manutenções:", error);
      }
    };

    const fetchBudgetes = async () => {
      try {
        const response = await fetch("/api/orcamentos", {
          method: "GET",
        });

        if (!response.ok) throw new Error("Erro ao buscar orçamentos");

        const data = await response.json();
        setBudgets(data);
      } catch (error) {
        console.error("Erro ao buscar orçamentos:", error);
      }
    };

    fetchProximasManutencoes();
    fetchBudgetes();
  }, []);

  return (
    <Card className="w-full p-4 rounded-[0px] flex items-center justify-end">
      <header>
        <NavigationMenu>
          <NavigationMenuList>
            {/* Centro de Custo */}
            <TooltipWrapper message="Clique para selecionar o centro de custo">
              <NavigationMenuItem asChild>
                <CentroDeCustoCard />
              </NavigationMenuItem>
            </TooltipWrapper>

            {/* Fullscreen */}
            <TooltipWrapper message="Clique para deixar o navegador em fullscreen">
              <NavigationMenuItem asChild>
                <Fullscreen />
              </NavigationMenuItem>
            </TooltipWrapper>

            {/* Tema */}
            <TooltipWrapper message="Clique para alterar o tema">
              <NavigationMenuItem asChild>
                <ToggleTheme />
              </NavigationMenuItem>
            </TooltipWrapper>

            {/* Notifications */}
            <NavigationMenuItem asChild>
              <TooltipWrapper message="Clique para visualizar as notificações">
                <Notifications
                  roles={session?.roles ?? []}
                  maintenances={maintenances}
                  budgets={budgets?.data ?? []} 
                />
              </TooltipWrapper>
            </NavigationMenuItem>

            <TooltipWrapper message="Clique para ver as opções de usuário">
              <NavigationMenuItem asChild>
                <ProfileCard setShowModal={setShowModal} />
              </NavigationMenuItem>
            </TooltipWrapper>
          </NavigationMenuList>
        </NavigationMenu>
      </header>

      {/* Modal de troca de senha */}
      {showModal && (
        <Modal type="newPassword" title="Trocar senha" onClose={() => setShowModal(false)} />
      )}
    </Card>
  );
}
