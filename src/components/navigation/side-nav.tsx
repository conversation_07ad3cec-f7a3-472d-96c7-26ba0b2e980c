"use client";
import logoImage from "@/../public/images/logo-dark-carletto.png";
import {
  ChevronRight,
  ClipboardCheck,
  FileText,
  LucideIcon,
  Fuel,
  CreditCard,
  Truck,
  Bell,
  Settings,
  Users,
  Car,
  Hammer,
  ListCheck,
  PiggyBank,
  File,
  FileBadge,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
} from "@/components/ui/sidebar";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "../ui/collapsible";
import Link from "next/link";
import { getRoute, ROUTE } from "@/lib/utils";
import Image from "next/image";
import { useSession } from "../session/use-session";

type SectionKey = "DIA A DIA" | "RELATÓRIOS" | "CADASTRO" | "SISTEMA";

const sectionRoutes: Record<SectionKey, ROUTE[]> = {
  "DIA A DIA": [
    "Ordens de Serviço",
    "Vistorias",
    "Checklists",
    "Abastecimentos",
    "Despesas",
    "Percursos",
    "Lembretes",
    "Orçamento",
  ]
    .map(getRoute)
    .filter(Boolean) as ROUTE[],
  RELATÓRIOS: ["Operacionais", "Financeiros", "Auditoria"]
    .map(getRoute)
    .filter(Boolean) as ROUTE[],
  CADASTRO: [
    "Rede Credenciada",
    "Centro de Custo",
    "Empenhos",
    "Veículos",
    "Condutores",
  ]
    .map(getRoute)
    .filter(Boolean) as ROUTE[],
  SISTEMA: ["Contratos", "Usuários"].map(getRoute).filter(Boolean) as ROUTE[],
};

const iconMap: Record<string, LucideIcon> = {
  "Ordens de Serviço": Hammer,
  Vistorias: ClipboardCheck,
  Checklists: ListCheck,
  Abastecimentos: Fuel,
  Despesas: CreditCard,
  Percursos: Truck,
  Lembretes: Bell,
  Operacionais: Settings,
  Financeiros: PiggyBank,
  Auditoria: FileText,
  "Rede Credenciada": Users,
  "Centro de Custo": CreditCard,
  Orçamento: FileBadge,
  Empenhos: FileText,
  Veículos: Car,
  Condutores: Users,
  Contratos: FileText,
  Usuários: Users,
};

export function SideBar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { session } = useSession();
  const userRoles = session?.roles || [];
  const hasPermission = (requiredRoles: string[]) => {
    return requiredRoles.some((role) => userRoles.includes(role));
  };

  const isOrcamentistaAndContractAllowsCreateOS =
    userRoles.includes("ORCAMENTISTA_OFICINA") &&
    !hasPermission(["ADMIN", "GESTOR_FROTA"]) &&
    Boolean(session?.contrato?.abertura_os_credenciado);

  const isOrcamentistaOnly =
    userRoles.includes("ORCAMENTISTA_OFICINA") &&
    !hasPermission(["ADMIN", "GESTOR_FROTA"]) &&
    !Boolean(session?.contrato?.abertura_os_credenciado);
  const isGestorFrota = userRoles.includes("GESTOR_FROTA");
  const isCredenciamento =
    userRoles.includes("CREDENCIAMENTO") && !isGestorFrota;
  const isAuditoria = userRoles.includes("AUDITORIA");

  let sectionsToRender;

  if (isOrcamentistaOnly) {
    const reportRoute = sectionRoutes["RELATÓRIOS"].filter(
      (route) => route.label === "Financeiros"
    );

    sectionsToRender = {
      "DIA A DIA": sectionRoutes["DIA A DIA"].filter(
        (route) => route.label === "Orçamento" || route.label === "Checklists"
      ),
      RELATÓRIOS: [
        {
          ...reportRoute[0],
          items: reportRoute[0].items?.filter(
            ({ label }) =>
              label === "Nota Fiscal" ||
              (label === "Faturamento Credenciado" &&
                userRoles.includes("ORCAMENTISTA_OFICINA"))
          ),
        },
      ],
    };
  } else if (isOrcamentistaAndContractAllowsCreateOS) {
    const reportRoute = sectionRoutes["RELATÓRIOS"].filter(
      (route) => route.label === "Financeiros"
    );

    sectionsToRender = {
      "DIA A DIA": sectionRoutes["DIA A DIA"].filter(
        (route) =>
          route.label === "Orçamento" ||
          route.label === "Checklists" ||
          route.label === "Ordens de Serviço"
      ),
      RELATÓRIOS: [
        {
          ...reportRoute[0],
          items: reportRoute[0].items?.filter(
            ({ label }) => label === "Nota Fiscal"
          ),
        },
      ],
    };
  } else if (isCredenciamento) {
    sectionsToRender = {
      CADASTRO: sectionRoutes["CADASTRO"].filter((route) =>
        route.items?.find((item) => item.label === "Vincular contratos")
      ),
    };
  } else {
    sectionsToRender = {
      "DIA A DIA": sectionRoutes["DIA A DIA"].filter((route) => {
        if (route.items) {
          route.items = route.items.filter((item) => {
            if (item.label === "Nova Ordem") {
              return userRoles.includes("GESTOR_FROTA");
            }
            return true;
          });
        }

        if (
          route.label !== "Checklists" &&
          route.label !== "Ordens de Serviço" &&
          userRoles.includes("APROVADOR")
        ) {
          return false;
        }

        return true;
      }),

      RELATÓRIOS: sectionRoutes["RELATÓRIOS"].map((route) => {
        if (route.label === "Financeiros" && route.items) {
          route.items = route.items.filter((item) => {
            if (item.label === "Nota Fiscal") {
              return hasPermission([
                "ADMIN",
                "FINANCEIRO",
                "ORCAMENTISTA_OFICINA",
              ]);
            }
            if (item.label === "Faturamento Credenciado") {
              return userRoles.includes("ORCAMENTISTA_OFICINA");
            }

            if (item.label === "Resumo Financeiro") {
              return hasPermission(["GESTOR_FROTA", "ADMIN", "FINANCEIRO"]);
            }

            return true;
          });
        }
        return route;
      }),

      CADASTRO: sectionRoutes["CADASTRO"].filter((route) => {
        if (route.label === "Empenhos") {
          return (
            userRoles.includes("ADMIN") ||
            userRoles.includes("FINANCEIRO") ||
            userRoles.includes("GESTOR_GERAL") ||
            userRoles.includes("ADMIN") || userRoles.includes("FINANCEIRO")
          );
        }
        if (route.items) {
          route.items = route.items.filter((item) => {
            if (item.label === "Vincular contratos") {
              return (
                userRoles.includes("ADMIN") ||
                (userRoles.includes("CREDENCIAMENTO") &&
                  !userRoles.includes("GESTOR_FROTA"))
              );
            }
            return true;
          });
        }

        if (route.label === "Usuários") {
          return hasPermission(["ADMIN"]);
        }
        return true;
      }),
      SISTEMA: sectionRoutes["SISTEMA"].filter((route) => {
        if (route.label !== "Usuários") {
          return hasPermission(["ADMIN"]);
        }

        if (route.items) {
          route.items = route.items.filter((item) => {
            if (item.label === "Usuários Sistema") {
              return userRoles.includes("ADMIN");
            }
            return true;
          });
        }

        return hasPermission(["ADMIN", "GESTOR_GERAL"]);
      }),
    };
  }

  return (
    <Sidebar
      collapsible="icon"
      className="bg-black text-white dark:bg-black"
      {...props}
    >
      <SidebarHeader className="bg-black dark:bg-black">
        <Team />
      </SidebarHeader>
      <SidebarContent className="bg-black dark:bg-black">
        {Object.entries(sectionsToRender)
          .filter(([, routes]) => routes.length > 0)
          .map(([sectionLabel, routes]) => (
            <NavSection
              key={sectionLabel}
              label={sectionLabel}
              items={routes.map((route) => ({
                label: route.label,
                url: route.href,
                icon: iconMap[route.label],
                items: route.items?.map((subItem) => ({
                  label: subItem.label,
                  url: subItem.href,
                })),
              }))}
            />
          ))}
      </SidebarContent>
      <SidebarFooter className="bg-black dark:bg-black"></SidebarFooter>
      <SidebarRail className="bg-black dark:bg-black" />
    </Sidebar>
  );
}

type NavSectionType = {
  label: string;
  items: {
    label: string;
    url: string;
    icon?: LucideIcon;
    isActive?: boolean;
    items?: {
      label: string;
      url: string;
    }[];
  }[];
};

export function NavSection({ label, items }: NavSectionType) {
  return (
    <SidebarGroup>
      {label && <SidebarGroupLabel>{label}</SidebarGroupLabel>}
      <SidebarMenu>
        {items.map((item) => (
          <Collapsible
            key={item.label}
            asChild
            defaultOpen={item.isActive}
            className="group/collapsible"
          >
            <SidebarMenuItem>
              <CollapsibleTrigger asChild>
                <SidebarMenuButton tooltip={item.label}>
                  {item.icon && <item.icon />}
                  <span>{item.label}</span>
                  {item.items && item.items.length > 0 && (
                    <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  )}
                </SidebarMenuButton>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <SidebarMenuSub>
                  {item.items?.map((subItem) => (
                    <SidebarMenuSubItem key={subItem.label}>
                      <SidebarMenuSubButton asChild>
                        <Link
                          href={`/dashboard${item.url.toLowerCase()}${subItem.url.toLowerCase()}`}
                        >
                          <span>{subItem.label}</span>
                        </Link>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  ))}
                </SidebarMenuSub>
              </CollapsibleContent>
            </SidebarMenuItem>
          </Collapsible>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}

export function Team() {
  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <SidebarMenuButton
          size="lg"
          className="data-[state=open]:bg-black hover:bg-black data-[state=open]:text-black"
          asChild
        >
          <Link href={"/dashboard"} className="w-full">
            <div className="flex p-4 items-center hover:bg-black justify-center rounded w-full bg-black">
              <Image alt="logo" src={logoImage.src} width={52} height={52} />
            </div>
          </Link>
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
