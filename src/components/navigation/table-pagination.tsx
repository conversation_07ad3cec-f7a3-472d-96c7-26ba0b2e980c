import { Table } from "@tanstack/react-table";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Button } from "../ui/button";
import { ServerSidePaginationProps } from "../tables/data-table";

interface DataTablePaginationProps<TData> {
  table: Table<TData>;
  serverSidePagination?: ServerSidePaginationProps;
}

export function DataTablePagination<TData>({
  table,
  serverSidePagination,
}: DataTablePaginationProps<TData>) {
  // Verifica se está usando paginação do servidor
  const isServerSide = serverSidePagination?.enabled;

  return (
    <div className="flex items-center justify-between px-2 py-2">
      <div className="flex-1 text-sm text-muted-foreground">
        {isServerSide
          ? `${serverSidePagination.totalItems} resultados no total`
          : `${table.getFilteredSelectedRowModel().rows.length} de ${
              table.getFilteredRowModel().rows.length
            } linhas selecionadas.`}
      </div>
      <div className="flex items-center space-x-6 lg:space-x-8">
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium">Linhas por página</p>
          <Select
            value={
              isServerSide
                ? String(serverSidePagination.perPage) // Use perPage aqui, não currentPage
                : `${table.getState().pagination.pageSize}`
            }
            onValueChange={(value) => {
              const numValue = Number(value);
              if (isServerSide) {
                serverSidePagination.onPerPageChange(numValue);
              } else {
                table.setPageSize(numValue);
              }
            }}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue
                placeholder={
                  isServerSide
                    ? serverSidePagination.perPage // Use perPage aqui também
                    : table.getState().pagination.pageSize
                }
              />
            </SelectTrigger>
            <SelectContent side="top">
              {[10, 20, 30, 40, 50, 100].map((pageSize) => (
                <SelectItem key={pageSize} value={`${pageSize}`}>
                  {pageSize}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
          Página{" "}
          {isServerSide
            ? `${serverSidePagination.currentPage} de ${serverSidePagination.totalPages}`
            : `${
                table.getState().pagination.pageIndex + 1
              } de ${table.getPageCount()}`}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            type="button"
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => {
              if (isServerSide) {
                serverSidePagination.onPageChange(1);
              } else {
                table.setPageIndex(0);
              }
            }}
            disabled={
              isServerSide
                ? serverSidePagination.currentPage <= 1
                : !table.getCanPreviousPage()
            }
          >
            <span className="sr-only">Voltar para a primeira página</span>
            <ChevronsLeft />
          </Button>
          <Button
            type="button"
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => {
              if (isServerSide) {
                serverSidePagination.onPageChange(
                  serverSidePagination.currentPage - 1
                );
              } else {
                table.previousPage();
              }
            }}
            disabled={
              isServerSide
                ? serverSidePagination.currentPage <= 1
                : !table.getCanPreviousPage()
            }
          >
            <span className="sr-only">Anterior</span>
            <ChevronLeft />
          </Button>
          <Button
            type="button"
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={(e) => {
              e.stopPropagation();
              if (isServerSide) {
                serverSidePagination.onPageChange(
                  serverSidePagination.currentPage + 1
                );
              } else {
                table.nextPage();
              }
            }}
            disabled={
              isServerSide
                ? serverSidePagination.currentPage >=
                  serverSidePagination.totalPages
                : !table.getCanNextPage()
            }
          >
            <span className="sr-only">Próximo</span>
            <ChevronRight />
          </Button>
          <Button
            type="button"
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={(e) => {
              e.stopPropagation();
              if (isServerSide) {
                serverSidePagination.onPageChange(
                  serverSidePagination.totalPages
                );
              } else {
                table.setPageIndex(table.getPageCount() - 1);
              }
            }}
            disabled={
              isServerSide
                ? serverSidePagination.currentPage >=
                  serverSidePagination.totalPages
                : !table.getCanNextPage()
            }
          >
            <span className="sr-only">Ir para a última</span>
            <ChevronsRight />
          </Button>
        </div>
      </div>
    </div>
  );
}
