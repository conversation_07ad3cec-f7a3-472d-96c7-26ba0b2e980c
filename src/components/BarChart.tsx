import React from "react";
import { Bar } from "react-chartjs-2";
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from "chart.js";

// Registrar os componentes do Chart.js
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const BarChart = () => {
  // Dados do gráfico
  const data = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Vendas',  // Título do gráfico
        data: [12, 19, 3, 5, 2, 3],  // Dados de vendas para cada mês
        backgroundColor: 'rgba(255, 99, 132, 0.2)',  // Cor de fundo das barras
        borderColor: 'rgba(255, 99, 132, 1)',  // Cor da borda das barras
        borderWidth: 1,
      },
    ],
  };

  // Opções de configuração do gráfico
  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,  // Posicionamento da legenda
      },
      title: {
        display: true,  // Exibir título
        text: 'Gráfico de Vendas Mensais',  // Texto do título
      },
    },
  };

  return <Bar data={data} options={options} />;
};

export default BarChart;