import { NextResponse } from "next/server";

// Endpoint de teste para verificar se a estrutura da API está correta
export async function GET() {
  try {
    // Simular uma resposta similar à API real
    const mockResponse = {
      success: true,
      data: {
        centrosCusto: [
          {
            id: "test-id-1",
            descricao: "Centro de Custo Teste 1",
            cnpj: "12.345.678/0001-90",
            razao_social: "Empresa Teste LTDA",
            nome_responsavel: "João Silva",
            contato: "(11) 99999-9999",
            email: "<EMAIL>",
            dotacao_orcamentista: "DOT-001",
            valor_dotacao: 50000.00,
            cep: "01234-567",
            logradouro: "Rua Teste, 123",
            numero: "123",
            bairro: "Centro",
            cidade: "São Paulo",
            estado: "SP",
            ativo: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ]
      },
      message: "Dados de teste retornados com sucesso"
    };

    return NextResponse.json(mockResponse);
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: "Erro no teste",
      error: error instanceof Error ? error.message : "Erro desconhecido"
    }, { status: 500 });
  }
}

export async function POST() {
  try {
    // Simular criação de centro de custo
    const mockCreatedCentro = {
      id: "test-created-" + Date.now(),
      descricao: "Centro de Custo Criado via Teste",
      cnpj: "98.765.432/0001-10",
      razao_social: "Nova Empresa Teste LTDA",
      nome_responsavel: "Maria Silva",
      contato: "(11) 88888-8888",
      email: "<EMAIL>",
      dotacao_orcamentista: "DOT-NEW-001",
      valor_dotacao: 75000.00,
      ativo: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      data: mockCreatedCentro,
      message: "Centro de custo criado com sucesso (teste)"
    }, { status: 201 });
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: "Erro ao criar centro de custo (teste)",
      error: error instanceof Error ? error.message : "Erro desconhecido"
    }, { status: 500 });
  }
}
