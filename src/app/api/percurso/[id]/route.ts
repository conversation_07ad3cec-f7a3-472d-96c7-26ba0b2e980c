import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { decode } from "@/lib/auth/server-session";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
interface DefaultResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<DefaultResponse<percurso>>> {
  const id = (await params).id;
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json({ message: "erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/percurso/percurso/${id}`, {
    method: "PUT",
    headers: { Authorization: `Bearer ${session}` },
    body: request.body,
  });
  if (!res.ok)
    return NextResponse.json(
      { message: "Erro ao editar percurso", success: false },
      { status: 404 }
    );
  return NextResponse.json({ data: await res.json(), success: true }, { status: 200 });
}
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<DefaultResponse<null>>> {
  const id = (await params).id;
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);

  if (!cookie) {
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/percurso/percurso/${id}`, {
    method: "DELETE",
    headers: { Authorization: `Bearer ${session}` },
  });

  if (!res.ok) {
    return NextResponse.json(
      { message: "Erro ao deletar vistoria", success: false },
      { status: 404 }
    );
  }

  return NextResponse.json(
    { message: "item deletado com sucesso", success: true },
    { status: 200 }
  );
}
