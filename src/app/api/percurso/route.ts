/* eslint-disable @typescript-eslint/ban-ts-comment */
import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { decode } from "@/lib/auth/server-session";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
interface DefaultResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
}

export async function GET(): Promise<NextResponse<DefaultResponse<percurso[]>>> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json({ message: "erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/percursos/percurso`, {
      method: "GET",
      headers: { Authorization: `Bearer ${session}` },
    });

    if (!res.ok) {
      console.error(`Erro ao buscar percursos: ${res.status} ${res.statusText}`);
      return NextResponse.json(
        {
          message: `Erro ao buscar percursos: ${res.status} ${res.statusText}`,
          success: false,
          data: [],
        },
        { status: res.status }
      );
    }

    const responseData = await res.json();
    let percursosArray: percurso[] = [];

    // Tenta extrair os percursos do formato da resposta
    if (responseData) {
      if (Array.isArray(responseData)) {
        percursosArray = responseData;
      } else if (responseData.percursos && Array.isArray(responseData.percursos)) {
        percursosArray = responseData.percursos;
      } else if (responseData.data && Array.isArray(responseData.data)) {
        percursosArray = responseData.data;
      } else if (
        responseData.data &&
        responseData.data.percursos &&
        Array.isArray(responseData.data.percursos)
      ) {
        percursosArray = responseData.data.percursos;
      }
    }

    return NextResponse.json({ data: percursosArray, success: true }, { status: 200 });
  } catch (error) {
    console.error("Erro ao buscar percursos:", error);
    return NextResponse.json(
      { message: "Erro interno ao buscar percursos", success: false, data: [] },
      { status: 500 }
    );
  }
}

interface PercursoFrontend {
  veiculoId: string;
  condutorId: string;
  motivo: string;
  origem: {
    local: string;
    data: string;
    odometro: number;
  };
  destino: {
    local: string;
    data: string;
    odometro: number;
  };
  arquivos: any[];
  observacoes: string;
}

interface PercursoBackend {
  veiculoId: string;
  condutorId: string;
  motivo: string;
  local_origem: string;
  data_partida: string;
  odometro_partida: number;
  local_destino: string;
  data_chegada: string;
  odometro_chegada: number;
  arquivos: any[];
  observacoes: string;
}

// Função de mapeamento
function mapPercursoToBackendFormat(percurso: PercursoFrontend): PercursoBackend {
  return {
    veiculoId: percurso.veiculoId,
    condutorId: percurso.condutorId,
    motivo: percurso.motivo,
    local_origem: percurso.origem.local,
    data_partida: percurso.origem.data,
    odometro_partida: percurso.origem.odometro,
    local_destino: percurso.destino.local,
    data_chegada: percurso.destino.data,
    odometro_chegada: percurso.destino.odometro,
    arquivos: percurso.arquivos,
    observacoes: percurso.observacoes,
  };
}

export async function POST(
  req: NextRequest
): Promise<NextResponse<DefaultResponse<PercursoBackend>>> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json({ message: "erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Ler o corpo da requisição
  const body = await req.json();

  // Aplicar o mapper para transformar o payload
  const mappedBody = mapPercursoToBackendFormat(body);

  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/percursos/percurso`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${session}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(mappedBody), // Enviar o payload mapeado
    //@ts-ignore
    duplex: "half",
  });

  if (!res.ok) {
    return NextResponse.json(
      { message: "Erro ao criar percurso", success: false },
      { status: res.status }
    );
  }

  return NextResponse.json({ data: await res.json(), success: true }, { status: 200 });
}
