/* eslint-disable @typescript-eslint/ban-ts-comment */
import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { decode } from "@/lib/auth/server-session";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

export interface DefaultResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
}

// GET - Buscar todos os contratos
export async function GET(): Promise<
  NextResponse<DefaultResponse<contrato[]>>
> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json(
      { message: "Erro", success: false },
      { status: 401 }
    );
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json(
      { message: "<PERSON>rro", success: false },
      { status: 401 }
    );
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json(
      { message: "Erro", success: false },
      { status: 401 }
    );
  }

  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/contratos`, {
    method: "GET",
    headers: { Authorization: `Bearer ${session}` },
  });

  if (!res.ok)
    return NextResponse.json(
      { message: "Erro ao buscar contratos", success: false },
      { status: 404 }
    );

  return NextResponse.json(
    { data: await res.json(), success: true },
    { status: 200 }
  );
}

function mapContratoData(frontendData: any) {
  return {
    nome_contrato: frontendData.nome,
    numero: frontendData.numero,
    data_inicial: frontendData.data_inicial,
    data_final: frontendData.data_final,
    valor_contrato: frontendData.valor_do_contrato,
    taxa_admin: frontendData.taxa_administrativa,
    cnpj: frontendData.cnpj,
    cnpj_financial: frontendData.cnpj_financial,
    razao_social: frontendData.razao_social,
    responsavel: frontendData.contato.responsavel,
    telefone: frontendData.contato.telefone,
    email: frontendData.contato.email,
    cep: frontendData.endereco.cep,
    logradouro: frontendData.endereco.logradouro,
    bairro: frontendData.endereco.bairro,
    estado: frontendData.endereco.estado,
    cidade: frontendData.endereco.cidade,
    logradouro_financial: frontendData.endereco_financial?.logradouro ?? "",
    cep_financial: frontendData.endereco_financial?.cep ?? "",
    bairro_financial: frontendData.endereco_financial?.bairro ?? "",
    estado_financial: frontendData.endereco_financial?.estado ?? "",
    cidade_financial: frontendData.endereco_financial?.cidade ?? "",
    numero_contrato_financial: frontendData?.numero_contrato_financial ?? "",
    placa_veiculo: frontendData.validacao_nf.placa_do_veiculo,
    modelo_veiculo: frontendData.validacao_nf.modelo_do_veiculo,
    numero_os: frontendData.validacao_nf.numero_da_os,
    send_nf_to: frontendData?.send_nf_to ?? "",
    numero_contrato: frontendData.validacao_nf.numero_do_contrato,
    logotipo: frontendData.logotipo,
    limite_gastos_percent: frontendData.limite_gastos_percent,
    contrato_ativo: frontendData.configuracao.ativo,
    abertura_via_os: frontendData.configuracao.abertura_os_credenciado,
    envio_emails: frontendData.configuracao.envio_de_emails,
    veiculo_rfid: frontendData.configuracao.veiculos_com_rfid,
    parametrizacao_obg: frontendData.configuracao.parametrizacao_obrigatoria,
    checklist_simplificado_pecas:
      frontendData.configuracao.checklist_simplificado_pecas,
    aprovacao_gestor: frontendData.configuracao.aprovacao_gestor,
    ajuste_gestor: frontendData.configuracao.ajuste_gestor,
    restringir_veiculos: frontendData.configuracao.restringir_veiculos,
  };
}

// POST - Criar um novo contrato
export async function POST(
  req: NextRequest
): Promise<NextResponse<DefaultResponse<contrato>>> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json(
      { message: "Erro", success: false },
      { status: 401 }
    );
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json(
      { message: "Erro", success: false },
      { status: 401 }
    );
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json(
      { message: "Erro", success: false },
      { status: 401 }
    );
  }

  try {
    // Lê o corpo da requisição
    const frontendData = await req.json();

    // Mapeia os dados para o formato esperado pelo backend
    const backendData = mapContratoData(frontendData);

    // Envia os dados para o backend
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/contratos`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${session}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(backendData),
        //@ts-ignore
        duplex: "half",
      }
    );

    if (!res.ok) {
      return NextResponse.json(
        { message: "Erro ao criar contrato", success: false },
        { status: 400 }
      );
    }

    const data = await res.json();

    return NextResponse.json({ data, success: true }, { status: 200 });
  } catch (error) {
    console.error("Erro ao processar a requisição:", error);
    return NextResponse.json(
      { message: "Erro interno do servidor", success: false },
      { status: 500 }
    );
  }
}
