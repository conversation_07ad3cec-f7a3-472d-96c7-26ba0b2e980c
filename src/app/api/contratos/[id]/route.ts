import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { decode } from "@/lib/auth/server-session";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import { DefaultResponse } from "../route";

function mapBackendToFrontend(backendData: any) {
  const contrato = backendData.contrato;
  return {
    id: contrato.id,
    nome: contrato.nome_contrato,
    numero: contrato.numero,
    data_inicial: contrato.data_inicial,
    data_final: contrato.data_final,
    valor_do_contrato: contrato.valor_contrato,
    taxa_administrativa: contrato.taxa_admin,
    cnpj: contrato.cnpj,
    razao_social: contrato.razao_social,
    logotipo: contrato.logotipo,
    send_nf_to: contrato.send_nf_to,
    limite_gastos_percent: contrato.limite_gastos_percent || 0,
    contato: {
      responsavel: contrato.responsavel,
      telefone: contrato.telefone,
      email: contrato.email,
    },
    endereco: {
      cep: contrato.cep,
      logradouro: contrato.logradouro,
      bairro: contrato.bairro,
      estado: contrato.estado,
      cidade: contrato.cidade,
    },
    endereco_financial: {
      cep: contrato.cep_financial,
      logradouro: contrato.logradouro_financial,
      bairro: contrato.bairro_financial,
      estado: contrato.estado_financial,
      cidade: contrato.cidade_financial,
    },
    cnpj_financial: contrato.cnpj_financial,
    numero_contrato_financial: contrato.numero_contrato_financial,
    validacao_nf: {
      placa_do_veiculo: contrato.placa_veiculo || "",
      modelo_do_veiculo: contrato.modelo_veiculo || "",
      numero_da_os: contrato.numero_os || "",
      numero_do_contrato: contrato.numero_contrato || "",
    },
    configuracao: {
      ativo: contrato.contrato_ativo || false,
      abertura_os_credenciado: contrato.abertura_via_os || false,
      envio_de_emails: contrato.envio_emails || false,
      veiculos_com_rfid: contrato.veiculo_rfid || false,
      parametrizacao_obrigatoria: contrato.parametrizacao_obg || false,
      checklist_simplificado_pecas:
        contrato.checklist_simplificado_pecas || false,
      aprovacao_gestor: contrato.aprovacao_gestor || false,
      ajuste_gestor: contrato.ajuste_gestor || false,
      restringir_veiculos: contrato.restringir_veiculos || false,
    },
    desconto: contrato.desconto,
  };
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<DefaultResponse<contrato>>> {
  const id = (await params).id;
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);

  if (!cookie) {
    return NextResponse.json(
      { message: "Erro", success: false },
      { status: 401 }
    );
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json(
      { message: "Erro", success: false },
      { status: 401 }
    );
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json(
      { message: "Erro", success: false },
      { status: 401 }
    );
  }

  const res = await fetch(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/contratos/${id}`,
    {
      method: "GET",
      headers: { Authorization: `Bearer ${session}` },
    }
  );

  if (!res.ok) {
    return NextResponse.json(
      { message: "Erro ao buscar contrato", success: false },
      { status: 404 }
    );
  }

  const backendData = await res.json();
  const contrato = mapBackendToFrontend(backendData);
  return NextResponse.json({ data: contrato, success: true }, { status: 200 });
}

// PUT - Atualizar um contrato específico
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<DefaultResponse<contrato>>> {
  const id = (await params).id;
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json(
      { message: "Erro", success: false },
      { status: 401 }
    );
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json(
      { message: "Erro", success: false },
      { status: 401 }
    );
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json(
      { message: "Erro", success: false },
      { status: 401 }
    );
  }

  const res = await fetch(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/contratos/${id}`,
    {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${session}`,
        "Content-Type": "application/json",
      },
      body: request.body,
    }
  );

  if (!res.ok) {
    return NextResponse.json(
      { message: "Erro ao atualizar contrato", success: false },
      { status: 400 }
    );
  }

  return NextResponse.json(
    { data: await res.json(), success: true },
    { status: 200 }
  );
}

// DELETE - Deletar um contrato específico
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<DefaultResponse<null>>> {
  const id = (await params).id;
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);

  if (!cookie) {
    return NextResponse.json(
      { message: "Erro", success: false },
      { status: 401 }
    );
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json(
      { message: "Erro", success: false },
      { status: 401 }
    );
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json(
      { message: "Erro", success: false },
      { status: 401 }
    );
  }

  const res = await fetch(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/contratos/${id}`,
    {
      method: "DELETE",
      headers: { Authorization: `Bearer ${session}` },
    }
  );

  if (!res.ok) {
    return NextResponse.json(
      { message: "Erro ao deletar contrato", success: false },
      { status: 404 }
    );
  }

  return NextResponse.json(
    { message: "Contrato deletado com sucesso", success: true },
    { status: 200 }
  );
}
