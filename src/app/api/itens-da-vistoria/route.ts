/* eslint-disable @typescript-eslint/ban-ts-comment */
import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { decode } from "@/lib/auth/server-session";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
interface DefaultResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
}

export async function GET(): Promise<NextResponse<DefaultResponse<vistoria[]>>> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json({ message: "erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/itens-da-vistoria`, {
    method: "GET",
    headers: { Authorization: `Bearer ${session}` },
  });
  if (!res.ok)
    return NextResponse.json(
      { message: "Erro ao buscar vistorias", success: false },
      { status: 404 }
    );
  return NextResponse.json({ itens_da_vistoria: await res.json(), success: true }, { status: 200 });
}
export async function POST(
  req: NextRequest
): Promise<NextResponse<DefaultResponse<item_da_vistoria[]>>> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json({ message: "erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/itens-da-vistoria`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${session}`,
      "Content-Type": "application/json",
    },
    body: req.body,
    // @ts-ignore
    duplex: "half",
  });
  if (!res.ok)
    return NextResponse.json({ message: "Erro ao buscar os", success: false }, { status: 404 });
  return NextResponse.json({ itens_da_vistoria: await res.json(), success: true }, { status: 200 });
}
