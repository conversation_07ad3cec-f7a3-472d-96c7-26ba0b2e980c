import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { decode } from "@/lib/auth/server-session";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

export async function POST(request: Request) {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  console.log("upload");

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }
  try {
    const formData = await request.formData();
    const file = formData.get("file") as File;
    console.log(file);
    if (!file) {
      return NextResponse.json({ message: "Nenhum arquivo enviado" }, { status: 400 });
    }

    // Envia o arquivo para o backend
    const backendResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/files/upload`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${session}`,
      },
      body: formData,
      //@ts-ignore
      duplex: "half",
    });

    if (!backendResponse.ok) {
      throw new Error("Erro ao enviar arquivo para o backend");
    }

    const backendData = await backendResponse.json();

    return NextResponse.json(backendData, { status: 200 });
  } catch (error) {
    console.error("Erro no upload do arquivo:", error);
    return NextResponse.json(
      {
        message: "Erro interno do servidor",
        error: error instanceof Error ? error.message : "Erro desconhecido",
      },
      { status: 500 }
    );
  }
}
