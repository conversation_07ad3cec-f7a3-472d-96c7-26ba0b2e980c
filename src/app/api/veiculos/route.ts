import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { decode } from "@/lib/auth/server-session";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";
interface DefaultResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
}

export async function GET(): Promise<NextResponse<DefaultResponse<veiculo[]>>> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json(
      { message: "erro", success: false },
      { status: 401 }
    );
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json(
      { message: "Erro", success: false },
      { status: 401 }
    );
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json(
      { message: "Erro", success: false },
      { status: 401 }
    );
  }

  const res = await fetch(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/veiculos/veiculo`,
    {
      method: "GET",
      headers: { Authorization: `Bearer ${session}` },
    }
  );
  if (!res.ok)
    return NextResponse.json(
      { message: "Erro ao buscar veículos", success: false },
      { status: 404 }
    );
  return NextResponse.json(
    { data: await res.json(), success: true },
    { status: 200 }
  );
}

export async function POST(
  request: Request
): Promise<NextResponse<DefaultResponse<veiculo>>> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json(
      { message: "Erro: sessão não encontrada", success: false },
      { status: 401 }
    );
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json(
      { message: "Erro", success: false },
      { status: 401 }
    );
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json(
      { message: "Erro", success: false },
      { status: 401 }
    );
  }

  try {
    const body: FrontendPayload = await request.json(); // Recebe o payload do frontend

    const backendPayload = mapFrontendToBackend(body);
    console.log(backendPayload);
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/veiculos/veiculo`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${session}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(backendPayload),
        //@ts-ignore
        duplex: "half",
      }
    );

    if (!res.ok) {
      return NextResponse.json(
        { message: "Erro ao criar veículo", success: false },
        { status: res.status }
      );
    }

    const data = await res.json();
    return NextResponse.json(
      { data, success: true, message: "Veículo criado com sucesso" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Erro ao processar a requisição:", error);
    return NextResponse.json(
      { message: "Erro interno do servidor", success: false },
      { status: 500 }
    );
  }
}

interface FrontendPayload {
  renovam: string;
  vin: string;
  numero_do_motor: string;
  ano_de_fabricacao: string;
  ano_do_modelo: string;
  cor: string;
  placa: string;
  odometro_atual: number;
  matricula: string;
  tag_rfid: string;
  data_compra?: string; // Nova data de compra
  data_cedencia?: string; // Nova data de cedência
  capacidade_do_tanque: string;
  tipos_de_combustiveis: string[];
  numero_de_cambio: string;
  cilindradas: number;
  potencia: string;
  segmento: string;
  carroceria: string;
  transmissao: string;
  quantidade_de_portas: number;
  quantidade_de_assentos: number;
  quantidade_de_eixos: number;
  numero_de_valvulas: string;
  litragem: number;
  combustivel: string[];
  origem_do_veiculo: string;
  centro_de_custoId: string;
  estado: string;
  cidade: string;
  codigo_fipe: string;
  valor_venal: string;
  status: string;
  marcaId: string;
  modeloId: string;
  versaoId?: string;
  empenhoId: string;
  tipo_de_veiculoId: string;
  tipo_de_frotaId: string;
  definicoes: {
    numero_de_cambio: string;
    cilindradas: number;
    potencia: string;
    segmento: string;
    carroceria: string;
    transmissao: string;
    quantidade_de_portas: number;
    quantidade_de_assentos: number;
    quantidade_de_eixos: number;
    numero_de_valvulas: string;
    litragem: number;
    combustivel: string;
    origem_do_veiculo: string;
  };
  lotacao: {
    centro_de_custoId: string;
    estado: string;
    cidade: string;
  };
  fipe: {
    codigo_fipe: string;
    valor_venal: string;
  };
}

interface BackendPayload {
  tipos_veiculoId: string;
  marcaId: string;
  modeloId: string;
  versaoId?: string;
  empenhoId: string;
  tipo_frotaId: string;
  definicoes_veiculoID: string;
  lotacao_veiculosId: string;
  codigo_fipeId: string;
  renavam: string;
  vin: string;
  numero_motor: string;
  ano_fab: number;
  ano_modelo: number;
  cor: string;
  odometro_atual: string;
  matricula: string;
  tag_rfid: string;
  data_compra?: string; // Nova data de compra
  data_cedencia?: string; // Nova data de cedência
  combustivel: string;
  valor_venal: string;
  status: string;
  placa: string;
  definicoes?: {
    numero_de_cambio: string;
    cilindradas: number;
    potencia: string;
    segmento: string;
    carroceria: string;
    transmissao: string;
    quantidade_de_portas: number;
    quantidade_de_assentos: number;
    quantidade_de_eixos: number;
    numero_de_valvulas: string;
    litragem: number;
    combustivel: string;
    origem_do_veiculo: string;
  };
  lotacao?: {
    // Adicionado como opcional
    centro_de_custoId: string;
    estado: string;
    cidade: string;
  };
}

function mapFrontendToBackend(payload: FrontendPayload): BackendPayload {
  return {
    tipos_veiculoId: payload.tipo_de_veiculoId,
    marcaId: payload.marcaId,
    modeloId: payload.modeloId,
    versaoId: payload.versaoId,
    empenhoId: payload.empenhoId,
    tipo_frotaId: payload.tipo_de_frotaId,
    definicoes_veiculoID: "", // Será preenchido após criar o registro em DefVeiculo
    lotacao_veiculosId: "", // Será preenchido após criar o registro em LotacaoVeiculo
    codigo_fipeId: payload.fipe.codigo_fipe,
    renavam: payload.renovam,
    vin: payload.vin,
    placa: payload.placa,
    numero_motor: payload.numero_do_motor,
    ano_fab: Number(payload.ano_de_fabricacao),
    ano_modelo: Number(payload.ano_do_modelo),
    cor: payload.cor,
    odometro_atual: String(payload.odometro_atual),
    matricula: payload.matricula,
    tag_rfid: payload.tag_rfid,
    data_compra: payload.data_compra,
    data_cedencia: payload.data_cedencia,
    combustivel: payload.combustivel.join(", "),
    valor_venal: payload.fipe.valor_venal,
    status: payload.status,
    definicoes: payload.definicoes,
    lotacao: payload.lotacao,
  };
}
