import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import { DefaultResponse } from "../../contratos/route";

// GET - Buscar um veículo específico por ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<DefaultResponse<veiculo>>> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  const id = (await params).id;

  if (!cookie) {
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "<PERSON>rro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/veiculos/veiculo/${id}`, {
    method: "GET",
    headers: { Authorization: `Bearer ${session}` },
  });

  if (!res.ok) {
    return NextResponse.json(
      { message: "Erro ao buscar veículo", success: false },
      { status: 404 }
    );
  }

  const data = await res.json();
  return NextResponse.json(data);
}

// PUT - Atualizar um veículo específico
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<DefaultResponse<veiculo>>> {
  const id = (await params).id;
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/veiculos/veiculo/${id}`, {
    method: "PUT",
    headers: {
      Authorization: `Bearer ${session}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(await request.json()),
  });

  if (!res.ok) {
    return NextResponse.json(
      { message: "Erro ao atualizar veículo", success: false },
      { status: 400 }
    );
  }

  const data = await res.json();
  return NextResponse.json({ data, success: true });
}

// DELETE - Deletar um veículo específico
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<DefaultResponse<null>>> {
  const id = (await params).id;
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);

  if (!cookie) {
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/veiculos/veiculo/${id}`, {
    method: "DELETE",
    headers: { Authorization: `Bearer ${session}` },
  });

  if (!res.ok) {
    return NextResponse.json(
      { message: "Erro ao deletar veículo", success: false },
      { status: 404 }
    );
  }

  return NextResponse.json(
    { message: "Veículo deletado com sucesso", success: true },
    { status: 200 }
  );
}
