import { cookies } from "next/headers";
import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
    if (!cookie) {
      return NextResponse.json({ message: "erro", success: false }, { status: 401 });
    }

    const session = cookie.value;
    if (!session) {
      (await cookies()).delete(SESSION_COOKIE_NAME);
      return NextResponse.json(
        { message: "Você não está logado", success: false },
        { status: 401 }
      );
    }

    // Buscar o modelo do backend
    const backendResponse = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/veiculos/modelo-planilha`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${session}`,
        },
      }
    );

    if (!backendResponse.ok) {
      const errorData = await backendResponse.json();
      return NextResponse.json(errorData, { status: backendResponse.status });
    }

    // Obter o blob do arquivo
    const blob = await backendResponse.blob();

    // Retornar o arquivo
    return new NextResponse(blob, {
      headers: {
        "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "Content-Disposition": "attachment; filename=modelo_importacao_veiculos.xlsx",
      },
    });
  } catch (error) {
    console.error("Erro ao gerar modelo:", error);
    return NextResponse.json({ message: "Erro ao gerar modelo" }, { status: 500 });
  }
}
