import { cookies } from "next/headers";
import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);

  if (!cookie) {
    return NextResponse.json({ message: "Não autenticado" }, { status: 401 });
  }

  const sessionToken = cookie.value;
  const formData = await req.formData(); // pega o arquivo do front

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/veiculos/importar-veiculos`,
    {
      method: "POST",
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
      body: formData as any, // necessário por causa da tipagem do Next
    }
  );

  const data = await response.json();

  return NextResponse.json(data, { status: response.status });
}
