import { NextResponse } from "next/server";

export async function GET() {
  return NextResponse.json({
    success: true,
    message: "API está funcionando!",
    timestamp: new Date().toISOString(),
    endpoints: {
      "GET /api/centro-de-custo": "Listar centros de custo",
      "POST /api/centro-de-custo": "Criar centro de custo",
      "GET /api/centro-de-custo/[id]": "Buscar centro por ID",
      "PUT /api/centro-de-custo/[id]": "Atualizar centro",
      "DELETE /api/centro-de-custo/[id]": "Excluir centro"
    }
  });
}
