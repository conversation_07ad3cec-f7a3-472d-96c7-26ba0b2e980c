import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { decode } from "@/lib/auth/server-session";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

interface DefaultResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
}

interface HistoricoManutencao {
  id: string;
  centro_de_custo: string;
  placa: string;
  marca: string;
  modelo: string;
  versao: string;
  data_servico: Date;
  tipo_servico: string;
  valor: number;
  credenciado: string;
}

export async function GET(request: NextRequest) {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json(
      {
        success: false,
        message: "Não autorizado",
      } as DefaultResponse<unknown>,
      { status: 401 }
    );
  }

  const session = cookie.value;
  if (!session) {
    return NextResponse.json(
      {
        success: false,
        message: "Sessão inválida",
      } as DefaultResponse<unknown>,
      { status: 401 }
    );
  }

  try {
    // Aqui seria a chamada à API real para buscar o histórico de manutenções
    // Por enquanto, vamos enviar dados fictícios
    const historicoManutencoes: HistoricoManutencao[] = [
      {
        id: "1",
        centro_de_custo: "Centro 01",
        placa: "ABC-1234",
        marca: "Toyota",
        modelo: "Corolla",
        versao: "2.0",
        data_servico: new Date(),
        tipo_servico: "Troca de óleo",
        valor: 350.0,
        credenciado: "Oficina ABC",
      },
      {
        id: "2",
        centro_de_custo: "Centro 02",
        placa: "DEF-5678",
        marca: "Honda",
        modelo: "Civic",
        versao: "1.8",
        data_servico: new Date(),
        tipo_servico: "Revisão completa",
        valor: 1200.0,
        credenciado: "Oficina XYZ",
      },
      {
        id: "3",
        centro_de_custo: "Centro 03",
        placa: "GHI-9012",
        marca: "Volkswagen",
        modelo: "Golf",
        versao: "1.6",
        data_servico: new Date(),
        tipo_servico: "Troca de freios",
        valor: 890.0,
        credenciado: "Oficina Delta",
      },
      {
        id: "4",
        centro_de_custo: "Centro 01",
        placa: "JKL-3456",
        marca: "Ford",
        modelo: "Fiesta",
        versao: "1.0",
        data_servico: new Date(),
        tipo_servico: "Alinhamento e balanceamento",
        valor: 250.0,
        credenciado: "Auto Center Omega",
      },
    ];

    return NextResponse.json(
      {
        success: true,
        data: { historicoManutencoes },
      } as DefaultResponse<{ historicoManutencoes: HistoricoManutencao[] }>,
      { status: 200 }
    );
  } catch (error) {
    console.error("Erro ao buscar histórico de manutenções:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Erro ao buscar histórico de manutenções",
      } as DefaultResponse<unknown>,
      { status: 500 }
    );
  }
}
