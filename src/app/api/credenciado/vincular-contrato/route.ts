import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

interface DefaultResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
}

export async function POST(
  req: NextRequest
): Promise<NextResponse<DefaultResponse<credenciado[]>>> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json({ message: "erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  try {
    const body = await req.json();
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/credenciados/vincular-contrato`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${session}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
        //@ts-ignore
        duplex: "half",
      }
    );

    if (!res.ok) {
      const errorText = await res.text();
      console.error("Erro ao enviar dados para o backend:", errorText);
      return NextResponse.json(
        { message: "Erro ao enviar dados do credenciado", success: false },
        { status: res.status }
      );
    }

    const data = await res.json();
    return NextResponse.json({ data, success: true }, { status: 200 });
  } catch (error) {
    console.error("Erro no processamento da requisição:", error);
    return NextResponse.json(
      {
        message: "Erro interno do servidor",
        success: false,
        error: error instanceof Error ? error.message : "Erro desconhecido",
      },
      { status: 500 }
    );
  }
}
