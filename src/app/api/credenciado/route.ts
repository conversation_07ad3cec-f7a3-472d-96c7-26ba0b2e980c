import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { decode } from "@/lib/auth/server-session";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
interface DefaultResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
}

export async function GET(
  request: NextRequest
): Promise<NextResponse<DefaultResponse<{ credenciados: credenciado[], pagination: any }>>> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json({ message: "erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const url = new URL(request.url);

  const params = [
    "page",
    "limit",
    "search",
    "poloId",
    "atividade_principal",
    "cnpj",
    "razao_social",
    "nome_fantasia",
    "email",
    "telefone",
    "celular",
    "cidade",
    "estado",
    "ativo",
    "orcamentista",
    "optante",
    "atende_placa_verde",
    "concessionaria"
  ];

  let apiUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/credenciados/credenciado?`;
  params.forEach((param) => {
    const value = url.searchParams.get(param);
    if (value !== null && value !== "") {
      apiUrl += `${encodeURIComponent(param)}=${encodeURIComponent(value)}&`;
    }
  });
  apiUrl = apiUrl.replace(/&$/, "");

  const res = await fetch(apiUrl, {
    method: "GET",
    headers: { Authorization: `Bearer ${session}` },
  });

  if (!res.ok)
    return NextResponse.json(
      { message: "Erro ao buscar credenciados", success: false },
      { status: res.status }
    );
  
  const data = await res.json();
  return NextResponse.json({ 
    data: data, 
    success: true 
  }, { status: 200 });
}

interface DefaultResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
}

interface CredenciadoPayload {
  polo_regionalId: string;
  informacoes: {
    cnpj: string;
    razao_social: string;
    inscricao_estadual: string;
    inscricao_municipal: string;
    nome_fantasia: string;
    atividade_principal: string;
    tipos_de_veiculos: string[];
    horario_funcionamento: string;
    data_abertura: string;
    porte_empresarial: string;
    capital_social: string;
    patrimonio_liquido: string;
    observacoes_gerais: string;
    logotipo_empresa: string;
  };
  contatos: {
    telefone: string;
    celular: string;
    email: string;
    nome_do_gerente: string;
    telefone_do_gerente: string;
    nome_do_proprietario: string;
    telefone_do_proprietario: string;
  };
  endereco: {
    cep: string;
    logradouro: string;
    bairro: string;
    estado: string;
    cidade: string;
    latitude: string;
    longitude: string;
  };
  estrutura: {
    capacidade_total_de_atendimento: number;
    box_para_veiculos_leves: number;
    box_para_veiculos_pesados: number;
    elevadores_para_veiculos: number;
    estufas_para_pintura: number;
    imagens: string[];
  };
  orcamentista: boolean;
  atende_placa_verde: boolean;
  concessionaria: boolean;
  orcamentacao: boolean;
  prazo_contratualId: string;
  tipos_de_veiculos: string;
  porte_empresarial: string;
  logotipo_empresa: any;
  tipos_de_veiculosId: string;
  imagens: any;
  configuracoes: {
    orcamentista: string;
    atende_placa_verde: string;
    concessionaria: string;
    orcamentacao: string;
  };
}

interface BackendPayload {
  cnpj: string;
  razao_social: string;
  inscri_estadual: string;
  inscri_municipal: string;
  nome_fantasia: string;
  atividade_principal: string;
  horario_funcionamento: string;
  data_abertura: string;
  porte_empresarial: string;
  capital_social: string;
  patrimonio_liquido: string;
  observacoes_gerais: string;
  logotipo_empresa: string;
  tipos_veiculo: string[];
  telefone: string;
  celular: string;
  email: string;
  nome_gerente: string;
  telefone_gerente: string;
  nome_proprietario: string;
  telefone_proprietario: string;
  cep: string;
  logradouro: string;
  bairro: string;
  estado: string;
  cidade: string;
  latitude: string;
  longitude: string;
  capacidade_atendimento: number;
  box_veiculos_leves: number;
  box_veiculos_pesados: number;
  elevadores_veiculos: number;
  estufas_pintura: number;
  images: string[];
  polo_regionalId: string;
  prazoId: string;
}

// Função para mapear o payload do frontend para o formato do backend
function mapPayloadToBackendFormat(payload: CredenciadoPayload): BackendPayload {
  return {
    cnpj: payload.informacoes.cnpj,
    razao_social: payload.informacoes.razao_social,
    inscri_estadual: payload.informacoes.inscricao_estadual,
    inscri_municipal: payload.informacoes.inscricao_municipal,
    nome_fantasia: payload.informacoes.nome_fantasia,
    atividade_principal: payload.informacoes.atividade_principal,
    horario_funcionamento: payload.informacoes.horario_funcionamento,
    data_abertura: payload.informacoes.data_abertura,
    porte_empresarial: payload.informacoes.porte_empresarial,
    capital_social: payload.informacoes.capital_social,
    patrimonio_liquido: payload.informacoes.patrimonio_liquido,
    observacoes_gerais: payload.informacoes.observacoes_gerais,
    logotipo_empresa: payload.informacoes.logotipo_empresa,
    tipos_veiculo: payload.informacoes.tipos_de_veiculos,
    telefone: payload.contatos.telefone,
    celular: payload.contatos.celular,
    email: payload.contatos.email,
    nome_gerente: payload.contatos.nome_do_gerente,
    telefone_gerente: payload.contatos.telefone_do_gerente,
    nome_proprietario: payload.contatos.nome_do_proprietario,
    telefone_proprietario: payload.contatos.telefone_do_proprietario,
    cep: payload.endereco.cep,
    logradouro: payload.endereco.logradouro,
    bairro: payload.endereco.bairro,
    estado: payload.endereco.estado,
    cidade: payload.endereco.cidade,
    latitude: payload.endereco.latitude,
    longitude: payload.endereco.longitude,
    capacidade_atendimento: payload.estrutura.capacidade_total_de_atendimento,
    box_veiculos_leves: payload.estrutura.box_para_veiculos_leves,
    box_veiculos_pesados: payload.estrutura.box_para_veiculos_pesados,
    elevadores_veiculos: payload.estrutura.elevadores_para_veiculos,
    estufas_pintura: payload.estrutura.estufas_para_pintura,
    images: payload.estrutura.imagens,
    polo_regionalId: payload.polo_regionalId,
    prazoId: payload.prazo_contratualId,
  };
}

export async function POST(
  req: NextRequest
): Promise<NextResponse<DefaultResponse<credenciado[]>>> {
  console.log("post chamado");
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json({ message: "erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  try {
    // Extrai o corpo da requisição
    const body = await req.json();
    console.log("Payload recebido do frontend:", body);

    // Mapeia o payload para o formato esperado pelo backend
    const mappedPayload = mapPayloadToBackendFormat(body);
    console.log("Payload mapeado para o backend:", mappedPayload);

    // Envia os dados mapeados para o backend
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/credenciados/credenciado`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${session}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(mappedPayload),
      //@ts-ignore
      duplex: "half",
    });

    if (!res.ok) {
      const errorText = await res.text();
      console.error("Erro ao enviar dados para o backend:", errorText);
      return NextResponse.json(
        { message: "Erro ao enviar dados do credenciado", success: false },
        { status: res.status }
      );
    }

    const data = await res.json();
    return NextResponse.json({ data, success: true }, { status: 200 });
  } catch (error) {
    console.error("Erro no processamento da requisição:", error);
    return NextResponse.json(
      {
        message: "Erro interno do servidor",
        success: false,
        error: error instanceof Error ? error.message : "Erro desconhecido",
      },
      { status: 500 }
    );
  }
}
