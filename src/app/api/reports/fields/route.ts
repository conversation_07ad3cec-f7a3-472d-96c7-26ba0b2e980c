import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { cookies } from "next/headers";

export async function GET() {
  try {
    const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
    if (!cookie) {
      return NextResponse.json({ message: "erro", success: false }, { status: 401 });
    }

    const session = cookie.value;
    if (!session) {
      (await cookies()).delete(SESSION_COOKIE_NAME);
      return NextResponse.json(
        { message: "Você não está logado", success: false },
        { status: 401 }
      );
    }

    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/reports/fields`, {
      headers: {
        Authorization: `Bearer ${session}`,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      return NextResponse.json(error, { status: response.status });
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Erro ao obter campos:", error);
    return NextResponse.json({ message: "Erro ao obter campos" }, { status: 500 });
  }
}
