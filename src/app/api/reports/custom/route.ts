import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

export async function POST(req: Request) {
  try {
    const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
    if (!cookie) {
      return NextResponse.json({ message: "erro", success: false }, { status: 401 });
    }

    const session = cookie.value;
    if (!session) {
      (await cookies()).delete(SESSION_COOKIE_NAME);
      return NextResponse.json(
        { message: "Você não está logado", success: false },
        { status: 401 }
      );
    }
    const body = await req.json();
    const { fields, filters } = body;

    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/reports/custom`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session}`,
      },
      body: JSON.stringify({ fields, filters }),
    });

    if (!response.ok) {
      const error = await response.json();
      return NextResponse.json(error, { status: response.status });
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Erro ao gerar relatório:", error);
    return NextResponse.json({ message: "Erro ao gerar relatório" }, { status: 500 });
  }
}
