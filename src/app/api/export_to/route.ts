import { NextRequest, NextResponse } from "next/server";
import * as XLSX from "xlsx";
import * as fs from "fs";
import * as path from "path";
import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";

// Função para criar pastas temporárias se não existirem
const createTempDirIfNotExists = () => {
  const tempDir = path.join(process.cwd(), "temp");
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }
  return tempDir;
};

// Função para gerar um nome de arquivo único
const generateUniqueFileName = (extension: string) => {
  return `export_${Date.now()}.${extension}`;
};

// Função para converter objetos em CSV
const convertToCSV = (data: any[]) => {
  if (!data || data.length === 0) return "";
  
  const headers = Object.keys(data[0]);
  const csvRows = [];
  
  // Adiciona cabeçalhos
  csvRows.push(headers.join(','));
  
  // Adiciona linhas de dados
  for (const row of data) {
    const values = headers.map(header => {
      const value = row[header];
      return typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value;
    });
    csvRows.push(values.join(','));
  }
  
  return csvRows.join('\n');
};

// Função para converter objetos em Excel
const convertToExcel = (data: any[]) => {
  const worksheet = XLSX.utils.json_to_sheet(data);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
  return XLSX.write(workbook, { type: "buffer", bookType: "xlsx" });
};

// Função para converter objetos em PDF
const convertToPDF = (data: any[]) => {
  const doc = new jsPDF();
  
  if (data.length > 0) {
    const headers = Object.keys(data[0]);
    const rows = data.map(obj => headers.map(key => obj[key]));
    
    autoTable(doc, {
      head: [headers],
      body: rows,
      theme: 'grid',
      styles: { fontSize: 8, cellPadding: 1 },
      headStyles: { fillColor: [66, 66, 66] }
    });
  } else {
    doc.text("Nenhum dado para exportar", 10, 10);
  }
  
  return doc.output('arraybuffer');
};

export async function POST(req: NextRequest) {
  try {
    const { format, data } = await req.json();
    
    if (!data || !Array.isArray(data) || data.length === 0) {
      return NextResponse.json(
        { error: "Dados inválidos para exportação" },
        { status: 400 }
      );
    }
    
    const tempDir = createTempDirIfNotExists();
    let buffer: Buffer;
    let fileName: string;
    let contentType: string;
    
    switch (format) {
      case "csv":
        fileName = generateUniqueFileName("csv");
        const csvContent = convertToCSV(data);
        buffer = Buffer.from(csvContent, "utf-8");
        contentType = "text/csv";
        break;
        
      case "excel":
        fileName = generateUniqueFileName("xlsx");
        buffer = Buffer.from(convertToExcel(data));
        contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        break;
        
      case "pdf":
        fileName = generateUniqueFileName("pdf");
        const pdfBuffer = convertToPDF(data);
        buffer = Buffer.from(pdfBuffer);
        contentType = "application/pdf";
        break;
        
      default:
        return NextResponse.json(
          { error: "Formato não suportado" },
          { status: 400 }
        );
    }
    
    // Salva o arquivo temporariamente
    const filePath = path.join(tempDir, fileName);
    fs.writeFileSync(filePath, buffer);
    
    // Cria um URL para download
    const fileUrl = `/api/export_to/download?file=${fileName}`;
    
    return NextResponse.json({ fileUrl });
  } catch (error) {
    console.error("Erro ao exportar:", error);
    return NextResponse.json(
      { error: "Erro ao processar a exportação" },
      { status: 500 }
    );
  }
}

// Rota para download do arquivo
export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const fileName = url.searchParams.get("file");
    
    if (!fileName) {
      return NextResponse.json(
        { error: "Nome do arquivo não fornecido" },
        { status: 400 }
      );
    }
    
    const tempDir = path.join(process.cwd(), "temp");
    const filePath = path.join(tempDir, fileName);
    
    if (!fs.existsSync(filePath)) {
      return NextResponse.json(
        { error: "Arquivo não encontrado" },
        { status: 404 }
      );
    }
    
    const fileContent = fs.readFileSync(filePath);
    
    // Determina o tipo de conteúdo com base na extensão
    let contentType = "application/octet-stream";
    if (fileName.endsWith(".csv")) {
      contentType = "text/csv";
    } else if (fileName.endsWith(".xlsx")) {
      contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    } else if (fileName.endsWith(".pdf")) {
      contentType = "application/pdf";
    }
    
    // Limpa o arquivo após o download
    setTimeout(() => {
      try {
        fs.unlinkSync(filePath);
      } catch (error) {
        console.error("Erro ao excluir arquivo temporário:", error);
      }
    }, 60000); // Remove após 1 minuto
    
    return new NextResponse(fileContent, {
      headers: {
        "Content-Type": contentType,
        "Content-Disposition": `attachment; filename=${fileName}`
      }
    });
  } catch (error) {
    console.error("Erro ao baixar arquivo:", error);
    return NextResponse.json(
      { error: "Erro ao processar o download" },
      { status: 500 }
    );
  }
} 