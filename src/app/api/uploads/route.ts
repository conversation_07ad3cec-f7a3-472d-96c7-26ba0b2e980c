/* eslint-disable @typescript-eslint/ban-ts-comment */
import { NextResponse } from "next/server";
import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { decode } from "@/lib/auth/server-session";
import { cookies } from "next/headers";

export async function POST(request: Request) {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  try {
    const formData = await request.formData();
    const files = formData.getAll("files") as File[];

    if (!files || files.length === 0) {
      return NextResponse.json({ message: "Nenhum arquivo enviado" }, { status: 400 });
    }
    console.log("uploads chamado");
    // Envia os arquivos para o backend
    const backendFormData = new FormData();
    files.forEach((file) => {
      backendFormData.append("files", file);
    });

    const backendResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/files/uploads`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${session}`,
      },
      body: backendFormData,
      //@ts-ignore
      duplex: "half",
    });

    if (!backendResponse.ok) {
      throw new Error("Erro ao enviar arquivos para o backend");
    }

    const backendData = await backendResponse.json();

    return NextResponse.json(backendData, { status: 200 });
  } catch (error) {
    console.error("Erro no upload dos arquivos:", error);
    return NextResponse.json(
      {
        message: "Erro interno do servidor",
        error: error instanceof Error ? error.message : "Erro desconhecido",
      },
      { status: 500 }
    );
  }
}
