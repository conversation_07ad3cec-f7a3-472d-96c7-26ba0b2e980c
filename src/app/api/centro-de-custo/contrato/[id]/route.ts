import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

interface DefaultResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
}

interface CentroCustoResponse {
  centrosCusto: centro_de_custo[];
  contrato?: any;
}

const path = `${process.env.NEXT_PUBLIC_API_BASE_URL}/centros-custo`;

// Função auxiliar para obter e validar sessão
async function getSessionToken(): Promise<string | null> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return null;
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return null;
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return null;
  }

  return session;
}

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<DefaultResponse<CentroCustoResponse>>> {
  const contratoId = (await params).id;
  const session = await getSessionToken();

  if (!session) {
    return NextResponse.json(
      { message: "Unauthorized", success: false },
      { status: 401 }
    );
  }

  try {
    // Buscar centros de custo por contrato
    const res = await fetch(`${path}/contrato/${contratoId}`, {
      method: "GET",
      headers: { Authorization: `Bearer ${session}` },
    });

    if (!res.ok) {
      const errorData = await res.json().catch(() => ({}));
      return NextResponse.json(
        {
          message:
            errorData.message || "Erro ao buscar centros de custo por contrato",
          success: false,
        },
        { status: res.status }
      );
    }

    const data = await res.json();
    return NextResponse.json(
      {
        data: {
          centrosCusto: data.centrosCusto || [],
          contrato: data.contrato || null,
        },
        success: true,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Erro no BFF - GET centros de custo por contrato:", error);
    return NextResponse.json(
      {
        message: "Erro interno do servidor",
        success: false,
      },
      { status: 500 }
    );
  }
}
