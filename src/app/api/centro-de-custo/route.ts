/* eslint-disable @typescript-eslint/ban-ts-comment */
import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

// Interfaces para tipagem
interface DefaultResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
}

interface CentroCustoCreateRequest {
  descricao: string;
  centro_custo_tomadorID?: string;
  centro_de_custo_ascendenteId?: string;
  dotacao_orcamentista: string;
  valor_dotacao: number;
  nome_responsavel: string;
  contato: string;
  email: string;
  cnpj: string;
  razao_social: string;
  cep?: string;
  logradouro?: string;
  bairro?: string;
  estado?: string;
  cidade?: string;
  ativo?: boolean;
}

interface CentroCustoResponse {
  centrosCusto: centro_de_custo[];
  contrato?: any;
}

const path = `${process.env.NEXT_PUBLIC_API_BASE_URL}/centros-custo`;

// Função auxiliar para obter e validar sessão
async function getSessionToken(): Promise<string | null> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return null;
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return null;
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return null;
  }

  return session;
}

// Função para mapear dados do frontend para o backend
function mapFrontendToBackend(frontendData: any): CentroCustoCreateRequest {
  return {
    descricao: frontendData.descricao,
    centro_custo_tomadorID: frontendData.centro_custo_tomadorID || undefined,
    centro_de_custo_ascendenteId:
      frontendData.centro_de_custo_ascendenteId || undefined,
    dotacao_orcamentista: frontendData.dotacao_orcamentista,
    valor_dotacao: Number(frontendData.valor_dotacao),
    nome_responsavel: frontendData.nome_responsavel,
    contato: frontendData.contato,
    email: frontendData.email,
    cnpj: frontendData.cnpj,
    razao_social: frontendData.razao_social,
    cep: frontendData.cep || undefined,
    logradouro: frontendData.logradouro || undefined,
    bairro: frontendData.bairro || undefined,
    estado: frontendData.estado || undefined,
    cidade: frontendData.cidade || undefined,
    ativo: frontendData.ativo !== undefined ? frontendData.ativo : true,
  };
}

export async function GET(): Promise<
  NextResponse<DefaultResponse<CentroCustoResponse>>
> {
  const session = await getSessionToken();
  if (!session) {
    return NextResponse.json(
      { message: "Unauthorized", success: false },
      { status: 401 }
    );
  }

  try {
    const res = await fetch(`${path}/centro-custo`, {
      method: "GET",
      headers: { Authorization: `Bearer ${session}` },
    });

    if (!res.ok) {
      const errorData = await res.json().catch(() => ({}));
      return NextResponse.json(
        {
          message: errorData.message || "Erro ao buscar centros de custo",
          success: false,
        },
        { status: res.status }
      );
    }

    const data = await res.json();
    return NextResponse.json({ data, success: true }, { status: 200 });
  } catch (error) {
    console.error("Erro no BFF - GET centros de custo:", error);
    return NextResponse.json(
      {
        message: "Erro interno do servidor",
        success: false,
      },
      { status: 500 }
    );
  }
}

export async function POST(
  req: NextRequest
): Promise<NextResponse<DefaultResponse<centro_de_custo | any>>> {
  const session = await getSessionToken();
  if (!session) {
    return NextResponse.json(
      { message: "Unauthorized", success: false },
      { status: 401 }
    );
  }

  try {
    // Extrai e valida o corpo da requisição
    const body = await req.json();
    console.log("Payload recebido do frontend:", body);

    // Validação de campos obrigatórios
    const requiredFields = [
      "descricao",
      "dotacao_orcamentista",
      "valor_dotacao",
      "nome_responsavel",
      "contato",
      "email",
      "cnpj",
      "razao_social",
    ];

    const missingFields = requiredFields.filter((field) => !body[field]);
    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          message: "Campos obrigatórios faltando",
          success: false,
          data: { required: requiredFields, missing: missingFields },
        },
        { status: 400 }
      );
    }

    // Mapeia o payload para o formato esperado pelo backend
    const mappedPayload = mapFrontendToBackend(body);
    console.log("Payload mapeado para o backend:", mappedPayload);

    // Envia os dados mapeados para o backend
    const res = await fetch(`${path}/centro-custo`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${session}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(mappedPayload),
      //@ts-ignore
      duplex: "half",
    });

    if (!res.ok) {
      const errorData = await res.json().catch(() => ({}));
      console.error("Erro do backend:", errorData);

      return NextResponse.json(
        {
          message: errorData.message || "Erro ao criar centro de custo",
          success: false,
        },
        { status: res.status }
      );
    }

    const data = await res.json();
    return NextResponse.json(
      {
        data: data.centroCusto,
        success: true,
        message: data.message || "Centro de custo criado com sucesso",
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Erro no BFF - POST centro de custo:", error);
    return NextResponse.json(
      {
        message:
          error instanceof Error ? error.message : "Erro interno do servidor",
        success: false,
      },
      { status: 500 }
    );
  }
}
