import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

interface DefaultResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
}

interface CentroCustoUpdateRequest {
  descricao?: string;
  centro_custo_tomadorID?: string;
  dotacao_orcamentista?: string;
  valor_dotacao?: number;
  nome_responsavel?: string;
  contato?: string;
  email?: string;
  cnpj?: string;
  razao_social?: string;
  cep?: string;
  logradouro?: string;
  bairro?: string;
  estado?: string;
  cidade?: string;
  ativo?: boolean;
}

const path = `${process.env.NEXT_PUBLIC_API_BASE_URL}/centros-custo`;

// Função auxiliar para obter e validar sessão
async function getSessionToken(): Promise<string | null> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return null;
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return null;
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return null;
  }

  return session;
}

// Função para mapear dados do frontend para o backend (update)
function mapFrontendToBackendUpdate(
  frontendData: any
): CentroCustoUpdateRequest {
  const mapped: CentroCustoUpdateRequest = {};

  if (frontendData.descricao !== undefined)
    mapped.descricao = frontendData.descricao;
  if (frontendData.centro_custo_tomadorID !== undefined)
    mapped.centro_custo_tomadorID = frontendData.centro_custo_tomadorID;
  if (frontendData.dotacao_orcamentista !== undefined)
    mapped.dotacao_orcamentista = frontendData.dotacao_orcamentista;
  if (frontendData.valor_dotacao !== undefined)
    mapped.valor_dotacao = Number(frontendData.valor_dotacao);
  if (frontendData.nome_responsavel !== undefined)
    mapped.nome_responsavel = frontendData.nome_responsavel;
  if (frontendData.contato !== undefined) mapped.contato = frontendData.contato;
  if (frontendData.email !== undefined) mapped.email = frontendData.email;
  if (frontendData.cnpj !== undefined) mapped.cnpj = frontendData.cnpj;
  if (frontendData.razao_social !== undefined)
    mapped.razao_social = frontendData.razao_social;
  if (frontendData.cep !== undefined) mapped.cep = frontendData.cep;
  if (frontendData.logradouro !== undefined)
    mapped.logradouro = frontendData.logradouro;
  if (frontendData.bairro !== undefined) mapped.bairro = frontendData.bairro;
  if (frontendData.estado !== undefined) mapped.estado = frontendData.estado;
  if (frontendData.cidade !== undefined) mapped.cidade = frontendData.cidade;
  if (frontendData.ativo !== undefined) mapped.ativo = frontendData.ativo;

  return mapped;
}
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<DefaultResponse<centro_de_custo>>> {
  const id = (await params).id;
  const session = await getSessionToken();

  if (!session) {
    return NextResponse.json(
      { message: "Unauthorized", success: false },
      { status: 401 }
    );
  }

  try {
    const res = await fetch(`${path}/centro-custo/${id}`, {
      method: "GET",
      headers: { Authorization: `Bearer ${session}` },
    });

    if (!res.ok) {
      const errorData = await res.json().catch(() => ({}));
      return NextResponse.json(
        {
          message: errorData.message || "Erro ao buscar centro de custo",
          success: false,
        },
        { status: res.status }
      );
    }

    const data = await res.json();
    return NextResponse.json(
      {
        data: data.centroCusto,
        success: true,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Erro no BFF - GET centro de custo por ID:", error);
    return NextResponse.json(
      {
        message: "Erro interno do servidor",
        success: false,
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<DefaultResponse<centro_de_custo>>> {
  const id = (await params).id;
  const session = await getSessionToken();

  if (!session) {
    return NextResponse.json(
      { message: "Unauthorized", success: false },
      { status: 401 }
    );
  }

  try {
    // Extrai e valida o corpo da requisição
    const body = await request.json();
    console.log("Payload recebido do frontend para atualização:", body);

    // Mapeia o payload para o formato esperado pelo backend
    const mappedPayload = mapFrontendToBackendUpdate(body);
    console.log("Payload mapeado para o backend:", mappedPayload);

    // Envia os dados mapeados para o backend
    const res = await fetch(`${path}/centro-custo/${id}`, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${session}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(mappedPayload),
    });

    if (!res.ok) {
      const errorData = await res.json().catch(() => ({}));
      console.error("Erro do backend:", errorData);

      return NextResponse.json(
        {
          message: errorData.message || "Erro ao atualizar centro de custo",
          success: false,
        },
        { status: res.status }
      );
    }

    const data = await res.json();
    return NextResponse.json(
      {
        data: data.centroCusto,
        success: true,
        message: data.message || "Centro de custo atualizado com sucesso",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Erro no BFF - PUT centro de custo:", error);
    return NextResponse.json(
      {
        message:
          error instanceof Error ? error.message : "Erro interno do servidor",
        success: false,
      },
      { status: 500 }
    );
  }
}
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<DefaultResponse<null>>> {
  const id = (await params).id;
  const session = await getSessionToken();

  if (!session) {
    return NextResponse.json(
      { message: "Unauthorized", success: false },
      { status: 401 }
    );
  }

  try {
    const res = await fetch(`${path}/centro-custo/${id}`, {
      method: "DELETE",
      headers: { Authorization: `Bearer ${session}` },
    });

    if (!res.ok) {
      const errorData = await res.json().catch(() => ({}));
      console.error("Erro do backend:", errorData);

      return NextResponse.json(
        {
          message: errorData.message || "Erro ao excluir centro de custo",
          success: false,
        },
        { status: res.status }
      );
    }

    const data = await res.json();
    return NextResponse.json(
      {
        data: null,
        success: true,
        message: data.message || "Centro de custo excluído com sucesso",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Erro no BFF - DELETE centro de custo:", error);
    return NextResponse.json(
      {
        message:
          error instanceof Error ? error.message : "Erro interno do servidor",
        success: false,
      },
      { status: 500 }
    );
  }
}
