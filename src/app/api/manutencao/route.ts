import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { decode } from "@/lib/auth/server-session";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
interface DefaultResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
}

export async function POST(req: NextRequest): Promise<NextResponse<DefaultResponse<OS[]>>> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json({ message: "erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Você não está logado", success: false }, { status: 401 });
  }

  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Você não está logado", success: false }, { status: 401 });
  }

  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/manutencoes`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${session}`,
        "Content-Type": "application/json",
      },
      body: req.body,
      // @ts-ignore
      duplex: "half",
    });

    if (!res.ok) {
      console.error(`Erro ao criar manutenção: ${res.status} ${res.statusText}`);
      return NextResponse.json(
        {
          message: `Erro ao criar manutenção: ${res.status} ${res.statusText}`,
          success: false,
        },
        { status: res.status }
      );
    }

    const responseData = await res.json();
    return NextResponse.json({ data: responseData, success: true }, { status: 201 });
  } catch (error) {
    console.error("Erro ao criar ordem de serviço:", error);
    return NextResponse.json(
      { message: "Erro interno ao criar ordem de serviço", success: false },
      { status: 500 }
    );
  }
}

export async function GET(): Promise<NextResponse<DefaultResponse<OS[]>>> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json({ message: "erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Você não está logado", success: false }, { status: 401 });
  }

  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Você não está logado", success: false }, { status: 401 });
  }

  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/manutencoes/proximas`, {
      method: "GET",
      headers: { Authorization: `Bearer ${session}` },
    });

    if (!res.ok) {
      console.error(`Erro ao buscar manutenções próximas: ${res.status} ${res.statusText}`);

      return NextResponse.json(
        {
          message: `Erro ao buscar manutenções próximas: ${res.status} ${res.statusText}`,
          success: false,
          data: [],
        },
        { status: res.status }
      );
    }

    const data = await res.json();
    return NextResponse.json({ data, success: true }, { status: 200 });
  } catch (error) {
    console.error("Erro ao manutenções próximas:", error);
    return NextResponse.json(
      {
        message: "Erro interno ao buscar manutenções próximas",
        success: false,
        data: [],
      },
      { status: 500 }
    );
  }
}
