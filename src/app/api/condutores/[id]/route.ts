import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { decode } from "@/lib/auth/server-session";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

interface DefaultResponse<T> {
  success: boolean;
  message?: string;
  data?: T; // Usando o nome da interface no retorno
}

// GET - Buscar um condutor específico por ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<DefaultResponse<condutor>>> {
  const id = (await params).id;
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);

  if (!cookie) {
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/condutores/condutor/${id}`, {
    method: "GET",
    headers: { Authorization: `Bearer ${session}` },
  });

  if (!res.ok) {
    return NextResponse.json(
      { message: "Erro ao buscar condutor", success: false },
      { status: 404 }
    );
  }

  return NextResponse.json(
    { data: await res.json(), success: true }, // Usando "condutores" no retorno
    { status: 200 }
  );
}

// PUT - Atualizar um condutor específico
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<DefaultResponse<condutor>>> {
  const id = (await params).id;
  console.log("ID do condutor:", id);
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/condutores/condutor/${id}`, {
    method: "PUT",
    headers: {
      Authorization: `Bearer ${session}`,
      "Content-Type": "application/json",
    },
    body: request.body,
    // @ts-ignore
    duplex: "half",
  });

  if (!res.ok) {
    return NextResponse.json(
      { message: "Erro ao atualizar condutor", success: false },
      { status: 400 }
    );
  }

  return NextResponse.json(
    { data: await res.json(), success: true }, // Usando "condutores" no retorno
    { status: 200 }
  );
}

// DELETE - Deletar um condutor específico
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<DefaultResponse<null>>> {
  const id = (await params).id;
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);

  if (!cookie) {
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/condutor/${id}`, {
    method: "DELETE",
    headers: { Authorization: `Bearer ${session}` },
  });

  if (!res.ok) {
    return NextResponse.json(
      { message: "Erro ao deletar condutor", success: false },
      { status: 404 }
    );
  }

  return NextResponse.json(
    { message: "Condutor deletado com sucesso", success: true },
    { status: 200 }
  );
}
