/* eslint-disable @typescript-eslint/ban-ts-comment */
import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { decode } from "@/lib/auth/server-session";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

interface DefaultResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
}

export async function GET(): Promise<NextResponse<DefaultResponse<condutor[]>>> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json({ message: "Erro: Não autenticado", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "<PERSON><PERSON>", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/condutores/condutor`, {
      method: "GET",
      headers: { Authorization: `Bearer ${session}` },
    });

    if (!res.ok) {
      throw new Error(`Erro HTTP: ${res.status}`);
    }

    const data = await res.json();
    return NextResponse.json({ data, success: true }, { status: 200 });
  } catch (error) {
    return NextResponse.json(
      {
        message: error instanceof Error ? error.message : "Erro ao buscar condutores",
        success: false,
      },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest): Promise<NextResponse<DefaultResponse<condutor>>> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json({ message: "Erro: Não autenticado", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }
  try {
    const body = await req.json();

    const mappedBody = mapPayloadToAPIFormat(body);

    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/condutores/condutor`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${session}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(mappedBody),
      // @ts-ignore
      duplex: "half",
    });

    if (!res.ok) {
      console.log(res);
      throw new Error(`Erro HTTP: ${res.status}`);
    }

    const data = await res.json();
    return NextResponse.json({ data, success: true }, { status: 201 });
  } catch (error) {
    console.log(error);
    return NextResponse.json(
      {
        message: error instanceof Error ? error.message : "Erro ao criar condutor",
        success: false,
      },
      { status: 500 }
    );
  }
}

function mapPayloadToAPIFormat(payload: any) {
  console.log("Payload recebido:", payload);

  // Verificar se os dados já estão no formato correto (com campos separados)
  if (payload.numero_cnh) {
    // Os dados já estão no formato esperado pela API, não precisa transformar
    return payload;
  }

  // Se chegou aqui, significa que temos a estrutura aninhada e precisamos mapear
  // Processar campos opcionais do MOPP de forma segura
  const mopp = payload.mopp || {};

  return {
    // Campos básicos
    nome: payload.nome,
    matricula: payload.matricula,
    cpf: payload.cpf || "",
    contato: payload.contato,
    email: payload.email,

    // Campos de endereço
    cep: payload.endereco?.cep || "",
    logradouro: payload.endereco?.logradouro || "",
    bairro: payload.endereco?.bairro || "",
    estado: payload.endereco?.estado || "",
    cidade: payload.endereco?.cidade || "",

    // Campos da CNH - com verificações de segurança
    numero_cnh: payload.cnh?.numero || "",
    categoria: payload.cnh?.categoria || "",
    estado_cnh: payload.cnh?.estado || "",
    data_emissao_cnh: payload.cnh?.data_emissao || null,
    data_vencimento_cnh: payload.cnh?.data_vencimento || null,

    // Campos do MOPP
    numero_mopp: mopp.numero || "",
    emissor_mopp: mopp.emissor || "",
    data_emissao_mopp: mopp.data_emissao || null,
    data_vencimento_mopp: mopp.data_vencimento || null,

    // Status e centro de custo
    status: payload.status,
    centro_custoID: payload.lotacao_condutorId || payload.centro_custoID || "",
  };
}
