import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { decode } from "@/lib/auth/server-session";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
interface DefaultResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
}

export async function GET(): Promise<NextResponse<DefaultResponse<despesa[]>>> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json({ message: "erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/despesas/despesa`, {
      method: "GET",
      headers: { Authorization: `Bearer ${session}` },
    });

    if (!res.ok) {
      return NextResponse.json(
        { message: "Erro ao buscar despesas", success: false },
        { status: 404 }
      );
    }

    // Processar a resposta para garantir que seja um array válido
    const responseData = await res.json();

    // Verificar se responseData é um array
    if (Array.isArray(responseData)) {
      return NextResponse.json({ data: responseData, success: true }, { status: 200 });
    } else {
      return NextResponse.json({ data: [], success: true }, { status: 200 });
    }
  } catch (error) {
    console.error("Erro ao buscar despesas:", error);
    return NextResponse.json(
      { message: "Erro interno ao buscar despesas", success: false, data: [] },
      { status: 500 }
    );
  }
}
export async function POST(req: NextRequest): Promise<NextResponse<DefaultResponse<despesa>>> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json({ message: "erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/despesas/despesa`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${session}`,
      "Content-Type": "application/json",
    },
    body: req.body,
    // @ts-ignore
    duplex: "half",
  });
  if (!res.ok)
    return NextResponse.json(
      { message: "Erro ao criar checklist", success: false },
      { status: 404 }
    );
  return NextResponse.json({ data: await res.json(), success: true }, { status: 200 });
}
