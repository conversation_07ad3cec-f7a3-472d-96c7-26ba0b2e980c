import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@/lib/auth/server-session";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();
    if (!session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const osId = searchParams.get("osId");
    
    if (!osId) {
      return NextResponse.json({ error: "ID da OS é obrigatório" }, { status: 400 });
    }
    
    // Aqui você implementaria a lógica para gerar o relatório
    // Por enquanto, retornamos uma mensagem de sucesso
    
    return NextResponse.json({
      message: "Relatório gerado com sucesso",
      downloadUrl: `/api/downloads/relatorio-comparacao-${osId}.pdf`
    });
  } catch (error: any) {
    console.error("Erro ao gerar relatório:", error);
    return NextResponse.json(
      { error: error.message || "Erro interno" },
      { status: 500 }
    );
  }
}