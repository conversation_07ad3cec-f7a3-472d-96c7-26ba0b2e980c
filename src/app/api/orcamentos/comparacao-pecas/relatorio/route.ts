import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@/lib/auth/server-session";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();
    if (!session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const codigo = searchParams.get("codigo");
    const descricao = searchParams.get("descricao");
    const periodo = searchParams.get("periodo") || "60d";

    if (!codigo && !descricao) {
      return NextResponse.json(
        {
          error: "Código ou descrição da peça é obrigatório",
        },
        { status: 400 }
      );
    }

    // Gerar dados mockados para o relatório
    const basePrice = Math.random() * 500 + 100;
    const variation = 0.2;

    const generatePrice = (factor: number) => {
      return (
        Math.round(
          basePrice * (1 + (Math.random() - 0.5) * variation * factor) * 100
        ) / 100
      );
    };

    const generateDate = (daysAgo: number) => {
      const date = new Date();
      date.setDate(date.getDate() - daysAgo);
      return date.toISOString().split("T")[0];
    };

    const relatorioData = {
      peca: {
        codigo: codigo || "N/A",
        descricao: descricao || "Peça não identificada",
      },
      periodo: periodo,
      dataGeracao: new Date().toISOString(),
      comparacoes: {
        ultimosSessentaDias: {
          data: generateDate(Math.floor(Math.random() * 60)),
          preco: generatePrice(1),
          fornecedor: "Auto Peças Regional",
          osNumero: `OS-${new Date().getFullYear()}-${Math.floor(
            Math.random() * 9999
          )
            .toString()
            .padStart(4, "0")}`,
          economia: Math.round((Math.random() * 50 + 10) * 100) / 100,
        },
        estado: {
          data: generateDate(Math.floor(Math.random() * 90)),
          preco: generatePrice(1.2),
          fornecedor: "Distribuidora Estadual",
          osNumero: `OS-${new Date().getFullYear()}-${Math.floor(
            Math.random() * 9999
          )
            .toString()
            .padStart(4, "0")}`,
          economia: Math.round((Math.random() * 30 + 5) * 100) / 100,
        },
        localidade: {
          data: generateDate(Math.floor(Math.random() * 30)),
          preco: generatePrice(0.8),
          fornecedor: "Oficina Local Ltda",
          osNumero: `OS-${new Date().getFullYear()}-${Math.floor(
            Math.random() * 9999
          )
            .toString()
            .padStart(4, "0")}`,
          economia: Math.round((Math.random() * 80 + 20) * 100) / 100,
        },
        fornecedor: {
          data: generateDate(Math.floor(Math.random() * 45)),
          preco: generatePrice(0.9),
          fornecedor: "Fornecedor Habitual",
          osNumero: `OS-${new Date().getFullYear()}-${Math.floor(
            Math.random() * 9999
          )
            .toString()
            .padStart(4, "0")}`,
          economia: Math.round((Math.random() * 60 + 15) * 100) / 100,
        },
      },
      resumo: {
        menorPreco: Math.min(
          generatePrice(1),
          generatePrice(1.2),
          generatePrice(0.8),
          generatePrice(0.9)
        ),
        maiorEconomia: Math.round((Math.random() * 80 + 20) * 100) / 100,
        recomendacao:
          "Considere negociar com fornecedores locais para obter melhores preços",
      },
    };

    return NextResponse.json({
      success: true,
      data: relatorioData,
      downloadUrl: `/api/downloads/relatorio-comparacao-${
        codigo || "sem-codigo"
      }-${Date.now()}.pdf`,
    });
  } catch (error: any) {
    console.error("Erro ao gerar relatório:", error);
    return NextResponse.json(
      { error: error.message || "Erro interno" },
      { status: 500 }
    );
  }
}
