import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@/lib/auth/server-session";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();
    if (!session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const codigo = searchParams.get("codigo");
    const descricao = searchParams.get("descricao");

    if (!codigo && !descricao) {
      return NextResponse.json(
        { error: "Código ou descrição da peça é obrigatório" },
        { status: 400 }
      );
    }

    // Aqui você implementaria a lógica para buscar as comparações no banco de dados
    // Por enquanto, retornamos dados simulados baseados nos parâmetros

    // Gerar dados simulados mais realistas
    const basePrice = Math.random() * 500 + 100; // Preço base entre R$ 100 e R$ 600
    const variation = 0.2; // Variação de 20%

    const generatePrice = (factor: number) => {
      return (
        Math.round(
          basePrice * (1 + (Math.random() - 0.5) * variation * factor) * 100
        ) / 100
      );
    };

    const generateDate = (daysAgo: number) => {
      const date = new Date();
      date.setDate(date.getDate() - daysAgo);
      return date.toISOString().split("T")[0];
    };

    const comparacoes = {
      ultimosSessentaDias: {
        data: generateDate(Math.floor(Math.random() * 60)),
        preco: generatePrice(1),
        fornecedor: "Auto Peças Regional",
        osNumero: `OS-${new Date().getFullYear()}-${Math.floor(
          Math.random() * 9999
        )
          .toString()
          .padStart(4, "0")}`,
      },
      estado: {
        data: generateDate(Math.floor(Math.random() * 90)),
        preco: generatePrice(1.2),
        fornecedor: "Distribuidora Estadual",
        osNumero: `OS-${new Date().getFullYear()}-${Math.floor(
          Math.random() * 9999
        )
          .toString()
          .padStart(4, "0")}`,
      },
      localidade: {
        data: generateDate(Math.floor(Math.random() * 30)),
        preco: generatePrice(0.8),
        fornecedor: "Oficina Local Ltda",
        osNumero: `OS-${new Date().getFullYear()}-${Math.floor(
          Math.random() * 9999
        )
          .toString()
          .padStart(4, "0")}`,
      },
      fornecedor: {
        data: generateDate(Math.floor(Math.random() * 45)),
        preco: generatePrice(0.9),
        fornecedor: "Fornecedor Habitual",
        osNumero: `OS-${new Date().getFullYear()}-${Math.floor(
          Math.random() * 9999
        )
          .toString()
          .padStart(4, "0")}`,
      },
    };

    return NextResponse.json(comparacoes);
  } catch (error: any) {
    console.error("Erro na API de comparação de peças:", error);
    return NextResponse.json(
      { error: error.message || "Erro interno" },
      { status: 500 }
    );
  }
}
