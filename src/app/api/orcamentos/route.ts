import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { cookies } from "next/headers";
import {  NextResponse } from "next/server";
import { decompressToken } from "@/lib/auth/decompress-token";

interface DefaultResponse<T> {
    success: boolean;
    message?: string;
    data?: T;
}

export async function GET(): Promise<NextResponse<DefaultResponse<Orcamento[]>>> {
    const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
    if (!cookie) {
        return NextResponse.json({ message: "erro", success: false }, { status: 401 });
    }

    const compressedToken = cookie.value;

    if (!compressedToken) {
        (await cookies()).delete(SESSION_COOKIE_NAME);
        return NextResponse.json({ message: "Você não está logado", success: false }, { status: 401 });
    }

    const session = decompressToken(compressedToken);
    console.log("Token enviado:", session);

    if (!session) {
        (await cookies()).delete(SESSION_COOKIE_NAME);
        return NextResponse.json({ message: "Você não está logado", success: false }, { status: 401 });
    }

    try {
        const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/orcamentos`, {
            method: "GET",
            headers: { Authorization: `Bearer ${session}` },
        });

        if (!res.ok) {
            console.error(`Erro ao buscar orçamentos: ${res.status} ${res.statusText}`);

            return NextResponse.json(
                {
                    message: `Erro ao buscar orçamentos: ${res.status} ${res.statusText}`,
                    success: false,
                    data: [],
                },
                { status: res.status }
            );
        }

        const data = await res.json();
        return NextResponse.json({ data, success: true }, { status: 200 });
    }
    catch (error) {
    console.error("Erro ao buscar orçamentos:", error);
    return NextResponse.json(
      {
        message: "Erro interno ao buscar orçamentos",
        success: false,
        data: [],
      },
      { status: 500 }
    );
  }
}
