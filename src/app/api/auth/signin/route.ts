import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_MAX_AGE, SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import { gzip, ungzip } from "pako";

// Função para comprimir string
function compressToken(token: string): string {
  const compressed = gzip(new TextEncoder().encode(token));
  return Buffer.from(compressed).toString("base64");
}

export async function POST(request: NextRequest) {
  const { email, password } = await request.json();

  if (!email || !password) {
    return NextResponse.json({ message: "Email e senha obrigatórios" }, { status: 400 });
  }

  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/login`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      email,
      senha: password,
    }),
  });
  const isProduction = process.env.NODE_ENV === "production";

  const session = await res.json();
  if (session && res.ok && session.token) {
    const compressedToken = compressToken(session.token);
    (await cookies()).set(SESSION_COOKIE_NAME, compressedToken, {
      httpOnly: true,
      secure: isProduction,
      expires: new Date(Date.now() + SESSION_COOKIE_MAX_AGE),
      sameSite: "lax",
      path: "/",
    });
    const decompressedToken = decompressToken(compressedToken);
    return NextResponse.json({...session, token: decompressedToken }, { status: 200 });
  }

  return NextResponse.json({ message: "Credenciais inválidas" }, { status: 401 });
}
