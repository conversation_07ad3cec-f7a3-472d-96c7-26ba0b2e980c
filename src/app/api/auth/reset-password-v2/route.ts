import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_MAX_AGE, SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { encode, getServerSession } from "@/lib/auth/server-session";

import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

export async function PATCH(request: NextRequest) {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json(
      { message: "erro", success: false },
      { status: 401 }
    );
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json(
      { message: "Erro", success: false },
      { status: 401 }
    );
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json(
      { message: "Erro", success: false },
      { status: 401 }
    );
  }
  const actualSession = await getServerSession();
  const { password, userId } = await request.json();

  if (!password) {
    return NextResponse.json({}, { status: 400 });
  }

  const res = await fetch(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/reset-password-v2`,
    {
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${session}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        userId,
        senha: password,
      }),
    }
  );

  const user = await res.json();
  if (res.ok) {
    return NextResponse.json({}, { status: 200 });
  }

  return NextResponse.json({}, { status: 401 });
}
