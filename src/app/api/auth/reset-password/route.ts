
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {

  const { email } = await request.json();

  if (!email) {
    return NextResponse.json({ message: "Email is required", success: false }, { status: 400 });
  }

  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/reset-password`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      email,
    }),
  });

  const user = await res.json();
  if (res.ok) {
    return NextResponse.json({}, { status: 200 });
  }

  return NextResponse.json({}, { status: 401 });
}
