import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { decode } from "@/lib/auth/server-session";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

interface DefaultResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
}

// GET - Buscar todos os empenhos
export async function GET(): Promise<NextResponse<DefaultResponse<empenho[]>>> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/empenhos/empenho`, {
    method: "GET",
    headers: { Authorization: `Bearer ${session}` },
  });
  if (!res.ok)
    return NextResponse.json(
      { message: "Erro ao buscar empenhos", success: false },
      { status: 404 }
    );
  return NextResponse.json({ data: await res.json(), success: true }, { status: 200 });
}

// POST - Criar um novo empenho
export async function POST(req: NextRequest): Promise<NextResponse<DefaultResponse<empenho>>> {
  const cookie = (await cookies()).get(SESSION_COOKIE_NAME);
  if (!cookie) {
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const compressedToken = cookie.value;
  if (!compressedToken) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  // Descomprime o token antes de usar
  const session = decompressToken(compressedToken);
  if (!session) {
    (await cookies()).delete(SESSION_COOKIE_NAME);
    return NextResponse.json({ message: "Erro", success: false }, { status: 401 });
  }

  const body = await req.json();
  const apiEmpenho = mapFrontendToApiEmpenho(body);

  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/empenhos/empenho`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${session}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(apiEmpenho),
    // @ts-ignore
    duplex: "half",
  });

  if (!res.ok)
    return NextResponse.json({ message: "Erro ao criar empenho", success: false }, { status: 400 });

  return NextResponse.json({ data: await res.json(), success: true }, { status: 200 });
}

interface ApiEmpenho {
  centroCustoId: string;
  nota_empenho: string;
  ano_competencia: string;
  data_inicial: Date;
  data_final: Date;
  dotacao_orcamentaria: string;
  valor_pecas: number;
  valor_servicos: number;
  empenho_bloqueado: boolean;
  empenho_ativo: boolean;
}

function mapFrontendToApiEmpenho(frontendEmpenho: empenho): ApiEmpenho {
  return {
    centroCustoId: frontendEmpenho.centro_de_custoId,
    nota_empenho: frontendEmpenho.nota_empenho,
    ano_competencia: frontendEmpenho.ano_de_competencia,
    data_inicial: frontendEmpenho.data_inicial,
    data_final: frontendEmpenho.data_final,
    dotacao_orcamentaria: frontendEmpenho.dotacao_orcamentada,
    valor_pecas: frontendEmpenho.valor_destinado_as_pecas,
    valor_servicos: frontendEmpenho.valor_destinado_aos_servicos,
    empenho_bloqueado: frontendEmpenho.bloqueado,
    empenho_ativo: frontendEmpenho.ativo,
  };
}
