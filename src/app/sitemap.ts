import { ROUTES } from "@/lib/utils";

export const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;

export default async function sitemap() {
  const allRoutes = ROUTES.flatMap((route) => {
    const isLogin = route.href === "/login";
    const prefix = isLogin ? "" : "/dashboard";

    const parentRoute = {
      url: `${baseUrl}${prefix}${route.href}`,
      lastModified: new Date().toISOString().split("T")[0],
    };

    const childRoutes = (route.items || []).map((item) => ({
      url: `${baseUrl}${prefix}${route.href}${item.href}`,
      lastModified: new Date().toISOString().split("T")[0],
    }));

    return [parentRoute, ...childRoutes];
  });

  const rootRoute = {
    url: `${baseUrl}/`,
    lastModified: new Date().toISOString().split("T")[0],
  };

  return [rootRoute, ...allRoutes];
}
