import { Toaster } from "@/components/ui/sonner";
import "./globals.css";

import { Metadata } from "next";
import { baseUrl } from "./sitemap";
import { ThemeProvider } from "@/providers/theme-provider";
import { AuditoriaProvider } from "@/context/auditoria";


const validBaseUrl =
  baseUrl && baseUrl.startsWith("http") ? baseUrl : "http://localhost:3000";

export const metadata: Metadata = {
  title: "Carletto Gestão de Frotas - Gestão de frotas",
  description: "Sistema de gestão de frotas",
  metadataBase: new URL(validBaseUrl),
  openGraph: {
    title: "Carletto Gestão de Frotas - Gestão de frotas",
    description: "Sistema de gestão de frotas",
    url: baseUrl,
    siteName: "Carletto Gestão de Frotas",
    locale: "pt_br",
    type: "website",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // const session = await auth();
  return (
    <html lang="pt-BR" suppressHydrationWarning>
      <body suppressHydrationWarning>
        <div
          className="flex flex-col h-screen w-full overflow-auto"
          suppressHydrationWarning
        >
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <AuditoriaProvider>
              {children}
            </AuditoriaProvider>
          </ThemeProvider>
        </div>
        <Toaster />
      </body>
    </html>
  );
}
