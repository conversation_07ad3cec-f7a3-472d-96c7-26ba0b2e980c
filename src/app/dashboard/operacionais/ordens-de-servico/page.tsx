"use client";

import React, { useEffect, useState } from "react";
import { OperacionalOSTable } from "@/components/sections/operacionais/operacional-os";
import { Bar } from "react-chartjs-2";
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
} from "chart.js";

// Registrar os componentes do Chart.js
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

export default function Page() {
    const [dataOS, setDataOS] = useState<any[]>([]); // Dados da tabela de OS

    useEffect(() => {
        // Aqui você vai buscar ou manipular os dados necessários da tabela, por exemplo:
        const fetchedData = [
            { centro_de_custo: "Centro 1", valor_servicos: 100 },
            { centro_de_custo: "Centro 2", valor_servicos: 200 },
            { centro_de_custo: "Centro 3", valor_servicos: 300 },
            // Dados de exemplo
        ];

        setDataOS(fetchedData);
    }, []);

    // Preparando os dados para o gráfico
    const labels = dataOS.map(item => item.centro_de_custo); // Usando "centro_de_custo" como rótulo
    const data = dataOS.map(item => item.valor_servicos); // Usando "valor_servicos" como os dados

    const chartData = {
        labels: labels,
        datasets: [
            {
                label: "Valor dos Serviços",
                data: data,
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1,
            },
        ],
    };

    const options = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top' as const,
            },
            title: {
                display: true,
                text: 'Valor dos Serviços por Centro de Custo',
            },
        },
    };

    return (
        <div className="w-full flex flex-col">
            <h1 className="scroll-m-20 text-3xl font-extrabold tracking-tight p-4">
                Operacionais
            </h1>

            {/* Colocando o gráfico acima da tabela */}
            <div  id="chartDivOS"className="p-4" style={{ height: "300px", width: "100%" }}>
                <Bar data={chartData} options={options} />
            </div>

            <div className="w-full">
                <OperacionalOSTable />
            </div>
        </div>
    );
}