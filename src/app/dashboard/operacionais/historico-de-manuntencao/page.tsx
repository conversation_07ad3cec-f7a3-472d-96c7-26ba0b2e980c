"use client";

import React, { useEffect, useState } from "react";
import { ExpandableTable } from "@/components/tables/expandeble-table";
import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "@/components/tables/column-header";
import { Checkbox } from "@/components/ui/checkbox";
import {
  HistoricoManutencaoProvider,
  useHistoricoManutencao,
  HistoricoManutencao,
} from "@/context/historico-manutencao-context";
import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { useOS } from "@/context/os-context";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { useSession } from "@/components/session/use-session";
import { Session } from "@/lib/auth/types";
import { getServerSession } from "@/lib/auth/server-session";
import { useCredenciado } from "@/context/credenciado-context";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { getCredenciadoById } from "@/serverActions/credenciadoAction";

// Define interfaces
interface DatePickerWithButtonProps {
  date: Date | null;
  onDateChange: (date: Date | null) => void;
  label: string;
}

interface DateRangeFilterProps {
  startDate: Date | null;
  endDate: Date | null;
  onStartDateChange: (date: Date | null) => void;
  onEndDateChange: (date: Date | null) => void;
  onFilterApply: () => void;
  onFilterClear: () => void;
  additionalFilters: AdditionalFilters;
  onAdditionalFiltersChange: (filters: AdditionalFilters) => void;
}

interface DateFilterState {
  startDate: Date | null;
  endDate: Date | null;
  isApplied: boolean;
}

interface AdditionalFilters {
  tipoVeiculo: string;
  tipoFrota: string;
  porteVeiculo: string;
}

interface HistoricoManutencaoTableProps {
  dateFilter: DateFilterState;
  additionalFilters: AdditionalFilters;
}

interface BarChartComponentProps {
  dateFilter: DateFilterState;
}

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

function DatePickerWithButton({
  date,
  onDateChange,
  label,
}: DatePickerWithButtonProps) {
  const [open, setOpen] = useState<boolean>(false);

  return (
    <div className="flex flex-col">
      <span className="text-sm font-medium">{label}</span>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-[200px] justify-start text-left font-normal",
              !date && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date
              ? format(date, "dd/MM/yyyy", { locale: ptBR })
              : "Selecione uma data"}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0">
          <Calendar
            mode="single"
            selected={date || undefined}
            onSelect={(date) => {
              onDateChange(date ?? null);
              setOpen(false);
            }}
            initialFocus
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}

function DateRangeFilter({
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  onFilterApply,
  onFilterClear,
  additionalFilters,
  onAdditionalFiltersChange,
}: DateRangeFilterProps) {
  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-4 p-4 bg-muted/20 rounded-lg">
        <DatePickerWithButton
          date={startDate}
          onDateChange={onStartDateChange}
          label="Data Inicial"
        />
        <DatePickerWithButton
          date={endDate}
          onDateChange={onEndDateChange}
          label="Data Final"
        />
        <div className="flex items-end gap-2">
          <Button
            onClick={onFilterApply}
            variant="default"
            size="sm"
            className="h-10"
          >
            Aplicar Filtro
          </Button>
          <Button
            onClick={onFilterClear}
            variant="outline"
            size="sm"
            className="h-10"
          >
            Limpar
          </Button>
        </div>
      </div>

      {/* Novos filtros */}
      <div className="flex flex-wrap gap-4 p-4 bg-blue-50 rounded-lg">
        <div className="flex flex-col">
          <span className="text-sm font-medium mb-2">Tipo de Veículo</span>
          <Select
            value={additionalFilters.tipoVeiculo}
            onValueChange={(value) =>
              onAdditionalFiltersChange({
                ...additionalFilters,
                tipoVeiculo: value,
              })
            }
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Selecione o tipo" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="todos">Todos</SelectItem>
              <SelectItem value="Leve">Leve</SelectItem>
              <SelectItem value="Médio">Médio</SelectItem>
              <SelectItem value="Pesado">Pesado</SelectItem>
              <SelectItem value="Máquina">Máquina</SelectItem>
              <SelectItem value="Moto">Moto</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex flex-col">
          <span className="text-sm font-medium mb-2">Tipo de Frota</span>
          <Select
            value={additionalFilters.tipoFrota}
            onValueChange={(value) =>
              onAdditionalFiltersChange({
                ...additionalFilters,
                tipoFrota: value,
              })
            }
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Selecione a frota" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="todos">Todos</SelectItem>
              <SelectItem value="Própria">Própria</SelectItem>
              <SelectItem value="Terceirizada">Terceirizada</SelectItem>
              <SelectItem value="Locada">Locada</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex flex-col">
          <span className="text-sm font-medium mb-2">Porte do Veículo</span>
          <Select
            value={additionalFilters.porteVeiculo}
            onValueChange={(value) =>
              onAdditionalFiltersChange({
                ...additionalFilters,
                porteVeiculo: value,
              })
            }
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Selecione o porte" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="todos">Todos</SelectItem>
              <SelectItem value="Pequeno">Pequeno</SelectItem>
              <SelectItem value="Médio">Médio</SelectItem>
              <SelectItem value="Grande">Grande</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}

function HistoricoManutencaoTable({
  dateFilter,
  additionalFilters,
}: HistoricoManutencaoTableProps) {
  const [sessionData, setSessionData] = useState<Session>();
  const [detailsData, setDetailsData] = useState<Record<string, any[]>>({});
  const [loadingDetails, setLoadingDetails] = useState(true);
  const { loading } = useHistoricoManutencao();
  const { ordensDeServico } = useOS();
  const { credenciados } = useCredenciado();
  const { centrosDeCusto } = useCentroDeCusto();
  useEffect(() => {
    async function fetchSessionData() {
      const sessionData = await getServerSession();
      setSessionData(sessionData);
    }
    fetchSessionData();
  }, []);

  // Novo useEffect para processar os detalhes de forma assíncrona
  useEffect(() => {
    async function processDetails() {
      if (!sessionData || ordensDeServico.length === 0) return;

      setLoadingDetails(true);
      const details: Record<string, any[]> = {};

      // Aplicar filtros de permissão
      let osData: OS[] = [];
      if (
        !sessionData.roles.includes("ADMIN") &&
        sessionData.centro_de_custoId
      ) {
        if (sessionData.unidade_filha_id) {
          osData = ordensDeServico.filter(
            (os) =>
              os.veiculo?.lotacao_veiculos?.centro_custoID ===
              sessionData.unidade_filha_id
          );
        } else if (sessionData.centro_de_custoId) {
          const unidadesFilhas = centrosDeCusto
            .flatMap((centro) => centro.centro_custos_filhos)
            .filter(
              (uniFilho) =>
                uniFilho.centro_custo_ascdID === sessionData.centro_de_custoId
            );
          osData = ordensDeServico.filter((os) =>
            unidadesFilhas.some(
              (centro) => centro.id === os.veiculo?.lotacao_veiculos?.centro_custoID
            )
          );
        } else {
          osData = ordensDeServico.filter(
            (os) =>
              os.veiculo?.lotacao_veiculos?.centro_custo_ascdID ===
              sessionData.centro_de_custoId
          );
        }
      } else {
        osData = ordensDeServico;
      }

      const osFinalizada = osData
        .filter((os) => os.status === "faturada")
        .filter((os) => {
          if (!dateFilter.isApplied) return true;

          const osDate = new Date(os.createdAt);
          const isAfterStartDate =
            !dateFilter.startDate || osDate >= dateFilter.startDate;
          const isBeforeEndDate =
            !dateFilter.endDate || osDate <= dateFilter.endDate;

          return isAfterStartDate && isBeforeEndDate;
        })
        .filter((os) => {
          // Aplicar filtros adicionais
          if (additionalFilters.tipoVeiculo && additionalFilters.tipoVeiculo !== "todos") {
            if (os.veiculo?.tiposVeiculos?.descricao !== additionalFilters.tipoVeiculo) {
              return false;
            }
          }

          if (additionalFilters.tipoFrota && additionalFilters.tipoFrota !== "todos") {
            if (os.veiculo?.tipoFrota !== additionalFilters.tipoFrota) {
              return false;
            }
          }

          if (additionalFilters.porteVeiculo && additionalFilters.porteVeiculo !== "todos") {
            if (os.veiculo?.porte !== additionalFilters.porteVeiculo) {
              return false;
            }
          }

          return true;
        });

      // Processar cada OS finalizada
      for (const os of osFinalizada) {
        const orcamentosFinalizados =
          os.orcamentos?.filter((orc) => orc.status === "faturada") || [];
        const detailItems: any[] = [];

        for (const orc of orcamentosFinalizados) {
          try {
            const responseCredenciado = await getCredenciadoById(orc.credenciadoId);
            const credenciadoInfo = responseCredenciado.data.credenciado.informacoes[0];
            const enderecoCredenciado = responseCredenciado.data.credenciado.endereco;

            // Processar peças
            if (orc.processedPecas && orc.processedPecas.length > 0) {
              orc.processedPecas.forEach((peca) => {
                // Calcular percentual de desconto
                const valorOriginal = peca.valorUnitario * peca.quantidade;
                const valorComDesconto = peca.valorNegociado * peca.quantidade;
                const percentualDesconto = valorOriginal > 0
                  ? ((valorOriginal - valorComDesconto) / valorOriginal) * 100
                  : 0;

                detailItems.push({
                  id: peca.id,
                  numeroOrcamento: orc.numeroOrcamento,
                  credenciado: credenciadoInfo.razao_social ?? "N/A",
                  localManutencao: enderecoCredenciado
                    ? `${enderecoCredenciado.cidade}-${enderecoCredenciado.estado}`
                    : "N/A",
                  codigo: peca.codigo || "",
                  descricao: peca.descricao,
                  marca: peca.marca,
                  vencimento: peca.garantia
                    ? new Date(peca.garantia).toLocaleDateString("pt-BR")
                    : "-",
                  quantidade: peca.quantidade,
                  valorItem: peca.valorUnitario / 100,
                  valorDesconto: peca.valorDesconto / 100,
                  valorNegociado: peca.valorNegociado / 100,
                  valorTotal: (peca.valorNegociado * peca.quantidade) / 100,
                  percentualDesconto: percentualDesconto.toFixed(2),
                  tipoVeiculo: os.veiculo?.tiposVeiculos?.descricao || "N/A",
                  tipoFrota: os.veiculo?.tipoFrota || "N/A",
                  porteVeiculo: os.veiculo?.porte || "N/A",
                  gestorAprovador: os.gestorAprovador,
                  condutor: os.condutor?.nome,
                });
              });
            }

            // Processar serviços
            if (orc.processedServicos && orc.processedServicos.length > 0) {
              orc.processedServicos.forEach((servico) => {
                detailItems.push({
                  id: servico.id,
                  numeroOrcamento: orc.numeroOrcamento,
                  credenciado: credenciadoInfo.razao_social ?? "N/A",
                  localManutencao: enderecoCredenciado
                    ? `${enderecoCredenciado.cidade}-${enderecoCredenciado.estado}`
                    : "N/A",
                  codigo: servico.tipoServico || "",
                  descricao: servico.descricao,
                  marca: "Serviço",
                  vencimento: "-",
                  quantidade: 1,
                  valorItem: servico.valor / 100,
                  valorDesconto: 0,
                  valorNegociado: servico.valor / 100,
                  valorTotal: servico.valor / 100,
                  percentualDesconto: "0.00",
                  tipoVeiculo: os.veiculo?.tiposVeiculos?.descricao || "N/A",
                  tipoFrota: os.veiculo?.tipoFrota || "N/A",
                  porteVeiculo: os.veiculo?.porte || "N/A",
                  gestorAprovador: os.gestorAprovador,
                  condutor: os.condutor?.nome,
                });
              });
            }
          } catch (error) {
            console.error("Erro ao buscar credenciado:", error);
          }
        }

        details[os.id] = detailItems;
      }

      setDetailsData(details);
      setLoadingDetails(false);
    }

    processDetails();
  }, [sessionData, ordensDeServico, dateFilter, additionalFilters, centrosDeCusto]);
  if (loading || loadingDetails) {
    return <div className="p-4">Carregando histórico de manutenções...</div>;
  }

  if (!sessionData) {
    return <div className="p-4">Carregando dados da sessão...</div>;
  }

  // Aplicar filtros de permissão para obter osData
  let osData: OS[] = [];
  if (
    !sessionData.roles.includes("ADMIN") &&
    sessionData.centro_de_custoId
  ) {
    if (sessionData.unidade_filha_id) {
      osData = ordensDeServico.filter(
        (os) =>
          os.veiculo?.lotacao_veiculos?.centro_custoID ===
          sessionData.unidade_filha_id
      );
    } else if (sessionData.centro_de_custoId) {
      const unidadesFilhas = centrosDeCusto
        .flatMap((centro) => centro.centro_custos_filhos)
        .filter(
          (uniFilho) =>
            uniFilho.centro_custo_ascdID === sessionData.centro_de_custoId
        );
      osData = ordensDeServico.filter((os) =>
        unidadesFilhas.some(
          (centro) => centro.id === os.veiculo?.lotacao_veiculos?.centro_custoID
        )
      );
    } else {
      osData = ordensDeServico.filter(
        (os) =>
          os.veiculo?.lotacao_veiculos?.centro_custo_ascdID ===
          sessionData.centro_de_custoId
      );
    }
  } else {
    osData = ordensDeServico;
  }

  const osFinalizada = osData
    .filter((os) => os.status === "faturada")
    .filter((os) => {
      if (!dateFilter.isApplied) return true;

      const osDate = new Date(os.createdAt);
      const isAfterStartDate =
        !dateFilter.startDate || osDate >= dateFilter.startDate;
      const isBeforeEndDate =
        !dateFilter.endDate || osDate <= dateFilter.endDate;

      return isAfterStartDate && isBeforeEndDate;
    })
    .filter((os) => {
      // Aplicar filtros adicionais
      if (additionalFilters.tipoVeiculo && additionalFilters.tipoVeiculo !== "todos") {
        if (os.veiculo?.tiposVeiculos?.descricao !== additionalFilters.tipoVeiculo) {
          return false;
        }
      }

      if (additionalFilters.tipoFrota && additionalFilters.tipoFrota !== "todos") {
        if (os.veiculo?.tipoFrota !== additionalFilters.tipoFrota) {
          return false;
        }
      }

      if (additionalFilters.porteVeiculo && additionalFilters.porteVeiculo !== "todos") {
        if (os.veiculo?.porte !== additionalFilters.porteVeiculo) {
          return false;
        }
      }

      return true;
    });

  // Mapeamento compatível com a interface VeiculoTableRow que será usada pelo ExpandableTable
  const vehicles = osFinalizada.map((os) => {
    const dataSaida = os.veiculo?.checklists.find(
      ({ tipo_checklist }) => tipo_checklist === "Saída"
    )?.entrega;

    return {
      id: os.id,
      osNumber: os.osNumber.toString(),
      centro_custo:
        os.veiculo?.lotacao_veiculos?.centro_custo?.descricao || "N/A",
      placa: os.veiculo?.placa || "N/A",
      marca: os.veiculo?.marca?.descricao || "N/A",
      tipoVeiculo: os.veiculo?.tiposVeiculos?.descricao || "N/A",
      tipoFrota: os.veiculo?.tipoFrota || "N/A",
      porteVeiculo: os.veiculo?.porte || "N/A",
      dataOS: os.createdAt
        ? new Date(os.createdAt).toLocaleDateString("pt-BR")
        : "N/A",
      dataSaida,
      tipoManutencao: os.tipo_manutencao,
    };
  });

  const tableData = {
    vehicles,
    details: detailsData,
  };

  return <ExpandableTable data={tableData} />;
}

function BarChartComponent({ dateFilter }: BarChartComponentProps) {
  const { ordensDeServico } = useOS();

  // Filtrar apenas ordens finalizadas com o filtro de data
  const osFinalizada = ordensDeServico
    .filter((os) => os.status === "faturada")
    .filter((os) => {
      if (!dateFilter.isApplied) return true;

      const osDate = new Date(os.createdAt);

      // Check date is within range (if filter is applied)
      const isAfterStartDate =
        !dateFilter.startDate || osDate >= dateFilter.startDate;
      const isBeforeEndDate =
        !dateFilter.endDate || osDate <= dateFilter.endDate;

      return isAfterStartDate && isBeforeEndDate;
    });

  // Preparar dados para o gráfico: Valor total gasto por marca de veículo
  const marcasData: Record<string, number> = {};

  osFinalizada.forEach((os) => {
    const marca = os.veiculo?.marca?.descricao || "Não especificado";

    // Calcular valor total de todos os orçamentos finalizados desta OS
    const valorTotal =
      os.orcamentos?.reduce((total, orc) => {
        // Só considerar orçamentos finalizados
        if (orc.status === "faturada") {
          return total + (orc.valorTotal / 100 || 0);
        }
        return total;
      }, 0) || 0;

    // Adicionar ao total da marca
    if (marcasData[marca]) {
      marcasData[marca] += valorTotal;
    } else {
      marcasData[marca] = valorTotal;
    }
  });

  // Preparar arrays para o Chart.js
  const marcas = Object.keys(marcasData);
  const valores = Object.values(marcasData);

  const chartData = {
    labels: marcas,
    datasets: [
      {
        label: "Valor Total Gasto (R$)",
        data: valores,
        backgroundColor: [
          "rgba(255, 99, 132, 0.6)",
          "rgba(54, 162, 235, 0.6)",
          "rgba(255, 206, 86, 0.6)",
          "rgba(75, 192, 192, 0.6)",
          "rgba(153, 102, 255, 0.6)",
        ],
        borderColor: [
          "rgba(255, 99, 132, 1)",
          "rgba(54, 162, 235, 1)",
          "rgba(255, 206, 86, 1)",
          "rgba(75, 192, 192, 1)",
          "rgba(153, 102, 255, 1)",
        ],
        borderWidth: 1,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
      },
      title: {
        display: true,
        text: "Valor Total Gasto por Marca de Veículo",
        font: {
          size: 16,
        },
      },
      tooltip: {
        callbacks: {
          label: function (context: any) {
            let label = context.dataset.label || "";
            if (label) {
              label += ": ";
            }
            if (context.parsed.y !== null) {
              label += new Intl.NumberFormat("pt-BR", {
                style: "currency",
                currency: "BRL",
              }).format(context.parsed.y);
            }
            return label;
          },
        },
      },
    },
  };

  return (
    <div id="chartDivMan" style={{ height: "300px", width: "100%" }}>
      <Bar data={chartData} options={options} />
    </div>
  );
}

export default function Page() {
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [dateFilter, setDateFilter] = useState<DateFilterState>({
    startDate: null,
    endDate: null,
    isApplied: false,
  });

  // Novos filtros
  const [additionalFilters, setAdditionalFilters] = useState<AdditionalFilters>({
    tipoVeiculo: "todos",
    tipoFrota: "todos",
    porteVeiculo: "todos",
  });

  const handleApplyFilter = () => {
    // If end date is provided but start date is not, set start date to distant past
    let adjustedStartDate = startDate;
    let adjustedEndDate = endDate;

    // Add one day to end date to include the entire day in filter
    if (endDate) {
      const nextDay = new Date(endDate);
      nextDay.setDate(nextDay.getDate() + 1);
      nextDay.setMilliseconds(-1); // Set to 23:59:59.999 of end date
      adjustedEndDate = nextDay;
    }

    setDateFilter({
      startDate: adjustedStartDate,
      endDate: adjustedEndDate,
      isApplied: true,
    });
  };

  const handleClearFilter = () => {
    setStartDate(null);
    setEndDate(null);
    setDateFilter({
      startDate: null,
      endDate: null,
      isApplied: false,
    });
  };

  return (
    <HistoricoManutencaoProvider>
      <div className="w-full flex flex-col">
        <h1 className="scroll-m-20 text-3xl font-extrabold tracking-tight p-4">
          Histórico de Manutenções
        </h1>

        {/* Filtros */}
        <div className="px-6 pt-4">
          <DateRangeFilter
            startDate={startDate}
            endDate={endDate}
            onStartDateChange={setStartDate}
            onEndDateChange={setEndDate}
            onFilterApply={handleApplyFilter}
            onFilterClear={handleClearFilter}
            additionalFilters={additionalFilters}
            onAdditionalFiltersChange={setAdditionalFilters}
          />
        </div>

        {/* Gráfico */}
        <div className="p-4">
          <BarChartComponent dateFilter={dateFilter} />
        </div>

        {/* Tabela */}
        <div className="w-full p-6">
          <HistoricoManutencaoTable
            dateFilter={dateFilter}
            additionalFilters={additionalFilters}
          />
        </div>
      </div>
    </HistoricoManutencaoProvider>
  );
}
