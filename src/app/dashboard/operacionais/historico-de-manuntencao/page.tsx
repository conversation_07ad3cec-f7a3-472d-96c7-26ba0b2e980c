"use client";

import React, { useEffect, useState } from "react";
import { ExpandableTable } from "@/components/tables/expandeble-table";
import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "@/components/tables/column-header";
import { Checkbox } from "@/components/ui/checkbox";
import {
  HistoricoManutencaoProvider,
  useHistoricoManutencao,
  HistoricoManutencao,
} from "@/context/historico-manutencao-context";
import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { useOS } from "@/context/os-context";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { useSession } from "@/components/session/use-session";
import { Session } from "@/lib/auth/types";
import { getServerSession } from "@/lib/auth/server-session";
import { useCredenciado } from "@/context/credenciado-context";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { getCredenciadoById } from "@/serverActions/credenciadoAction";

// Define interfaces
interface DatePickerWithButtonProps {
  date: Date | null;
  onDateChange: (date: Date | null) => void;
  label: string;
}

interface DateRangeFilterProps {
  startDate: Date | null;
  endDate: Date | null;
  onStartDateChange: (date: Date | null) => void;
  onEndDateChange: (date: Date | null) => void;
  onFilterApply: () => void;
  onFilterClear: () => void;
}

interface DateFilterState {
  startDate: Date | null;
  endDate: Date | null;
  isApplied: boolean;
}

interface HistoricoManutencaoTableProps {
  dateFilter: DateFilterState;
}

interface BarChartComponentProps {
  dateFilter: DateFilterState;
}

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

function DatePickerWithButton({
  date,
  onDateChange,
  label,
}: DatePickerWithButtonProps) {
  const [open, setOpen] = useState<boolean>(false);

  return (
    <div className="flex flex-col">
      <span className="text-sm font-medium">{label}</span>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-[200px] justify-start text-left font-normal",
              !date && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date
              ? format(date, "dd/MM/yyyy", { locale: ptBR })
              : "Selecione uma data"}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0">
          <Calendar
            mode="single"
            selected={date || undefined}
            onSelect={(date) => {
              onDateChange(date ?? null);
              setOpen(false);
            }}
            initialFocus
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}

function DateRangeFilter({
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  onFilterApply,
  onFilterClear,
}: DateRangeFilterProps) {
  return (
    <div className="flex flex-wrap gap-4 p-4 bg-muted/20 rounded-lg mb-4">
      <DatePickerWithButton
        date={startDate}
        onDateChange={onStartDateChange}
        label="Data Inicial"
      />
      <DatePickerWithButton
        date={endDate}
        onDateChange={onEndDateChange}
        label="Data Final"
      />
      <div className="flex items-end gap-2">
        <Button
          onClick={onFilterApply}
          variant="default"
          size="sm"
          className="h-10"
        >
          Aplicar Filtro
        </Button>
        <Button
          onClick={onFilterClear}
          variant="outline"
          size="sm"
          className="h-10"
        >
          Limpar
        </Button>
      </div>
    </div>
  );
}

function HistoricoManutencaoTable({
  dateFilter,
}: HistoricoManutencaoTableProps) {
  const [sessionData, setSessionData] = useState<Session>();
  const { loading } = useHistoricoManutencao();
  const { ordensDeServico } = useOS();
  const { credenciados } = useCredenciado();
  const { centrosDeCusto } = useCentroDeCusto();
  useEffect(() => {
    async function fetchSessionData() {
      const sessionData = await getServerSession();
      setSessionData(sessionData);
    }
    fetchSessionData();
  }, []);
  let osData: OS[] = [];
  if (
    sessionData &&
    !sessionData.roles.includes("ADMIN") &&
    sessionData.centro_de_custoId
  ) {
    if (sessionData.unidade_filha_id) {
      osData = ordensDeServico.filter(
        (os) =>
          os.veiculo?.lotacao_veiculos?.centro_custoID ===
          sessionData.unidade_filha_id
      );
    } else if (sessionData.centro_de_custoId) {
      const unidadesFilhas = centrosDeCusto
        .flatMap((centro) => centro.centro_custos_filhos)
        .filter(
          (uniFilho) =>
            uniFilho.centro_custo_ascdID === sessionData.centro_de_custoId
        );
      osData = ordensDeServico.filter((os) =>
        unidadesFilhas.some(
          (centro) => centro.id === os.veiculo?.lotacao_veiculos?.centro_custoID
        )
      );
    } else {
      osData = ordensDeServico.filter(
        (os) =>
          os.veiculo?.lotacao_veiculos?.centro_custo_ascdID ===
          sessionData.centro_de_custoId
      );
    }
  } else {
    osData = ordensDeServico;
  }

  const osFinalizada = osData
    .filter((os) => os.status === "faturada")
    .filter((os) => {
      if (!dateFilter.isApplied) return true;

      const osDate = new Date(os.createdAt);

      // Check date is within range (if filter is applied)
      const isAfterStartDate =
        !dateFilter.startDate || osDate >= dateFilter.startDate;
      const isBeforeEndDate =
        !dateFilter.endDate || osDate <= dateFilter.endDate;

      return isAfterStartDate && isBeforeEndDate;
    });

  if (loading) {
    return <div className="p-4">Carregando histórico de manutenções...</div>;
  }

  // Mapeamento compatível com a interface VeiculoTableRow que será usada pelo ExpandableTable
  const vehicles = osFinalizada.map((os) => {
    const dataSaida = os.veiculo?.checklists.find(
      ({ tipo_checklist }) => tipo_checklist === "Saída"
    )?.entrega;

    const dataAprovacao = os.veiculo?.checklists.find(
      ({ tipo_checklist }) => tipo_checklist === "Entrada"
    )?.data_checklist;

    return {
      id: os.id,
      osNumber: os.osNumber.toString(),
      centro_custo:
        os.veiculo?.lotacao_veiculos?.centro_custo?.descricao || "N/A",
      placa: os.veiculo?.placa || "N/A",
      marca: os.veiculo?.marca?.descricao || "N/A",
      modelo: os.veiculo?.modelo?.descricao || "N/A",
      dataOS: os.createdAt
        ? new Date(os.createdAt).toLocaleDateString("pt-BR")
        : "N/A",
      dataSaida,
      tipoManutencao: os.tipo_manutencao,
      dataAprovacao,
    };
  });

  const details: Record<string, any[]> = {};
  osFinalizada.forEach((os) => {
    const orcamentosFinalizados =
      os.orcamentos?.filter((orc) => orc.status === "faturada") || [];
    const detailItems: any[] = [];

    orcamentosFinalizados.forEach(async (orc) => {
      const credenciadoData = credenciados.find(
        (cred) => cred.id === orc.credenciadoId
      );

      const responseCredenciado = await getCredenciadoById(orc.credenciadoId);
      const credenciadoInfo =
        responseCredenciado.data.credenciado.informacoes[0];

      const enderecoCredenciado = responseCredenciado.data.credenciado.endereco;

      // Processar peças
      if (orc.processedPecas && orc.processedPecas.length > 0) {
        orc.processedPecas.forEach((peca) => {
          detailItems.push({
            id: peca.id,
            numeroOrcamento: orc.numeroOrcamento, // Adicionado
            credenciado: credenciadoInfo.razao_social ?? "N/A",
            localManutencao: enderecoCredenciado
              ? `${enderecoCredenciado.cidade}-${enderecoCredenciado.estado}`
              : "N/A",
            codigo: peca.codigo || "",
            descricao: peca.descricao,
            marca: peca.marca,
            vencimento: peca.garantia
              ? new Date(peca.garantia).toLocaleDateString("pt-BR")
              : "-",
            quantidade: peca.quantidade,
            valorItem: peca.valorUnitario / 100,
            valorDesconto: peca.valorDesconto / 100,
            valorNegociado: peca.valorNegociado / 100,
            valorTotal: (peca.valorNegociado * peca.quantidade) / 100,
            gestorAprovador: os.gestorAprovador,
            condutor: os.condutor?.nome,
          });
        });
      }
      // Processar serviços
      if (orc.processedServicos && orc.processedServicos.length > 0) {
        orc.processedServicos.forEach((servico) => {
          detailItems.push({
            id: servico.id,
            numeroOrcamento: orc.numeroOrcamento, // Adicionado
            credenciado: credenciadoInfo.razao_social ?? "N/A",
            localManutencao: enderecoCredenciado
              ? `${enderecoCredenciado.cidade}-${enderecoCredenciado.estado}`
              : "N/A",
            codigo: servico.tipoServico || "",
            descricao: servico.descricao,
            marca: "Serviço",
            vencimento: "-",
            quantidade: 1,
            valorItem: servico.valor / 100,
            valorDesconto: 0,
            valorNegociado: servico.valor / 100,
            valorTotal: servico.valor / 100,
            gestorAprovador: os.gestorAprovador,
            condutor: os.condutor?.nome,
          });
        });
      }
    });

    details[os.id] = detailItems;
  });
  const tableData = {
    vehicles,
    details,
  };

  return <ExpandableTable data={tableData} />;
}

function BarChartComponent({ dateFilter }: BarChartComponentProps) {
  const { ordensDeServico } = useOS();

  // Filtrar apenas ordens finalizadas com o filtro de data
  const osFinalizada = ordensDeServico
    .filter((os) => os.status === "faturada")
    .filter((os) => {
      if (!dateFilter.isApplied) return true;

      const osDate = new Date(os.createdAt);

      // Check date is within range (if filter is applied)
      const isAfterStartDate =
        !dateFilter.startDate || osDate >= dateFilter.startDate;
      const isBeforeEndDate =
        !dateFilter.endDate || osDate <= dateFilter.endDate;

      return isAfterStartDate && isBeforeEndDate;
    });

  // Preparar dados para o gráfico: Valor total gasto por marca de veículo
  const marcasData: Record<string, number> = {};

  osFinalizada.forEach((os) => {
    const marca = os.veiculo?.marca?.descricao || "Não especificado";

    // Calcular valor total de todos os orçamentos finalizados desta OS
    const valorTotal =
      os.orcamentos?.reduce((total, orc) => {
        // Só considerar orçamentos finalizados
        if (orc.status === "faturada") {
          return total + (orc.valorTotal / 100 || 0);
        }
        return total;
      }, 0) || 0;

    // Adicionar ao total da marca
    if (marcasData[marca]) {
      marcasData[marca] += valorTotal;
    } else {
      marcasData[marca] = valorTotal;
    }
  });

  // Preparar arrays para o Chart.js
  const marcas = Object.keys(marcasData);
  const valores = Object.values(marcasData);

  const chartData = {
    labels: marcas,
    datasets: [
      {
        label: "Valor Total Gasto (R$)",
        data: valores,
        backgroundColor: [
          "rgba(255, 99, 132, 0.6)",
          "rgba(54, 162, 235, 0.6)",
          "rgba(255, 206, 86, 0.6)",
          "rgba(75, 192, 192, 0.6)",
          "rgba(153, 102, 255, 0.6)",
        ],
        borderColor: [
          "rgba(255, 99, 132, 1)",
          "rgba(54, 162, 235, 1)",
          "rgba(255, 206, 86, 1)",
          "rgba(75, 192, 192, 1)",
          "rgba(153, 102, 255, 1)",
        ],
        borderWidth: 1,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
      },
      title: {
        display: true,
        text: "Valor Total Gasto por Marca de Veículo",
        font: {
          size: 16,
        },
      },
      tooltip: {
        callbacks: {
          label: function (context: any) {
            let label = context.dataset.label || "";
            if (label) {
              label += ": ";
            }
            if (context.parsed.y !== null) {
              label += new Intl.NumberFormat("pt-BR", {
                style: "currency",
                currency: "BRL",
              }).format(context.parsed.y);
            }
            return label;
          },
        },
      },
    },
  };

  return (
    <div id="chartDivMan" style={{ height: "300px", width: "100%" }}>
      <Bar data={chartData} options={options} />
    </div>
  );
}

export default function Page() {
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [dateFilter, setDateFilter] = useState<DateFilterState>({
    startDate: null,
    endDate: null,
    isApplied: false,
  });

  const handleApplyFilter = () => {
    // If end date is provided but start date is not, set start date to distant past
    let adjustedStartDate = startDate;
    let adjustedEndDate = endDate;

    // Add one day to end date to include the entire day in filter
    if (endDate) {
      const nextDay = new Date(endDate);
      nextDay.setDate(nextDay.getDate() + 1);
      nextDay.setMilliseconds(-1); // Set to 23:59:59.999 of end date
      adjustedEndDate = nextDay;
    }

    setDateFilter({
      startDate: adjustedStartDate,
      endDate: adjustedEndDate,
      isApplied: true,
    });
  };

  const handleClearFilter = () => {
    setStartDate(null);
    setEndDate(null);
    setDateFilter({
      startDate: null,
      endDate: null,
      isApplied: false,
    });
  };

  return (
    <HistoricoManutencaoProvider>
      <div className="w-full flex flex-col">
        <h1 className="scroll-m-20 text-3xl font-extrabold tracking-tight p-4">
          Histórico de Manutenções
        </h1>

        {/* Filtro de data compartilhado */}
        <div className="px-6 pt-4">
          <DateRangeFilter
            startDate={startDate}
            endDate={endDate}
            onStartDateChange={setStartDate}
            onEndDateChange={setEndDate}
            onFilterApply={handleApplyFilter}
            onFilterClear={handleClearFilter}
          />
        </div>

        {/* Gráfico */}
        <div className="p-4">
          <BarChartComponent dateFilter={dateFilter} />
        </div>

        {/* Tabela */}
        <div className="w-full p-6">
          <HistoricoManutencaoTable dateFilter={dateFilter} />
        </div>
      </div>
    </HistoricoManutencaoProvider>
  );
}
