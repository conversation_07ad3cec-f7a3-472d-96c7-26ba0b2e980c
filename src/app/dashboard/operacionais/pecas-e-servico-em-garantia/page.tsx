"use client";

import React, { useEffect, useState } from "react";
import { ExpandableTable } from "@/components/tables/expandeble-table";
import { useOS } from "@/context/os-context";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Button } from "@/components/ui/button";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { useSession } from "@/components/session/use-session";
import { Session } from "@/lib/auth/types";
import { getServerSession } from "@/lib/auth/server-session";
import { useCredenciado } from "@/context/credenciado-context";
import { Badge } from "@/components/ui/badge";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";

// Define interfaces
interface DatePickerWithButtonProps {
  date: Date | null;
  onDateChange: (date: Date | null) => void;
  label: string;
}

interface DateRangeFilterProps {
  startDate: Date | null;
  endDate: Date | null;
  onStartDateChange: (date: Date | null) => void;
  onEndDateChange: (date: Date | null) => void;
  onFilterApply: () => void;
  onFilterClear: () => void;
}

interface DateFilterState {
  startDate: Date | null;
  endDate: Date | null;
  isApplied: boolean;
}

interface PartsWarrantyTableProps {
  dateFilter: DateFilterState;
}

function DatePickerWithButton({ date, onDateChange, label }: DatePickerWithButtonProps) {
  const [open, setOpen] = useState<boolean>(false);

  return (
    <div className="flex flex-col">
      <span className="text-sm font-medium">{label}</span>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-[200px] justify-start text-left font-normal",
              !date && "text-muted-foreground"
            )}>
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date ? format(date, "dd/MM/yyyy", { locale: ptBR }) : "Selecione uma data"}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0">
          <Calendar
            mode="single"
            selected={date || undefined}
            onSelect={(date) => {
              onDateChange(date ?? null);
              setOpen(false);
            }}
            initialFocus
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}

function DateRangeFilter({
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  onFilterApply,
  onFilterClear,
}: DateRangeFilterProps) {
  return (
    <div className="flex flex-wrap gap-4 p-4 bg-muted/20 rounded-lg mb-4">
      <DatePickerWithButton
        date={startDate}
        onDateChange={onStartDateChange}
        label="Data Inicial"
      />
      <DatePickerWithButton date={endDate} onDateChange={onEndDateChange} label="Data Final" />
      <div className="flex items-end gap-2">
        <Button onClick={onFilterApply} variant="default" size="sm" className="h-10">
          Aplicar Filtro
        </Button>
        <Button onClick={onFilterClear} variant="outline" size="sm" className="h-10">
          Limpar
        </Button>
      </div>
    </div>
  );
}

// Custom component for warranty status
function WarrantyStatus({ status, diasRestantes }: { status: string; diasRestantes: number }) {
  return (
    <div className="inline-block ml-2">
      {status === "Válida" ? (
        <Badge className="bg-green-500 hover:bg-green-600 text-white">
          Válida ({diasRestantes} dias)
        </Badge>
      ) : (
        <Badge className="bg-red-500 hover:bg-red-600 text-white">
          Vencida
        </Badge>
      )}
    </div>
  );
}

function PartsWarrantyTable({ dateFilter }: PartsWarrantyTableProps) {
  const [sessionData, setSessionData] = useState<Session>();
  const { ordensDeServico, loading: osLoading } = useOS();
  const { credenciados, loading: credenciadosLoading } = useCredenciado();
  const { centrosDeCusto } = useCentroDeCusto();
  useEffect(() => {
    async function fetchSessionData() {
      const sessionData = await getServerSession();
      setSessionData(sessionData);
    }
    fetchSessionData();
  }, []);
  
  if (osLoading || credenciadosLoading) {
    return <div className="p-4">Carregando dados de garantias...</div>;
  }
  
  let osData: OS[] = [];
  if (sessionData && !sessionData.roles.includes("ADMIN") && sessionData.centro_de_custoId) {
    if (sessionData.unidade_filha_id) {
      osData = ordensDeServico.filter(
        (os) => os.veiculo?.lotacao_veiculos?.centro_custoID === sessionData.unidade_filha_id
      );
    } else if (sessionData.centro_de_custoId) {
      const unidadesFilhas = centrosDeCusto
        .flatMap((centro) => centro.centro_custos_filhos)
        .filter((uniFilho) => uniFilho.centro_custo_ascdID === sessionData.centro_de_custoId);
      osData = ordensDeServico.filter((os) =>
        unidadesFilhas.some((centro) => centro.id === os.veiculo?.lotacao_veiculos?.centro_custoID)
      );
    } else {
      osData = ordensDeServico.filter(
        (os) => os.veiculo?.lotacao_veiculos?.centro_custo_ascdID === sessionData.centro_de_custoId
      );
    }
  } else {
    osData = ordensDeServico;
  }

  const osFinalizada = osData
    .filter((os) => os.status === "faturada")
    .filter((os) => {
      if (!dateFilter.isApplied) return true;

      const osDate = new Date(os.createdAt);

      // Check date is within range (if filter is applied)
      const isAfterStartDate = !dateFilter.startDate || osDate >= dateFilter.startDate;
      const isBeforeEndDate = !dateFilter.endDate || osDate <= dateFilter.endDate;

      return isAfterStartDate && isBeforeEndDate;
    });

  const vehicles = osFinalizada.map((os) => {
    // Count parts with valid and expired warranties
    let validCount = 0;
    let expiredCount = 0;
    
    // Count parts with warranties
    os.orcamentos?.forEach(orc => {
      if (orc.status === "faturada" && orc.processedPecas) {
        orc.processedPecas.forEach(peca => {
          if (peca.garantia) {
            const garantiaDate = new Date(peca.garantia);
            const today = new Date();
            garantiaDate > today ? validCount++ : expiredCount++;
          }
        });
      }
    });
    
    // Add warranty counts to vehicle data
    return {
      id: os.id,
      osNumber: os.osNumber.toString(),
      placa: os.veiculo?.placa || "N/A",
      marca: os.veiculo?.marca?.descricao || "N/A",
      modelo: os.veiculo?.modelo?.descricao || "N/A",
      centro_custo: os.veiculo?.lotacao_veiculos?.centro_custo?.descricao || "N/A",
      dataOS: os.createdAt ? new Date(os.createdAt).toLocaleDateString("pt-BR") : "N/A",
      // Add warranty summary to the vehicle row display
      garantias: `Válidas: ${validCount} | Vencidas: ${expiredCount}`,
    };
  });

  const details: Record<string, any[]> = {};
  
  osFinalizada.forEach((os) => {
    const orcamentosFinalizados = os.orcamentos?.filter((orc) => orc.status === "faturada") || [];
    const partsWithWarranty: any[] = [];

    orcamentosFinalizados.forEach((orc) => {
      const credenciadoData = credenciados.find((cred) => cred.id === orc.credenciadoId);
      
      if (orc.processedPecas && orc.processedPecas.length > 0) {
        orc.processedPecas.forEach((peca) => {
          if (peca.garantia) {
            const garantiaDate = new Date(peca.garantia);
            const today = new Date();
            const statusGarantia = garantiaDate > today ? "Válida" : "Vencida";
            const diasRestantes = statusGarantia === "Válida" 
              ? Math.ceil((garantiaDate.getTime() - today.getTime()) / (1000 * 3600 * 24))
              : 0;

            // Create a formatted string with warranty status
            const warrantyStatusDisplay = statusGarantia === "Válida" 
              ? `✅ Válida (${diasRestantes} dias restantes)` 
              : "❌ Vencida";
            
            partsWithWarranty.push({
              id: peca.id,
              numeroOrcamento: orc.numeroOrcamento,
              credenciado: credenciadoData ? credenciadoData.informacoes[0].razao_social : "N/A",
              localManutencao: credenciadoData ? `${credenciadoData.endereco.cidade}-${credenciadoData.endereco.estado}` : "N/A",
              codigo: peca.codigo || "",
              // Add warranty status to description
              descricao: `${peca.descricao}`,
              marca: peca.marca,
              // Format as date + status indicator
              vencimento: `${new Date(peca.garantia).toLocaleDateString("pt-BR")} ${warrantyStatusDisplay}`,
              quantidade: 1,
              valorItem: 0,
              valorDesconto: 0,
              valorNegociado: 0,
              valorTotal: 0,
              
              // Keep these for future reference if needed
              statusGarantia: statusGarantia,
              diasRestantes: diasRestantes
            });
          }
        });
      }
    });

    details[os.id] = partsWithWarranty;
  });
  
  const tableData = {
    vehicles,
    details,
  };

  // Count how many vehicles have parts with warranty
  const vehiclesWithWarranty = vehicles.length;
  
  return (
    <>
      <div className="mb-4">
        <div className="flex gap-4 mb-4">
          <div className="p-4 rounded-lg shadow border flex-1">
            <h3 className="text-lg font-medium">Veículos com peças em garantia</h3>
            <p className="text-3xl font-bold">{vehiclesWithWarranty}</p>
          </div>
        </div>
      </div>
      <ExpandableTable data={tableData} />
    </>
  );
}

export default function Page() {
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [dateFilter, setDateFilter] = useState<DateFilterState>({
    startDate: null,
    endDate: null,
    isApplied: false,
  });

  const handleApplyFilter = () => {
    let adjustedEndDate = endDate;

    // Add one day to end date to include the entire day in filter
    if (endDate) {
      const nextDay = new Date(endDate);
      nextDay.setDate(nextDay.getDate() + 1);
      nextDay.setMilliseconds(-1); // Set to 23:59:59.999 of end date
      adjustedEndDate = nextDay;
    }

    setDateFilter({
      startDate: startDate,
      endDate: adjustedEndDate,
      isApplied: true,
    });
  };

  const handleClearFilter = () => {
    setStartDate(null);
    setEndDate(null);
    setDateFilter({
      startDate: null,
      endDate: null,
      isApplied: false,
    });
  };

  return (
    <div className="w-full flex flex-col">
      <h1 className="scroll-m-20 text-3xl font-extrabold tracking-tight p-4">
        Controle de Garantias de Peças
      </h1>

      {/* Filtro de data */}
      <div className="px-6 pt-4">
        <DateRangeFilter
          startDate={startDate}
          endDate={endDate}
          onStartDateChange={setStartDate}
          onEndDateChange={setEndDate}
          onFilterApply={handleApplyFilter}
          onFilterClear={handleClearFilter}
        />
      </div>

      {/* Tabela de Garantias */}
      <div className="w-full p-6">
        <PartsWarrantyTable dateFilter={dateFilter} />
      </div>
    </div>
  );
}