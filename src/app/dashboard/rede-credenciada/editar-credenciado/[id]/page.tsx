"use client";
import { EditCredenciadoForm } from "@/components/forms/edit-accreditor-form";
import { getCredenciadoById } from "@/serverActions/credenciadoAction";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";

export default function EditarContratoPage() {
  const { id } = useParams();
  const [credenciado, setCredenciado] = useState<any>(null);
  const [loading, setLoading] = useState(true); // Add loading state
  const [error, setError] = useState<string | null>(null); // Add error state for better UX

  useEffect(() => {
    async function fetchCredenciado() {
      try {
        setLoading(true);
        const response = await getCredenciadoById(id as string);
        if (!response.success) {
          throw new Error(response.error || "Failed to fetch credenciado");
        }
        const data = response.data;
        setCredenciado(data.credenciado);
      } catch (err) {
        console.error("Error fetching credenciado:", err);
        setError(err instanceof Error ? err.message : "Failed to load data");
      } finally {
        setLoading(false);
      }
    }

    fetchCredenciado();
  }, [id]);

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Editar Credenciado</h1>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          {/* Replace with your spinner component or create a simple loading message */}
          <div className="flex flex-col items-center">
            <div className="h-8 w-8 border-4 border-t-blue-500 rounded-full animate-spin mb-4"></div>
            <p>Carregando dados do credenciado...</p>
          </div>
        </div>
      ) : error ? (
        <div className="bg-red-50 p-4 rounded-md border border-red-200 text-red-700">
          <h3 className="font-medium text-lg">Erro ao carregar dados</h3>
          <p>{error}</p>
        </div>
      ) : (
        <EditCredenciadoForm credenciadoData={credenciado} />
      )}
    </div>
  );
}
