"use client";
import { CredenciadoTable } from "@/components/sections/credenciados/credenciados";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { getServerSession } from "@/lib/auth/server-session";
import { Session } from "@/lib/auth/types";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import FilterCredenciado from "@/components/sections/credenciados/filtros-credenciado";

export default function Page() {
  const [sessionData, setSessionData] = useState<Session | null>();

  useEffect(() => {
    async function carregarEstados() {
      const session = await getServerSession();
      setSessionData(session);
    }
    carregarEstados();
  }, []);
  return (
    <div className="w-full flex flex-col p-6">
      <div className=" w-full flex justify-between">
        <h1 className="scroll-m-20 text-3xl font-extrabold tracking-tight p-4">
          Consultar Credenciado
        </h1>
        {(sessionData?.roles.includes("ADMIN") ||
          sessionData?.roles.includes("CREDENCIAMENTO")) && (
          <Link href={"/dashboard/rede-credenciada/novo-credenciado"}>
            <Button className="p-4 m-4">
              <Plus />
              Novo credenciado
            </Button>
          </Link>
        )}
      </div>
      <div className="w-full">
        <CredenciadoTable />
      </div>
    </div>
  );
}
