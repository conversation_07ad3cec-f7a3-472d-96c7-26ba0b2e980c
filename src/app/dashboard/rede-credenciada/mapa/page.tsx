"use client";
import React from "react";
import { useCredenciado } from "@/context/credenciado-context";
import { Mapa } from "@/components/mapa/mapa";

export default function Page() {
  const { credenciados } = useCredenciado();
  const informacoesCredenciados = credenciados.map(
    ({ endereco, informacoes, contato }: credenciado) => ({
      endereco: { ...endereco },
      informacoesAdicionais: { ...informacoes[0], ...contato },
    })
  );
  return (
    <div className="w-full flex flex-col">
      <h1 className="scroll-m-20 text-3xl font-extrabold tracking-tight p-4">
        Encontre um credenciado perto de você.
      </h1>
      <div className="w-full">
        <Mapa informacoesCredenciados={informacoesCredenciados} />
      </div>
    </div>
  );
}
