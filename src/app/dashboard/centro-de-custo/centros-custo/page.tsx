"use client";

import { CentroDeCustoTable } from "@/components/sections/centro de custo/centro-de-custo";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FileText } from "lucide-react";
import { useRouter } from "next/navigation";
import React from "react";

export default function Page() {
  const router = useRouter();

  return (
    <div className="w-full flex flex-col">
      <div className="flex items-center justify-between p-4">
        <h1 className="scroll-m-20 text-3xl font-extrabold tracking-tight">
          Centro de Custo
        </h1>
        <Button
          onClick={() => router.push("/dashboard/centro-de-custo/relatorio")}
          className="flex items-center gap-2"
          variant="outline"
        >
          <FileText className="h-4 w-4" />
          Relatório Detalhado
        </Button>
      </div>
      <div className="w-full">
        <CentroDeCustoTable />
      </div>
    </div>
  );
}
