import Breadcrumbs from "@/components/navigation/Breadcrumbs";
import MenuNav from "@/components/navigation/menu-nav";
import { SideBar } from "@/components/navigation/side-nav";
import SessionProvider from "@/components/session/session-provider";
import { Separator } from "@/components/ui/separator";

import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { getServerSession } from "@/lib/auth/server-session";
import { Providers } from "@/providers/context-providers";

export default async function Layout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const session = await getServerSession();

  return (
    <SessionProvider initialSession={session}>
      <Providers>
        <MenuNav />
        <SidebarProvider>
          <SideBar />
          <SidebarInset>
            <div className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
              <div className="flex items-center px-4 max-w-ful">
                <SidebarTrigger className="-ml-1" />
                <Separator orientation="vertical" className="mr-2 h-4" />
                <Breadcrumbs />
              </div>
            </div>
            <main>{children}</main>
          </SidebarInset>
        </SidebarProvider>
      </Providers>
    </SessionProvider>
  );
}
