"use client";

import { Head<PERSON> } from "@/components/sections/veiculos/detalhes/header";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { VisaoGeral } from "@/components/sections/veiculos/detalhes/visao-geral";
import { FichaTecnica } from "@/components/sections/veiculos/detalhes/ficha-tecnica";
import { Atividades } from "@/components/sections/veiculos/detalhes/atividades";
import { AcervoDigital } from "@/components/sections/veiculos/detalhes/acervo-digital";
import { PlanoRevisao } from "@/components/sections/veiculos/detalhes/plano-revisao";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { getRevisionPlan } from "@/serverActions/suivAction";
import { getVehicleByPlateWithCache } from "@/service/suivCache.service";

export default function VeiculoDetalhesPage() {
  const { id } = useParams();
  const veiculoId = id as string;

  const [veiculo, setVeiculo] = useState<any | null>(null);

  useEffect(() => {
    async function fetchData() {
      try {
        // Buscar os dados do veículo
        const response = await fetch(`/api/veiculos/${veiculoId}`);
        const data = await response.json();
        setVeiculo(data.veiculo);

        if (data.veiculo) {
          const cleanPlate = data.veiculo.placa.replace(/[^a-zA-Z0-9]/g, "");
          const plateResponse = await getVehicleByPlateWithCache(cleanPlate);
          const revisionPlan = await getRevisionPlan(
            plateResponse.suivDataCollection[0].versionId,
            plateResponse.yearModel
          );
          if (revisionPlan) {
            // Persistir cada item do plano de revisão na nossa API, evitando duplicados por km
            for (const plan of revisionPlan) {
              const alreadyExists = data.veiculo.manutencoes.some(
                (m: any) => m.km === plan.kilometers
              );
              if (alreadyExists) continue;

              try {
                await fetch("/api/manutencao", {
                  method: "POST",
                  headers: { "Content-Type": "application/json" },
                  body: JSON.stringify({
                    periodo: plan.months,
                    km: plan.kilometers,
                    itens: plan.inspections.map((i: any) => i.description).join(";"),
                    valor: 0,
                    veiculoId: data.veiculo.id,
                  }),
                });
              } catch (err) {
                console.error("Erro ao persistir revisão:", err);
              }
            }
          }
        }
      } catch (error) {
        console.error("Erro ao buscar detalhes do veículo ou plano de revisão:", error);
      }
    }
    fetchData();
  }, [veiculoId]);

  return (
    <div className="p-6">
      <Header veiculoId={veiculoId} />

      <Tabs defaultValue="visao-geral" className="space-y-4">
        <TabsList>
          <TabsTrigger value="visao-geral">Visão geral</TabsTrigger>
          {/* <TabsTrigger value="acervo-digital">Acervo digital</TabsTrigger>
          <TabsTrigger value="atividades">Atividades</TabsTrigger>
          <TabsTrigger value="ficha-tecnica">Ficha técnica</TabsTrigger> */}
          <TabsTrigger value="plano-revisao">Plano de revisão</TabsTrigger>
        </TabsList>

        <TabsContent value="visao-geral">
          <VisaoGeral veiculo={veiculo} />
        </TabsContent>

        {/* <TabsContent value="acervo-digital">
          <AcervoDigital veiculoId={veiculoId} />
        </TabsContent> */}

        {/* <TabsContent value="atividades">
          <Atividades veiculoId={veiculoId} />
        </TabsContent> */}

        {/* <TabsContent value="ficha-tecnica">
          <FichaTecnica veiculoId={veiculoId} />
        </TabsContent> */}

        <TabsContent value="plano-revisao">
          <PlanoRevisao veiculo={veiculo} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
