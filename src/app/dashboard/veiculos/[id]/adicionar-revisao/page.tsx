"use client";
import { AdicionarRevisaoForm } from "@/components/sections/veiculos/detalhes/adicionar-revisao-form"
import { Card } from "@/components/ui/card"
import { useParams } from "next/navigation"

interface PageProps {
  params: {
    id: string
  }
}

export default function AdicionarRevisaoPage() {
  const { id } = useParams();
  return (
    <div className="container mx-auto py-6">
      <Card className="p-6">
        <h1 className="text-2xl font-bold mb-6">Adicionar Nova Revisão</h1>
        <AdicionarRevisaoForm veiculoId={id as string} />
      </Card>
    </div>
  )
} 