"use client";
import { EditarRevisaoForm } from "@/components/sections/veiculos/detalhes/editar-revisao-form";
import { Card } from "@/components/ui/card"
import { useParams } from "next/navigation"

interface PageProps {
  params: {
    id: string
  }
}

export default function EditarRevisaoPage() {
  const { id } = useParams();
  return (
    <div className="container mx-auto py-6">
      <Card className="p-6">
        <h1 className="text-2xl font-bold mb-6">Editar Revisão</h1>
        <EditarRevisaoForm manutencaoId={id as string} />
      </Card>
    </div>
  );
} 