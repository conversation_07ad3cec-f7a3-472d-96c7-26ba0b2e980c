"use client";

import * as React from "react";
import { CustomReportBuilder } from "@/components/reports/CustomReportBuilder";
import { VehicleComprehensiveReport } from "@/components/reports/VehicleComprehensiveReport";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Car, FileText, BarChart3 } from "lucide-react";

export default function RelatoriosPage() {
  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">Relatórios</h1>

      <Tabs defaultValue="veiculos" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="veiculos" className="flex items-center gap-2">
            <Car className="h-4 w-4" />
            Relatório de Veículos
          </TabsTrigger>
          <TabsTrigger value="customizados" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Relatórios Customizados
          </TabsTrigger>
          <TabsTrigger value="outros" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Outros Relatórios
          </TabsTrigger>
        </TabsList>

        <TabsContent value="veiculos" className="mt-6">
          <VehicleComprehensiveReport />
        </TabsContent>

        <TabsContent value="customizados" className="mt-6">
          <CustomReportBuilder />
        </TabsContent>

        <TabsContent value="outros" className="mt-6">
          <div className="text-center py-12">
            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Outros Relatórios</h3>
            <p className="text-gray-500">
              Outros tipos de relatórios serão adicionados aqui no futuro.
            </p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}