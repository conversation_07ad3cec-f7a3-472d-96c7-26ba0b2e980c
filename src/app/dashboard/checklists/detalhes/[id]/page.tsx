import { redirect } from "next/navigation";
import { toast } from "sonner";

export default async function Page({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const id = (await params).id;
  const res = await fetch(`/api/checklist/${id}`)

  if(!res.ok){
    toast('Ops, algo deu errado',{
      description:'Houve um erro ao buscar o checklist'
    })
    redirect('/dashboard/checklists/consultar-checklist')
  }
  const data = await res.json()
  return
}
