"use client";
import { ChecklistForm } from "@/components/sections/checklists/checklist-form";
import { useParams, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

export default function Page() {
  const { id } = useParams();
  const searchParams = useSearchParams();
  const osId = searchParams.get("osId");
  const checklistType = searchParams.get("checklistType");
  const [veiculo, setVeiculo] = useState<any>(null);

  useEffect(() => {
    async function fetchVeiculo() {
      if (!id) return;
      try {
        const response = await fetch(`/api/veiculos/${id}`);
        const data = await response.json();
        setVeiculo(data.veiculo);
      } catch (error) {
        console.error("Erro ao buscar dados do veículo:", error);
      }
    }
    fetchVeiculo();
  }, [id]);

  return (
    <div className="w-full flex flex-col p-6">
      <h1 className="scroll-m-20 text-xl font-extrabold tracking-tight p-4">
        NOVO CHECKLIST
      </h1>
      <div className="w-full border rounded-lg">
        <ChecklistForm
          veiculo={veiculo}
          osId={osId as string}
          checklistType={checklistType as string}
        />
      </div>
    </div>
  );
}
