import { OrConsltar } from "@/components/sections/orcamento/consultar-orcamento";
import { listOsAvailable } from "@/serverActions/orcamentoAction";
import React from "react";

const ConsultarOcamento = async () => {
const osAvalible = await listOsAvailable();
  return (
    <div className="w-full flex flex-col p-6">
      <h1 className="scroll-m-20 text-xl font-extrabold tracking-tight p-4">Orçamentos</h1>
      <div className="w-full">
        <OrConsltar os={osAvalible.data}/>
      </div>
    </div>
  );
};

export default ConsultarOcamento;
