"use client";

import { OSOrdemServico } from "@/components/sections/os/os-ordem-servico";
import { useEffect, useState } from "react";
import { EdOrcamento } from "@/components/sections/orcamento/editar-orcamento";
import { toast } from "sonner";
import { useParams } from "next/navigation";

export default function OrcamentacaoPage() {
  const { id, status } = useParams();
  const [ordensDeServico, setOrdensDeServico] = useState<OS>();
  const [loading, setLoading] = useState(true);
  const decodedStatus = typeof status === "string" ? decodeURIComponent(status) : "";

  useEffect(() => {
    const fetchOrdensDeServico = async () => {
      try {
        const response = await fetch(`/api/os/${id}`);
        if (!response.ok) throw new Error("Erro ao buscar ordens de serviço.");
        const data = await response.json();
        setOrdensDeServico(data.data);
      } catch (err) {
        toast.error("Erro ao obter OS. Tente Novamente!");
      } finally {
        setLoading(false);
      }
    };

    fetchOrdensDeServico();
  }, [id]);

  return (
    <div className="container mx-auto py-6 pl-6">
      <h1 className="text-2xl font-bold mb-6">Ordem de Serviço #{ordensDeServico?.osNumber}</h1>
      {loading ? <p>Carregando...</p> : <EdOrcamento os={ordensDeServico} status={decodedStatus} />}
    </div>
  );
}
