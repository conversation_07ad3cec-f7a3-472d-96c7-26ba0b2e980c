'use client';
import { EditUsuarioSimple } from "@/components/sections/despesas/usuario/edit-usuario";
import { useCentroDeCusto } from "@/context/centro-de-custo-context";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";

export default function Page() {
  const { id } = useParams();
  const usuarioId = id as string;

  const [usuario, setUsuario] = useState<any | null>(null);
  const [loading, setLoading] = useState(true); // Add loading state
  const { centrosDeCusto } = useCentroDeCusto();

  useEffect(() => {
    async function fetchUsuario() {
      setLoading(true);
      try {
        const response = await fetch(`/api/usuario/${usuarioId}`);
        const data = await response.json();
        setUsuario(data.data.user);
      } catch (error) {
        console.error("Erro ao buscar detalhes do usuario:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchUsuario();
  }, [usuarioId]);

  return (
    <div className="w-full flex flex-col p-6">
      <h1 className="scroll-m-20 text-xl font-extrabold tracking-tight p-4">Editar Usuário</h1>
      <div className="w-full">
        {loading ? (
          <div className="flex justify-center items-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Carregando dados do usuário...</span>
          </div>
        ) : usuario ? (
          <EditUsuarioSimple
            usuario={usuario}
            centrosDeCusto={centrosDeCusto}
          />
        ) : (
          <div className="p-4 text-red-500">Erro ao carregar dados do usuário</div>
        )}
      </div>
    </div>
  );
}