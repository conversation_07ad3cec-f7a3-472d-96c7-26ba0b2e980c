import { getServerSession } from "@/lib/auth/server-session";
import { redirect } from "next/navigation";

export default async function Page() {
  const session = await getServerSession();
  if (!session) redirect("/login");
  if (session.roles.includes("ORCAMENTISTA_OFICINA")) {
    redirect("/dashboard/orcamento/consultar-orcamento");
  } 
  if(session.roles.includes("CREDENCIAMENTO")){
    redirect("/dashboard/rede-credenciada/consultar-credenciados");
  }
    redirect("/dashboard/ordens-de-servico/ordens");
}
