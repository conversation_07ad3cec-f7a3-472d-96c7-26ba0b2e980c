"use client";

import { EditarCondutorForm } from "@/components/sections/condutor/editar-condutor-form";
import { useParams } from "next/navigation";
import { useState, useEffect } from "react";
import { toast } from "sonner";

export default function EditarCondutorPage() {
  const { id } = useParams();
  const [condutorData, setCondutorData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function fetchCondutorData() {
      try {
        setIsLoading(true);
        const res = await fetch(`/api/condutores/${id}`);

        if (!res.ok) {
          throw new Error("Erro ao buscar dados do condutor");
        }

        const data = await res.json();
        setCondutorData(data.data);
      } catch (error) {
        console.error("Erro ao carregar condutor:", error);
        toast.error("Não foi possível carregar os dados do condutor");
      } finally {
        setIsLoading(false);
      }
    }

    if (id) {
      fetchCondutorData();
    }
  }, [id]);

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <h1 className="text-2xl font-bold mb-6">Editar Condutor</h1>
        <div className="flex justify-center items-center h-64">
          <span className="ml-2">Carregando dados do condutor...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Editar Condutor</h1>
      {condutorData ? (
        <EditarCondutorForm condutorData={condutorData.condutor} condutorId={id as string} />
      ) : (
        <div className="p-4 border border-red-300 bg-red-50 text-red-700 rounded-md">
          Não foi possível carregar o condutor. Verifique se o ID é válido.
        </div>
      )}
    </div>
  );
}
