"use client";

import { DataTable } from "@/components/tables/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { extratoEmpenhoMock } from "../../../../components/modal/extratoEmpenho";

interface Extrato {
  data: string;
  tipo: string;
  observacao: string;
  valor: number;
}

const columns: ColumnDef<Extrato>[] = [
  {
    accessorKey: "data",
    header: "Data",
    cell: ({ row }) => row.original.data,
  },
  {
    accessorKey: "tipo",
    header: "Tipo de lançamento",
    cell: ({ row }) => row.original.tipo,
  },
  {
    accessorKey: "observacao",
    header: "Observação",
    cell: ({ row }) => (
      <div className="whitespace-pre-wrap text-sm text-muted-foreground">
        {row.original.observacao}
      </div>
    ),
  },
  {
    accessorKey: "valor",
    header: "Valor",
    cell: ({ row }) => (
      <span
        className={`text-sm font-semibold ${
          row.original.valor < 0 ? "text-red-600" : ""
        }`}
      >
        {row.original.valor.toLocaleString("pt-BR", {
          style: "currency",
          currency: "BRL",
        })}
      </span>
    ),
  },
];

export default function ExtratoEmpenho() {
  return (
    <div className="p-4">
      <h1 className="text-xl font-semibold mb-4">Extrato do Empenho</h1>
      <DataTable data={extratoEmpenhoMock} columns={columns} exportTo />
    </div>
  );
}
