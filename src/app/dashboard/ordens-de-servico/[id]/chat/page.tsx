"use client";

import { useParams } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Send } from "lucide-react";
import clsx from "clsx";
import { exportarChatComoPDF } from "./exportarChatComoPDF";
import { useChat } from "@/hooks/chatHooks";

const mockUser = {
  userId: "cmbathz4s0002vbg4eo889nns",
  name: "<PERSON>",
  roles: ["ADMIN"],
};

export default function Page() {
  const { id: numeroDaOS, status } = useParams();
  const [loading, setLoading] = useState(false);

  const { mensagens, mensagemAtual, setMensagemAtual, enviarMensagem } =
    useChat(mockUser, String(numeroDaOS), {
      gestorId: "mockGestorId", // ou pegue da sessão/autenticado
      credenciadoId: "mockCredenciadoId",
      numeroDoContrato: "mockContrato",
    });

  const handleSend = async () => {
    if (mensagemAtual.trim() === "") {
      toast.warning("Digite uma mensagem.");
      return;
    }

    try {
      await enviarMensagem(mensagemAtual);
      setMensagemAtual("");
    } catch (error) {
      console.error("Erro ao enviar mensagem", error);
      toast.error("Erro ao enviar a mensagem.");
    }
  };

  return (
    <div className="w-full h-full flex flex-col px-6 py-4 gap-4">
      <div className="flex justify-between items-center">
        <h1 className="text-xl font-extrabold tracking-tight">
          Chat da Ordem de Serviço
        </h1>
        <Button
          variant="outline"
          onClick={() =>
            exportarChatComoPDF({
              mensagens,
              usuarioLogado: mockUser,
              osId: String(numeroDaOS),
              status: String(status),
            })
          }
        >
          Exportar PDF
        </Button>
      </div>

      <Card className="flex-1 p-4 overflow-hidden">
        {loading ? (
          <p>Carregando mensagens ...</p>
        ) : (
          <ScrollArea className="h-[60vh] pr-4">
            <div className="flex flex-col gap-4">
              {mensagens.map((msg) => {
                const isOwnMessage = msg.usuarioId === mockUser.userId;
                return (
                  <div
                    key={msg.id}
                    className={clsx("flex", {
                      "justify-end": isOwnMessage,
                      "justify-start": !isOwnMessage,
                    })}
                  >
                    <div
                      className={clsx(
                        "max-w-[70%] p-3 rounded-xl shadow-sm",
                        isOwnMessage
                          ? "bg-blue-500 text-white"
                          : "bg-muted text-foreground"
                      )}
                    >
                      <p className="text-sm whitespace-pre-line">
                        {msg.conteudo}
                      </p>
                      <span className="text-xs text-muted-foreground block mt-1 text-right">
                        {new Date(msg.enviadaEm).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        )}
      </Card>

      <div className="flex items-center gap-2">
        <Input
          placeholder="Digite sua mensagem..."
          value={mensagemAtual}
          onChange={(e) => setMensagemAtual(e.target.value)}
          onKeyDown={(e) => e.key === "Enter" && handleSend()}
        />
        <Button onClick={handleSend}>
          <Send className="w-4 h-4 mr-2" />
          Enviar
        </Button>
      </div>
    </div>
  );
}
