import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { toast } from "sonner";

// Types (ajuste conforme seu modelo)
interface Mensagem {
  id: string;
  chatId: string;
  remetenteId: string;
  tipo: "TEXTO" | "ARQUIVO";
  conteudo: string;
  urlArquivo: string | null;
  enviadaEm: string;
  usuarioId: string;
}

interface User {
  userId: string;
  name: string;
}

interface ExportarChatParams {
  mensagens: Mensagem[];
  usuarioLogado: User;
  osId: string;
  status?: string;
}

export function exportarChatComoPDF({
  mensagens,
  usuarioLogado,
  osId,
  status,
}: ExportarChatParams) {
  try {
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();
    const margin = 10;
    const maxBubbleWidth = pageWidth * 0.65;

    let y = margin;

    // Cabeçalho
    doc.setFontSize(16);
    doc.text("Chat da Ordem de Serviço", margin, y);
    y += 8;
    doc.setFontSize(10);
    doc.text(`OS ID: ${osId}`, margin, y);
    y += 5;
    doc.text(`Status: ${status || "Indefinido"}`, margin, y);
    y += 5;
    doc.text(`Exportado em: ${new Date().toLocaleString()}`, margin, y);
    y += 10;

    mensagens.forEach((msg) => {
      const isUser = msg.usuarioId === usuarioLogado.userId;
      const alignRight = isUser;

      const remetente = isUser ? "Você" : msg.remetenteId;
      const dataFormatada = new Date(msg.enviadaEm).toLocaleString();

      const text = msg.conteudo;
      const fontSize = 10;

      doc.setFontSize(8);
      const remetenteLinha = `${remetente} - ${dataFormatada}`;
      const remetenteX = alignRight ? pageWidth - margin : margin;

      doc.text(remetenteLinha, remetenteX, y, {
        align: alignRight ? "right" : "left",
      });
      y += 4;

      doc.setFontSize(fontSize);

      // Quebrar texto em múltiplas linhas
      const textLines = doc.splitTextToSize(text, maxBubbleWidth);
      const lineHeight = 5;
      const bubblePadding = 2;

      // Calcular largura do balão com segurança
      const maxLineWidth = Math.max(
        ...textLines.map((line: string) => doc.getTextWidth(line))
      );
      const bubbleWidth = Math.min(
        maxBubbleWidth,
        maxLineWidth + 2 * bubblePadding
      );
      const bubbleHeight = textLines.length * lineHeight + 2 * bubblePadding;

      // Quebrar página se não couber
      if (y + bubbleHeight > pageHeight - margin) {
        doc.addPage();
        y = margin;
      }

      const x = alignRight ? pageWidth - margin - bubbleWidth : margin;

      // Desenhar balão
      doc.setFillColor(isUser ? 220 : 240, isUser ? 248 : 240, 255); // azul claro / cinza claro
      doc.roundedRect(x, y, bubbleWidth, bubbleHeight, 3, 3, "F");

      // Escrever o texto
      const textX = x + bubblePadding;
      let textY = y + bubblePadding + fontSize / 2;

      textLines.forEach((line: string) => {
        doc.text(line, textX, textY);
        textY += lineHeight;
      });

      y += bubbleHeight + 6;
    });

    doc.save(`chat-os-${osId}.pdf`);
    toast.success("PDF exportado com sucesso!");
  } catch (error) {
    console.error(error);
    toast.error("Erro ao exportar PDF");
  }
}
