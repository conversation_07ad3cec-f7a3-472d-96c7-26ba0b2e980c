"use client";
import { OSOrdemServico } from "@/components/sections/os/os-ordem-servico";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";

export default function Page() {
  const { id, status } = useParams();
  const [ordensDeServico, setOrdensDeServico] = useState<OS>();
  const [loading, setLoading] = useState(true);
  const decodedStatus = typeof status === "string" ? decodeURIComponent(status) : "";

  useEffect(() => {
    const fetchOrdensDeServico = async () => {
      try {
        const response = await fetch(`/api/os/${id}`);
        if (!response.ok) throw new Error("Erro ao buscar ordens de serviço.");
        const data = await response.json();
        setOrdensDeServico(data.data);
      } catch (err) {
        toast.error("Erro ao obter OS. Tente Novamente!");
      } finally {
        setLoading(false);
      }
    };

    fetchOrdensDeServico();
  }, []);

  return (
    <div className="w-full flex flex-col px-6">
      <h1 className="scroll-m-20 text-xl font-extrabold tracking-tight p-4">Ordem de Serviço</h1>
      <div className="w-full">
        {loading ? (
          <p>Carregando...</p>
        ) : (
          <OSOrdemServico os={ordensDeServico} currentStatus={decodedStatus} />
        )}
      </div>
    </div>
  );
}
