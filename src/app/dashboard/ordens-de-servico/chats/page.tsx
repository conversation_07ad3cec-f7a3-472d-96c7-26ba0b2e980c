"use client";

import Link from "next/link";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { getChatsList } from "@/serverActions/chatActions";
import { <PERSON>, CardH<PERSON>er, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { mostrarUsuario } from "@/hooks/usuariosHooks";

type Chat = {
  id: string;
  numeroDoContrato: string;
  cnpjDoCliente: string;
  numeroDaOS: number;
  gestorId: string;
  credenciadoId: string;
  status: "ABERTO" | "FINALIZADO";
  createdAt: string;
  updatedAt: string;
};

export default function Page() {
  const [chats, setChats] = useState<Chat[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const limit = 10;
  const [total, setTotal] = useState(0);
  const totalPages = Math.ceil(total / limit);
  const [usuario, setUsuario] = useState<any>();
  useEffect(() => {
    const loadChats = async () => {
      setLoading(true);
      try {
        const { success, data, error } = await getChatsList({ page, limit });
        if (!success) throw new Error(error || "Falha ao listar chats");
        if (!!data?.chats && !!data?.total) {
          setChats(data.chats);
          setTotal(data.total || 0);
        }
      } catch (err: any) {
        toast.error(err.message);
      } finally {
        setLoading(false);
      }
    };

    loadChats();
  }, [page]);
  useEffect(() => {
    const fetchUsuario = async () => {
      const _usuario = await mostrarUsuario();

      setUsuario(_usuario);
    };
    fetchUsuario();
  }, []);

  return (
    <div className="w-full max-w-4xl mx-auto px-4 py-6 space-y-6">
      <h1 className="text-2xl font-bold"> Listagem de Chats</h1>
      <div>
        <p>{JSON.stringify(usuario)}</p>
      </div>
      {loading ? (
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <Skeleton key={i} className="h-24 w-full rounded-lg" />
          ))}
        </div>
      ) : chats.length === 0 ? (
        <p className="text-muted-foreground">Nenhum chat encontrado.</p>
      ) : (
        <>
          <div className="space-y-4">
            {chats.map((chat) => (
              <Link
                href={`/dashboard/ordens-de-servico/${chat.id}/chat`}
                key={chat.id}
              >
                <Card className="hover:shadow-md transition-shadow">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-lg">
                      OS #{chat.numeroDaOS}
                    </CardTitle>
                    <Badge
                      variant={chat.status === "ABERTO" ? "default" : "outline"}
                      className={
                        chat.status === "ABERTO"
                          ? "bg-green-100 text-green-700"
                          : "text-gray-500 border-gray-300"
                      }
                    >
                      {chat.status}
                    </Badge>
                  </CardHeader>
                  <CardContent className="text-sm text-muted-foreground">
                    <p>
                      <strong>Chat ID:</strong> {chat.id}
                    </p>
                    <p>
                      <strong>Cliente CNPJ:</strong> {chat.cnpjDoCliente}
                    </p>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>

          {/* Paginação */}
          <div className="flex justify-center items-center gap-4 pt-6">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage((prev) => Math.max(prev - 1, 1))}
              disabled={page === 1}
            >
              <ChevronLeft className="h-4 w-4 mr-1" /> Anterior
            </Button>

            <span className="text-sm text-muted-foreground">
              Página <strong>{page}</strong> de{" "}
              <strong>{totalPages || 1}</strong>
            </span>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage((prev) => prev + 1)}
              disabled={page >= totalPages}
            >
              Próxima <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </>
      )}
    </div>
  );
}
