"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { Truck, Bike, Car, Wrench, Package, CheckCircle, XCircle } from 'lucide-react';

const TIPOS_FROTA_PADRAO = [
  {
    descricao: "Leve",
    categoria: "leve",
    icon: Car,
    description: "Veículos de passeio, utilitários leves"
  },
  {
    descricao: "Médio", 
    categoria: "medio",
    icon: Package,
    description: "Caminhões médios, vans grandes"
  },
  {
    descricao: "Pesado",
    categoria: "pesado", 
    icon: Truck,
    description: "Caminhões pesados, carretas"
  },
  {
    descricao: "Máquina",
    categoria: "maquina",
    icon: Wrench,
    description: "Máquinas pesadas, equipamentos"
  },
  {
    descricao: "Moto",
    categoria: "moto",
    icon: Bike,
    description: "Motocicletas, ciclomotores"
  }
];

interface SeedResult {
  tipo: string;
  success: boolean;
  message: string;
}

export default function SeedTiposFrotaPage() {
  const [isSeeding, setIsSeeding] = useState(false);
  const [seedResults, setSeedResults] = useState<SeedResult[]>([]);

  const executeSeed = async () => {
    setIsSeeding(true);
    setSeedResults([]);
    
    try {
      const results: SeedResult[] = [];
      
      for (const tipo of TIPOS_FROTA_PADRAO) {
        try {
          const response = await fetch('/api/tipo-de-frota', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              descricao: tipo.descricao
            }),
          });

          if (response.ok) {
            const data = await response.json();
            results.push({
              tipo: tipo.descricao,
              success: true,
              message: `Cadastrado com sucesso! ID: ${data.data?.id}`
            });
          } else {
            const errorText = await response.text();
            results.push({
              tipo: tipo.descricao,
              success: false,
              message: `Erro: ${errorText}`
            });
          }
        } catch (error) {
          results.push({
            tipo: tipo.descricao,
            success: false,
            message: `Erro: ${error}`
          });
        }
      }
      
      setSeedResults(results);
      
      const successCount = results.filter(r => r.success).length;
      if (successCount === TIPOS_FROTA_PADRAO.length) {
        toast.success('Todos os tipos de frota foram cadastrados com sucesso!');
      } else if (successCount > 0) {
        toast.warning(`${successCount}/${TIPOS_FROTA_PADRAO.length} tipos cadastrados com sucesso`);
      } else {
        toast.error('Nenhum tipo de frota foi cadastrado');
      }
      
    } catch (error) {
      toast.error('Erro durante o cadastro dos tipos de frota');
      console.error('Erro:', error);
    } finally {
      setIsSeeding(false);
    }
  };

  return (
    <div className="container mx-auto py-6 px-4 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Cadastro de Tipos de Frota Padrão</h1>
          <p className="text-gray-600">
            Execute este script para cadastrar os tipos de frota padrão no sistema
          </p>
        </div>
      </div>

      {/* Tipos que serão cadastrados */}
      <Card>
        <CardHeader>
          <CardTitle>Tipos de Frota a serem cadastrados</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {TIPOS_FROTA_PADRAO.map((tipo) => {
              const IconComponent = tipo.icon;
              return (
                <div key={tipo.categoria} className="border rounded-lg p-4 space-y-2">
                  <div className="flex items-center gap-2">
                    <IconComponent className="h-5 w-5" />
                    <h3 className="font-medium">{tipo.descricao}</h3>
                  </div>
                  <p className="text-sm text-gray-600">{tipo.description}</p>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Botão de execução */}
      <Card>
        <CardHeader>
          <CardTitle>Executar Cadastro</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <Button 
              onClick={executeSeed} 
              disabled={isSeeding}
              size="lg"
            >
              {isSeeding ? 'Cadastrando...' : 'Executar Cadastro dos Tipos de Frota'}
            </Button>
            
            {isSeeding && (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                <span className="text-sm text-gray-600">Processando...</span>
              </div>
            )}
          </div>
          
          <p className="text-sm text-gray-500">
            ⚠️ Este script irá tentar cadastrar todos os tipos de frota listados acima. 
            Tipos já existentes podem gerar erro, mas isso é normal.
          </p>
        </CardContent>
      </Card>

      {/* Resultados */}
      {seedResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Resultados do Cadastro</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {seedResults.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded">
                  <div className="flex items-center gap-2">
                    {result.success ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                    <span className="font-medium">{result.tipo}</span>
                    <Badge variant={result.success ? "default" : "destructive"}>
                      {result.success ? "Sucesso" : "Erro"}
                    </Badge>
                  </div>
                  <span className="text-sm text-gray-600 max-w-md truncate">{result.message}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
