"use server";

import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { cookies } from "next/headers";
import { withErrorHandling } from "./serverActionErrors";

async function getSessionToken(): Promise<string> {
  const cookieStore = await cookies();
  const cookie = cookieStore.get(SESSION_COOKIE_NAME);
  if (!cookie || !cookie.value) {
    throw new Error("Unauthorized");
  }
  const decompressedToken = decompressToken(cookie.value);
  return decompressedToken;
}

export async function updateEmpenhoAction(
  id: string,
  {
    centroCustoId,
    nota_empenho,
    ano_competencia,
    data_inicial,
    data_final,
    dotacao_orcamentaria,
    valor_pecas,
    valor_servicos,
    bloqueado_pecas,
    bloqueado_servicos,
    faturado_pecas,
    faturado_servicos,
    empenho_bloqueado,
    empenho_ativo,
  }: {
    centroCustoId: string;
    nota_empenho: string;
    ano_competencia: number;
    data_inicial: string;
    data_final: string;
    dotacao_orcamentaria: string;
    valor_pecas: number;
    valor_servicos: number;
    bloqueado_pecas: boolean;
    bloqueado_servicos: boolean;
    faturado_pecas: boolean;
    faturado_servicos: boolean;
    empenho_bloqueado: boolean;
    empenho_ativo: boolean;
  }
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(`${process.env.API_BASE_URL}/empenhos/empenho/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session}`,
      },
      body: JSON.stringify({
        centroCustoId,
        nota_empenho,
        ano_competencia,
        data_inicial,
        data_final,
        dotacao_orcamentaria,
        valor_pecas,
        valor_servicos,
        bloqueado_pecas,
        bloqueado_servicos,
        faturado_pecas,
        faturado_servicos,
        empenho_bloqueado,
        empenho_ativo,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao atualizar empenho");
    }
    return await response.json();
  });
}

export async function updateEmpenhoSaldosBloqueadosAction(
  id: string,
  {
    bloqueado_pecas,
    bloqueado_servicos,
  }: {
    bloqueado_pecas: number;
    bloqueado_servicos: number;
  }
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(
      `${process.env.API_BASE_URL}/empenhos/empenho/${id}/saldos-bloqueados`,
      {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
        body: JSON.stringify({
          bloqueado_pecas,
          bloqueado_servicos,
        }),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao atualizar saldos bloqueados do empenho");
    }
    return await response.json();
  });
}

export async function updateSaldosPecasEServicos(
  id: string,
  {
    saldo_pecas,
    saldo_servicos,
  }: {
    saldo_pecas: number;
    saldo_servicos: number;
  }
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(
      `${process.env.API_BASE_URL}/empenhos/empenho/${id}/ajuste-saldo`,
      {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
        body: JSON.stringify({
          saldo_pecas,
          saldo_servicos,
        }),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao atualizar saldos do empenho");
    }
    return await response.json();
  });
}
