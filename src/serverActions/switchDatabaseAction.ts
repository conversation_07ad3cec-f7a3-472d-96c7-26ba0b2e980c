"use server";

import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { cookies } from "next/headers";
import { ServerActionError, withErrorHandling } from "./serverActionErrors";
import { gzip } from "pako";

function compressToken(token: string): string {
  const compressed = gzip(new TextEncoder().encode(token));
  return Buffer.from(compressed).toString("base64");
}

export async function switchDatabase(
  contratoId: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const cookieStore = await cookies();
    const cookie = cookieStore.get(SESSION_COOKIE_NAME);

    if (!cookie) {
      throw new Error("Unauthorized");
    }
    const session = cookie.value;
    if (!session) {
      cookieStore.delete(SESSION_COOKIE_NAME);
      throw new Error("Unauthorized");
    }
    const decompressedToken = decompressToken(session);
    const response = await fetch(`${process.env.API_BASE_URL}/switch-database`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${decompressedToken}`,
      },
      body: JSON.stringify({ contratoId }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error response:", errorData);
      throw new ServerActionError(
        errorData.message || "Erro ao trocar o banco de dados",
        response.status
      );
    }

    const data = await response.json();
    console.log("Response data:", data.token);
    const compressedToken = compressToken(data.token);
    if (data.token) {
      cookieStore.set(SESSION_COOKIE_NAME, compressedToken, { path: "/" });
    }

    return data;
  });
}
