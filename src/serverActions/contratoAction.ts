"use server";

import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { cookies } from "next/headers";
import { getServerSession } from "@/lib/auth/server-session";
import { withErrorHandling, ServerActionError } from "./serverActionErrors";

// Interface for discount data
export interface DescontoContrato {
  descricao: string;
  tipoItem: string;
  tipoVeiculo: string;
  desconto: number;
  precoMaximo?: number;
}

// Interface for contract data
export interface ContratoData {
  nome_contrato: string;
  numero: string;
  data_inicial: string;
  data_final: string;
  valor_contrato: number;
  taxa_admin: number;
  cnpj: string;
  razao_social: string;
  responsavel: string;
  telefone: string;
  email: string;
  cep: string;
  logradouro: string;
  bairro: string;
  estado: string;
  cidade: string;
  logotipo?: string;
  limite_gastos_percent: number;
  // Boolean fields
  placa_veiculo?: boolean;
  modelo_veiculo?: boolean;
  numero_os?: boolean;
  numero_contrato?: boolean;
  contrato_ativo?: boolean;
  abertura_via_os?: boolean;
  envio_emails?: boolean;
  veiculo_rfid?: boolean;
  parametrizacao_obg?: boolean;
  checklist_simplificado_pecas?: boolean;
  // Discount data
  descontos?: DescontoContrato[];
}

async function getSessionToken(): Promise<string> {
  const cookieStore = await cookies();
  const cookie = cookieStore.get(SESSION_COOKIE_NAME);
  if (!cookie || !cookie.value) {
    throw new Error("Unauthorized");
  }
  const decompressedToken = decompressToken(cookie.value);
  return decompressedToken;
}

export async function updateContratoAction(
  id: string,
  contratoData: ContratoData
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(`${process.env.API_BASE_URL}/contratos/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session}`,
      },
      body: JSON.stringify(contratoData),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao atualizar contrato");
    }
    return await response.json();
  });
}

export async function getDescontosContratoAction(
  id: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<{ descontos: ItemContrato[] }>(async () => {
    console.log("Fetching discounts for contract ID:", id);
    const session = await getSessionToken();
    const response = await fetch(`${process.env.API_BASE_URL}/contratos/${id}/descontos`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao buscar descontos do contrato");
    }

    return await response.json();
  });
}

export async function getContratoByIdAction(
  id: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(`${process.env.API_BASE_URL}/contratos/${id}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao buscar contrato");
    }

    return await response.json();
  });
}
