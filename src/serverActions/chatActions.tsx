"use server";

import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { cookies } from "next/headers";
import { withErrorHandling } from "./serverActionErrors";

async function getSessionToken(): Promise<string> {
  const cookieStore = await cookies();
  const cookie = cookieStore.get(SESSION_COOKIE_NAME);
  if (!cookie || !cookie.value) {
    throw new Error("Unauthorized");
  }
  const decompressedToken = decompressToken(cookie.value);
  return decompressedToken;
}

export async function createChat(orderServiceId: string): Promise<any> {
  const session = await getSessionToken();
  console.log(`Bearer ${session}`);
  const response = await fetch(`${process.env.API_BASE_URL}/chats`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${session}`,
    },
    body: JSON.stringify({
      numeroDoContrato: "123456",
      numeroDaOS: orderServiceId,
      gestorId: "clwxyz1230001",
      credenciadoId: "clwxyz1230002",
    }),
  });
  if (!response.ok) {
    const errorData = await response.json();
    console.error(`errorData: `, errorData);
    console.error(`erro: ${process.env.API_BASE_URL}/chat`);
    throw new Error(errorData.message || "Erro ao criar chat");
  }
  return response.json();
}

export async function getChatByOrderServiceId(
  orderServiceId: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling(async () => {
    const session = await getSessionToken();

    const response = await fetch(
      `${process.env.API_BASE_URL}/chat/os/${orderServiceId}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${session}`,
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao buscar chat pela OS");
    }

    return await response.json();
  });
}
export async function getChatsList(params?: {
  page?: number;
  limit?: number;
}): Promise<{
  success: boolean;
  data?: {
    chats: any[];
    total: number;
    page: number;
    limit: number;
  };
  error?: string;
}> {
  return await withErrorHandling(async () => {
    const session = await getSessionToken();

    const query = new URLSearchParams();
    if (params?.page) query.append("page", params.page.toString());
    if (params?.limit) query.append("limit", params.limit.toString());

    const response = await fetch(
      `${process.env.API_BASE_URL}/chats?${query.toString()}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${session}`,
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao buscar chat pela OS");
    }

    return await response.json();
  });
}

export async function getMessagesByChatId(
  chatId: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling(async () => {
    const session = await getSessionToken();

    const response = await fetch(
      `${process.env.API_BASE_URL}/chat/${chatId}/messages`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${session}`,
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao buscar mensagens");
    }

    return await response.json();
  });
}

export async function createMessage(
  chatId: string,
  content: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling(async () => {
    const session = await getSessionToken();

    const response = await fetch(
      `${process.env.API_BASE_URL}/chat/${chatId}/messages`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
        body: JSON.stringify({ content }),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao enviar mensagem");
    }

    return await response.json();
  });
}

export async function sendMessageToChat({
  chatId,
  conteudo,
  tipo = "TEXTO",
  urlArquivo = null,
}: {
  chatId: string;
  conteudo: string;
  tipo?: "TEXTO" | "ARQUIVO";
  urlArquivo?: string | null;
}): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling(async () => {
    const session = await getSessionToken();

    const response = await fetch(
      `${process.env.API_BASE_URL}/chats/${chatId}/messages`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
        body: JSON.stringify({
          chatId,
          tipo,
          conteudo,
          urlArquivo,
        }),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao enviar mensagem");
    }

    const data = await response.json();
    return { success: true, data };
  });
}
