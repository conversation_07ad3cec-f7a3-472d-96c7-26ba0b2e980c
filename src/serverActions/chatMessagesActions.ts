"use server";

import { withErrorHandling } from "./serverActionErrors";
import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { cookies } from "next/headers";

async function getSessionToken(): Promise<string> {
  const cookieStore = await cookies();
  const cookie = cookieStore.get(SESSION_COOKIE_NAME);
  if (!cookie || !cookie.value) {
    throw new Error("Unauthorized");
  }
  const decompressedToken = decompressToken(cookie.value);
  return decompressedToken;
}

export async function createMessage(
  chatId: string,
  conteudo: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling(async () => {
    const session = await getSessionToken();

    const response = await fetch(
      `${process.env.API_BASE_URL}/chats/${chatId}/messages`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
        body: JSON.stringify({
          chatId,
          tipo: "TEXTO",
          conteudo,
          urlArquivo: null,
        }),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao enviar mensagem");
    }

    const data = await response.json();
    return { success: true, data };
  });
}

export async function getMessagesByOrderServiceId(
  orderServiceId: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    console.log("orderServiceId ->", orderServiceId);
    const token = await getSessionToken();
    const response = await fetch(
      `${process.env.API_BASE_URL}/chats/os/${orderServiceId}/messages`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (!response.ok) {
      const err = await response.json();
      console.error("err Reponse", err);
      return {
        success: false,
        error: err.message || "Erro ao buscar mensagens",
      };
    }

    const json = await response.json();

    return { success: true, data: json.mensagens };
  } catch (err: any) {
    return { success: false, error: err.message || "Erro inesperado" };
  }
}
