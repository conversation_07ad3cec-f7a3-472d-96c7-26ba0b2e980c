"use server";

import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { cookies } from "next/headers";
import { ServerActionError, withErrorHandling } from "./serverActionErrors";
import { getServerSession } from "@/lib/auth/server-session";

async function getSessionToken(): Promise<string> {
  const cookieStore = await cookies();
  const cookie = cookieStore.get(SESSION_COOKIE_NAME);
  if (!cookie || !cookie.value) {
    throw new Error("Unauthorized");
  }
  const decompressedToken = decompressToken(cookie.value);
  return decompressedToken;
}

export async function getVehicleByRfId(
  rfId: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(`${process.env.API_BASE_URL}/veiculos/tag/${rfId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session}`,
      },
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao buscar veiculo");
    }
    return await response.json();
  });
}

export interface VehicleUpdateData {
  id?: string;
  placa?: string;
  renavam?: string;
  vin?: string;
  numero_motor?: string;
  ano_fab?: number;
  ano_modelo?: number;
  cor?: string;
  odometro_atual?: number | string;
  matricula?: string;
  tag_rfid?: string;
  combustivel?: string;
  valor_venal?: number | string;
  status?: string;
  multipla_os?: boolean;
}

export async function updateVehiclesInBatch(vehicles: VehicleUpdateData[]): Promise<{
  success: boolean;
  message?: string;
  atualizados?: string[];
  erros?: { placa: string; erro: string }[];
}> {
  return await withErrorHandling(async () => {
    const session = await getSessionToken();
    const response = await fetch(`${process.env.API_BASE_URL}/veiculos/atualizar-veiculos`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session}`,
      },
      body: JSON.stringify(vehicles),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao atualizar veículos");
    }

    const result = await response.json();
    return {
      success: true,
      message: result.message,
      atualizados: result.atualizados,
      erros: result.erros,
    };
  });
}
