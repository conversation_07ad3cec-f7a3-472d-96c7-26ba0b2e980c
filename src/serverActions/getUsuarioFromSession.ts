"use server";

import { cookies } from "next/headers";

import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";

import { decompressToken } from "@/lib/auth/decompress-token";

export async function getUsuarioFromSession(): Promise<string> {
  const cookieStore = await cookies();
  const cookie = cookieStore.get(SESSION_COOKIE_NAME);
  if (!cookie || !cookie.value) {
    throw new Error("Unauthorized");
  }
  const decompressedToken = decompressToken(cookie.value);
  return decompressedToken;
}
