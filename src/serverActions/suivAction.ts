"use server";

import { VehicleInfoResponse } from "@/interfaces/suiv.interface";
import { fetchSuivAPI } from "@/lib/suiv-api";

interface Maker {
  id: number;
  description: string;
}

interface Model {
  id: number;
  description: string;
}

interface Version {
  id: number;
  description: string;
  startingYear: number;
  endingYear: number;
}

//marcas
export async function getMakers() {
  return fetchSuivAPI<Maker[]>("/Makers", {});
}

//modelos
export async function getModels(makerId: number) {
  return fetchSuivAPI<Model[]>("/Models", { makerId });
}

//versões
export async function getVersions(modelId: number) {
  return fetchSuivAPI<Version[]>("/Versions", { modelId });
}

//token do veiculo
export async function getVehicleToken(versionId: number, year: number) {
  return fetchSuivAPI<{ token: string }>("/VehicleToken", { versionId, year });
}

//grupos de peças
export async function getVehicleSets(vehicleToken: string) {
  return fetchSuivAPI<Array<{ id: number; description: string }>>("/Sets", { vehicleToken });
}

//subgrupos de peças
export async function getNicknames(vehicleToken: string, setId: number) {
  return fetchSuivAPI<Array<{ id: number; description: string }>>("/Nicknames", {
    vehicleToken,
    setId,
  });
}

//peças
export async function getAftermarketParts(vehicleToken: string, nicknameId: number) {
  return fetchSuivAPI<
    Array<{
      id: number;
      description: string;
      maker: string;
      partNumber: string;
      price: number;
    }>
  >("/AftermarketParts/allmakers", { vehicleToken, nicknameId });
}

//peças
export async function getParts(vehicleToken: string, nicknameId: number) {
  return fetchSuivAPI<
    Array<{
      id: number;
      description: string;
      labor: string;
      partNumber: string;
      price: number;
      amount: number;
      availableServies: {
        hasUsed: boolean;
        hasOriginal: boolean;
      };
    }>
  >("/Parts", { vehicleToken, nicknameId });
}

export async function getVehicleByPlate(
  plate: string,
  options: { withFipe?: boolean; searchOtherProviders?: boolean } = {}
) {
  return fetchSuivAPI<VehicleInfoResponse>("/VehicleInfo/byplate", {
    plate,
    withFipe: "true",
    searchOtherProviders: "true",
  });
}

export async function getMarkerLogoByPlate(plate: string) {
  return fetchSuivAPI<{ makerLogoUrl: string }>("/MakerLogo/byplate", { plate });
}

export async function searchPartsPrice(vehicleToken: string, pn: string) {
  return fetchSuivAPI<any>("/SearchPrice", { vehicleToken, pn });
}
export async function searchParts(vehicleToken: string, search: string) {
  return fetchSuivAPI<any>("/SearchParts", { vehicleToken, search });
}

export async function getRevisionPlan(versionId: number, year: number) {
  return fetchSuivAPI<any>("/RevisionPlan", { versionId, year });
}

export async function getIndividualOverlapsByNickname(vehicleToken: string, parts: number[]) {
  const partsParam = parts.join(",");
  return fetchSuivAPI<any>("/IndividualOverlapsByNickname", {
    vehicleToken,
    parts: partsParam,
  });
}
