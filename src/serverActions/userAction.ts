"use server";

import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { cookies } from "next/headers";
import { ServerActionError, withErrorHandling } from "./serverActionErrors";
import { getServerSession } from "@/lib/auth/server-session";

async function getSessionToken(): Promise<string> {
  const cookieStore = await cookies();
  const cookie = cookieStore.get(SESSION_COOKIE_NAME);
  if (!cookie || !cookie.value) {
    throw new Error("Unauthorized");
  }
  const decompressedToken = decompressToken(cookie.value);
  return decompressedToken;
}

export async function inactivateUserAction(
  id: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(
      `${process.env.API_BASE_URL}/auth/inactivate-user/${id}`,
      {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
      }
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao inativar usuário");
    }
    return await response.json();
  });
}

export async function inactivateUserCreateOsAction(
  id: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(
      `${process.env.API_BASE_URL}/auth/inactivate-user-create-os/${id}`,
      {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
      }
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao inativar usuário");
    }
    return await response.json();
  });
}
