"use server";

import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { cookies } from "next/headers";
import { withErrorHandling } from "./serverActionErrors";

async function getSessionToken(): Promise<string> {
  const cookieStore = await cookies();
  const cookie = cookieStore.get(SESSION_COOKIE_NAME);
  if (!cookie || !cookie.value) {
    throw new Error("Unauthorized");
  }
  const decompressedToken = decompressToken(cookie.value);
  return decompressedToken;
}

export async function bloquearCredenciado(
  id: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    console.log(
      `${process.env.API_BASE_URL}/credenciados/bloquear-credenciado/${id}`
    );
    const session = await getSessionToken();
    const response = await fetch(
      `${process.env.API_BASE_URL}/credenciados/bloquear-credenciado/${id}`,
      {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao bloquear credenciado");
    }
    return await response.json();
  });
}

export interface CriarCredenciadoData {
  informacoes: {
    cnpj: string;
    razao_social: string;
    inscri_estadual?: string;
    inscri_municipal?: string;
    nome_fantasia?: string;
    atividade_principal?: string;
    horario_funcionamento?: string;
    data_abertura?: string;
    porte_empresarial?: any;
    capital_social?: string | null;
    patrimonio_liquido?: string | null;
    observacoes_gerais?: string;
    logotipo_empresa?: string;
  };
  estrutura: {
    capacidade_atendimento?: number;
    box_veiculos_leves?: number;
    box_veiculos_pesados?: number;
    elevadores_veiculos?: number;
    estufas_pintura?: number;
    images?: string[];
    tipos_veiculo?: string[];
    orcamentista?: string;
    atende_placa_verde?: boolean;
    concessionaria?: boolean;
    orcamentacao?: boolean;
  };
  endereco: {
    cep?: string;
    logradouro: string;
    bairro: string;
    estado: string;
    cidade: string;
    latitude?: string;
    longitude?: string;
  };
  contatos: {
    telefone: string;
    celular?: string;
    email: string;
    nome_gerente?: string;
    telefone_gerente?: string;
    nome_proprietario?: string;
    telefone_proprietario?: string;
  };
  documentacao?: any[];
  servicos?: string[];
  polo_regionalId: string;
  prazoId?: string;
  url_logo?: string;
  url_estrutura?: string;
  optante?: boolean;
  responsavelPrazo?: string;
  contratosIds?: string[]; // New field to receive contract IDs
}

export async function criarCredenciado(
  data: CriarCredenciadoData
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    console.log(`${process.env.API_BASE_URL}/credenciados/credenciado`);
    const session = await getSessionToken();

    const response = await fetch(
      `${process.env.API_BASE_URL}/credenciados/credenciado`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
        body: JSON.stringify(data),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao criar credenciado");
    }

    return await response.json();
  });
}

export async function desvincularCredenciadoContrato(
  credenciadosIds: string | string[],
  contratoId: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    console.log(
      `${process.env.API_BASE_URL}/credenciados/desvincular-contrato`
    );
    const session = await getSessionToken();

    const response = await fetch(
      `${process.env.API_BASE_URL}/credenciados/desvincular-contrato`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
        body: JSON.stringify({
          credenciadosIds,
          contratoId,
        }),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || "Erro ao desvincular credenciado do contrato"
      );
    }

    return await response.json();
  });
}

export async function atualizarCredenciado(
  id: string,
  data: CriarCredenciadoData // Reusing the same interface since data structure is identical
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    console.log(`${process.env.API_BASE_URL}/credenciados/credenciado/${id}`);
    const session = await getSessionToken();

    const response = await fetch(
      `${process.env.API_BASE_URL}/credenciados/credenciado/${id}`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
        body: JSON.stringify(data),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao atualizar credenciado");
    }

    return await response.json();
  });
}

export async function getCredenciadoById(
  id: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    console.log(
      `URLLLLLLL: ${process.env.API_BASE_URL}/credenciados/credenciado/${id}`
    );
    const session = await getSessionToken();
    console.log("getCredenciadoById -->", id);
    console.log("--------");
    console.log(session);
    console.log("--------");

    const response = await fetch(
      `${process.env.API_BASE_URL}/credenciados/credenciado/${id}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao buscar credenciado");
    }

    return await response.json();
  });
}

export async function deleteCredenciado(
  id: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    console.log(`${process.env.API_BASE_URL}/credenciados/credenciado/${id}`);
    const session = await getSessionToken();

    const response = await fetch(
      `${process.env.API_BASE_URL}/credenciados/credenciado/${id}`,
      {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao deletar credenciado");
    }

    return await response.json();
  });
}

export async function existCredenciadoByCnpj(
  cnpj: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(
      `${process.env.API_BASE_URL}/credenciados/exist-by-cnpj`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
        body: JSON.stringify({ cnpj }),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao buscar credenciado");
    }

    return await response.json();
  });
}

export async function existCredenciadoByEmail(
  email: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();

    const response = await fetch(
      `${process.env.API_BASE_URL}/credenciados/exist-by-email`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
        body: JSON.stringify({ email }),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao buscar credenciado");
    }

    return await response.json();
  });
}

export async function getCredenciadosByContratoId(
  contratoId: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    console.log("getCredenciadosByContratoId -->", contratoId);
    const response = await fetch(
      `${process.env.API_BASE_URL}/credenciados/contrato/${contratoId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || "Erro ao buscar credenciados pelo contrato"
      );
    }

    return await response.json();
  });
}
