"use server";

import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { cookies } from "next/headers";
import { getServerSession } from "@/lib/auth/server-session";
import { withErrorHandling, ServerActionError } from "./serverActionErrors";

async function getSessionToken(): Promise<string> {
  const cookieStore = await cookies();
  const cookie = cookieStore.get(SESSION_COOKIE_NAME);
  if (!cookie || !cookie.value) {
    throw new Error("Unauthorized");
  }
  const decompressedToken = decompressToken(cookie.value);
  return decompressedToken;
}

export async function createChecklistAction(
  checklistData: any
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const sessionToken = await getSessionToken();
    await getServerSession();

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/checklists/criar-checka`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${sessionToken}`,
        },
        body: JSON.stringify(checklistData),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      console.log("Error creating checklist:", errorData);
      throw new Error(errorData.message || "Erro ao criar checklist");
    }

    return await response.json();
  });
}
