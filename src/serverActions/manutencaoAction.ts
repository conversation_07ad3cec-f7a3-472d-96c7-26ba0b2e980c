"use server";

import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { cookies } from "next/headers";
import { withErrorHandling } from "./serverActionErrors";

async function getSessionToken(): Promise<string> {
  const cookieStore = await cookies();
  const cookie = cookieStore.get(SESSION_COOKIE_NAME);
  if (!cookie || !cookie.value) {
    throw new Error("Unauthorized");
  }
  const decompressedToken = decompressToken(cookie.value);
  return decompressedToken;
}

interface ManutencaoData {
  id?: string;
  periodo?: number;
  km?: number;
  valor?: number;
  itens?: string;
  veiculoId?: string;
}

export async function updateManutencaoAction(
  id: string,
  manutencaoData: ManutencaoData
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(`${process.env.API_BASE_URL}/manutencoes/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session}`,
      },
      body: JSON.stringify(manutencaoData),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao atualizar manutenção");
    }
    return await response.json();
  });
}
export async function getManutencaoByIdAction(
  id: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(`${process.env.API_BASE_URL}/manutencoes/${id}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao obter manutenção");
    }

    const json = await response.json();
    const raw = (json as any).manutencao ?? json;

    const manutencao = {
      ...raw,
      valor:
        typeof raw.valor === "string" && !isNaN(Number(raw.valor)) ? Number(raw.valor) : raw.valor,
    };

    return { success: true, data: manutencao };
  });
}
