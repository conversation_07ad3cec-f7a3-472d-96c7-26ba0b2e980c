"use server";

import { decompressToken } from "@/lib/auth/decompress-token";
import { SESSION_COOKIE_NAME } from "@/lib/auth/helper";
import { cookies } from "next/headers";
import { ServerActionError, withErrorHandling } from "./serverActionErrors";
import { getServerSession } from "@/lib/auth/server-session";

async function getSessionToken(): Promise<string> {
  const cookieStore = await cookies();
  const cookie = cookieStore.get(SESSION_COOKIE_NAME);
  if (!cookie || !cookie.value) {
    throw new Error("Unauthorized");
  }
  const decompressedToken = decompressToken(cookie.value);
  return decompressedToken;
}

export async function listOsAvailable(): Promise<{
  success: boolean;
  data?: any;
  error?: string;
}> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const sessionData = await getServerSession();
    const response = await fetch(
      `${process.env.API_BASE_URL}/os/disponiveis/${sessionData?.credenciadoId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      if (
        response.status === 404 &&
        errorData.message === "Nenhuma ordem de serviço encontrada"
      ) {
        return { success: true, data: [] };
      }
      throw new ServerActionError(
        errorData.message || "Erro ao trocar o banco de dados",
        response.status
      );
    }
    const data = await response.json();
    return data;
  });
}

export async function createOrcamentoAction(
  orcamentoData: any
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    await getServerSession(); // caso seja necessário para fins de verificação
    const response = await fetch(`${process.env.API_BASE_URL}/orcamentos`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session}`,
      },
      body: JSON.stringify(orcamentoData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao criar orçamento");
    }
    return await response.json();
  });
}

export async function getOrcamentoAction(
  id: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(
      `${process.env.API_BASE_URL}/orcamentos/${id}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
      }
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao buscar orçamento");
    }
    return await response.json();
  });
}

export async function listOrcamentosAction(): Promise<{
  success: boolean;
  data?: any;
  error?: string;
}> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(`${process.env.API_BASE_URL}/orcamentos`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session}`,
      },
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao listar orçamentos");
    }
    return await response.json();
  });
}

export async function updateOrcamentoAction(
  id: string,
  orcamentoData: any
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(
      `${process.env.API_BASE_URL}/orcamentos/${id}`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
        body: JSON.stringify(orcamentoData),
      }
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao atualizar orçamento");
    }
    return await response.json();
  });
}

export async function updateStatusAndObservationsAction(
  id: string,
  orcamentoData: any
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(
      `${process.env.API_BASE_URL}/orcamentos/status-e-observacoes/${id}`,
      {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
        body: JSON.stringify(orcamentoData),
      }
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao atualizar orçamento");
    }
    return await response.json();
  });
}

export async function deleteOrcamentoAction(
  id: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(
      `${process.env.API_BASE_URL}/orcamentos/${id}`,
      {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
      }
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao excluir orçamento");
    }
    return await response.json();
  });
}

export async function replicarOrcamentoAction(
  orcamentoId: string,
  accreditedEntities: string[]
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(
      `${process.env.API_BASE_URL}/orcamentos/replicar`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
        body: JSON.stringify({ orcamentoId, accreditedEntities }),
      }
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao replicar orçamento");
    }
    return await response.json();
  });
}

export async function updateOsStatus(
  osId: string,
  status: string,
  orcamentoId?: string,
  arquivos?: any[],
  arquivosNotaFiscal?: any[],
  gestorAprovador?: string,
  observacao?: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(
      `${process.env.API_BASE_URL}/os/update-os-status/${osId}`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
        body: JSON.stringify({
          status,
          orcamentoId,
          arquivos,
          gestorAprovador,
          arquivosNotaFiscal,
          observacao,
        }),
      }
    );
    if (!response.ok) {
      const errorData = await response.json();
      console.log("updateOsStatus error", errorData);
      throw new Error(errorData.message || "Erro ao atualizar status da OS");
    }
    return await response.json();
  });
}

export async function updateOrcamentoStatus(
  orcamentoId: string,
  status: string,
  data_emissao?: Date,
  data_entrada?: Date
): Promise<{ success: boolean; data?: any; error?: string }> {
  console.log(
    "updateOrcamentoStatus",
    orcamentoId,
    status,
    data_emissao,
    data_entrada
  );
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(
      `${process.env.API_BASE_URL}/orcamentos/status/${orcamentoId}`,
      {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
        body: JSON.stringify({ status, data_emissao, data_entrada }),
      }
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || "Erro ao atualizar status do orçamento"
      );
    }
    return await response.json();
  });
}

export async function getOrcamentoByVehicleId(
  vehicleId: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(
      `${process.env.API_BASE_URL}/os/veiculo/${vehicleId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
      }
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || "Erro ao buscar orçamento pelo veículo"
      );
    }
    return await response.json();
  });
}

export async function invoiceOSAction(
  osId: string,
  invoiceData: any
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(
      `${process.env.API_BASE_URL}/os/invoice/${osId}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
        body: JSON.stringify(invoiceData),
      }
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao faturar OS");
    }
    return await response.json();
  });
}

export async function createOrcamentoV1Action(
  orcamentoData: any
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(`${process.env.API_BASE_URL}/orcamentos/v1`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session}`,
      },
      body: JSON.stringify(orcamentoData),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao criar o orçamento V1");
    }
    return await response.json();
  });
}

export async function getMuralOsLogsAction(
  osId: string,
  page: number = 1,
  limit: number = 25
): Promise<{
  success: boolean;
  data?: any;
  pagination?: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  error?: string;
}> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();

    const url = new URL(`${process.env.API_BASE_URL}/mural-os/os/${osId}`);
    url.searchParams.append("page", page.toString());
    url.searchParams.append("limit", limit.toString());

    const response = await fetch(url.toString(), {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Erro ao buscar logs da OS");
    }

    return await response.json();
  });
}

export async function hasOsByCredenciadoId(
  credenciadoId: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(
      `${process.env.API_BASE_URL}/os/credenciado/${credenciadoId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
      }
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || "Erro ao verificar OS do credenciado"
      );
    }
    return await response.json();
  });
}

export async function updateOsCredenciadoAction(
  osId: string,
  credenciadoId: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  return await withErrorHandling<any>(async () => {
    const session = await getSessionToken();
    const response = await fetch(
      `${process.env.API_BASE_URL}/os/${osId}/credenciado`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session}`,
        },
        body: JSON.stringify({ credenciadoId }),
      }
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || "Erro ao atualizar credenciado da OS"
      );
    }
    return await response.json();
  });
}