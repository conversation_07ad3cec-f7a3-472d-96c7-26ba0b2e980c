import { format } from "date-fns";

export function formatData(data: string) {
  return format(new Date(data), "dd/MM/yyyy HH:mm");
}

export const formatDateToBrazilian = (date: string): string => {
  if (!date) return "";
  try {
    const [year, month, day] = date.split("-");
    return `${day}/${month}/${year}`;
  } catch {
    return date;
  }
};

export const formatDateToInput = (date: string): string => {
  if (!date) return "";
  try {
    const [day, month, year] = date.split("/");
    return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
  } catch {
    return date;
  }
};