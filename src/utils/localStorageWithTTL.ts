export function setWithTTL(key: string, value: any, ttlMs: number) {
  const item = {
    value,
    expiry: new Date().getTime() + ttlMs,
  };
  localStorage.setItem(key, JSON.stringify(item));
}

export function getWithTTL<T>(key: string): T | null {
  const itemStr = localStorage.getItem(key);
  if (!itemStr) return null;

  const item = JSON.parse(itemStr);
  const now = new Date().getTime();

  if (now > item.expiry) {
    localStorage.removeItem(key);
    return null;
  }

  return item.value as T;
}
