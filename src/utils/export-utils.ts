import { format } from "date-fns";
import J<PERSON><PERSON><PERSON> from "jszip";
import FileSaver from "file-saver";
import { jsPDF } from "jspdf";
import "jspdf-autotable";

// Function to convert string to ArrayBuffer
function s2ab(s: string): ArrayBuffer {
  const buf = new ArrayBuffer(s.length);
  const view = new Uint8Array(buf);
  for (let i = 0; i < s.length; i++) {
    view[i] = s.charCodeAt(i) & 0xff;
  }
  return buf;
}

// Simple XLSX generation function
export function generateXLSX(data: any[], headers: string[], filename: string) {
  // Create worksheet content
  let worksheet = "";

  // Add headers
  worksheet += "<row>";
  headers.forEach((header) => {
    worksheet += `<c t="inlineStr"><is><t>${header}</t></is></c>`;
  });
  worksheet += "</row>";

  // Add data rows
  data.forEach((row) => {
    worksheet += "<row>";
    Object.values(row).forEach((cell: any) => {
      // Handle different data types
      if (typeof cell === "number") {
        worksheet += `<c t="n"><v>${cell}</v></c>`;
      } else if (cell instanceof Date) {
        worksheet += `<c t="inlineStr"><is><t>${format(cell, "dd/MM/yyyy")}</t></is></c>`;
      } else {
        worksheet += `<c t="inlineStr"><is><t>${cell || ""}</t></is></c>`;
      }
    });
    worksheet += "</row>";
  });

  // XML template for XLSX file
  const xmlTemplate = `<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
    <workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships">
      <sheets>
        <sheet name="Faturamento" sheetId="1" r:id="rId1"/>
      </sheets>
    </workbook>`;

  const worksheetXML = `<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
    <worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships">
      <sheetData>${worksheet}</sheetData>
    </worksheet>`;

  // Create a simple ZIP file structure
  const zip = new JSZip();
  zip.file("xl/workbook.xml", xmlTemplate);
  zip.file("xl/worksheets/sheet1.xml", worksheetXML);
  zip.file(
    "[Content_Types].xml",
    `<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types"><Default Extension="xml" ContentType="application/xml"/></Types>`
  );

  // Generate the XLSX file
  zip.generateAsync({ type: "blob" }).then((content) => {
    FileSaver.saveAs(content, filename);
  });
}

// Format currency values
export function formatCurrency(value: number): string {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(value / 100); // Assuming values are in cents
}
// Extend jsPDF type to include autoTable
declare module "jspdf" {
  interface jsPDF {
    autoTable: (...args: any[]) => void;
  }
}

// PDF generation with jsPDF-like approach
export function generatePDF(
  data: any[],
  summary: any,
  credenciadoInfo: any,
  filename: string,
  options?: {
    includeHeader?: boolean;
    includeSummary?: boolean;
    includeDetails?: boolean;
    includeFooter?: boolean;
    orientation?: "portrait" | "landscape";
  }
) {
  const opts = {
    includeHeader: true,
    includeSummary: true,
    includeDetails: true,
    includeFooter: true,
    orientation: "portrait" as const,
    ...options,
  };

  const doc = new jsPDF({
    orientation: opts.orientation,
    unit: "mm",
    format: "a4",
  });

  // Header
  if (opts.includeHeader) {
    doc.setFontSize(18);
    doc.text("RELATÓRIO DE FATURAMENTO", 10, 20);
    doc.setFontSize(12);
    doc.text(`Credenciado: ${credenciadoInfo?.nome || "N/A"}`, 10, 30);
    doc.text(`CNPJ: ${credenciadoInfo?.cnpj || "N/A"}`, 10, 40);
    doc.text(`Data de Geração: ${format(new Date(), "dd/MM/yyyy HH:mm")}`, 10, 50);
    doc.line(10, 55, 200, 55);
    doc.addPage();
  }

  // Summary
  if (opts.includeSummary) {
    doc.setFontSize(14);
    doc.text("RESUMO FINANCEIRO", 10, 20);
    doc.setFontSize(12);
    doc.text(`Total de Orçamentos: ${summary.totalOrcamentos}`, 10, 30);
    doc.text(`Total em Peças: ${formatCurrency(summary.totalPecas)}`, 10, 40);
    doc.text(`Total em Serviços: ${formatCurrency(summary.totalServicos)}`, 10, 50);
    doc.text(`TOTAL GERAL: ${formatCurrency(summary.totalGeral)}`, 10, 60);
    doc.line(10, 65, 200, 65);
    doc.addPage();
  }

  // Table
  if (opts.includeDetails) {
    // Table headers
    const headers = [
      "Nº OS",
      "Nº Orçamento",
      "Data Emissão",
      "Veículo",
      "Placa",
      "Valor Peças",
      "Valor Serviços",
      "Valor Total",
    ];

    doc.autoTable({
      head: [headers],
      body: data.map((item) => [
        item.osNumber,
        item.numeroOrcamento,
        item.dataEmissao,
        item.veiculo,
        item.placa,
        `R$ ${item.valorPecas.toFixed(2).replace(".", ",")}`,
        `R$ ${item.valorServicos.toFixed(2).replace(".", ",")}`,
        `R$ ${item.valorTotal.toFixed(2).replace(".", ",")}`,
      ]),
      startY: 20,
    });
  }

  // Footer
  if (opts.includeFooter) {
    doc.text(
      "Relatório gerado automaticamente pelo sistema de gestão",
      10,
      doc.internal.pageSize.height - 10
    );
  }

  // Save the PDF
  doc.save(filename);
}

// Function to generate a proper PDF using browser print functionality
export function generatePrintablePDF(
  data: any[],
  summary: any,
  credenciadoInfo: any,
  options?: {
    includeHeader?: boolean;
    includeSummary?: boolean;
    includeDetails?: boolean;
    includeFooter?: boolean;
    orientation?: "portrait" | "landscape";
  }
) {
  const opts = {
    includeHeader: true,
    includeSummary: true,
    includeDetails: true,
    includeFooter: true,
    orientation: "portrait" as const,
    ...options,
  };

  // Create PDF content using HTML
  let htmlContent = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <title>Relatório de Faturamento</title>
        <style>
          @page {
            size: ${opts.orientation === "portrait" ? "A4 portrait" : "A4 landscape"};
            margin: 20mm;
          }
          body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            position: relative;
            width: ${opts.orientation === "portrait" ? "210mm" : "297mm"};
            min-height: ${opts.orientation === "portrait" ? "297mm" : "210mm"};
          }
          .page-header {
            margin-bottom: 30px;
          }
          .page-header h1 {
            font-size: 18px;
            margin-bottom: 15px;
          }
          .header-line {
            border-bottom: 1px solid #000;
            margin-bottom: 15px;
          }
          .company-info {
            margin-bottom: 30px;
          }
          .company-info p {
            margin: 5px 0;
          }
          .summary-section {
            margin-bottom: 30px;
          }
          .summary-section h2 {
            font-size: 14px;
            margin-bottom: 10px;
          }
          .summary-line {
            border-bottom: 1px solid #000;
            width: 260px;
            margin-bottom: 15px;
          }
          .summary-item {
            margin: 8px 0;
          }
          .summary-total {
            font-weight: bold;
            margin-top: 10px;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
          }
          th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-size: 10px;
          }
          th {
            background-color: #f5f5f5;
            font-weight: bold;
          }
          tr:nth-child(even) {
            background-color: #f9f9f9;
          }
          .footer {
            position: fixed;
            bottom: 20px;
            left: 0;
            right: 0;
            border-top: 1px solid #000;
            padding-top: 10px;
            font-size: 8px;
            text-align: center;
          }
          @media print {
            body { print-color-adjust: exact; }
          }
        </style>
      </head>
      <body>
  `;

  // Header
  if (opts.includeHeader) {
    htmlContent += `
      <div class="page-header">
        <h1>RELATÓRIO DE FATURAMENTO</h1>
        <div class="header-line"></div>
        <div class="company-info">
          <p><strong>Credenciado:</strong> ${credenciadoInfo?.nome || "N/A"}</p>
          <p><strong>CNPJ:</strong> ${credenciadoInfo?.cnpj || "N/A"}</p>
          <p><strong>Data de Geração:</strong> ${format(new Date(), "dd/MM/yyyy HH:mm")}</p>
        </div>
      </div>
    `;
  }

  // Summary
  if (opts.includeSummary) {
    htmlContent += `
      <div class="summary-section">
        <h2>RESUMO FINANCEIRO</h2>
        <div class="summary-line"></div>
        <p class="summary-item">Total de Orçamentos: ${summary.totalOrcamentos}</p>
        <p class="summary-item">Total em Peças: ${formatCurrency(summary.totalPecas)}</p>
        <p class="summary-item">Total em Serviços: ${formatCurrency(summary.totalServicos)}</p>
        <p class="summary-total">TOTAL GERAL: ${formatCurrency(summary.totalGeral)}</p>
      </div>
    `;
  }

  // Table
  if (opts.includeDetails) {
    // Table headers
    const headers = [
      "Nº OS",
      "Nº Orçamento",
      "Data Emissão",
      "Veículo",
      "Placa",
      "Valor Peças",
      "Valor Serviços",
      "Valor Total",
    ];

    htmlContent += `
      <table>
        <thead>
          <tr>
    `;

    headers.forEach((header) => {
      htmlContent += `<th>${header}</th>`;
    });

    htmlContent += `
          </tr>
        </thead>
        <tbody>
    `;

    // Table data
    data.forEach((item) => {
      htmlContent += `
        <tr>
          <td>${item.osNumber}</td>
          <td>${item.numeroOrcamento}</td>
          <td>${item.dataEmissao}</td>
          <td>${item.veiculo}</td>
          <td>${item.placa}</td>
          <td>R$ ${item.valorPecas.toFixed(2).replace(".", ",")}</td>
          <td>R$ ${item.valorServicos.toFixed(2).replace(".", ",")}</td>
          <td>R$ ${item.valorTotal.toFixed(2).replace(".", ",")}</td>
        </tr>
      `;
    });

    htmlContent += `
        </tbody>
      </table>
    `;
  }

  // Footer
  if (opts.includeFooter) {
    htmlContent += `
      <div class="footer">
        Relatório gerado automaticamente pelo sistema de gestão
      </div>
    `;
  }

  htmlContent += `
      </body>
    </html>
  `;

  // Open a new window with the HTML content
  const printWindow = window.open("", "_blank");
  if (printWindow) {
    printWindow.document.write(htmlContent);
    printWindow.document.close();

    // Wait for content to load then print
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print();
        // Close the window after printing (or if print is cancelled)
        setTimeout(() => {
          printWindow.close();
        }, 500);
      }, 250);
    };
  }
}
