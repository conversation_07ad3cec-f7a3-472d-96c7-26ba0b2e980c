# Ignorar dependências instaladas localmente
node_modules
bun_modules

# Ignorar arquivos de build
.next
out
build
dist

# Ignorar arquivos de lock alternativos (usar só o bun.lock)
package-lock.json
yarn.lock
pnpm-lock.yaml

# Arquivos de ambiente e config local (exceto exemplo)
.env*
!.env.example

# Logs e debug
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
bun-debug.log*
storybook.log

# Arquivos de sistema e IDEs
.DS_Store
.vscode
.idea
*.swp
*.tsbuildinfo
next-env.d.ts

# Outros arquivos que não devem estar na imagem
.vercel
coverage
.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions
*.pem
