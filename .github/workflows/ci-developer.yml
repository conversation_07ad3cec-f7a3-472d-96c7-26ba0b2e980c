name: CI/CD - Developer Branch

# Executa apenas na branch developer
on:
  push:
    branches: [ developer ]
  pull_request:
    branches: [ developer ]

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [20.x]
    
    steps:
    # Checkout do código
    - name: Checkout repository
      uses: actions/checkout@v4
    
    # Setup do Node.js
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
    
    # Setup do Bun
    - name: Setup Bun
      uses: oven-sh/setup-bun@v1
      with:
        bun-version: latest
    
    # Instalar dependências
    - name: Install dependencies
      run: bun install
    
    # Verificar lint (opcional)
    # - name: Run ESLint
    #   run: bun run lint
    #   continue-on-error: false
    
    # Executar testes (remover se não houver testes)
    # - name: Run tests
    #   run: bun test
    #   continue-on-error: false
    
    # Build do projeto (com mais memória se necessário)
    - name: Build Next.js project
      run: bun run build
    
    # Verificar se o build foi gerado corretamente
    - name: Check build output
      run: |
        if [ -d ".next" ]; then
          echo "✅ Build gerado com sucesso!"
          ls -la .next/
        else
          echo "❌ Erro: Pasta .next não encontrada!"
          exit 1
        fi
    
    # Opcional: Cache do Bun para acelerar próximas execuções
    - name: Cache Bun dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.bun/install/cache
          ${{ github.workspace }}/.next/cache
        key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lockb') }}-${{ hashFiles('**.[jt]s', '**.[jt]sx') }}
        restore-keys: |
          ${{ runner.os }}-bun-${{ hashFiles('**/bun.lockb') }}-

  # Job adicional para build do Storybook (opcional)
  # storybook-build:
  #   runs-on: ubuntu-latest
  #   steps:
  #   - name: Checkout repository
  #     uses: actions/checkout@v4
    
  #   - name: Setup Node.js
  #     uses: actions/setup-node@v4
  #     with:
  #       node-version: '20.x'
    
  #   - name: Setup Bun
  #     uses: oven-sh/setup-bun@v1
  #     with:
  #       bun-version: latest
    
  #   - name: Install dependencies
  #     run: bun install
    
  #   # Build do Storybook para verificar se não há erros
  #   - name: Build Storybook
  #     run: bun run build-storybook

  # Job adicional para verificações de segurança (opcional)
  security-check:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
    
    - name: Setup Bun
      uses: oven-sh/setup-bun@v1
      with:
        bun-version: latest
    
    - name: Install dependencies
      run: bun install
    
    # Auditoria de segurança usando Bun
    - name: Run security audit
      run: bun audit
      continue-on-error: true
