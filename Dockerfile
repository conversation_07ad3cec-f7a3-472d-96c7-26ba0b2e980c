# ----------------
# Base Image
# ----------------
FROM node:20 AS base

# Definindo o diretório de trabalho no contêiner
WORKDIR /usr/src
ENV PATH="/root/.bun/bin:${PATH}"
ENV TZ=America/Sao_Paulo

# Instalando dependências básicas
RUN apt-get update -y \
    && apt-get install -y curl net-tools \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

RUN curl -fsSL https://bun.sh/install | bash

# ----------------
# Build Stage (Next.js)
# ----------------
FROM base AS build

ENV NODE_ENV=production

# Copiando arquivos de dependências antes para melhor cache
COPY package.json bun.lock ./

# Instalando dependências de produção
RUN bun install

# Copiando o restante do código
COPY . .

# Gerando a build do Next.js
RUN bun run build

# ----------------
# Produção
# ----------------
FROM base AS production

ENV NODE_ENV=production

# Copiar todos os arquivos necessários para a produção
COPY --from=build /usr/src/.next ./.next
COPY --from=build /usr/src/public ./public
COPY --from=build /usr/src/package.json ./
COPY --from=build /usr/src/next.config.ts ./
COPY --from=build /usr/src/bun.lock ./

# Instalando apenas dependências necessárias para produção
RUN bun install --production

# Expor porta padrão do Next.js
EXPOSE 3000

CMD ["sh", "-c", "npm run build && npm run start"]
# ----------------
# Desenvolvimento
# ----------------
FROM base AS development

ENV NODE_ENV=development

# Copiando o restante do código antes da instalação
COPY . .

# Instalando todas as dependências
RUN bun install

CMD ["npm", "run", "dev"]
